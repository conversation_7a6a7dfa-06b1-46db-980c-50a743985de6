using System;
using System.Linq;
using System.Linq.Expressions;
using System.Transactions;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity.Infrastructure;
using System.Text;

using Newtonsoft.Json.Linq;

using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;

using RIB.Visual.Basics.Core.Core;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.BusinessComponents.Final;

using RIB.Visual.Basics.Unit.BusinessComponents;

using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.Config.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using NLS = RIB.Visual.Resource.Master.Localization.Properties.Resources;
using RIB.Visual.Resource.Type.BusinessComponents;
using RIB.Visual.Basics.Common.Core.Final;
using RIB.Visual.Basics.Site.BusinessComponents;

using RIB.Visual.Resource.Skill.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Procurement.Contract.BusinessComponents;
using CCB = RIB.Visual.Cloud.Common.BusinessComponents;


namespace RIB.Visual.Resource.Master.BusinessComponents
{
	/// <summary>
	/// Resource Master Logic should be placed here
	/// 
	/// </summary>
	[Export(typeof(IResourceMasterLogic))]
	public class ResourceMasterResourceLogic : EntityProvidingUpdateLogic<ResourceEntity, IdentificationData>, IUpdateCompleteData, IEntityFacade, IResourceMasterLogic, IEntityAggregator
	{
		/// <summary>
		/// The singleton identifier instance for the <see cref="ResourceEntity"/> type.
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<ResourceEntity>> IdentifierInstance =
				IdentifierFactory.Create<ResourceEntity>("Id");

		/// <summary>
		/// Constructor
		/// </summary>
		public ResourceMasterResourceLogic()
		{
			SetRelationInfoIdentifier("resource.master.resource");
			Identifier = IdentifierInstance.Value;
			PermissionGUID = PermissionDescriptors.Resource;
			CompleteUpdater = this;
			OrderByExpressions = new[]
			{
				OrderTerm.Create( e => e.SortCode),
				OrderTerm.Create(e => e.Code),
				OrderTerm.Create(e => e.Id)
			};

			OrderByKey = e => Identifier.GetEntityIdentification(e);
			GetTranslatedProperties = new Func<ResourceEntity, DescriptionTranslateType>[] { ent => ent.DescriptionInfo };
			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
		}

		//public string check(ResourceEntity entity)
		//{

		//	string result = null;  
		//	if(entity.SortCode != null)
		//	{
		//		result = entity.SortCode; 
		//	}
		//	if (entity.Code != null)
		//	{
		//		result = entity.Code; 
		//	}
		//	//if (entity.Id != null)
		//	//{
		//	//	result = RIB.Visual.Platform.Common.Convert.ToString(entity.Id);
		//	//}
		//	return result; 

		//}

		/// <summary>
		/// Get DbModel
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		private ICompanyInfo GetLogonCompanyInfo()
		{
			var currentContext = BusinessApplication.BusinessEnvironment.CurrentContext;
			var companyInfoProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<ICompanyInfoProvider>();

			return companyInfoProvider.GetInstanceInfo(currentContext.ClientId);
		}

		private int GetCurrentResourceContext(ICompanyInfo companyInfo)
		{

			var nullableId = companyInfo.GetResourceContext();

			if (nullableId.HasValue)
			{
				return nullableId.Value;
			}

			throw new BusinessLayerException
			{
				ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError,
				ErrorMessage = NLS.ERR_TitleNoCompanyResourceContext,
				ErrorDetail = NLS.ERR_TitleNoCompanyResourceContext
			};
		}

		/// <summary>
		/// Gets search context for flat list.
		/// </summary>
		/// <param name="request">The filter request</param><param name="response">The filter response</param>
		/// <returns>
		/// The SearchSpecification instance
		/// </returns>
		protected override SearchSpecification<ResourceEntity, IdentificationData> GetListSearchContext(FilterRequest request, out FilterResponse response)
		{
			var ctx = base.GetListSearchContext(request, out response);

			if (!request.IncludeNonActiveItems.HasValue || !request.IncludeNonActiveItems.Value)
			{
				ctx.RestrictToActivePredicate = e => e.IsLive;
			}

			var compInfo = GetLogonCompanyInfo();
			if (!request.UseCurrentClient.HasValue || !request.UseCurrentClient.Value)
			{
				var resourceContextId = GetCurrentResourceContext(compInfo);
				ctx.UseCurrentClientPredicate = e => e.ResourceContextFk == resourceContextId;
			}
			else
			{
				var companyId = compInfo.Id;
				ctx.UseCurrentClientPredicate = e => e.CompanyFk == companyId;
			}

			response = ctx.FilterOut;
			return ctx;
		}

		/// <summary>
		/// Create a new Resource
		/// </summary>
		/// <returns></returns>
		public override ResourceEntity Create(IdentificationData creationData)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);
			ResourceEntity entity = new ResourceEntity();
			entity.Id = SequenceManager.GetNext(GetEntityTableName());

			entity.Code = GenerateCode(RubricConstant.ResourceMaster);

			var companyInfo = GetLogonCompanyInfo();

			entity.CompanyFk = companyInfo.Id;
			entity.UomBasisFk = 0;
			entity.TypeFk = 0;
			entity.ResourceContextFk = GetCurrentResourceContext(companyInfo);
			entity.CalendarFk = companyInfo.GetCalendar();

			RelationDefaultValueSetter.Handle(entity, new List<Tuple<string, Action<ResourceEntity, int>, int?>>() {
				new Tuple<string, Action<ResourceEntity, int>, int?>("basics.customize.resourcekind", (e, i) => e.KindFk = i, null)
			});
			return entity;
		}

		/// <summary>
		/// Override to get all not provided values defaulted
		/// </summary>
		/// <param name="creationData"></param>
		/// <param name="resource"></param>
		protected override void InitMissingDefaults(JObject creationData, ref ResourceEntity resource)
		{
			var companyInfo = GetLogonCompanyInfo();
			resource.ResourceContextFk = GetCurrentResourceContext(companyInfo);
			resource.CompanyFk = companyInfo.Id;

			InitIfNotInCreationData(creationData, "Code", (p, s) => p.Code = s,
				GenerateCode(RubricConstant.ResourceMaster), ref resource);
			InitIfNotInCreationData(creationData, "UomBasisFk", (p, i) => p.UomBasisFk = i,
				0, ref resource);
			InitIfNotInCreationData(creationData, "TypeFk", (p, i) => p.TypeFk = i,
				0, ref resource);
			InitIfNotInCreationData(creationData, "CalendarFk", (p, i) => p.CalendarFk = i,
				companyInfo.GetCalendar(), ref resource);
			InitIfNotInCreationData(creationData, "KindFk", (p, i) => p.KindFk = i,
				GeResourceDefaultValue("basics.customize.resourcekind"), ref resource);
		}
		private int GeResourceDefaultValue(string identifier)
		{
			var defAccess = BusinessApplication.BusinessEnvironment.GetExportedValue<IDefaultEntityProvider>(identifier);
			var def = defAccess.GetDefaultForCurrentContext();
			if (def != null)
			{
				return def.Id;
			}

			return 0;
		}

		/// <summary>
		/// CreateMultipleResources
		/// </summary>
		/// <param name="amountCounter"></param>
		/// <returns></returns>
		public List<ResourceEntity> CreateMultipleResources(int amountCounter)
		{
			RVPBizComp.Permission.Ensure(PermissionGUID, Permissions.Create);

			var nextIdList = this.SequenceManager.GetNextList(GetEntityTableName(), amountCounter);
			List<ResourceEntity> newResourceList = new List<ResourceEntity>();
			var companyInfo = GetLogonCompanyInfo();
			foreach (var id in nextIdList)
			{
				var entity = new ResourceEntity();
				entity.Id = id;
				entity.CompanyFk = companyInfo.Id;
				entity.UomBasisFk = 0;
				entity.TypeFk = 0;
				entity.ResourceContextFk = GetCurrentResourceContext(companyInfo);
				entity.CalendarFk = companyInfo.GetCalendar();
				newResourceList.Add(entity);
			}
			return newResourceList;
		}

		IEnumerable<IIdentifyable> IUpdateCompleteData.HandleUpdate(IEnumerable<IIdentifyable> complexData)
		{
			var updates = complexData.Select(e => (ResourceComplete)e);
			var resourceCompletes = updates as ResourceComplete[] ?? updates.ToArray();
			foreach (var update in resourceCompletes)
			{
				SaveResourceComplete(update);
			}
			return resourceCompletes;
		}
		/// <summary>
		/// Save Resource Complete
		/// </summary>
		public void SaveResourceComplete(ResourceComplete resourceComplete)
		{
			try
			{
				using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
				{
					#region Logic class
					var photoLogic = new ResourceMasterPhotoLogic();
					var poolLogic = new ResourceMasterPoolLogic();
					var contextLogic = new ResourceMasterResource2ContextLogic();
					#endregion

					if(resourceComplete.PartsToSave != null || resourceComplete.PartsToDelete != null)
					{
						HandlePartUpdates(resourceComplete);
					}

					if (resourceComplete.Resources != null)
					{

						resourceComplete.Resources = Save(resourceComplete.Resources);
					}

					#region Photo Delete And Save
					photoLogic.DeleteComplete(GetDbModel(), resourceComplete.PhotoToDelete);
					photoLogic.SaveComplete(GetDbModel(), resourceComplete.PhotoToSave);
					#endregion

					EntityUpdateDispatcher.Handle(resourceComplete);

					transaction.Complete();
				}
			}
			catch (Exception ex)
			{
				throw Cloud.Common.BusinessComponents.Helper.BuildBizLayerException(ex);
			}
		}

		/// <summary>
		/// Handle part updates
		/// </summary>
		/// <param name="resourceComplete"></param>
		private void HandlePartUpdates(ResourceComplete resourceComplete)
		{
			var partLogic = new ResourcePartLogic();
			var resEntity = this.GetById(new IdentificationData() { Id = resourceComplete.MainItemId });
			if (resEntity != null)
			{
				var partEntities = partLogic.GetByFilter(p => p.ResourceFk == resEntity.Id);

				var partEntitiesInstance = partEntities
					.Concat(resourceComplete.PartsToSave)
					.GroupBy(p => p.Id)
					.Select(g => g.Last());

				if (resourceComplete.PartsToDelete != null)
				{
					partEntitiesInstance = partEntitiesInstance.Where(p => !resourceComplete.PartsToDelete.Any(d => d.Id == p.Id));
				}

				CheckResTypeIsValid(resEntity, partEntitiesInstance);

			}
		}

		/// <summary>
		/// Check resource type is valid from parts
		/// </summary>
		/// <param name="resEntity"></param>
		/// <param name="partEntites"></param>
		/// <exception cref="Exception"></exception>
		private void CheckResTypeIsValid(ResourceEntity resEntity, IEnumerable<ResourcePartEntity> partEntites)
		{
			var partLogic = new ResourcePartLogic();

			// Check if resource type is valid
			if (partEntites.Any())
			{
				var res2ValidResTypeIds = partLogic.GetValidResTypeIds(partEntites);
				if (res2ValidResTypeIds.ContainsKey(resEntity.Id))
				{
					if (!res2ValidResTypeIds[resEntity.Id].Contains(resEntity.TypeFk))
					{
						throw new Exception(NLS.ERR_CheckResTypeIsValidFail);
					}
				}

			}
		}

		/// <summary>
		/// Delete in transaction
		/// </summary>
		/// <param name="entities"></param>
		public bool DeleteInTransaction(IEnumerable<ResourceEntity> entities)
		{
			var result = true;
			var contextLogic = new ResourceMasterResource2ContextLogic();
			var resource2PlantLogic = new Resource2etmPlantLogic();
			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				var resourceEntities = entities as ResourceEntity[] ?? entities.ToArray();
				var resourceIds = resourceEntities.CollectIds(e => e.Id).ToList();

				var listRes2MdcCtxt = contextLogic.GetByFilter(e => resourceIds.Contains(e.ResourceFk));
				var res2EtmPlant = resource2PlantLogic.GetByFilter(e => resourceIds.Contains(e.ResourceFk));

				try
				{
					contextLogic.Delete(listRes2MdcCtxt);
					resource2PlantLogic.Delete(res2EtmPlant);
				}
				catch
				{
					result = false;
				}
				Delete(resourceEntities);
				transaction.Complete();
			}
			return result;
		}

		///// <summary>
		///// Translation
		///// </summary>
		///// <returns></returns>
		//public override IList<Func<ResourceEntity, DescriptionTranslateType>> GetTranslationDescriptors()
		//{
		//	return new List<Func<ResourceEntity, DescriptionTranslateType>>(1) { e => e.DescriptionInfo };
		//}

		/// <summary>
		/// Generate Code
		/// </summary>
		/// <param name="rubicId"></param>
		/// <returns></returns>
		public string GenerateCode(int rubicId)
		{
			var code = string.Empty;
			var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;

			var rubricCatalog = new RubricCatLogic().GetList(rubicId).Where(e => e.Sorting > 0).OrderBy(e => e.Sorting).FirstOrDefault();
			if (rubricCatalog != null)
			{
				var rubricCatalogId = rubricCatalog.Id;

				var companyNumber = new BasicsCompanyNumberLogic().SelectNextCompanyNumber(companyId, rubicId, rubricCatalogId, 0);

				if (companyNumber != null)
				{
					code = companyNumber.Service_FormattedCompanyNo;
				}
			}
			return code;
		}

		//if (item == null) {throw new ArgumentNullException("item");}

		/// <summary>
		/// Check whether the code already exists
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		public bool IsUnique(Expression<Func<ResourceEntity, bool>> filter)
		{
			using (var dbContext = new DbContext(ModelBuilder.DbModel))
			{
				return !dbContext.GetFiltered(filter).Any();
			}
		}

		/// <summary>
		/// GetForPlanningBoard
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		public IEnumerable<ResourceEntity> GetForPlanningBoard(PlanningBoardFilter filter)
		{
			var resourceContextId = GetCurrentResourceContext(GetLogonCompanyInfo());

			PlanningboardFilterLogic filterLogic = new PlanningboardFilterLogic();
			ModuleLogic moduleLogic = new ModuleLogic();
			var firstOrDefault = moduleLogic.GetFilteredModules(e => e.InternalName == filter.ModuleName).FirstOrDefault();
			var moduleId = -1;
			if (firstOrDefault != null)
			{
				moduleId = firstOrDefault.Id;
			}

			var resourceTypeIdsFromPlanningBoard = EvaluateFilterByRequiredResourceType(filter);
			IEnumerable<PlanningboardFilterEntity> configuredResourceTypes = filterLogic.GetPlanningBoardFilterByModule(moduleId).ToList();

			using (var dbContext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
			{
				IQueryable<ResourceEntity> query = dbContext.Entities<ResourceEntity>();
				query = query.Where(e => e.ResourceContextFk == resourceContextId);
				query = query.Where(e => e.IsLive);

				if (configuredResourceTypes.Any() || resourceTypeIdsFromPlanningBoard.Any())
				{
					var configuredResourceTypesIds = configuredResourceTypes.CollectIds(e => e.TypeFk).ToArray();
					if(configuredResourceTypesIds == null)
					{
						configuredResourceTypesIds = Array.Empty<int>();
					}

					var allTypeIds = configuredResourceTypesIds.Union(resourceTypeIdsFromPlanningBoard).Distinct().ToArray();

					query = query.Where(e => allTypeIds.Contains(e.TypeFk));
				}

				//filter by DispatcherGroupFk
				if (filter.DispatcherGroupFk.HasValue)
				{
					var dispGroups = new BasicsCustomizeLogisticsDispatcherGroup2GroupLogic().GetCooperatingGroups(filter.DispatcherGroupFk.Value).ToArray();
					var resTypes = new ResourceTypeMainLogic().GetListByFilter(rt => rt.DispatcherGroupFk.HasValue && dispGroups.Contains(rt.DispatcherGroupFk.Value)).ToArray();
					var configuredResourceTypesIds = resTypes.Select(e => e.Id).ToArray();

					//query = query.Where(e => e.DispatcherGroupFk == filter.DispatcherGroupFk || configuredResourceTypesIds.Contains(e.TypeFk));
					query = query.Where(e => (e.DispatcherGroupFk.HasValue && dispGroups.Contains(e.DispatcherGroupFk.Value)) ||
						(!e.DispatcherGroupFk.HasValue && configuredResourceTypesIds.Contains(e.TypeFk)));
				}
				else if (filter.ExpectsDispatcherGroupFilter)
				{
					query = query.Where(e => false);
				}

				//filter by projectFk
				if (filter.ProjectFk.HasValue)
				{
					var reqLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IResRequisitionLogic>();
					List<IResRequisitionEntity> requisitionList = reqLogic.GetRequisitionsByProject(filter.ProjectFk.Value).ToList();
					var resLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IResReservationLogic>();
					var reservationList = resLogic.GetReservationsByRequisitions(requisitionList).ToArray();

					var resourceIds = requisitionList.CollectIds(e => e.ResourceFk).ToArray().Union(reservationList.CollectIds(r => r.ResourceFk).ToArray()).Distinct().ToArray();
					var unhandledReqs = requisitionList.Where(e => !e.ResourceFk.HasValue).ToArray();
					var resourceTypes = unhandledReqs.CollectIds(e => e.TypeFk).ToArray();

					query = query.Where(e => resourceIds.Contains(e.Id) || resourceTypes.Contains(e.TypeFk));
				}
				else if (filter.ExpectsProjectFilter)
				{
					query = query.Where(e => false);
				}

				var res = query.ToArray();
				DoTranslate(res);
				PostProcess(res);
				return res;
			}
		}

		private int[] EvaluateFilterByRequiredResourceType(PlanningBoardFilter filter)
		{
			if(filter.ResourceTypeIdList.IsNullOrEmpty())
			{
				return Array.Empty<int>();
			}

			var ids = filter.ResourceTypeIdList.Distinct().ToArray();
			var resTypeLogic = new ResourceTypeMainLogic();
			var entities = resTypeLogic.GetListByFilter(t => ids.Contains(t.Id));
			var parentIds = entities.CollectIds(t => t.ResourceTypeFk).ToArray();

			var roots = resTypeLogic.GetTreeByFilter(t => parentIds.Contains(t.Id));
			var all = roots.Flatten(r => r.SubResources).ToArray();

			return all.Select(rt => rt.Id).Distinct().ToArray();
		}


		/// <summary>
		/// Calculates the daily capacity of given resources in a given time span
		/// </summary>
		/// <param name="resourceIds"></param>
		/// <param name="from"></param>
		/// <param name="to"></param>
		/// <returns></returns>
		public Dictionary<int, Dictionary<DateTime, decimal>> GetCapacityPerDayPerResource(IEnumerable<int> resourceIds, DateTime from, DateTime to)
		{
			var calendarLogic = Injector.Get<ICalendarUtilitiesLogic>();

			IDictionary<DateTime, decimal> workingHours = new Dictionary<DateTime, decimal>();

			var resourceLogic = Injector.Get<IResourceMasterLogic>();
			var uomLogic = new BasicsUnitLogic();

			var resources = resourceLogic.GetByIds(resourceIds);
			var uoms = uomLogic.GetUomList();
			var capacityPerDayPerRrcs = new Dictionary<int, Dictionary<DateTime, decimal>>();

			foreach (var resource in resources)
			{
				if (resource.CalendarFk != null)
				{
					Dictionary<DateTime, decimal> capacityPerDay = new Dictionary<DateTime, decimal>();
					var uom = uoms.FirstOrDefault(u => u.Id == resource.UomBasisFk);
					decimal baseworkingHours = 0;
					if (uom != null && uom.Factor != null)
					{
						workingHours = calendarLogic.GetDetailedWorkingHours(resource.CalendarFk.Value, from, to);
						foreach (var workingHour in workingHours)
						{
							capacityPerDay.Add(workingHour.Key, (workingHour.Value * (resource.Capacity * (decimal)uom.Factor)));
							if (baseworkingHours == 0 && workingHour.Value > 0)
							{
								baseworkingHours = workingHour.Value;
							}
						}
					}
					capacityPerDayPerRrcs.Add(resource.Id, capacityPerDay);
				}
			}

			return capacityPerDayPerRrcs;
		}

		/// <summary>
		/// Performs some post-processing on retrieved entities before they are returned.
		/// </summary>
		/// <param name="entities">The enumeration of entities.
		///               This will not be <see langword="null"/> and will not contain <see langword="null"/> as an element.</param>
		/// <remarks>
		/// <para>
		/// This method post-processes all retrieved entities right before they are returned.
		///               It may be overridden to include sub-entities or transient data.
		/// </para>
		/// </remarks>
		protected override void PostProcess(IEnumerable<ResourceEntity> entities)
		{

			if (!entities.IsNullOrEmpty())
			{
				var resources = entities.ToArray();

				ProvideSkillInformation(resources);
				ProvideResourceInformation(resources);
				ProvideResTypeParentList(resources);
				ProvideResourceProvSkillsInfo(resources);
			}
		}

		// due to issue 123197 (planning board)
		private void ProvideResourceProvSkillsInfo(IEnumerable<ResourceEntity> resources)
		{
			var resource2ProvSkillLogic = new ProvidedResourceSkillLogic();
			var resIds = resources.Select(e => e.Id).ToArray();
			var resource2ProvSkills = resource2ProvSkillLogic.GetByFilter(e => resIds.Contains(e.ResourceFk)).ToArray();

			foreach (var res in resources)
			{
				var matchedResource2ProvSkills = resource2ProvSkills.Where(e => e.ResourceFk == res.Id).ToArray();
				res.ProvidedResourceSkillList = matchedResource2ProvSkills;
			}

		}

		// due to issue 130962 (planning board)
		private void ProvideResTypeParentList(ResourceEntity[] resources)
		{
			var resTypeLogic = new ResourceTypeMainLogic();
			var resTypes = resTypeLogic.GetResourceTypesByCompanyFlat(BusinessApplication.BusinessEnvironment.CurrentContext.ClientId);
			ResourceTypeEntity resTypeEntity = null;
			foreach (var entity in resources)
			{
				var typeList = new List<ResourceTypeEntity>();
				var resTypeId = entity.TypeFk;
				resTypeEntity = resTypes.FirstOrDefault(e => e.Id == resTypeId);

				if (resTypeEntity != null)
				{
					typeList.Add(resTypeEntity);

					while (resTypeEntity.ResourceTypeFk.HasValue)
					{
						resTypeId = resTypeEntity.ResourceTypeFk.Value;
						resTypeEntity = resTypes.FirstOrDefault(e => e.Id == resTypeId);
						typeList.Add(resTypeEntity);
						resTypeEntity.SubResources.Clear();

					}
					entity.ResTypeParentList = typeList;
				}
				else
				{
					entity.ResTypeParentList = new List<ResourceTypeEntity>();
				}
			}
		}
		private void ProvideSkillInformation(ResourceEntity[] resources)
		{
			var resourceProvSkillDict = new Dictionary<int, List<int>>();
			var resourceReqSkillDict = new Dictionary<int, List<int>>();
			var skillIdList = CollectSkillIdsForResource(resources, resourceProvSkillDict, resourceReqSkillDict);
			SetSkills(resources, resourceProvSkillDict, resourceReqSkillDict, skillIdList);
		}

		private void ProvideResourceInformation(ResourceEntity[] resources)
		{
			var resourceIds = resources.Select(e => new IdentificationData() { Id = e.Id }).ToArray();

			// In case more properties are needed as transient fields, please enhance the view for performance reasons
			var resourceInfos = new ResourceMasterInformationLogic().GetByIds(resourceIds).ToArray();

			foreach (var resource in resources)
			{
				var resourceInfo = resourceInfos.FirstOrDefault(pi => pi.Id == resource.Id);
				if (resourceInfo != null)
				{
					resource.IsRateReadOnly = resourceInfo.HasParts;
					resource.HeaderCode = resourceInfo.ContractCode;
					resource.HeaderDescription = resourceInfo.ContractDesc;
					resource.IsHired = resourceInfo.IsHired;
				}
			}
		}

		/// <summary>
		/// Collect skill ids for a resource entity.
		/// </summary>
		/// <param name="resourceEntities"/>
		/// <param name="resourceProvSkillDict"/>
		/// <param name="resourceReqSkillDict"/>
		/// <returns>List of skill ids for the resource entity</returns>
		private List<int> CollectSkillIdsForResource(ResourceEntity[] resourceEntities, Dictionary<int, List<int>> resourceProvSkillDict, Dictionary<int, List<int>> resourceReqSkillDict)
		{
			var resIds = resourceEntities.CollectIds(r => r.Id).ToArray();
			var shunkedResIds = resIds.ToSizedChunks(2048).ToArray();
			var provSkillsList = new List<ProvidedResourceSkillEntity>();
			var reqSkillsList = new List<RequiredResourceSkillEntity>();
			var provLogic = new ProvidedResourceSkillLogic();
			var reqLogic = new RequiredResourceSkillLogic();
			foreach (var shunk in shunkedResIds)
			{
				provSkillsList.AddRange(provLogic.GetByFilter(e => shunk.Contains(e.ResourceFk)).ToArray());
				reqSkillsList.AddRange(reqLogic.GetByFilter(e => shunk.Contains(e.ResourceFk)).ToArray());
			}

			var skillIdList = new List<int>();

			foreach (var resourceEntity in resourceEntities)
			{
				var helper = provSkillsList.Where(rs => rs.ResourceFk == resourceEntity.Id).CollectIds(s => s.SkillFk).ToList();
				resourceProvSkillDict.Add(resourceEntity.Id, helper);
				skillIdList = skillIdList.Union(helper).ToList();

				helper = reqSkillsList.Where(rs => rs.ResourceFk == resourceEntity.Id).CollectIds(s => s.SkillFk).ToList();
				resourceReqSkillDict.Add(resourceEntity.Id, helper);
				skillIdList = skillIdList.Union(helper).ToList();
			}

			return skillIdList;
		}

		/// <summary>
		/// Set skills for a resource entity.
		/// </summary>
		/// <param name="resources"/>
		/// <param name="resourceProvSkillDict"/>
		/// <param name="resourceReqSkillDict"/>
		/// <param name="skillIdList"/>
		private void SetSkills(ResourceEntity[] resources, Dictionary<int, List<int>> resourceProvSkillDict, Dictionary<int, List<int>> resourceReqSkillDict, List<int> skillIdList)
		{
			var skillList = new ResourceSkillLogic().GetSkillEntities(skillIdList);

			foreach (var resourceEntity in resources)
			{
				var resourceProvSkillList = new List<ISkillEntity>();
				var resourceReqSkillList = new List<ISkillEntity>();
				var resourceProvSkillIdList = resourceProvSkillDict[resourceEntity.Id];
				var resourceReqSkillIdList = resourceReqSkillDict[resourceEntity.Id];

				SetResourceSkillList(skillList, resourceProvSkillIdList, resourceProvSkillList);
				SetResourceSkillList(skillList, resourceReqSkillIdList, resourceReqSkillList);

				resourceEntity.ProvidedSkillList = resourceProvSkillList;
				resourceEntity.RequiredSkillList = resourceReqSkillList;
			}
		}

		/// <summary>
		/// Set resource skills for a resource.
		/// </summary>
		/// <param name="skillList"/><param name="skillIdList"/><param name="resourceSkillList"/>
		private void SetResourceSkillList(IEnumerable<ISkillEntity> skillList, List<int> skillIdList, List<ISkillEntity> resourceSkillList)
		{
			foreach (var skillId in skillIdList)
			{
				foreach (var skill in skillList)
				{
					if (skillId == skill.Id)
					{
						resourceSkillList.Add(skill);
					}
				}
			}
		}

		/// <summary>
		/// Method to get one sequence id
		/// </summary>
		/// <returns/>
		protected override string GetEntityTableName()
		{
			return "RES_RESOURCE";
		}

		/// <summary>
		/// Save process of entity.
		/// </summary>
		/// <param name="entity"/><param name="dbContext"/>
		protected override void SavePostProcessing(ResourceEntity entity, DbContext dbContext)
		{
			dbContext.ExecuteStoredProcedure("RES_RESOURCE_PROC", entity.Id);
		}

		/// <summary>
		/// Save process of entity.
		/// </summary>
		/// <param name="entities"/><param name="dbContext"/>
		protected override void SavePostProcessing(IEnumerable<ResourceEntity> entities, DbContext dbContext)
		{
			foreach (var entity in entities)
			{
				SavePostProcessing(entity, dbContext);
			}
		}

		/// <summary>
		/// get all resources to the current context
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceEntity> GetResourcesByContext()
		{
			var resourceContextId = GetCurrentResourceContext(GetLogonCompanyInfo());

			return GetListByFilter(e => e.ResourceContextFk == resourceContextId && e.IsLive).ToList();
		}

		/// <summary>
		/// Gets lookup search list by request.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public override LookupSearchResponse<ResourceEntity> GetLookupSearchList(RIB.Visual.Basics.Common.Core.Final.LookupSearchRequest request)
		{
			var response = new Basics.Common.Core.LookupSearchResponse<ResourceEntity>();
			if (request != null && request.RequestIds != null && request.RequestIds.Any()) //get by ids
			{
				response.SearchList = GetByIds(request.RequestIds);
				response.RecordsFound = response.RecordsRetrieved = response.SearchList.Count();
			}
			else if (request == null)
			{
				response.SearchList = GetResourcesByContext();
				response.RecordsFound = response.RecordsRetrieved = response.SearchList.Count();
			}
			else
			{
				response = GetLookupSearchList4FlatList(request);
			}

			return response;
		}
		/// <summary>
		/// Provides extension for subclass should build specific filer for lookup data.
		/// </summary>
		/// <param name="commonQuery"/><param name="request"/><param name="context"/>
		/// <returns/>
		protected override IQueryable<ResourceEntity> DoBuildLookupSearchFilter(IQueryable<ResourceEntity> commonQuery, LookupSearchRequest request, DbContext context)
		{
			var resourceCtx = GetCurrentResourceContext(GetLogonCompanyInfo());
			commonQuery = commonQuery.Where(e => e.ResourceContextFk == resourceCtx && e.IsLive);

			var list = commonQuery.ToArray();
			if (request.FilterKey == "resource-master-filter" || request.FilterKey == "resource-master-filter2" || request.FilterKey == "resource-master-filter3")
			{
				var siteFk = request.TryGetParameterValueAsInt("siteFk");
				var kindFk = request.TryGetParameterValueAsInt("kindFk");
				var groupFk = request.TryGetParameterValueAsInt("groupFk");
				var typeFk = request.TryGetParameterValueAsInt("typeFk");
				var dispatchGroupFk = request.TryGetParameterValueAsInt("dispatchGroupFk");
				var validTo = request.TryGetDateTimeUtc("validTo");
				var validFrom = request.TryGetDateTimeUtc("validFrom");
				var isParentIncluded = request.TryGetParameterValueAsBool("isParentIncluded");

				var requisitionFk = request.TryGetParameterValueAsInt("requisitionFk");
				if (request.FilterKey == "resource-master-filter2" && requisitionFk.HasValue)
				{
					List<int> resourceIds = new List<int>();

					// 1. Get all hard skills from requisition with ID == requisitionFk.Value
					var requisition2SkillLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IRequisitionRequiredSkillLogic>();
					var requiredHardSkillIds = requisition2SkillLogic.GetSkillIdsByRequisition(requisitionFk.Value);

					if (requiredHardSkillIds.Any())
					{
						// 2. Get all resources that cover all of the hard skills from the 1.
						var providedResourceSkillLogic = new ProvidedResourceSkillLogic();
						var providedHardSkillsByResource = providedResourceSkillLogic.GetCoresByFilter(e => requiredHardSkillIds.Contains(e.SkillFk)).Distinct().GroupBy(e => e.ResourceFk).ToDictionary(e => e.Key, g => g.ToList());

						foreach (var grp in providedHardSkillsByResource)
						{
							var providedHardSkillIds = grp.Value;

							// requiredHardSkillIds contains a distinct list of the required mandatory skills
							// providedHardSkillIds contains a distinct list of the mandatory skills provided by a resource
							// to check if the resource completely covers the required skills, it should be enough to simply compare the lengths of the two arrays

							if (requiredHardSkillIds.Count() == providedHardSkillIds.Count)
							{
								// 3. Add the ids from 2. to resourceIds
								resourceIds.Add(grp.Key);
							}
						}

						commonQuery = commonQuery.Where(e => resourceIds.Contains(e.Id));
					}
				}

				if (siteFk.HasValue)
				{
					var siteIds = new BasicsSiteNewLogic().GetTreeWithParentSite(resourceCtx, siteFk.Value, isParentIncluded ?? false, true).Select(x => x.Id).ToArray();
					if (siteIds.Any())
					{
						commonQuery = commonQuery.Where(e => siteIds.Contains(e.SiteFk));
					}
				}
				if (kindFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.KindFk == kindFk);
				}
				if (groupFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.GroupFk == groupFk);
				}
				if (dispatchGroupFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.DispatcherGroupFk == dispatchGroupFk);
				}
				if (typeFk.HasValue)
				{
					List<int> typeIds = new List<int>();
					typeIds.Add(typeFk.Value);
					var typeLogic = new ResourceTypeMainLogic();
					var typeChildren = typeLogic.GetListByFilter(e => e.ResourceTypeFk == typeFk.Value).ToArray();
					foreach (var type in typeChildren)
					{
						typeIds.Add(type.Id);
						if (type.SubResources.Count > 0)
						{
							typeIds.AddRange(GetTypeChildrenIds(type.SubResources.ToArray(), typeLogic));
						}
					}
					if (typeIds.Any())
					{
						commonQuery = commonQuery.Where(e => typeIds.Contains(e.TypeFk));
					}
				}
				if (validFrom.HasValue)
				{
					if (request.FilterKey != "resource-master-filter3")
					{
						commonQuery = commonQuery.Where(e => !e.Validfrom.HasValue || DateTime.Compare(e.Validfrom.Value, validFrom.Value) >= 0);
					}
					else
					{
						commonQuery = commonQuery.Where(e => !e.Validfrom.HasValue || DateTime.Compare(e.Validfrom.Value, validFrom.Value) <= 0);
					}

				}
				if (validTo.HasValue)
				{
					if (request.FilterKey != "resource-master-filter3")
					{
						commonQuery = commonQuery.Where(e => !e.Validto.HasValue || DateTime.Compare(e.Validto.Value, validTo.Value) <= 0);
					}
					else
					{
						commonQuery = commonQuery.Where(e => !e.Validto.HasValue || DateTime.Compare(e.Validto.Value, validTo.Value) >= 0);
					}
				}
			}

			if (!String.IsNullOrEmpty(request.SearchText))
			{
				//quick fix for: Defect: #126319 ICW 4224_Resource Master_ Unable to add Resource pool with DUTCH language settings in Willemen Instance

				// specify fields to search
				//var opts = new QueryFilterOptions<ResourceEntity> { SearchText = request.SearchText, IgnoreSearchPattern = true };
				//opts.Add(request.SearchFields);

				//commonQuery = commonQuery.JoinTrAndFilter(context, opts);

				commonQuery = commonQuery.Where(e => e.Code.Contains(request.SearchText) || e.DescriptionInfo.Description.Contains(request.SearchText) || e.SearchPattern.Contains(request.SearchText));
			}
			return commonQuery;
		}

		/// <summary>
		/// gets resource by filter
		/// </summary>
		/// <param name="siteFk"></param>
		/// <param name="groupFk"></param>
		/// <param name="kindFk"></param>
		/// <param name="typeFk"></param>
		/// <param name="validFrom"></param>
		/// <param name="validTo"></param>
		/// <returns></returns>
		public IEnumerable<ResourceEntity> GetLookupListByFilter(int? siteFk = null, int? groupFk = null, int? kindFk = null, int? typeFk = null,
				DateTime? validFrom = null, DateTime? validTo = null)
		{
			//var list = GetListByFilter(null);
			var list = GetResourcesByContext();
			if (siteFk > 0)
			{
				list = list.Where(e => e.SiteFk == siteFk);
			}
			if (groupFk.HasValue)
			{
				list = list.Where(e => e.GroupFk == groupFk.Value);
			}
			if (kindFk.HasValue)
			{
				list = list.Where(e => e.KindFk == kindFk.Value);
			}
			if (typeFk.HasValue)
			{
				list = list.Where(e => e.TypeFk == typeFk.Value);
			}
			if (validFrom.HasValue)
			{
				list = list.Where(e => !e.Validfrom.HasValue || DateTime.Compare(e.Validfrom.Value, validFrom.Value) >= 0);
			}
			if (validTo.HasValue)
			{
				list = list.Where(e => !e.Validto.HasValue || DateTime.Compare(e.Validto.Value, validTo.Value) <= 0);
			}
			return list.ToList();
		}

		#region IEntityFacade members

		string IEntityFacade.Name
		{
			get { return "Resource"; }
		}

		string IEntityFacade.Id
		{
			get { return EntitiyFacadeIdentifier.RESOURCE_RESOURCE; }
		}

		string IEntityFacade.ModuleName
		{
			get { return "resource.master"; }
		}

		string[] IEntityFacade.Properties
		{
			get { return _entityProperties.GetPropertyNames(); }
		}

		IDictionary<string, object> IEntityFacade.Get(int id)
		{
			var entity = GetById(new IdentificationData { Id = id });
			var objectDic = ToDictionary(entity);
			return objectDic;
		}

		IDictionary<string, object> IEntityFacade.Save(IDictionary<string, object> entityDictionary)
		{
			var id = entityDictionary.GetId<int>();
			var entity = GetById(new IdentificationData { Id = id });

			entity.SetObject(entityDictionary, _entityProperties);

			return ToDictionary(Save(entity));
		}

		private IDictionary<string, object> ToDictionary(ResourceEntity entity)
		{
			var objectDic = entity.AsDictionary(_entityProperties);

			return objectDic;
		}

		private static readonly ConvertProperties _entityProperties = new ConvertProperties()
				.Add("Id", true)
				.Add("Code", true)
				.Add("DescriptionInfo")
				.Add("SiteFk", "Site")
				.Add("ResourceContextFk", "ResourceContext", true)
				.Add("TypeFk", "Type")
				.Add("KindFk", "Kind")
				.Add("GroupFk", "Group")
				.Add("CompanyFk", "Company", true)
				.Add("CalendarFk", "Calendar")
				.Add("UomBasisFk", "BaseUoM")
				.Add("UomTimeFk", "TimeUoM")
				.Add("Capacity")
				.Add("Validfrom", "ValidFrom")
				.Add("Validto", "ValidTo")
				.Add("Userdefined1", "UserDefined01")
				.Add("Userdefined2", "UserDefined02")
				.Add("Userdefined3", "UserDefined03")
				.Add("Userdefined4", "UserDefined04")
				.Add("Userdefined5", "UserDefined05")
				.Add("IsLive", true);

		#endregion

		#region IResourceMasterLogic implementation

		/// <summary>
		/// Get resources by code
		/// </summary>
		/// <param name="code">resource code</param>
		/// <returns></returns>
		/// \since 2018-02-23 (16:35), by zov
		IEnumerable<IResourceEntity> IResourceMasterLogic.GetByCode(string code)
		{
			return this.GetListByFilter(e => e.Code == code).ToList();
		}

		/// <summary>
		/// Get resource by ids
		/// </summary>
		/// <param name="ids">resource ids</param>
		/// <returns></returns>
		/// \since 2018-02-23 (16:35), by zov
		public IEnumerable<IResourceEntity> GetByIds(IEnumerable<int> ids)
		{
			if (ids.IsNullOrEmpty())
			{
				return Array.Empty<IResourceEntity>();
			}
			return this.GetByFilter(r => ids.Contains(r.Id)).Select(x => (IResourceEntity)x).ToList();
		}

		/// <summary>
		/// Get the resource identified by the given identifier
		/// </summary>
		/// <param name="ident">Identifier for the requested plant, the Id property must be set</param>
		/// <returns></returns>
		IResourceEntity IResourceMasterLogic.GetResourceById(IdentificationData ident)
		{
			return GetById(ident);
		}

		/// <summary>
		/// Get resourceIds by PpsEvent->Requisition->Reservation->ResourceFk
		/// </summary>
		/// <param name="ppsEventId"></param>
		/// <returns></returns>
		IEnumerable<IResourceEntity> IResourceMasterLogic.GetResourcesByPpsEvent(int ppsEventId)
		{
			// Get ResourceRequisitionId
			var reqLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IResRequisitionLogic>();
			var requisitions = reqLogic.GetRequisitionsByPpsEvent(ppsEventId);

			//Get Reservations
			var reservationLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IResReservationLogic>();
			var reservations = reservationLogic.GetReservationsByRequisitions(requisitions);
			var resourceIds = reservations.Select(r => r.ResourceFk).Distinct().ToArray();

			return this.GetListByFilter(e => resourceIds.Contains(e.Id));
		}

		/// <summary>
		///  Id id of the plant
		///  PKey1: bas_etm_Context or bas_timesheet_context (depends on resourceparttype)
		/// </summary>
		/// <param name="ident"></param>
		/// <returns></returns>
		IEnumerable<IResourceEntity> IResourceMasterLogic.GetResourcesByPlantId(IdentificationData ident)
		{
			IEnumerable<IResourceEntity> result = null;
			var resPartLogic = new ResourcePartLogic();
			var identData = new IdentificationData()
			{
				Id = 0,
				PKey1 = ResourcePartType.Plant,
				PKey2 = ident.PKey1,
				PKey3 = ident.Id
			};
			var parts = resPartLogic.GetResourceParts(identData);
			if (parts != null)
			{
				var ids = parts.Select(e => e.ResourceFk).ToArray();
				result = GetByIds(ids).ToList();
			}

			return result;
		}

		IResourceEntity IResourceMasterLogic.GetResourceByUser(int userId)
		{
			return DoGetResourceViaEmployee((e, i) => e.GetEmployeeByUser(i), userId);
		}

		IResourceEntity IResourceMasterLogic.GetResourceByClerk(int clerkId)
		{
			return DoGetResourceViaEmployee((e, i) => e.GetEmployeeByClerk(i), clerkId);
		}

		IEnumerable<IResourceEntity> IResourceMasterLogic.GetResourcesByEmployees(IEnumerable<int> employeeIds)
		{
			if (employeeIds != null && employeeIds.Any())
			{
				var emplIds = employeeIds.Distinct().ToArray();
				var resParts = new ResourcePartLogic().GetByFilter(rp => rp.EmployeeFk.HasValue && emplIds.Contains(rp.EmployeeFk.Value)).ToArray();
				var resIds = resParts.CollectIds(rp => rp.ResourceFk).ToArray();

				if (resIds != null && resIds.Any())
				{
					return this.GetByFilter(r => r.IsLive && resIds.Contains(r.Id)).ToArray();
				}
			}

			return new List<IResourceEntity>();
		}

		IEnumerable<IResourceEntity> IResourceMasterLogic.CreateResources(Int32 numberOfInstances)
		{
			var prototype = Create(new IdentificationData());

			var result = new List<ResourceEntity>();
			result.Add(prototype);

			if (numberOfInstances > 1)
			{
				var remaining = numberOfInstances - 1;
				var ids = SequenceManager.GetNextList(GetEntityTableName(), remaining).ToArray();

				for (int n = 0; n < remaining; ++n)
				{
					var copy = prototype.Clone() as ResourceEntity;
					copy.Id = ids[n];
					result.Add(copy);
				}
			}

			return result;
		}
		IEnumerable<IResourceEntity> IResourceMasterLogic.SaveResources(IEnumerable<IResourceEntity> toSave)
		{
			var resToSave = toSave.OfType<ResourceEntity>().ToArray();

			if (resToSave != null && resToSave.Any())
			{
				return Save(resToSave);
			}

			return resToSave;
		}

		IEnumerable<IResourceEntity> IResourceMasterLogic.GetResourcesByProcurementItems(IEnumerable<long> procurementItemIds)
		{
			if (procurementItemIds != null && procurementItemIds.Any())
			{
				var idArray = procurementItemIds.ToArray();
				return this.GetByFilter(r => r.ItemFk.HasValue && idArray.Contains(r.ItemFk.Value)).ToArray();
			}

			return Enumerable.Empty<IResourceEntity>();
		}

		private IResourceEntity DoGetResourceViaEmployee(Func<IEmployeeLogic, int, IEmployee> getEmployeeFn, int key)
		{
			var employeeLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IEmployeeLogic>();
			var employee = getEmployeeFn(employeeLogic, key);

			if (employee != null)
			{
				var resPart = new ResourcePartLogic().GetByFilter(rp => rp.EmployeeFk == employee.Id).FirstOrDefault();

				if (resPart != null)
				{
					return this.GetByFilter(r => r.IsLive && r.Id == resPart.ResourceFk).FirstOrDefault();
				}
			}

			return null;
		}

		#endregion

		private List<int> GetSiteChildrenIds(IEnumerable<SiteEntity> siteChildren)
		{
			List<int> ids = new List<int>();
			foreach (var child in siteChildren)
			{
				ids.Add(child.Id);
				if (child.SiteChildren.Count > 0)
				{
					ids.AddRange(GetSiteChildrenIds(child.SiteChildren.Where(e => e.IsLive).ToArray()));
				}
			}
			return ids;
		}

		private List<int> GetTypeChildrenIds(IEnumerable<ResourceTypeEntity> typeChildren, ResourceTypeMainLogic typeLogic)
		{
			List<int> ids = new List<int>();
			foreach (var child in typeChildren)
			{
				ids.Add(child.Id);
				var children = typeLogic.GetListByFilter(e => e.ResourceTypeFk == child.Id).ToArray();
				if (children.Length > 0)
				{
					ids.AddRange(GetTypeChildrenIds(children, typeLogic));
				}
			}
			return ids;
		}


		#region Aggregator
		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			var updateEntity = to as ResourceComplete;
			if (updateEntity != null && toBeAggregated.Any())
			{
				DoAggregate(updateEntity, relInfo, toBeAggregated);
			}
		}

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IIdentifyable toBeAggregated)
		{
			var updateEntity = to as ResourceComplete;
			if (updateEntity != null && toBeAggregated != null)
			{
				DoAggregate(updateEntity, relInfo, new List<IIdentifyable>() { toBeAggregated });
			}
		}

		IIdentifyable IEntityAggregator.ProvideCompleteEntity()
		{
			return new ResourceComplete();
		}

		/// <summary>
		/// Gives access to the instance being responsible for creating complete entities. base implmentation returns null, which is appropriate return for all leave entities
		/// </summary>
		/// <returns>
		/// null
		/// </returns>
		protected override IEntityAggregator ProvideAggregator()
		{
			return this;
		}

		/// <summary>
		/// Takes over a list of entities and appends it to the passed update entity
		/// </summary>
		/// <param name="updateEntity">Complete entity to build up</param>
		/// <param name="relInfo">Information about content of toBeAggregated</param>
		/// <param name="toBeAggregated">Entities which need to be added to updateEntity</param>
		/// protected virtual IEntityAggregator ProvideAggregator(
		protected void DoAggregate(ResourceComplete updateEntity, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			switch (relInfo.GetIdentifier())
			{
				case "resource.master.resource":
					updateEntity.Resources = toBeAggregated.OfType<ResourceEntity>();
					updateEntity.Id = updateEntity.Resources.FirstOrDefault().Id;
					break;
				case "required.resource.skill":
					updateEntity.RequiredSkillsToSave = toBeAggregated.OfType<RequiredResourceSkillEntity>();
					break;
				case "provided.resource.skill":
					updateEntity.ProvidedSkillsToSave = toBeAggregated.OfType<ProvidedResourceSkillComplete>();
					break;
				case "resource.master.pool":
					updateEntity.PoolToSave = toBeAggregated.OfType<PoolEntity>();
					break;
				case "resource.resource.part":
					updateEntity.PartsToSave = toBeAggregated.OfType<ResourcePartEntity>();
					break;
			}
		}


		#endregion
		/// <summary>
		/// SavePreProcessing
		/// </summary>
		/// <param name="entity"></param>
		protected override void SavePreProcessing(ResourceEntity entity)
		{
			var partLogic = new ResourcePartLogic();

			if (String.IsNullOrEmpty(entity.SortCode) && entity.Version == 0)
			{
				entity.SortCode = entity.Code;
			}

			// Check if resource type is valid
			var partEntities = partLogic.GetByFilter(p => p.ResourceFk == entity.Id);
			CheckResTypeIsValid(entity, partEntities);
		}
		/// <summary>
		/// SavePreProcessing
		/// </summary>
		/// <param name="entities"></param>
		protected override void SavePreProcessing(IEnumerable<ResourceEntity> entities)
		{
			PreCheckCodeIsUnique(entities);

			foreach (var entity in entities)
			{
				SavePreProcessing(entity);
			}
		}

		private void PreCheckCodeIsUnique(IEnumerable<ResourceEntity> entities)
		{
			var codeList = entities.Select(e => e.Code).ToList();
			var resWithSameCodes = GetListByFilter(e => codeList.Contains(e.Code)).ToList();
			var realProblems = new List<string>();

			if (resWithSameCodes != null && resWithSameCodes.Any())
			{
				foreach (var resource in entities)
				{
					var resWithDifferentIdAndSameCode = resWithSameCodes.FirstOrDefault(r => r.Id != resource.Id && r.Code == resource.Code);
					if (resWithDifferentIdAndSameCode != null)
					{
						realProblems.Add(resWithDifferentIdAndSameCode.Code);
					}
				}
			}

			if (realProblems.Any())
			{
				StringBuilder sb = new StringBuilder();
				sb.Append(NLS.ERR_DublicateCode);

				for (int i = 0; i < realProblems.Count; i++)
				{
					if (i > 0) { sb.Append(", "); }

					sb.AppendFormat(realProblems[i]);
				}

				throw new BusinessLayerException
				{
					ErrorMessage = sb.ToString()
				};
			}
		}

		/// <summary>
		/// EvaluateGroupingFilter for Structure Container 
		/// </summary>
		/// <param name="filterRequest"></param>
		/// <param name="dbContext"></param>
		/// <param name="query"></param>
		/// <returns></returns>
		protected override IQueryable<ResourceEntity> EvaluateGroupingFilter(FilterRequest filterRequest, DbContext dbContext, IQueryable<ResourceEntity> query)
		{
			if (filterRequest.GroupingFilter != null)
			{
				var groupingLogic = new GroupingHierarchyLogic();
				var filterOut = new FilterResponse<int>();
				FilterRequest<int> filterIn = new FilterRequest<int>()
				{
					EnhancedFilterDef = filterRequest.EnhancedFilterDef,
					ExecutionHints = filterRequest.ExecutionHints,
					GroupingFilter = filterRequest.GroupingFilter,
					IncludeNonActiveItems = filterRequest.IncludeNonActiveItems,
					IncludeResultIds = filterRequest.IncludeResultIds,
					IsEnhancedFilter = filterRequest.IsEnhancedFilter,
					PageNumber = filterRequest.PageNumber,
					PageSize = filterRequest.PageSize,
					Pattern = filterRequest.Pattern,
					ProjectContextId = filterRequest.ProjectContextId,
					UseCurrentClient = filterRequest.UseCurrentClient
				};
				var execInfo = new FilterExecutionInfo<int>(filterIn, filterOut);
				string requestId = null;
				groupingLogic.GetGroupSelection(execInfo, filterIn, ref requestId, new CCB.FurtherKeysProvider());
				query = query.Where(e => e.DdTempIdsEntities.Any(d => d.RequestId == requestId));
			}
			return query;
		}

		/// <summary>
		/// EvaluatePatternSearch
		/// </summary>
		protected override IQueryable<ResourceEntity> EvaluatePatternSearch(SearchSpecification<ResourceEntity, IdentificationData> searchContext, DbContext dbContext, IQueryable<ResourceEntity> query)
		{
			if (!string.IsNullOrWhiteSpace(searchContext.FilterIn.Pattern))
			{
				var option = new QueryFilterOptions<ResourceEntity>();
				option.Add(new List<string>() { "DescriptionInfo", "SearchPattern" });
				option.IgnoreSearchPattern = true;
				option.SearchText = searchContext.FilterIn.Pattern;
				query = query.JoinTrAndFilter<ResourceEntity, CCB.TranslationEntity>(dbContext.Set<CCB.TranslationEntity>(), option);
			}
			return query;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="copies"></param>
		/// <param name="responsible"></param>
		protected override void DoValidateCopies(IEnumerable<ResourceEntity> copies, IEntityRelationInfo responsible)
		{
			foreach (var copy in copies)
			{
				var origCode = copy.Code;
				copy.Code = GenerateCode(RubricConstant.ResourceMaster);

				if (string.IsNullOrEmpty(copy.Code))
				{
					var guid = Guid.NewGuid().ToString();
					guid = guid.Replace("-", "");

					copy.Code = guid.Substring(0, 16);
				}
			}
		}
	}
}
