import { sidebar, commonLocators, app, cnt, btn, tile, apiParameters } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _businessPartnerPage, _common, _commonAPI, _estimatePage, _procurementPage, _projectPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const REMINDER_DESC = _common.generateRandomString(5)
const REMINDER_DESC_1 = _common.generateRandomString(5)

let MODAL_PROJECTS;
let CONTAINERS_BUSINESS_PARTNER, CONTAINERS_REMINDERS;
let CONTAINER_COLUMNS_BUSINESS_PARTNER, CONTAINER_COLUMNS_REMINDERS;
let BUSINESS_PARTNER_PARAMETER,BUSINESS_CERTIFICATE_PARAMETER: DataCells;


describe("PCM- 4.254 | BPD_Certificate_Reminders container_Check column filter in Reminders container", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture('pcm/pcm-4.254-bpd-certificate-reminders-container-check-column-filter-in-reminders-container.json')
            .then((data) => {
                this.data = data;
                MODAL_PROJECTS = this.data.MODAL.PROJECTS
                CONTAINERS_BUSINESS_PARTNER = this.data.CONTAINERS.BUSINESS_PARTNER
                CONTAINER_COLUMNS_BUSINESS_PARTNER = this.data.CONTAINER_COLUMNS.BUSINESS_PARTNER
                CONTAINERS_REMINDERS = this.data.CONTAINERS.REMINDERS
                CONTAINER_COLUMNS_REMINDERS = this.data.CONTAINER_COLUMNS.REMINDERS
            }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear();
                _common.openTab(app.TabBar.PROJECT).then(() => {
                    _common.setDefaultView(app.TabBar.PROJECT);
                    _common.waitForLoaderToDisappear();
                    _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                });
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            });
    });
    after(() => {
        cy.LOGOUT();
    });

     it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

      it("TC - API: Create a business partner in business partner module", function () {

        BUSINESS_PARTNER_PARAMETER = {
            [commonLocators.CommonLabels.STREET]: CONTAINERS_BUSINESS_PARTNER.STREET_NAME,
            [commonLocators.CommonLabels.CITY]: CONTAINERS_BUSINESS_PARTNER.CITY_NAME,
            [commonLocators.CommonLabels.ZIP_CODE]: CONTAINERS_BUSINESS_PARTNER.ZIP_CODE,
            [commonLocators.CommonLabels.COUNTY]: CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME,
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.BUSINESS_PARTNER);
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.setDefaultView(app.TabBar.BUSINESS_PARTNERS)
            _common.select_tabFromFooter(cnt.uuid.BUSINESS_PARTNERS, app.FooterTab.BUSINESS_PARTNER, 0);
            _common.setup_gridLayout(cnt.uuid.BUSINESS_PARTNERS, CONTAINER_COLUMNS_BUSINESS_PARTNER)
        });
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _commonAPI.createBusinessPartner(BUSINESS_PARTNER_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create a record in certificates container", function () {
         BUSINESS_CERTIFICATE_PARAMETER = {
            [apiParameters.Keys.BUSINESS_PARTNER_ID]: Cypress.env("API_BP_ID_1")
        }

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CERTIFICATE);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _commonAPI.createCertificates(BUSINESS_CERTIFICATE_PARAMETER)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("API_BP_NAME_1"))
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env("API_BP_NAME_1"))
        _common.select_rowHasValue(cnt.uuid.CERTIFICATE, Cypress.env("API_BP_NAME_1"))
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_BP_NAME_1'))
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        _common.select_rowHasValue(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create a record in reminders container", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.REMINDERS_DETAILS, app.FooterTab.REMINDER_DETAIL, 3)
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.REMINDERS, app.FooterTab.REMINDERS, 1)
        });
        //1
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)
        _common.clickOn_toolbarButton(cnt.uuid.REMINDERS, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.COMMENT[0])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.EMAIL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.dateConversion_toNumberFormat(_common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL), "Batch_Field")
        _common.assert_forNumericValues(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.waitForLoaderToDisappear()

        //2
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)
        _common.clickOn_toolbarButton(cnt.uuid.REMINDERS, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.COMMENT[0])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.EMAIL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.dateConversion_toNumberFormat(_common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL), "Batch_Field")
        _common.assert_forNumericValues(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.waitForLoaderToDisappear()

        //3
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)
        _common.clickOn_toolbarButton(cnt.uuid.REMINDERS, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.COMMENT[0])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.EMAIL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.dateConversion_toNumberFormat(_common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL), "Batch_Field")
        _common.assert_forNumericValues(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.waitForLoaderToDisappear()

        //4
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)
        _common.clickOn_toolbarButton(cnt.uuid.REMINDERS, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, REMINDER_DESC)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_dropdownCellWithCaret(cnt.uuid.REMINDERS, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.LIST, CommonLocators.CommonKeys.DATE_RECEIVED)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.BATCH)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.COMMENT[1])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.TELEFAX[1])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.EMAIL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REMINDERS.E_MAIL[1])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED_SMALL, 1))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        //5
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)
        _common.clickOn_toolbarButton(cnt.uuid.REMINDERS, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, REMINDER_DESC_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.REMINDERS)
        _common.edit_dropdownCellWithCaret(cnt.uuid.REMINDERS, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.LIST, CommonLocators.CommonKeys.DATE_RECEIVED)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.BATCH)
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.COMMENT[2])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_REMINDERS.TELEFAX[2])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.EMAIL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REMINDERS.E_MAIL[2])
        _common.edit_containerCell(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED_SMALL, 1))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Check column filter in reminders container", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0)
        });
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        _common.select_rowHasValue(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.REMINDERS_DETAILS, app.FooterTab.REMINDER_DETAIL, 3)
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.REMINDERS, app.FooterTab.REMINDERS, 1)
        });
        //Batch_ID
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, Cypress.env("Batch_Field"), 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, Cypress.env("Batch_Field"), 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, Cypress.env("Batch_Field"), 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BATCH_ID, Cypress.env("Batch_Field"))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)

        //Description
        _common.waitForLoaderToDisappear()
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.DESC_1, 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.DESC_1, 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.DESC_1, 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.DESCRIPTION, CONTAINERS_REMINDERS.DESC_1)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)

        //Status
        _common.waitForLoaderToDisappear()
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.CERTIFICATE_STATUS_FK, CommonLocators.CommonKeys.REQUESTED)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CommonLocators.CommonKeys.REQUESTED, 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.CERTIFICATE_STATUS_FK, CommonLocators.CommonKeys.REQUESTED)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CommonLocators.CommonKeys.REQUESTED, 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.CERTIFICATE_STATUS_FK, CommonLocators.CommonKeys.REQUESTED)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CommonLocators.CommonKeys.REQUESTED, 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.CERTIFICATE_STATUS_FK, CommonLocators.CommonKeys.REQUESTED)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)

        //Comment
        _common.waitForLoaderToDisappear()
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, CONTAINERS_REMINDERS.COMMENT[0])
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.COMMENT[0], 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, CONTAINERS_REMINDERS.COMMENT[0])
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.COMMENT[0], 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, CONTAINERS_REMINDERS.COMMENT[0])
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.COMMENT[0], 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.COMMENT_TEXT, CONTAINERS_REMINDERS.COMMENT[0])
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)

        //Telefax
        _common.waitForLoaderToDisappear()
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.TELEFAX[0], 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.TELEFAX[0], 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.TELEFAX[0], 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.TELEFAX, CONTAINERS_REMINDERS.TELEFAX[0])
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)

        //Email
        _common.waitForLoaderToDisappear()
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.EMAIL, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.E_MAIL[0], 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.EMAIL, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.E_MAIL[0], 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.EMAIL, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, CONTAINERS_REMINDERS.E_MAIL[0], 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.EMAIL, CONTAINERS_REMINDERS.E_MAIL[0])
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REMINDERS)

        //Batch Date
        _common.waitForLoaderToDisappear()
        _common.columnFilter_inSubContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_allContainerData(cnt.uuid.REMINDERS)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL), 0)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL), 1)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.select_rowHasValue_onIndexBased(cnt.uuid.REMINDERS, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL), 2)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.REMINDERS, app.GridCells.BARCH_DATE, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
    })
})