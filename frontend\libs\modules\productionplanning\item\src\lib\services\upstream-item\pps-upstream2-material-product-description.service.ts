import { Injectable } from '@angular/core';
import { DataServiceFlatNode, IDataServiceEndPointOptions, IDataServiceOptions, IDataServiceRoleOptions, ServiceRole } from '@libs/platform/data-access';
import {
	IPpsItem2MdcMaterialEntity,
	PpsItem2MdcMaterialComplete,
	IPpsItem2MdcMaterialProductDescriptionEntity,
	PpsItem2MdcMaterialProductDescriptionComplete,
} from '../../model/models';
import { PpsUpstream2MaterialService } from './pps-upstream-2-material.service';

@Injectable({
	providedIn: 'root'
})
export class PpsUpstream2MaterialProductDescriptionService extends DataServiceFlatNode<IPpsItem2MdcMaterialProductDescriptionEntity, PpsItem2MdcMaterialProductDescriptionComplete,
	IPpsItem2MdcMaterialEntity, PpsItem2MdcMaterialComplete> {

	public constructor(private parentDataService: PpsUpstream2MaterialService) {
		const options: IDataServiceOptions<IPpsItem2MdcMaterialProductDescriptionEntity> = {
			apiUrl: 'productionplanning/ppsmaterial/mdcproductdescription',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
				prepareParam: () => {
					const parentSelection = this.getSelectedParent();
					return {mainItemId: parentSelection?.MdcMaterialFk};
				}
			},
			createInfo: {
				endPoint: 'create',
				prepareParam: () => {
					const parentSelection = this.getSelectedParent();
					return {
						Id: parentSelection?.MdcMaterialFk ?? -1
					};
				}
			},
			roleInfo: <IDataServiceRoleOptions<IPpsItem2MdcMaterialProductDescriptionEntity>>{
				role: ServiceRole.Node,
				itemName: 'MdcProductDescription',
				parent: parentDataService,
			},
		};
		super(options);
		this.init();
	}

	private init() {
		this.listChanged$.subscribe(() => {
			this.listChanged();
		});
	}

	private listChanged() {
		const list = this.getList();
		if (list) {
			setTimeout(() => {
				this.selectFirst();
			}, 1000);
		}
	}

	public override canCreate(): boolean {
		//const selectedParentItem = this.getSelectedParent();
		return true;
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override isParentFn(parentKey: IPpsItem2MdcMaterialEntity, entity: IPpsItem2MdcMaterialProductDescriptionEntity): boolean {
		return parentKey.MdcMaterialFk === entity.MaterialFk && parentKey.Id === this.getSelectedParent()!.Id;
	}

	public override registerNodeModificationsToParentUpdate(complete: PpsItem2MdcMaterialComplete, modified: PpsItem2MdcMaterialProductDescriptionComplete[], deleted: IPpsItem2MdcMaterialProductDescriptionEntity[]) {
		if (modified && modified.length > 0) {
			complete.MdcProductDescriptionToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			complete.MdcProductDescriptionToDelete = deleted;
		}
	}

	public override createUpdateEntity(modified: IPpsItem2MdcMaterialProductDescriptionEntity | null): PpsItem2MdcMaterialProductDescriptionComplete {
		const complete = new PpsItem2MdcMaterialProductDescriptionComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.MdcProductDescription = modified;
		}

		return complete;
	}

	public override getSavedEntitiesFromUpdate(complete: PpsItem2MdcMaterialComplete): IPpsItem2MdcMaterialProductDescriptionEntity[] {
		return (complete && complete.MdcProductDescriptionToSave)
			? complete.MdcProductDescriptionToSave.map(e => e.MdcProductDescription)
			: [];
	}

	public override getModificationsFromUpdate(complete: PpsItem2MdcMaterialProductDescriptionComplete): IPpsItem2MdcMaterialProductDescriptionEntity[] {
		if (complete.MdcProductDescription === null) {
			return [];
		}
		return [complete.MdcProductDescription];
	}

}