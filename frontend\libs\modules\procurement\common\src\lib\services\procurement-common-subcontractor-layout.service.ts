/*
 * Copyright(c) RIB Software GmbH
 */
import { FieldType, ILayoutConfiguration } from '@libs/ui/common';
import { inject, Injectable, ProviderToken } from '@angular/core';
import { CompleteIdentification, IEntityIdentification, PlatformLazyInjectorService, prefixAllTranslationKeys } from '@libs/platform/common';
import { IPrcSubreferenceEntity } from '../model/entities';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { BUSINESSPARTNER_RELATED_LOOKUP_PROVIDER_TOKEN } from '@libs/businesspartner/interfaces';
import { BusinessPartnerSharedLookupLayoutProvider } from '@libs/businesspartner/shared';
import { ProcurementCommonSubcontractorDataService } from '../services/procurement-common-subcontractor-data.service';

/**
 * Common procurement subcontractor layout service
 */
@Injectable({
	providedIn: 'root',
})
export class ProcurementCommonSubcontractorLayoutService {
	private readonly lazyInjector = inject(PlatformLazyInjectorService);
	private readonly bpSharedLookupLayoutProvider = inject(BusinessPartnerSharedLookupLayoutProvider);

	public async generateLayout<T extends IPrcSubreferenceEntity, PT extends IEntityIdentification, PU extends CompleteIdentification<PT>>(config: {
		dataService: ProviderToken<ProcurementCommonSubcontractorDataService<T, PT, PU>>;
	}): Promise<ILayoutConfiguration<T>> {
		const bpRelatedLookupProvider = await this.lazyInjector.inject(BUSINESSPARTNER_RELATED_LOOKUP_PROVIDER_TOKEN);
		const layout = <ILayoutConfiguration<T>>{
			groups: [
				{
					gid: 'baseGroup',
					attributes: ['PrcStructureFk', 'BpdBusinesspartnerFk', 'BpdSubsidiaryFk', 'BpdSupplierFk', 'BpdContactFk', 'Description', 'UserDefinedDate1', 'UserDefinedDate2', 'UserDefinedDate3', 'UserDefinedDate4', 'UserDefinedDate5'],
				},
			],
			labels: {
				...prefixAllTranslationKeys('cloud.common.', {
					PrcStructureFk: { text: 'Structure', key: 'entityStructureCode' },
					BpdBusinesspartnerFk: { text: 'Business Partner', key: 'entityBusinessPartner' },
					BpdSubsidiaryFk: { text: 'Subsidiary', key: 'entitySubsidiary' },
					BpdSupplierFk: { text: 'Supplier', key: 'entitySupplier' },
					Description: { text: 'Comment', key: 'entityDesc' },
					UserDefinedDate1: { text: 'User Defined Date 1', key: 'entityUserDefinedDate', params: { p_0: '1' } },
					UserDefinedDate2: { text: 'User Defined Date 2', key: 'entityUserDefinedDate', params: { p_0: '2' } },
					UserDefinedDate3: { text: 'User Defined Date 3', key: 'entityUserDefinedDate', params: { p_0: '3' } },
					UserDefinedDate4: { text: 'User Defined Date 4', key: 'entityUserDefinedDate', params: { p_0: '4' } },
					UserDefinedDate5: { text: 'User Defined Date 5', key: 'entityUserDefinedDate', params: { p_0: '5' } },
				}),
				...prefixAllTranslationKeys('procurement.common.', {
					BpdContactFk: { text: 'Contact', key: 'ConHeaderContact' },
				}),
			},
			overloads: {
				PrcStructureFk: BasicsSharedLookupOverloadProvider.providerBasicsProcurementStructureLookupOverload(true),
				BpdBusinesspartnerFk: bpRelatedLookupProvider.getBusinessPartnerLookupOverload({
					showClearButton: true,
					filterIsLive: true,
				}),
				BpdSubsidiaryFk: bpRelatedLookupProvider.getSubsidiaryLookupOverload({
					showClearButton: true,
					serverFilterKey: 'businesspartner-main-subsidiary-common-filter',
					restrictToBusinessPartners: (entity) => entity.BpdBusinesspartnerFk,
				}),
				BpdSupplierFk: bpRelatedLookupProvider.getSupplierLookupOverload({
					showClearButton: true,
					serverFilterKey: 'businesspartner-main-supplier-common-filter',
					restrictToBusinessPartners: (entity) => entity.BpdBusinesspartnerFk,
					restrictToSubsidiaries: (entity) => entity.BpdSubsidiaryFk,
				}),
				BpdContactFk: bpRelatedLookupProvider.getContactLookupOverload({
					showClearButton: true,
					serverFilterKey: 'prc-subcontactor-bpdcontact-filter',
					restrictToBusinessPartners: (entity) => entity.BpdBusinesspartnerFk,
					restrictToSubsidiaries: (entity) => entity.BpdSubsidiaryFk,
				}),
				UserDefinedDate1: {
					type: FieldType.DateUtc,
				},
				UserDefinedDate2: {
					type: FieldType.DateUtc,
				},
				UserDefinedDate3: {
					type: FieldType.DateUtc,
				},
				UserDefinedDate4: {
					type: FieldType.DateUtc,
				},
				UserDefinedDate5: {
					type: FieldType.DateUtc,
				},
			},
		};

		this.bpSharedLookupLayoutProvider.provideContactLookupFields<T>(layout, {
			gid: 'baseGroup',
			lookupKeyGetter: (e) => e.BpdContactFk,
			dataService: config.dataService,
		});

		return layout;
	}
}
