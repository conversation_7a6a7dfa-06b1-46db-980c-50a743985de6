/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { QtoShareDetailValidationService } from '@libs/qto/shared';
import { ISalesBillingQtoDetailEntity } from '../../../model/entities/sales-billing-qto-detail-entity.interface';
import { SalesBillingQtoDetailComplete } from '../../../model/complete-class/sales-billing-qto-detail-complete.class';
import { ISalesBillingQtoHeaderEntity } from '../../../model/entities/sales-billing-qto-header-entity.interface';
import { SalesBillingQtoHeaderComplete } from '../../../model/complete-class/sales-billing-qto-header-complete.class';
import { SalesBillingQtoDetailDataService } from '../sales-billing-qto-detail-data.service';

/**
 * Sales Billing QTO Detail Validation Service
 * Handles validation logic for billing QTO detail entities
 */
@Injectable({
	providedIn: 'root',
})
export class SalesBillingQtoDetailValidationService extends QtoShareDetailValidationService<
	ISalesBillingQtoDetailEntity,
	SalesBillingQtoDetailComplete,
	ISalesBillingQtoHeaderEntity,
	SalesBillingQtoHeaderComplete
> {
	public constructor(dataService: SalesBillingQtoDetailDataService) {
		super(dataService);
	}

	// Add any billing-specific validation logic here if needed
}
