namespace CommonLocators {
  export enum CommonContainerElements {
    MAXIMIZE = "button[title='Maximize']",
    MINIMIZE = "[class*='ico-minimized2']",
    BOQ_FOLDER_ICON = "[class*='ico-folder-doc']",
    LOOKUP_GRID_CONTAINER = "[class*='lookup-grid-container']",
    PROFILE ="button[class*='profile']"
  }

  export enum CommonModalElements {
    MODAL_DIALOG_CLASS = "[class*='modal-dialog']",
    MODAL_CONTENT_CLASS = "[class*='modal-content']",
    MODEL_ITEM_FIELD = `[class*="item-field_MdcMaterialFk"]`,
    MODAL_RESIZABLE = `[class*="modal-content ui-resizable"]`,
    CHANGE_ORDERS = `[class*="form-control ng-pristine ng-valid"]`,
    MODAL_FLEX = "[class='category-tree ng-scope']",
    MODAL_CATEGORY = "[class*='ticket-categories']",
    FOOTER = "[class*='ms-sv-cart-item-count']",
    SUB_CONTAINER_IN_MODEL = "[ng-controller='boqMainStructureDetailsController'] [class*='subview-container']",
    MODAL_TITLE = "[class*='modal-title']",
    MODAL_FOOTER = "[class*='modal-footer']",
    MODAL_ROW_CLASS = "spaceToUp",
    MODAL_BODY = "[class*='modal-body']",
    MULTIPLE_SELECTIONS_0_ITEMS_SELECTED = "Multiple Selection - 0 Items Selected",
    MODAL_PRISTINE = "[class*='pristine']",
    ALERT_WARNING = "[class*='alert-warning']",
    MODAL_DIALOG="modal-dialog",
    DELETE="[class*='delete']",
    YES="[class*='yes']",
    YES_DATA_NG="[data-ng-click='modalOptions.yes()']",
    FILE_BUTTON_NAME="fileInput",
    FILE_INPUT="input[type='file']"

  }

  export enum ModalInput {
    LOOKUP_CELL = `[class*="input-group-content"]`,
    TEXT_CELL = 'input[data-ng-model="entity.',
    MODAL_LABEL = `.modal-dialog .platform-form-row label`,
    TARGET_5 = "Target5",
    SOURCE_7 = "Source7",
    SOURCE_5 = "Source5",
    TARGET_7 = "Target7"
  }

  export enum CommonLabels {
    WorkOperationType = "Work Operation Type",
    JOB = "Job",
    CONFIGURATION = "Configuration",
    STREET = "Street",
    ZIP_CODE = "Zip Code",
    COUNTRY = "Country",
    SELECT = "Select",
    BUSINESS_PARTNER = "Business Partner",
    OUTLINE_SPECIFICATION = "Outline Specification",
    UOM = "UoM",
    UNIT_RATE = "Unit Rate",
    LANGUAGE = "Language",
    UI_LANGUAGE = "UI Language",
    USER_DATA_LANGUAGE = "User Data Language",
    BILL_TYPE = "Type",
    CONTRACT = "Contract",
    DESCRIPTION = "Description",
    CLERK = "Clerk",
    CONTRACT_TYPE = "Contract Type",
    COPY_FROM = "Copy From",
    WIC_GROUP = "WIC Group",
    BOQ_SELECTION = "BoQ Selection",
    BOQ_STRUCTURE_OUTLINE_SPEC = "BoQ Structure Outline Specification",
    SOURCE_BOQ_OUTLINE_SPEC = "Source Outline Specification",
    PROJECT = "Project",
    SOURCE_BOQ = "Source BoQ",
    TARGET_WIC_GROUP = "Target WIC Group",
    REFERENCE_NO = "Reference No.",
    SELECT_ESTIMATE_SCOPE = "Select Estimate Scope",
    SELECT_PACKAGES = "Select Packages",
    SELECT_RESOURCES = "Select Resources",
    CREATE_ACCESS_RIGHT = "Create Access Right",
    PROJECT_CHANGE = "Project Change",
    RUBRIC_CATEGORY = "Rubric Category",
    CHANGE_TYPE = "Change Type",
    CHANGE_REASON = "Change Reason",
    CHANGE_ORDER_CONTRACT_DESC = "Change Order Contract Desc",
    CONTRACT_STATUS = "Contract Status",
    PES = "PES",
    QUOTATION = "Quotation",
    REQUISITION = "Requisition",
    MATERIAL_SEARCH = "Material Search",
    STRUCTURE_TYPE = "Structure Type",
    HEADER_TEXT = "Header Text",
    EDIT_ESTIMATE_TYPE = "Edit Estimate Type",
    IS_COLUMN_CONFIGURE_ACTIVATED = "Is Column Configure Activated",
    EDIT_TYPE = "Edit Type",
    ESTIMATE_STRUCTURE_CONFIG_DETAILS = "Estimate Structure Config Details",
    VOUCHER_NO = "Voucher No",
    PROCUREMENT_TYPE = "Procurement Type",
    REQUISITION_OWNER = "Requisition Owner",
    RESPONSIBLE = "Responsible",
    CONTROLLING_UNIT = "Controlling Unit",
    DESCRIPTION_PREFIX = "Description Prefix",
    START_DATE = "Start Date",
    END_DATE = "End Date",
    REPEAT = "Repeat",
    TIME_REQUIRED = "Time Required",
    ROUND_UP_QUANTITY = "Round up the Quantity",
    DATE_DELIVERED = "Date Delivered",
    CITY = "City",
    ADDRESS = "Address",
    VENDOR = "Vendor:",
    MATERIAL_TYPE = "Material Type",
    PRICE_LIST = "Price List",
    TYPE = "type",
    CATALOG = "Catalog",
    ATTRIBUTE = "Attributes",
    BRANCH_DESCRIPTION = "Branch Description",
    CHECKBOX_LIST_GROUP_TYPE = "Checkbox List Group Type",
    PLANT = "Plant",
    PLANT_CODE = "Plant Code",
    PLANT_GROUP = "Plant Group",
    PLANT_KIND = "Plant Kind",
    PLANT_TYPE = "Plant Type",
    PLANT_DESCRIPTION = "Plant Description",
    SET_THE_REPORTING_DATE_TO = "Set the reporting date to",
    WHERE_CONTRACT_STATUS_IS = "Where the contract status is",
    DISTANCE = "Distance",
    REGION = "Region",
    REQUIRED_DATE = "Required Date:",
    SCOPE = "Scope",
    REPLACE_FROM = "Replace from",
    FACTOR = "Factor",
    IS_BASE = "Is Base",
    RFQ_HEADER = "RfQ Header",
    LENGTH_DIMENSION = "Length Dimension",
    BOQ_SOURCE = "BoQ Source",
    BOQS = "BoQs",
    TOTAL_OC_NET = "Total OC (Net)",
    TOTAL_CONFIGURE_DETAILS = "Totals Configure Details",
    TOTAL_CONFIGURE_EDIT_TYPE = "Edit Type",
    STRUCTURE = "Structure",
    AMOUNT = "Amount",
    PROCUREMENT_STRUCTURE = "Procurement Structure",
    BUDGET = "Budget",
    COSTCODES = "Cost Codes",
    BP_STATUS = "Status",
    SELECTION = "Selection",
    PREVIOUS_BILL = "Previous Bill",
    PROJECT_NAME = "Project Name",
    CHANGE_ORDER = "Change Order",
    CHANGE_ORDER_CODE = "New Change Order Code",
    CHANGE_ORDER_DESCRIPTION = "New Change Order Description",
    RADIO = "radio",
    ENTIRE_ESTIMATE = "Entire Estimate",
    GRAND_TOTAL = "Grand Total",
    X_FACTOR = "x Factor",
    SOURCE_LEADING_STRUCTURE = "Source Leading Structure",
    ADDITIONAL_SETTING = "Additional Setting",
    COPYING_DESCRIPTION_AND_USER_DEFINED = "Copying Description and User Defined",
    COPYING_ASSIGNMENT = "Copying Assignment",
    CREATE_NEW = "Create New",
    BASIC_SETTING = "Basic setting",
    PROJECT_NUMBER = "Project Number",
    NAME = "Name",
    BUDGET_FROM = "Budget From",
    NEW_RECORD = "New Record",
    GENERATE_UPDATE_BASE_COST = "Generate/Update Base Cost",
    UPDATE_REVENUE = "Update Revenue",
    DISTRIBUTE_BY = "Distribute By",
    KIND = "kind",
    SUCCESSORS = "Successors",
    CREATE_RELATIONS = "Create Relations",
    GENERATE_ACTIVITIES = "Generate Activities",
    GENERATE_ACTIVITIES_FROM_ONE_ACTIVE_ESTIMATE = "Generate Activities from one active Estimate",
    GENERATE_ACTIVITIES_FREE_SCHEDULE_NO_LINK_WITH_ESTIMATE = "Generate Activities - free Schedule (no link with Estimate)",
    EVALUATION_SCHEMA = "Evaluation Schema",
    REMARK = "Remark",
    ROLE = "Role",
    SERVICE = "Service",
    AREA_CODE = "Area Code",
    PHONE_NUMBER = "Phone Number",
    TELEPHONE = "Telephone",
    EXTENSION = "Extension",
    ESTIMATE_TYPE_CONFIGURATION = "Estimate Type Configuration",
    NEW_ESTIMATE_TYPE = "New Estimate Type",
    UPDATE_ESTIMATE = "Update Estimate",
    ITEM_ASSIGNMENT = "Item Assignment",
    FUNCTION_TYPE = "Function Type",
    IGNORE_CURRENT_ELEMENT_JOB = "Ignore Current Element Job",
    REPLACE_WITH_ELEMENT = "Replace With Element",
    REPLACE_DETAILS = "Replace Details",
    RESOURCE_TYPE = "Resource Type",
    CURRENT_ELEMENT = "Current Element",
    CURRENT_ELEMENT_JOB = "Current Element Job",
    CHANGE_VALUE = "Change Value",
    BASIC_SETTING_CAPS = "Basic Setting",
    BID = "Bid",
    INCLUDE_INACTIVE_ITEMS = "Include inactive items",
    MODIFY_DETAILS = "Modify Details",
    ASSEMBLY_CATEGORY = "Assembly Category",
    RADIO_HIGHLETED_LINE_ITEM = "Highlighted Line Item",
    SPLIT_QUANTITY = "Split Quantity",
    SPLIT_QUANTITY_ITEM="Split Quantity Item",
    PROCUREMENT_STRUCTURE_SMALL = "Procurement structure",
    BILL_TO = "Bill To",
    UPDATED_PLANNED_QUANTITY = "Update Planned Quantity",
    IQ_QUANTITY_UPDATE = "IQ Quantity Update",
    UPDATE_BILLING_QUANTITY = "Update Billing Quantity",
    ESTIMATE_CONFIGURATION = "Estimate Configuration",
    ESTIMATE_STRUCTURE = "Estimate Structure",
    IBAN = "IBAN",
    START = "Start",
    MAIN_CONTRACT = "Main Contract",
    SELECT_CONTRACT_HEADER_SCOPE = "Select Contract Header Scope",
    DISTRIBUTE_BASED_ON = "Distribute based on",
    LAYOUT_6 = "layout6",
    LAYOUT_2 ="layout2",
    SYSTEM_OPTION = "System Option",
    ACTIVITY_STATUS = "Activity Status",
    TEMPLATE_PROJECT = "Template Project",
    SCHEDULE = "Schedule",
    CODE = "Code",
    UPDATE_THE_EXISTING_ITEMS = "Update the existing items",
    HIGHLIGHTED_ASSEMBLY = "Highlighted Assembly",
    CO2_SOURCE = "CO2 Source",
    CO2 = "CO2",
    UPDATE_MATERIAL_PRICES = "Update Material Prices",
    BASE = "Base",
    ENTERPRISE_CATALOG_CONFIGURATION = "Enterprise Catalog Configuration",
    CUSTOMIZING = "Customizing",
    CONTRACT_SALES = "Contract Sales",
    MAIN_BID = "Main Bid",
    STRUCTURE_SETTINGS = "Structure Settings",
    BASED_ON_MAJOR_LINE_ITEMS = "Based on major line items",
    BASED_ON_PROJECT_CHANGE_LINE_ITEMS = "Based on project change line items",
    TYPE_CAPS = "Type",
    CREATE = "Create",
    WIP = "WIP",
    SELECTED_PLANTS_ONLY = 'Selected Plant(s) only',
    SELECT_A_JOB = "Select a Job",
    JOB_CARD = "Job Card",
    CREATE_A_NEW_LOGISTIC_JOB = "Create a new Logistic Job",
    FINISH = "Finish",
    UNDER_MAINTENANCE = "Under Maintenance",
    DONE = "Done",
    IS_DUE = "Is Due",
    REMARKS = "Remarks",
    BASIC_DATA = "Basic Data",
    ASSIGN_BUSINESS_PARTNER = "Assign Business Partner",
    REQUISITION_STATUS = "Requisition Status",
    MODAL_OVERWRITE_RADIO_BUTTON = "Overwrite the only requisition with entire sub package(not only change)*",
    MULTI_PACKAGE_ASSIGNMENT_MODEL = "Multi Package Assignment Mode",
    PROJECT_DOCUMENT_TYPE = "Project Document Type",
    DELETE = "Delete",
    EVALUATE_CURRENT_PACKAGE = "Evaluate current package",
    MATERIAL_PACKAGE = "Material Package",
    STANDARD_CUMULATIVE = "Standard Cumulative",
    CODE_FORMAT = "Code Format",
    LEVEL_1 = "Level 1",
    SORTING_LEVEL_1 = "Sorting Level 1",
    PROCUREMENT_CONFIGURATION = "Procurement Configuration",
    CONSOLIDATION_LEVEL = "Consolidation Level",
    REGIONAL = "Regional",
    TICKET_ORDER = "Ticket Order",
    CRITERIA_1 = "Criteria 1",
    KIND_CAPS = "Kind",
    BRANCH = "Branch",
    CONSTRUCTION_SYSTEM_MASTER = "Construction System Master",
    SERVICE_REQ = "Service REQ",
    PROCUREMENT_SCHEDULE = "Procurement Schedule",
    UPDATE_PROCUREMENT_SCHEUDLE_FOR_CURRENT_PACKAGE = "Update procurement schedule for current package",
    UPDATE_EVENTS_FOR_CURRENT_PACKAGE_FROM_SCHEUDLE = "Update events for current package from Scheduling",
    EXISTED_CATALOG = "Existed Catalog",
    BILL_NO = "Bill No",
    CALL_OFF_ORDER = "Call-Off Order",
    QUOTATION_STATUS = "Quotation Status",
    CREATE_NEW_BOQ = "Create new BoQ",
    ALLOCATED_FROM = "Allocated From",
    ADVANCED_CRITERIA = "Advanced Criteria",
    PARAMETERS = "Parameters",
    RP_4 = "RP_4",
    RP_2 = "RP_2",
    GA_4 = "GA_4",
    GA_2 = "GA_2",
    AM_2 = "AM_2",
    GA_1 = "GA_1",
    RP_1 = "RP_1",
    GC_1 = "GC_1",
    GC_2 = "GC_2",
    PLANT_PRICING_GROUP = "Plant Pricing Group",
    TEMPLATE = "Template",
    CONTROL_TEMPLATE = "Control Template",
    RECEIVING_JOB = "Receiving Job",
    WORK_OPERATION_TYPE = "Work Operation Type",
    DUE_DATE = "Due Date",
    ASSIGNED_LEVEL = "Assigned Level",
    STRUCTURE_OR_ROOT = "Structure or Root",
    SEARCH_RULES = "Search Rules",
    CREATE_CHANGE_ORDER_REQUISITION_BASED_ON_BELOW_SELECTED_BASE_REQUISITION = "Create change order requisition based on below selected base requisition",
    COPY_HEADER_TEXT_FROM_PACKAGE = "Copy header text from Package",
    CREATE_RULE = "Create Rule",
    UPDATE_ACTIVITIES = "Update Activities",
    COPY_KEYDATES = "Copy Keydates",
    RULES_MASTER = "Rules Master",
    CLAIM_REASON = "Claim Reason",
    COMMENTS = "Comments",
    COPY_SUB_SCHEDULES = "Copy Sub-Schedules",
    EXPECTED_WOT = "Expected WOT",
    EXPECTED_EFFECTIVE_DATE = "Expected Effective Date",
    UPDATE_INSTALLED_BY_ACTIVITY = "Update Installed By Activity",
    UPDATE_PLANNED_BY_ACTIVITY = "Update Planned By Activity",
    TEST_RUN = "Test Run",
    UPDATE_LINE_ITEM_QUANTITIES = "Update Line Item Quantities",
    UPDATE_PLANNED_QUANTITIES = "Update Planned Quantities(Last update at : No Update Record",
    MASTER_CREW_MIXES = "Master Crew Mixes",
    EXPECTED_QUANTITY = "Expected Quantity",
    PROJECT_CHANGE_SETTING = "Project Change Settings",
    ASSIGN_PROJECT_CHANGE_TO_EACH_BID_HEADER = "Assign Project Change to each Bid Header",
    REPORT_PERIOD = "Report Period",
    BOQ_CONFIGURATION_TYPE = "BoQ Configuration Type",
    SUB_SCHEDULE = "Sub Schedule",
    MASTER_SCHEDULE = "Master Schedule",
    REPORTING_DATE = "Reporting Date",
    ENABLE_TRANSIENT_ROOT_ENTITY = "Enable Transient Root Entity",
    BELONGING_TO_THIS_PLANT_DIVISION = "Belonging to this Plant Division",
    BY_LINEITEM_QUANTITY = "by Line Item Quantity",
    NET_OC = "Net (OC):",
    TOTAL_SOURCE = "Total Source:",
    PACKAGE = "Package",
    HTML_FILE = "HTML File",
    MODIFY = "Modify",
    SUB_PACKAGE = "Sub Package",
    SPRACHE = "Sprache",
    OBERFLÄCHENSPRACHE = "Oberflächensprache",
    BENUTZERDATENSPRACHE = "Benutzerdatensprache",
    PACKAGE_TEMPLATE = "Package Template",
    CREATE_ESTIMATE_VERSION = "Create Estimate Version:",
    DOCUMENT_CATEGORY = "Document Category",
    PROJECT_GROUP = "Project Group",
    PRECALCULATED_WORK_OPERATION_TYPE = "Precalculated Work Operation Type",
    QUANTITY = "Quantity",
    WARRANTY_BOND = "Warranty Bond",
    COPY_ESTIMATE_OR_ASSEMBLY = "Copy Estimate or Assembly",
    UPDATE_FORECASTING_PLANNED_QUANTITY = "Update Forecasting Planned Quantity",
    PROFILE_NAME = "Profile Name",
    NEW_PROFILE = "New Profile",
    CLERK_ROLE = "Clerk Role",
    VALID_FROM = "Valid From",
    VALID_TO = "Valid To",
    COMMENT_TEXT = "Comment Text",
    BUILDINGSITE_SUPERVISORCERTIFICATE = "Building Site Supervisor Certificate",
    TOTAL_SOURCE_NET_OC = "Total Source (Net OC):",
    EXTERNAL_DESCRIPTION = "External Description",
    EXTERNAL_ID = "External Id",
    EXTERNAL_SOURCE = "External Source",
    BOQ_REFERENCE_NO = "BoQ Reference No.",
    QTO_TYPE = "QTO Type",
    QTO_PURPOSE = "QTO Purpose",
    CONTRACT_CONFIGURATION = "Contract Configuration",
    REQUISITION_CONFIGURATION = "Requisition Configuration",
    SUBSIDIARY = "Subsidiary",
    SUPPLIER = "Supplier",
    PROJECT_NO = "Project No.",
    STOCK_CODE = "Stock Code",
    FIFO_FIRST_IN_FIRST_OUT="FIFO - First In First Out",
    LIFO_LAST_IN_FIRST_OUT="LIFO - Last In First Out",
    POPULATE_BASIS_AND_CHANGE_ORDERS = "Populate Basis & Change Orders",
    MATERIAL_CATALOG_PRICE_VERSION="Material Catalog Price Version",
    INCLUDE_NEUTRAL_MATERIAL="Include Neutral Material",
    LASTEST_PRICE_VERSION="Latest Price version",
    BASE_PRICE="Base Price",
    TRANSACTION_TYPE = "Transaction Type",
    TRANSACTION_DATE = "Transaction Date",
    PAPER_SIZE="Paper Size",
    HEADER="Header",
    FOOTER="Footer",
    OUTSTANDING_CONTRACT_PES_STOCK_ITEM="Outstanding Contract & PES Stock Item",
    DELIVERED_PES_STOCK_ITEM="Delivered PES Stock Item",
    COMMENT="Comment",
    VOUCHER_NO_="Voucher No.",
    WATCHLIST="watchlist",
    TOTAL_VALUE="Total Value",
    TOTAL_PROVISION="Total Provision",
    EXPENSES="Expenses",
    TOTAL_QUANTITY="Total Quantity",
    COMPANY="Company",
    RESPONSIBLE_PROFIT_CENTRE="Responsible Profit Centre",
    REGISTERED_FOR_COMPANY_DETAIL="Registered for Company Detail",
    BASE_VERSION="Base+Versions",
      TO_="To :",
    FROM_="From :",
    IS_ACTIVE="Is Active",
    PASSWORD="Password",
    CONFIRM_PASSWORD="Confirm Password",
    CURRENCY="Currency",
    TOTAL="Total",
    PROVISION_TOTAL="Provision Total",
    MATERIAL_CODE="Material Code",
    STOCK_HEADER="Stock Header",
    STOCK_TOTAL="Stock Total",
    TOTAL_VALUE_RECEIPT="Total Value(Receipt)",
    TOTAL_VALUE_CONSUMED="Total Value(Consumed)",
    TOTAL_VALUE_DIFFERENCE="Total Value(Difference)",
    TOTAL_PROVISION_CONSUMED="Total Provision(Consumed)",
    TOTAL_PROVISION_DIFFERENCE="Total Provision(Difference)",
    TOTAL_PROVISION_RECEIPT="Total Provision(Receipt)",
    EXPENSE_TOTAL_RECEIPT="Expense Total(Receipt)",
    EXPENSE_CONSUMED="Expense(Consumed)",
    EXPENSES_DIFFERENCE="Expenses(Difference)",
    TOTAL_QUANTITY_RECEIPT="Total Quantity(Receipt)",
    TOTAL_QUANTITY_CONSUMED="Total Quantity(Consumed)",
    TOTAL_QUANTITY_DIFFERENCE="Total Quantity(Difference)",
    TOTAL_QUANTITY_PENDING="Total Quantity(Pending)",
    STATUS ="Status",
    FROM_POINTS ="From Points",
    TO_POINTS="To Points",
    SORTING = "Sorting",
    POSSIBLE_POINTS = "Possible Points",
    POSSIBLE_MINIMUM = "Possible Minimum",
    USER_DEFINED="User Defined",
    USER_DEFINED_1="User-Defined 1",
    EVALUATION_MOTIVE="Evaluation Motive",
    IS_DEFAULT="Is Default",
    QUOTE="Quote",
    BID_HEADER="Bid Header",
    TEXT_TYPE="Text Type",
    POINTS = "Points",
    RFQS="RFQs",
    MIXED="mixed",
    SPECIAL="special",
    ALPHABETIC="alphabetic",
    NUMERIC="numeric",
    CHANGE_FORMDATA_STATUS="Change Formdata Status",
    SERVICE_RFQ="Service RFQ",
    PAYMENT_SCHEDULE_TARGET_NET_OC="Payment Schedule Target (Net OC):",
    BILLING="Billing",
    MEASURED_PERFORMANCE="Measured Performance %",
    PROCUREMENT_CLERK="Procurement Clerk",
    REMARKS_2="Remarks2",
    ASSIGN_PROJECT="Assign Project",
    USER_DEFINED_2="User-Defined 2",
    USER_DEFINED_3="User-Defined 3",
    REFERENCES="References",
    USER_DEFINED_FIELDS="User-Defined Fields",
    EDITABLE="Editable",
    OPTIONAL="Optional",
    MULTI_SELECT="Multi Select",
    BUSINESS_PARTNER_NAME="Business Partner Name",
    E_MAIL="E-Mail",
    FIRST_NAME="First Name",
    LAST_NAME="Last Name",
    CONTACT_EMAIL="Contact Email",
    COUNTY="County",
    UPDATE_CURRENT_PAYMENT_SCHEDULE_LINE="Update current payment schedule line.",
    UPDATE_ALL_PAYMENT_SCHEDULE_LINES_OF_THIS_HEADER="Update all payment schedule lines of this header.",
    MATERIAL_QUALITY ="Material Quality",
    SPEED="Speed",
    QUALITY	="Quality",
    DESIGN_QUALITY="Design Quality",
    SERVICE_QUALITY="Service Quality",
    PERFORMANCE_SPEED="Performance Speed",
    DELIVERY_SPEED="Delivery Speed",
    REMARKS2 = "Remarks2",
    UPDATE_QUANTITIES_PRICE_OC="update quantities and prices(oc)",
    COST_CODE_ASSIGNMENT_DETAILS="Cost Code Assignment Details",
    TOTALS_CONFIGURATION="Totals Configuration",
    REPORTED = "Reported",
    GENERATE_TYPE="Generate Type",
    MAIN_ORDER="Main Order",
    UPDATE="Update",
    CONTRACT_SALES_1="Contract Sales(1)",
    GUARANTEE_COST = "Guarantee Cost",
    GUARANTEE_COST_PERCENTAGE = "Guarantee Cost Percentage",
    IS_LIMITED = "Is Limited",
    COST_REIMBURSABLE = "Cost Reimbursable",
    DATE = "Date",
    REFERENCE_NAME = "Reference Name",
    REFERENCE_DATE = "Reference Date",
    HIGHLIGHTED_LINE_ITEM = "Highlighted Line Item",
    CURRENT_SET_RESULT="Current Result Set",
    START_VALUE="Start Value",
    END_VALUE="End Value",
    SOURCE_CONTAINER_GRID="Source Grid",
    SHIPMENT_NUMBER="Shipment Number",
    PACKINGLIST_NUMBER="Packinglist Number",
    COUNTRY_OF_ORIGIN="Country Of Origin",
    TOTAL_DIMENSION="Total Dimension",
    TOTAL_WEIGHT="Total Weight",
    TRACKING_NUMBER="Tracking Number",
    CARRIER_NAME="Carrier Name",
    CARRIER_CODE="Carrier Code",
    SELECT_GROUPING_STRUCTURE_TO_CREATE_PACKAGE="Select Grouping Structure to create Package",
    DISSOLVE_SELECTED_REFERENCE_LINE_ITEM="Dissolve selected reference line item",
    MULTIPLE_PACKAGES_ASSIGNMENT_MODE="Multiple packages assignment mode:",
    CHOOSE_SELECTION_STRUCTURE_TO_CREATE_PACKAGE="Choose Selection Structure to create Package",
    SELECT_PROJECT_BOQ_MODE="Select Project BoQ:",
    SELECT_PROJECT_BOQ_RADIO_INDEX="Project BoQ Index",
    BOQ_ITEMS_SELECTION="BoQ Item Selection",
    COLUMN_FILTER_SELECTION="Column Filter",
    ASSIGNMENTS="Assignments",
    ASSIGNMENT_GRID="Assignment Grid",
    ASSIGNMENT_VALUE="Assignment Value",
    ASSIGNMENT_VALUE_GRID="Assignment Value grid",
    NEW_PACKAGE_OPTION="New Package Option",
    CONSOLIDATE_TO_ONE_PACKAGE_FOR_ALL_SELECTED_CRITERIA="Consolidate to one package for all selected criteria",
    SEPARATE_PACKAGE_FOR_EACH_BOQ_HEADER="Separate package for each BoQ Header",
    CREATE_PACKAGE_FOR_LINE_ITEM_WITH_NO_RESOURCE="Create Package for Line item with no resource",
    CONSIDER_BOQ_QTY_RELATION="Consider BoQ Qty Relation",
    CREATE_UPDATE_BOQ_IN_PACKAGE="Create/Update BoQ in Package",
    QUANTITY_TRANSFER_FROM="Quantity Transfer From",
    ASSIGNMENTS_PROCUREMENT_STRUCTURE="Assignments Procurement Structure",
    PROCUREMENT_PACKAGE="Procurement Package",
    PROCUREMENT_PACKAGE_DESCRIPTION="Procurement Package",
    RADIO_INDEX="Radio Index",
    CREATE_UPDATE_BOQ_PACKAGE="Create/Update BoQ Package",
    REFERENCE="Reference",
    ACTIVITY_SETTING="Activity Settings",
    SUB_DESCRIPTION="Sub-Description",
    UPDATED_STATUS_FOR_PAYMENT_SCHEDULE="Updated Status For Payment Schedule",
    PAGE_LAYOUT="Page Layout",
    REPORT_SETTING="Report Setting",
    HEADER1="Header",
    FOOTER1="Footer",
    NO_HEADER="No Header",
    NO_FOOTER="No Footer",
    HEADER_A1_REPORT_LANDSCAPE ="Header: A1 Report Landscape",
    FOOTER_A1_REPORT_LANDSCAPE ="Footer: A1 Report Landscape",
    HEADER_A2_REPORT_LANDSCAPE ="Header: A2 Report Landscape",
    FILE_TYPE= "File Type",
    BARCODE="Barcode",
    ORIGIN="Origin",
    DOCUMENT_DATE ="Document Date",
    EXPIRATION_DATE ="Expiration Date",
    CONTACT="Contact",
    MATERIAL_CATALOG="Material Catalog",
    ESTIMATE="Estimate",
    SALES_CONTRACT=  "Sales Contract",
    PES_NO="PES No.",
    RFI="RFI",
    SCHEDULE_ACTIVITY="Schedule Activity",
    REQ="REQ",
    DISPATCHING_HEADER="Dispatching Header",
    USER_DEFINED_4="User-Defined 4",
    USER_DEFINED_5="User-Defined 5",
    SETTLEMENT="Settlement",
    URL="URL",
    OPTIONAL_WITH_IT="Optional With IT",
    OPTIONAL_WITHOUT_IT="Optional Without IT",
    DEEP_COPY_IN_PROGRESS="Deep Copy in Progress, this Operation may take Several Minutes",
    ORD_HEADER="Ord Header",
    FOR_RECEIVING_PROJECT="For Receiving Project",
    CHANGE_ENTITY="Change Entity",
    SIDE_CONTRACT="Side Contract",
    FOOTER_A2_REPORT_LANDSCAPE ="Footer: A2 Report Landscape",
    SIDE_BID="Side Bid",
    NUMBER_MASK="Number Mask",
    CHECK_MASK="Check Mask",
    SEQUENCE_TYPE="Sequence Type",
    CHECK_NUMBER="Check Number",
    GENERATE_NUMBER="Generate Number",
    MINIMAL_LENGTH="Minimal Length",
    MAXIMUM_LENGTH="Maximum Length",
    NUMBER_SEQUENCE="Number Sequence",
    BP_NAME_1_SMALL="bpname1",
    CREATE_OR_UPDATED="Create Or Update",
    EVALUATION_DOCUMENT="Evaluation Document",
    BILL_TO_ACTION_1="billToAction-1",
     BILL_TO_ACTION_2="billToAction-2",    
     MARKUP_CALCULATION_TYPE="Markup Calculation Type",SPLIT_METHOD="Split method ",
    MASTER_DATA_CONTEXT="Master Data Context",
    AREA_WISE="Area Wise",
    DCM_BASED_ON_WQ_AQ_QUANTITY="DCM based on WQ/AQ Quantity",
    SINGLE_STEP_ALLOWANCE="Single step Allowance",
    LEVEL_OUT_DIFFERENCES_FROM_FP_ITEMS="Level out differences from FP items",
    G_A="G&A[%]",
    A_M="AM[%]",
    R_P="R&P[%]",
    ALLOWANCE_AREAS_AND_GC_AREAS="Allowance Areas & GC Areas",
    ALLOWANCE_CODE = "Allowance Code",
    REST="Rest[%]",
    GC_AREA="GC Areas",
    PREVIOUS_WIP="Previous WIP",
    USE_UR_BREAKDOWN="Use UR Breakdown",
    USE_BASE_BOQ="Use Base BoQ",
    BASE_BOQ_OPT="Base BoQ (Opt.)",
    EXTERNAL_ROLE="External Role",
    FAMILY_NAME="Family Name",
    DEPARTMENT="Department",
    ICON="Icon",
    INVOICE_PROGRESS_APPLICATION="Invoice Progress Application",
    IS_POPUP="IsPopUp",
    IS_NOTIFICATION="IsNotification",
    EVALUATE_PROXY="EvaluateProxy",
    DISABLE_REFRESH="DisableRefresh",
    ALLOW_REASSIGN="Allow Reassign",
    EXCEL_PROFILE="Excel Profile",
    BUSINESS_PARTNER_MATCH_CODE="Business Partner Match Code",
    CALL_OFF_CONTRACT="Call Off Contract",
    CHANGE_ORDER_CONTRACT = "Change Order Contract",
    CALL_OFF_FRAMEWORK_CONTRACT="Call Off Framework Contract",
    LOGISTIC_SETTLEMENT_TYPE="Logistics Settlement Type",
    ESTIMATE_HEADER="Estimate Header",
    LOGIN_ALLOWED="Login Allowed",
    COMBINATIONS="Combinations",
    MAIN_QUOTE="Main Quote",
    CHANGE_QUOTE="Change Quote",
    SIDE_QUOTE="Side Quote",
    SELECT_RADIO_BUTTON="selectRadioButton",
    SIDE_ORDER="Side Order",
    FINISH_START="Finish - Start",
    REVISION="Revision",
    CERTIFICATE="Certificate",
    EMPTY_RECORD="Empty Record",
    UPDATE_UNIT_RATE_FIXED_PRICE_BOQ_ITEMS="Update Unit Rate of fixed price BoQ items",
    LEASING="Leasing",
    INSTALLATION="Installation",
    CROSS="Cross",
    FORM_DATA_STATUS="Formdata Status",
    BUSINESS_PARTNER_OPPOSITE="Business Partner Opposite",
    NONE="None",
    SUM_OF_QUANTITY_PORTIONS_EXCEEDS_100_PERCENT="The sum of Quantity Portions of all selected Bill-To's exceeds 100 percent!",
    COLUMN_CONFIGURATION="Column Configuration",
    COLUMN_CONFIGURATION_DETAILS="Column Configure Details",
    PRODUCTIVITY_FACTOR_DETAIL="Productivity Factor Detail",
    OWNER_STATUS="Owner Status",
    OPPOSITE_STATUS="Opposite Status",
    BP_BRANCH_ADDRESS="BP Branch Address",
    BP_OPPOSITE_BRANCH_ADDRESS="BP Opposite Branch Address",
    BUSINESS_PARTNER_RELATION="Business Partner Relation",
    CREATE_UPDATE_MATERIAL_PACKAGE="Create / Update Material Package Assignment",
    BP_BRANCH="BP Branch",
    BP_OPPOSITE_BRANCH= "BP Opposite Branch",
    QUANTITY_TAKEOFF_HEADER="Quantity Takeoff Header",
    SCHEDULE_TYPE="Schedule Type",
    EXECUTION_SCHEDULE="Execution Schedule",
    REFRESH="Refresh",
    OK="OK",
    FROM_BILL="From Bill",
    TITLE="Title",
    LOCATION="Location",
    MEETING_URL="Meeting Url",
    IMPORTANCE="Importance",
    START_TIME="Start Time",
    END_TIME="End Time",
    SYNC_TO_EXTERNAL_MEETING="Sync to Ext. Meeting",
    ALL_DAY="All Day",
    MAKE_RECURRING="Make Recurring",
    RECURRANCE_END_DATE="recurrance end date",
    GRAND_NET_OC="Grand Net (OC):"
  }

  export enum CommonKeys {
    SEARCH_RESULT = "search_result",
    PATH = "path",
    FILTER = "Filter",
    MANUAL_INPUT = "Manual Input",
    CODE = "Code",
    ADDRESS_INDEX = "address_index",
    VALUE = "Value",
    SELECTION = "Selection",
    BUTTON_TAG = "button",
    LOGIN_VIEW = "loginView",
    MENU_MAIN = "menu main",
    RADIO = "radio",
    BUTTON_HANDLE = "Button",
    GRID = "grid",
    LIST = "list",
    LIST_EXACT = "listexact",
    GRID_1 = "grid1",
    SPAN = "span",
    WIZARD = "wizard",
    CHECKED = "Checked",
    CHECK = "check",
    UNCHECK = "uncheck",
    SUBMITTED = "Submitted",
    APPROVED = "Approved",
    PROCESSED = "Processed",
    PART_DELIVERED = "Part Delivered",
    AVAILABLE = "Available",
    DELIVERED = "Delivered",
    DATE_RECEIVED = "Date Received",
    FI_BILLED = "FI Billed",
    ACCEPTION = "Acception",
    PUBLISHED = "Published",
    IN_PROGRESS = "In-Progress",
    STANDARD = "standard",
    MATERIAL = "Material",
    MATERIAL_AND_COST_CODE = "Material & Cost Code",
    QUICKSTART = "quickstart",
    RADIO_INDEX = "Radio Index",
    CHECKBOX = "Checkbox",
    DEFAULT = "Default",
    IN_MINUS_PROGRESS = "In-Progress",
    FILTER_SMALL = "filter",
    INDEX = "Index",
    ROOT = "Root",
    POSITION = "Position",
    CONTRACTED = "Contracted",
    A = "a",
    LABEL = "label",
    DIV = "div",
    CLASS = "class",
    NEW_CONFIG_NAME = "New Config Name",
    TOTAL = "Total",
    NO_FILTER = "noFilter",
    CURRENT_SMALL = "current",
    INCREMENTED_SMALL = "incremented",
    BASE_COST_TOTAL = "Base Cost Total",
    VALIDATION_WIZARD = "Validation Wizard",
    ROUNDING = "Rounding",
    WITHOUT_ROUNDING = "WithoutRounding",
    QUANTITY = "QUANTITY",
    FINAL_PRICE = "FINAL_PRICE",
    UNIT_RATE = "UNIT_RATE",
    FINAL_PRICE_IN = 'FINAL_PRICE_IN',
    BOTH = "Both",
    EDIT = "edit",
    SCHEDULES = "Schedules",
    FROM_STRUCTURE = "From Structure",
    PAYMENT_TERM = "Payment Terms",
    CONTROLLING_UNITS = "Controlling Units",
    CONTROLLING_UNIT = "Controlling Unit",
    CONTROLLING_COST_CODE = "Controlling Cost Code",
    TO_STRUCTURE = "To Structure",
    RECORDED = "Recorded",
    TO_STRUCTURE_AQ = "ToStructure AQ",
    BIDIRECTIONAL_WQ_AQ = "Bidirectional WQ AQ",
    IS_DUE = "Is Due",
    PROJECT_COST_GROUP = "Project Cost Group",
    CREATE = "create",
    ENTERPRISE_COST_GROUP = "Enterprise Cost Group",
    LOCATION = "Location",
    NO_RELATION = "No relation",
    BOQS = "BoQs",
    ACTIVE = "Active",
    SELECT_RADIO_BUTTON = "selectRadioButton",
    SCHEDULING = "Scheduling",
    NOT_VISIBLE = "notVisible",
    NOT_VISIBLE_COLUMN = "notVisibleColumn",
    INPUT_SHOULD_NOT_EXIST = "inputShouldNotExist",
    IN_ARBEIT = "In Arbeit",
    PROJECT_ESTIMATE = "Project Estimate",
    ALLOWANCE = "Allowance",
    ESTIMATE_ALLOWANCE = "Estimate Allowance",
    ESTIMATE_ALLOWANCE_TYPE = "Est. Allowance Assignment Type",
    CO2_PROJECT_TOTAL = "co2projecttotal",
    CO2_SOURCE_TOTAL = "co2sourcetotal",
    CO2_TOTAL_VARIANCE = "co2totalvariance",
    ESTIMATE_COST_CODE = "Estimate Cost Code",
    INFORMATION = "Information",
    ASSEMBLIES = "Assemblies",
    INCREMENTED = "incremented",
    CURRENT = "current",
    SHOULD_NOT_EXIST = "shouldNotExist",
    SHOULD_EXIST = "shouldExist",
    VISIBLE = "Visible",
    NOT_SPACE_VISIBLE = "not visible",
    VISIBLE_SMALL = "visible",
    COST_CODE_TYPE = "Cost Code Type",
    ASSEMBLY_TYPE = "Assembly Type",
    EST_ASSEMBLY_TYPE = "Est. Assembly Type",
    CREW_ASSEMBLY = "Crew Assembly",
    STANDARD_ASSEMBLY = "Standard Assembly",
    PROGRESS_INVOICE = "Progress Invoice",
    BPA_BILLED = "BPA Billed",
    MODAL_FOOTER = "modal-footer",
    USER = "user",
    USER_DEFINED_COST_COLUMNS = "User Defined Cost Columns",
    LAYOUT_6 = "layout6",
    WIC_ITEM_REF_NO = "WICItemRefNo",
    STATUS_BAR = "statusbar",
    VALID = "Valid",
    SELECT_MAINTENANCE = "Select Maintenances",
    HOOK = "Hook",
    SALES_WIP_AND_BILL = 'Sales / WIP&Bill',
    CHANGE_ORDER = "Change Order",
    PROJECT_BOQ = "Project BoQ",
    PACKAGE_BOQ = "Package BOQ",
    PROCUREMENT_CONTRACT_BOQ = "Procurement Contract BoQ",
    LAYOUT_19 = "layout19",
    TARGET = "Target",
    ONE_BID_FOR_ALL_CHANGES = "One bid for all changes",
    SEPARATE_BID_FOR_EACH_CHANGE = "Separate bid for each change",
    PLANNED = "Planned",
    STARTED = "Started",
    FINISHED = "Finished",
    CHANGE_BID = "Change Bid",
    IDENTIFIED = "Identified",
    DESIGN_CHANGE = "Design Change",
    CHANGE_REQUEST = "Change Request",
    CHANGE_CONTRACT = "Change Contract",
    POSTED = "Posted",
    FINAL_INVOICE = "Final Invoice",
    GENEHMIGTER_NETTOBETRAG = "Genehmigter Nettobetrag",
    LAYOUT_21 = "layout21",
    BILLED = "Billed",
    FROM_PESS = "From PES",
    COLLAPSE = "Collapse",
    INVOICES = "Invoices",
    NETTO = "Netto",
    STANDARD_SINGEL = "Standard Singel",
    CREATE_ONE_INVOICE_PER_PES = "Create one invoice per PES",
    AMOUNT = "Amount",
    FROM_CONTRACT_ITEMS = "From Contract Items",
    FROM_OTHER = "From Other",
    REJECTIONS = "Rejections",
    FROM_BILLING_SCHEMA = "From Billing Schema",
    BALANCE = "Balance",
    CLERK = "Clerk",
    RECHNUNGSKORREKTUR = "Rechnungskorrektur",
    HISTORY = "History",
    NEW = "New",
    BOQ = "BoQ",
    WIC_BOQ = "WIC BoQ",
    LAYOUT_2 = "layout2",
    USER_FREQUENCE = "User Frequence",
    WIP = "WIP",
    OFFER_ISSUED = "Offer issued",
    FREIG_LEISTUNG_STAND = "Freig. Leistungsstand",
    SERVICE_PERSONNEL = "Service Personnel",
    FIND_BIDDER = "Find Bidder",
    PACKAGE_STATUS = "Package Status",
    PROGRESS = "Progress",
    WIP1 = "WIP1",
    MATERIAL_CATALOG_AND_GROUP = "Material Catalog & Group",
    LEVEL_2 = "Level 2",
    LEVEL_1 = "Level 1",
    LEVEL_3 = "Level 3",
    PRICE_COMPARISON_1 = "Price Comparison(1)",
    MATERIAL_CATALOG_TYPE = "Materialcatalog Type",
    INTERNET_CATALOG = "internet catalog",
    NEUTRAL_MATERIAL = "Neutral Material",
    MATERIAL_TYPE = "Material Type",
    STOCK_MANAGED_MATERIAL = "Stock Managed Material",
    MASTER_DATA = "Masterdata",
    CATALOG_DOES_NOT_EXISTS = "catalogdoesnotexists",
    CATALOG_EXISTS = "catalogexists",
    DROP_DOWN = "dropdown",
    SEARCH_TERM = "searchterm",
    ATTRIBUTE = "attribute",
    ICON = "icon",
    MIN_QUANTITY = "minQuantity",
    MATERIAL_TYPE_SMALL = "materialType",
    SORTING_LOW_TO_HIGH = "sortingLowToHigh",
    SORTING_HIGH_TO_LOW = "sortingHighToLow",
    LIST_PRICE = "listPrice",
    CART_RECORD = "cartRecord",
    ALTERNATIVE = "alternative",
    DETAILS_CO2_ATTRIBUTE_PRICE_LIST = "detailsCO2AttributePriceList",
    PERCENT = "Percent",
    PROJECT_MANAGER = "Project Manager",
    CERTIFICATE_TYPE = "Certificate Type",
    CIS_CERTIFICATE_EN = "CIS Certificate (en)",
    PLACE_ORDER_SUCCESSFULLY = "Place Order Successfully",
    DISABLED = "disabled",
    ENABLED = "enabled",
    SERVICE = "Service",
    PACKAGE = "Package",
    CONSTRUCTION = "Construction",
    INFO_TEXT = "Info Text",
    INVOICE_FOOTER = "Invoice Footer",
    INTERNAL_FOOTER = "Internal Footer",
    QUANTITY_SMALL = "Quantity",
    ORDERED = "Ordered",
    SELECTED_ITEMS = "Selected item(s)",
    BASE_PRICE = "Base Price",
    ALTERNATIVE_CAPS = "Alternative",
    BASE_POSTPONED = "Base postponed",
    ALTERNATIVE_AWARDED = "Alternative awarded",
    DISABLE_TARGET_ITEM_AFTER_REPLACEMENT = "Disable Target Item After Replacement",
    PACKAGE_1 = "Package(1)",
    BLOCKED = "Blocked",
    MATERIAL_REQ = "Material Req",
    GENERAL = "General",
    CONVERSION = "Conversion",
    UNIT = "Unit",
    GRAM = "Gram",
    KILOGRAM = "Kilogram",
    ALL_QUOTE_FROM_CURRENT_PROJECT = "All Quotes from Current Project",
    SPECIFIC_CATALOG = "Specific catalog",
    MATERIAL_PO = "Material PO",
    CONTRACT = "Contract",
    SUPPLIER_TEXT = "Supplier Text",
    CONTRACT_SALUTATION = "Contract Salutation",
    PROCURMENT_EVENT_TYPES = "Procurement Event Types",
    DESIGN_READY = "Design Ready",
    SYSYTEM_OPTION = "System Option",
    AUTO_UPDATE_CHAINED_EVENTS = "Auto Update Chained Events",
    BEFORE_EVENT_END = "Before Event End",
    AFTER_EVENT_START = "After Event Start",
    AFTER_CUSTOM_EVENT_START = "After Custom Event Start",
    AFTER_CUSTOM_EVENT_END = "After Custom Event End",
    BEFORE_CUSTOM_EVENT_START = "Before Custom Event Start",
    BEFORE_CUSTOM_EVENT_END = "Before Custom Event End",
    BEFORE_SYSTEM_EVENT = "Before System Event",
    PLANNED_END_PACKAGE = "Planned End (Package)",
    AFTER_SYSTEM_EVENT = "After System Event",
    ACTUAL_START_PACKAGE = "Actual Start (Package)",
    ACTUAL_END_PACKAGE = "Actual End (Package)",
    SAFETY_LEAD_TIME = "Safety Lead Time",
    PLANNED_START_PACKAGE = "Planned Start (Package)",
    NO_START_DATE = "No Start Date",
    NO_END_DATE = "No End Date",
    FETCHED_DATE_INCREMENT = "fetchedDateIncrement",
    FETCHED_DATE_DECREMENT = "fetchedDateDecrement",
    BYPASS_EVALUATED_ITEM = "Bypass evaluated item",
    INCLUDE_NON_CONTRACTED_ITEM_IN_PREVIOUS_PES = "Include non contracted item in previous PES",
    REQUISITION_STATUS = "Requisition Status",
    NSTB = "NSTB",
    DOWNWARDS = "Downwards",
    LAYOUT_1 = "layout1",
    PROCUREMENT_BUDJET = "Procurement Budget",
    NET_TOTAL = "Net Total",
    SERVICE_PA = "Service PA",
    SERVICE_PES = "Service PES",
    ALLOCATION = 'Allocation',
    RECONCILIATION = "Reconciliation",
    DISCOUNT_AMOUNT = "Discount Amount",
    DISCOUNT_BASIS = "Discount Basis",
    DISCOUNT_DATE = "Discount Date",
    NET_PAYABLE = "Net Paybel",
    INVOICE_TYPE = "Invoice Type",
    INVOICE = 'Invoice',
    PROCUREMENT_BUDGET = "Procurement Budget",
    FRAMEWORK_AGREEMENTS = "framework agreements",
    CHANGE_STRUCTURE = "Change Structure",
    NEW_REQUISITION = "New Requisition",
    HEADER = "HEADER",
    TODAY = "Today",
    DATE_REQUESTED = "Date Requested",
    PURCHASE_ORDER = "Purchase Order",
    RFQ_CREATED = "RfQ Created",
    CRITERIA_LABEL = "Criteria Label",
    CRITERIA = "Criteria",
    CRITERIA_1 = "Criteria 1",
    CRITERIA_2 = "Criteria 2",
    RIB_DEMO_STAMM = "RIB_DEMO_Stamm",
    HIGHLIGHTED_MATERIAL = "Highlighted Material",
    CURRENT_CONTRACT = "Current Contract",
    ALL_CONTRACTS_FROM_CURRENT_PROJECT = "All Contracts from Current Project",
    ALL_CATALOGS = "All catalogs",
    BY_IDENTICAL_MATERIAL_CODE = "By identical material code",
    BY_PROCUREMENT_STRUCTURE = "By procurement structure",
    SELECTED_LINE_ITEM = "Selected Line Item",
    CRITERIA_SELECTION = "Criteria Selection",
    CURRENT_RESULT_SET = "Current Result Set",
    PERFECTLY_MATCHED = "Perfectly Matched",
    PES_STATUS = "PES Status",
    ONE_TRANSACTION_PER_PES_ITEM = "One Transaction per PES Item",
    TRANSCATION_MODE = "Transaction Mode",
    COMPANY_YEAR = "Company Year",
    REPORTING_PERIOD = "Reporting Period",
    YEAR = "year",
    CREATE_TRANSACTION = "Create Transaction",
    LINE_ITEMS = "Line Items",
    FIXED_TIME = "Fixed Time",
    DAY = "Day",
    FIXED_UNITS = "Fixed Units",
    NACHUNTERNEHMER = "Nachunternehmer",
    FIXED_WORK = "Fixed Work",
    FINLAND = "Finland",
    LAYOUT_0 = "layout0",
    PRICE = "Price",
    MODIFICATION = "modification",
    AS_SOON_AS_POSSIBLE = "As Soon As Possible",
    M = "M",
    DE = "DE",
    MANUAL = "manual",
    TENDERING = "Tendering",
    SIDE_ORDER = "Side Contract",
    CONSECUTIVE_BILL_NO = "Consecutive Bill No.",
    CUSTOMER = "Customer",
    BILL_NO = "Bill No.",
    TRANSPORT_PLANNING = "Transport Planning",
    PICKING = "Picking",
    LOADED = "Loaded",
    SERVICE_REQ = "Service REQ",
    WASTAGE="Wastage",
    MATERIAL_CONSUMPTION="Material Consumption",
    ACTIVITY = "Activity",
    BOQ_ITEM_REF_NO = "BoQ Item Ref. No.",
    LIMITS = "Limits",
    WEEKLY_LIMIT = "Weekly Limit",
    MONTHLY_LIMIT = "Monthly Limit",
    TIME_SYMBOL_LIMITS = "Time Symbol Limits",
    TIME_SYMBOL_BEFORE_DAILY_LIMIT = "Time Symbol before daily limit",
    TIME_SYMBOL_SAVING_LIMITS = "Time Symbol Saving Limits",
    TIME_SYMBOL_FOR_HRS_OVER_SAVING_LIMIT = "Time symbol for hrs over saving limit",
    GA = "G&A",
    RP = "R&P",
    AM = "AM",
    ACCOUNTING = "Accounting",
    ORDER_TEXT = "Order Text",
    AS_LATE_AS_POSSIBLE = "As Late As Possible",
    ACCEPTED = "Accepted",
    DONE = "Done",
    PLANNING_FINISHED = "Planning finished",
    STD = "Std",
    UPDATE_IQ_QUANTITIES = "Update IQ Quantities",
    LAYOUT_16 = "layout16",
    REJECTED = "Rejected",
    LAYOUT_3 = "layout3",
    FIXED = "Fixed",
    BID_BOQ = "Bid BoQ",
    IN_PROGRESS_SPACE = "In Progress",
    ASSIGNED = "Assigned",
    PRICE_CONDITION_TYPE = "Price Condition Type",
    STAGING_ACTUALS = "Staging Actuals",
    WORST_CASE_FACTOR = "Worst Case Factor",
    BEST_CASE_FACTOR = "Best Case Factor",
    LOCKED = "Locked",
    MATERIAL_RECEIPT = "Material Receipt",
    NOTE = "NOTE",
    OPTIONAL_WITH_IT = "Optional with IT",
    COMMENT_TEXT = "commenttext",
    APPROVE = "Approve",
    CANCEL = "Cancel",
    PROCUREMENT_PAYMENT_SCHEDULE_STATUS = "Procurement Payment Schedule Status",
    CANCELLED = "Cancelled",
    PRONUNCIATION = "pronunciation",
    CALL_OFF = "Call Off",
    ROLE = "Role",
    COPPER_PRICE_PER_TON = "Copper Price per Ton",
    COPPER_WEIGHT = "Copper Weight",
    VAMED_STANDARD = "Vamed Standard",
    SURCHARGE = "Surcharge",
    DISPOSAL = "Disposal",
    COPPER_SURCHARGE = "Copper Surcharge",
    PROGRESS_REPORT_METHOD = "Progress Report Method",
    NEW_CONTRACT = "New Contract",
    FORM_FK = "FormFk",
    FORM_DATA_STATUS_FK = "FormDataStatusFk",
    PROCUREMENT_STOCK_TRANSACTION_TYPE = "Procurement Stock Transaction Type",
    JOINT_VENTURE_JOINT_VENTURE_ARGEN="JOINT VENTURE Joint Ventures (ARGEN)",
    JV0001_RIB_JOINT_VENTURE_01 ="JV_0001 RIB Joint Venture_01",
    CASE_TYPE="Case Type",
    LAYOUT_8="layout8",
    CHECKBOX_SMALL="checkbox",
    RESOURCES="Resources",
    RESOURCE_REQUISITION = "Resource Requisition",
    BASELINE="Baseline",
    ESCALATION="Escalation",
    DEPRECIATED="Depreciated",
    IN_DOUBT="In Doubt",
    UNCHECKED = "Unchecked",
    HIFO_HIGHEST_IN_FIRST_OUT="HIFO - Highest In First Out",
    LARGEST="largest",
    LOWEST="lowest",
    CONSUMPTION_RESERVATION="Consumption Reservation",
    ARTIKELKATALOG ="Artikelkatalog",
    LOFO_LOWEST_IN_FIRST_OUT="LOFO - Lowest In In First Out",
    EDITABLE = "Editable",
    READ_ONLY = "Read Only",
    ALL="All",
    CHARACTERISTIC_GROUP="Characteristic Group",
    CON_HEADER_DESCRIPTION="CON_HEADER_DESCRIPTION",
    PICKED="Picked",
    REQUESTED='Requested',
    INPUT_CLASS = "Input Class",
    LAYOUT_5 = "layout5",
    RIB_DEMO="RIB Demo",
    WEIGHTING="Weighting",
    MATERIAL_RFQ="Material RFQ",
    BID_SALUTATION="Bid Salutation",
    MATERIAL_QTN="Material QTN",
    BATCH="Batch",
    BATCH_DATE="Batch Date",
    CONTAINER_UUID="Container UUID",
    POPUP_TYPE="Popup Type",
    BILLING_SCHEMA="Billing Schema",
    PROCUREMENT_BOQS="Procurement BoQs",
    TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT="Text Assemblies Module  - Formatted Text",
    ITEMS="Items",
    QUOTES="Quotes",
    CERTIFICATES="Certificates",
    MILESTONES="Milestones",
    GENERALS="Generals",
    DOCUMENT="Document",
    CHARACTERISTICS="Characteristics",
    DOCUMENTS_PROJECT="Documents Project",
    PAYMENT_SCHEDULE="Payment Schedule",
    ALL_ITEM_UNDER_CURRENT_SELECTED_LEAD_RECORD="All item under current selected lead record",
    QUOTED = "Quoted",
    ENV_VARIABLE = "Env Variable",
    RECORD_TYPE="Record Type",
    SENT="Sent",
    FORMULA="Formula",
    SELECT_OPTION_TO_UPDATE_DETAILS="Select option to update details",
    DAY_RENTAL_INTERNALLY="Dayrental Internaly",
    TEST_SALES_SETTLEMENT="Test Sales Settlement",
    LAYOUT_11 = "layout11",      
    SINGLE_INVOICE="Single Invoice",
    CONTRACT_BOQ="Contract BoQ",
    WIP_BOQ="WIP BoQ",
    AGREED="Agreed",
    ERROR_MESSAGE="Error Message",
    EMPTY_CODE_DEFINED="EmptyCodeDefined",
    CODE_NOT_PASSED="CodeNotPassed",
    CODE_DEFINED="CodeDefined",
    EMPTY_BILL_NO_DEFINED="EmptyBillNoDefined",
    BILL_NO_NOT_PASSED="BillNoNotPassed",
    BILL_NO_DEFINED="BillNoDefined",
    DELETE_RECORD="DeleteRecord",
    CREATE_NEW="CreateNew",
    CLOSED="Closed",
    PROFIT_CENTER="Profit Center",
    SIDE_BID = "Side Bid",
    CHANGE_QUOTE="Change Quote",
    UPDATE_OPTION="Update Option",
    UPDATE_OPTION_BOQ="Update Option BoQ",
    INSERT_LINE_ITEMS="Insert Line Items",
    ALLOWANCE_AREAS_AND_GC_AREAS_2="Allowance Areas & GC Areas",
    BASIC_SETTINGS="Basic Settings",
    SET_LINE_ITEM_TO_NO_RELATION="Set Line Item to No Relation",
    ACCEPTED_IN_PRINCIPLE="Accepted in principle",
    REJECTED_WITH_PROJECT="Rejected with protest",
    WITHDRAWN="Withdrawn",
    PES_1 = "PES(1)",
    CONFIG_MAIN_ORDER="CONFIG-Main Order",
    DOCUMENT_SMALL="document",
    PROCUREMENT_OR_PES="Procurement / PES",
    STANDARD_CAPS = "Standard",
    PREDEFINE="Predefine",
    LI="li",
    RIB_EXCEL_BIDDER="RIB Excel Bidder",
    DESELECT_RADIO_BUTTON="deSelectRadioButton",
    SALES_CONTRACT_TYPE="Sales Contract Type",
    BILL_PROGRESS_APPLICATION="Bill Progress Application",
    UPDATE_ALREADY_BILLED_QUANTITY="Update already billed quantity",
    BILL_STATUS="Bill Status",
    FRAMEWORK_CONTRACT_CALL_OFF="Framework Contract Call Off",
    BPA_POSTED = "BPA Posted",
    COST_TOTAL = "Cost Total",
    IN_PLANNING_PHASE ="In Planning Phase",
    CONSIDER_S_CURVE_FOR_PLANNED_QUANTITY="Consider SCurve for Planned Quantity",
    LINE_ITEM_QTY_SCHEDULE_CONFIG_1="lineItemQtyScheduleConfig-1",
    LINE_ITEM_QTY_SCHEDULE_CONFIG_2="lineItemQtyScheduleConfig-2",
    LINE_ITEM_QTY_UPDATE_CONFIG_1="lineItemQtyUpdateConfig-1",
    LINE_ITEM_QTY_UPDATE_CONFIG_2="lineItemQtyUpdateConfig-2",
    UPDATE_AQ_IQ_LINE_ITEM_QUANTITY="Update AQ or IQ in Line Item Quantity",
    ASSEMBLY="Assembly",
    NO_ACCESS="No access",
    DIRECT_COST_CENTRE ="Direct Cost Centre",
    INDIRECT_COST_CENTRE="indirect cost centre",
    CREATED="Created",
    BPA_CANCELLED="BPA Cancelled",    
    BPA_ARCHIVED="BPA Archived",
    ALLOW_APPLYING_A_BILL_STATUS_THAT_USES_PARAMETER_IS_CANCELLED_TRUE='Allow applying a bill status that uses parameter "Is Cancelled" = true',
    DUE_DATE="Due Date",
    ORDER_STATUS='Order Status',
    FINALLY_BILLED="Finally Billed",
    CUSTOM_WIZARD="Custom Wizard",
    START_WORKFLOW="Start Workflow",
    TEMPLATE_ID="TemplateId",
    VERIFY_THE_UNIQUESS_OF_THE_DESCRIPTION_FIELD_IN_THE_PES_HEADER_BASED_ON_THE_BP="Verify the Uniqueness of the Description Field in the PES Header Based on the BP",
    TO_BE_APPROVED="To be Approved",
    OTHER = "other",
    USED_IN_COMPANY="Used In Company",
    ALLOWANCE_ASSIGNMENT="Allowance Assignment",
    BILLS_CREATE_DIRCTLY_FROM_CONTRACT_BASED_PAYMENT_SCHEDULE="Bills created directly from Contract, based on payment schedule",
    BILLS_BASED_ON_WIP_WHICH_MAY_HAVE_BEEN_CREATED_FROM_QTO="Bills based on WIP which may have been created from QTO",
    LOOKUP_EMPTY="lookup-empty",
    BILLS_BASED_ON_WIP_ONLY="Bills based on WIP only",
    BILLS_CREATED_DIRECTLY_FROM_QTO="Bills created directly from QTO",
    BILLS_CREATE_DIRCTLY_FROM_CONTRACT="Bills created directly from Contract",
    SHOW_EQUIPMENT_ASSEMBLY_AS_ONE_RECORD = "Show Equipment Assembly as one record",
    RECEIVED = "Received",
    ARCHIVED = "Archived",
    CANCELED = "Canceled",
    FORM_DATA_STATUS_ROLE="Form Data Status Role",
    PAYMENT_TERM_FI="Payment Term (FI)",
    PAYMENT_TERM_PA="Payment Term (PA)",
    PRODUCTION_SCHEDULE="Production Schedule",
    BANK="Bank",
    NAME_SMALL='name',
    CHANGE_BID_WITH_MULTIPLE_PROJECT_CHANGE="Change Bid with multiple Project Change",
    COST_GROUP_CONFIGURATION="Cost Group Configuration",
    DELAYED="delayed",
    EXECUTION_FINISHED="Execution finished",
    IS_FINISHED_DELAYED="Is finished delayed",
    COMPETITOR="Competitor",
    READY_FOR_SETTLEMENT="Ready for Settlement",
    TEST="TEST",
    ORDED = "orded",
    PROVED = "proved",
    PETITOR="petitor",
    ANCH = "anch",
    SUCCESSFUL="Successful"
  }

  export enum CommonGridLayout {
    AllColumnsToLeft = `.middle-container .ico-ar1-left2`,
    ColumnToRight = `.middle-container .ico-ar1-right1`,
    AllColumnToRight = `.middle-container .ico-ar1-right2`,

  }

  export enum CommonElements {
    // This enum includes inputs,classes,button,id
    POPUP_FOOTER = "[class*='popup-footer']",
    CARET_MODAL = "[class*='btn btn-default dropdown-toggle']",
    ICO_DISCARD = ".ico-discard",
    ACTIVE = "[class*='active']",
    BOQ_FOLDER_ICON = "[class*='ico-folder-doc']",
    BOQ_ITEM_ICON = "[class*='ico-boq-item']",
    SUBCONTAINER_INMODEL = "[ng-controller='boqMainStructureDetailsController'] [class*='subview-container']",
    MENUMAIN = "menu main",
    MENU = "menu",
    LOGINVIEW = "loginView",
    CARET = "[class*='caret']",
    ICON_TICK = "[class*='ico-tick']",
    NO_TICK = "[class*='control-icons ']",
    SUBVIEW = "[class*='subview-content']",
    SEARCH_ICON = "[class*='ico-indicator-search']",
    SEARCH = "[class*='ico-sdb-search2']",
    SEARCH_FORM = "[class*='ico-sdb-search3']",
    ICO_MENU = "[class*='ico-menu']",
    SEARCH_BUTTON = "[class*='search-button']",
    SEARCH1 = "[class*='ico-sdb-search1']",
    ICO_TREE_COLLAPSE = "[class*='ico-tree-collapse']",
    INPUT_TEXT = "input[type=text]",
    CHECK_TICK = "[class='fa fa-check']",
    CHECKBOX_INPUT = "[type='checkbox']",
    DOMAIN_TYPE_COMMENT = '[class*="domain-type-comment"]',
    SPECIFIC_CATALOG_INPUT = '[class*="input-group-content"]',
    CHECKBOX_TYPE = "[type='checkbox']",
    CHARACTERISTIC_LABEL = "[class*='tree-label']",
    MODAL_FLEX_ELEMENT = "[class*='flex-element']",
    RADIO_INPUT = "[type='radio']",
    GROUP_BTN = "[class*='input-group-btn']",
    RADIO_CLASS = "[class*='radio']",
    SEARCH_TERM_INPUT = "input[placeholder='Search Term']",
    GRID_CONFIGURATOR_ID = "[id='gridConfigurator']",
    COLUMN_ID = "column-id_",
    GRID_LAYOUT = "[class*='popup-menu'] button[title='Grid Layout']",
    ICON_SETTING = "button[class$='ico-settings']",
    WIZARD_POPUP_MESSAGE = "[class='message ng-binding']",
    CODE_MIRROR = "[class*=' CodeMirror-line ']",
    CONTRACT_POPUP_MESSAGE = "[class='message selectable ng-binding']",
    CHANGE_STATUS_VALUES = "[class*='change-status-detail-item ng-scope']",
    POP_UP_MODAL = "div.message",
    BRANCH_CONTAINER = "branch-container",
    CONTACT_CONTAINER = "contact-container",
    INVALID_CELL = "invalid-cell",
    REQUIRED_CELL = "required-cell",
    CELL_READONLY = "cell-readonly",
    FULL_WIDTH = " .fullwidth",
    COST = "[title='Cost']",
    DESC = ".column-id_desc",
    STYLE_RED = "color:red;",
    STYLE_GREEN = "color:green;",
    BOQ_OUT_SPEC = "[class*=`briefinfo`]",
    MARKER = "marker",
    IS_MARKED = "IsMarked",
    REVERT_BUTTON = "div[class*=ms-sv-revert-button]",
    LOOKUP_DROPDOWN = "[class*='button bg-color-bar1 control-icons ico-down']",
    SEARCH_RESULT_COLUMN = "[class*='col-md-9 ms-sv-result-panel-right']",
    SEARCH_RESULT_ROW = "[class*='row ms-sv-commodity-result-pane']",
    MASTER_ITEM = "[class='master-item']",
    HEADER_TEXT_CONTAINER = "[class*='ql-container']",
    HEADER_TEXT_DESCRIPTION = "[class*='ql-editor ql-blank']",
    PANEL_GROUP = "[class*='panel-group']",
    PLATFORM_FORM_GROUP_HEADER_TEXT = "[class*='platform-form-group-header-text']",
    ROW = "[class*='platform-form-row']",
    ROW_READONLY = "row-readonly",
    PLATFORM_FORM_LABEL = "[class*='platform-form-label']",
    TITLE = "[class*='item-title']",
    MATERIAL_ROW = "div.flex-box div.ms-commodity-row",
    MATERIAL_TITLE = "span.ms-commodity-row-item-title",
    LINE_TYPE = "[class*='column-id_lineTyp']",
    START_VALUE = "[class*='column-id_val selected']",
    STEP_INC = "[class*='column-id_stepInc selected']",
    PLS_IN_TEXT_AREA = "[class*='k-content-frame']",
    FLEX_BOX = "[class*='flex-box']",
    CART_VIEW_MATERIALS_HEADER = "[id='cart-view-materials-header']",
    PROC_STRUCTURE = "Proc.Structure",
    SIDEBAR = "[class='sidebar ng-scope expanded']",
    RULE = "[class*='rule']",
    TABLE_SELECTION = "[class*='fullheight k-pane k-scrollable']",
    SUB_VIEW = "[class*='subview-content']",
    RIGHT_COLUMN = "[class*='slick-headerrow-columns-right']",
    NAME = "[class*='item-field_name']",
    LEFT_COLUMN = "[class*='slick-headerrow-columns-left']",
    RULE_OPERAND = "[class*='rule-editor-operand-element']",
    RULE_DESCRIPTION = "[class*='domain-type-description']",
    DROPDOWN_MENU = "[class*='dropdown-menu']",
    CARET_SELECTION = "[class*='domain-type-select']",
    PLATFORM_GROUP = "[class*='platform-form-group']",
    TICKET_SYSTEM_ORDER_LIST = "div.ticket-system-order-list-cell",
    TICKET_SYSTEM_ORDER_LIST_HEADER = "div.ticket-system-order-list-header",
    TICKET_SYSTEM_ORDER_ATTRIBUTE_STATUS = "div.ticket-system-order-list-attribute-status",
    TICKET_SYSTEM_ORDER_LIST_BUTTON = "div.ticket-system-order-list-showbtn",
    TICKET_TOTAL = `[class*="ts-cart-item-total-panel"]`,
    TRANSACTION_INPUT_ROW = "[class='row pes-row']",
    PLATFORM_FORM_COL = "[class*='platform-form-col']",
    BUTTON = "[type='button']",
    SELECT_CHECKBOX = "checkbox-radio-box",
    PLATFORM_COL = "platform-form-col",
    VISIBLE = "[class*='visible']",
    VISIBLE_CHECKBOX = "[class*='column-id_Visible selected']",
    GRID_CONTAINER = "[class*='grid-container']",
    BID_BOQ_TAB = "[class='nav nav-tabs'] [heading='BoQ']",
    BID_BASIC_TAB = "[class='nav nav-tabs'] [heading='Basics']",
    POPUP_CONTAINER = ".popup-container",
    ICON = "[class*='block-image']",
    SUB_VIEW_CONTAINER = "[class*='subview-container']",
    RADIO1 = "platform-form-row form-group",
    COLUMN_READONLY = "column-readonly",
    CLASS_SELECTED = "[class*='selected']",
    BODY = "body",
    VIEW_DESCRIPTION = "div.popup-content ul>li>button[data-ng-bind='view.Description']",
    GRID_CANVAS_RIGHT = "[class*='grid-canvas grid-canvas-top grid-canvas-right']",
    UI_WIDGET_CONTENT = "[class*='ui-widget-content']",
    LOOKUP_ITEM = "lookup-item",
    ACTIVE_CHECKBOX = ".active [class*='column-id_selected']",
    ACCORDION_ROOT = "[class*='ico-accordion-root']",
    RADIO_SPACE_TO_UP = "radio spaceToUp",
    LABEL = "label",
    LABEL_FOR_INCLUDE_NON_ACTIVE_ITEMS_ID = "label[for='includeNonActiveItemsId']",
    PLATFORM_FORM_ROW = "platform-form-row",
    ESTIMATE_SELECT_CRITERIA_PLATFORM_FORM_ROW = "estimate-select-criteria platform-form-row",
    DOMAIN_TYPE_CODE = "[class*='domain-type-code']",
    DOMAIN_TYPE_TRANSLATION = "[class*='domain-type-translation']",
    USE_IN_SPLIT = "[class*=`selected item-field_Delete column-id_useinsplit]",
    NAV_TABS = "[class*='nav-tabs']",
    DOMAIN_TYPE_RADIO = "[class*=`domain-type-radio`]",
    MATERIALCATALOG_BUTTON = "[data-options='materialOptionList']",
    TD_HEADER_STYLE = 'tdHeaderStyle',
    GRID_Layout_LEFT_ID = "[id='fffffe94db154e72bed8fb879b08ffff']",
    LABEL_FOR_CUSTOM_OPTION_ID = "label[for='customOptionId']",
    INVALID_CELL_CLASS = "div[class*='invalid-cell']",
    QUILL_EDITOR = "quill-editor",
    QL_EDITOR = "ql-editor",
    SLICK_PANE_HEADER = 'slick-pane-header',
    UI_DROPPABLE = 'ui-droppable',
    MESSAGE_FLEX_ELEMENT_NG_BINDING = "[class='message flex-element ng-binding']",
    TEXT_RIGHT = "text-right",
    ONLY_SHOW_AVAILABLE_STATUS = "Only show available status",
    ID_PAYMENT_SCHEDULE_TOTAL_SETTING_BOX = "#paymentschedule-total-setting-box",
    LABEL_FOR_INCLUDE_CHAINED_ITEMS_ID = "label[for='includeChainedItemsId']",
    SPACE_TO_UP="[class*='spaceToUp']",
    DOMAIN_TYPE_MONEY_INPUT_GROUP_CONTENT="input[class*='input-group-content']",
    PANEL_HEADING="panel-heading",
    FILTER_PANEL="[class*='filterPanel']",
    FILETR_INPUT_FORM_CONTROL="[class*='filterinput form-control']",
    PLATFORM_FORM_GROUP_HEADER="platform-form-group-header",
    ROW_PES_ROW="row pes-row",
    ALERT_SUCCESS =".alert-success",
    MESSAGE=".message",
    PLATFORM_FORM_GROUP="platform-form-group",
    SLICK_CELL='[class*="slick-cell"]',
    MIDDLE_CONTAINER='[class*=middle-container]',
    DOMAIN_TYPE_BOOLEAN='[class*="domain-type-boolean"]',      
    INPUT_GROUP_CONTENT='[class*="input-group-content"]',
    CLASS_PLATFORM_FORM_ROW='[class*="platform-form-row"]',
    TEXTAREA_REMARK=`textarea[class^='domain-type-remark']`,
    BUTTON_TAG = "button",
    TOOLS = "tools",
    SPINNER_LG=".spinner-lg'",
    SELECT_UPDATE_OPTIONS='select[ng-model="UpdateOptions.PrcItemUpdateType"]',
    SELECT_UPDATE_OPTIONS_BOQ='select[ng-model="UpdateOptions.BoqUpdateType"]',
    SLICK_CONTAINER='[class="slick-container"]',
    SLICK_HEADER_COLUMN='[class*="slick-header-column"]',
    CHANGE_STATUS_DETAIL_ITEM="change-status-detail-item",
    NG_SCOPE="ng-scope",
    RW_CONTENT='[class*="rw-content"]',
    TITLE_SMALL='[class*="title"]',
    WORKFLOW_TASKS="workflow-tasks",
    ICO_TASK_NOTIFICATION="ico-task-notification",
    ICO_TASK="ico-task",
    GRID_CANVAS_TOP= "grid-canvas grid-canvas-top grid-canvas-right",
    ICO_SEARCH="[class*='ico-search']",
    SELECT_POPUP="[class*='select-popup']",
    POPUP_CONTAINER_GENERIC_POPUP_FORM_CONTROL="[class*='popup-container'] [class*='generic-popup'] [class='form-control']",
    MESSAGE_FLEX_ELEMENT="[class*='message flex-element']",
    UI_HEADER_CLOCKING = ".popup-content button[class*='ui-header-clocking']",
    E2E_MAX_LENGTH="[class*='e2e-maxlength']",
    E2E_NUMBER_SEQUENCE="[class*='e2e-numbersequencefk']",
    DOMAIN_TYPE_RADIO_CLASS="domain-type-radio",

  }
}

export default CommonLocators;
