import { DataServiceFlatNode, IDataServiceEndPointOptions, IDataServiceOptions, IDataServiceRoleOptions, ServiceRole } from '@libs/platform/data-access';
import {
	IPpsItem2MdcMaterialEntity, IPpsUpstreamItemEntity,
	PpsItem2MdcMaterialComplete,
	PpsUpstreamItemComplete
} from '../../model/models';
import _ from 'lodash';
import { Injectable } from '@angular/core';
import { PpsUpstreamItemDataService } from './pps-upstream-item-data.service';

@Injectable({
	providedIn: 'root'
})
export class PpsUpstream2MaterialService extends DataServiceFlatNode<IPpsItem2MdcMaterialEntity, PpsItem2MdcMaterialComplete, IPpsUpstreamItemEntity, PpsUpstreamItemComplete> {

	public constructor(private parentDataService: PpsUpstreamItemDataService) {
		const options: IDataServiceOptions<IPpsItem2MdcMaterialEntity> = {
			apiUrl: 'productionplanning/item/upstreamitem/material',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
				prepareParam: () => {
					const parentSelection = this.getSelectedParent();
					return {itemFk: parentSelection?.PpsItemFk};
				}
			},
			createInfo: {
				endPoint: 'create',
				prepareParam: () => {
					const parentSelection =  this.getSelectedParent();
					return {
						Id: parentSelection?.PpsItemFk ?? -1
					};
				}
			},
			roleInfo: <IDataServiceRoleOptions<IPpsItem2MdcMaterialEntity>>{
				role: ServiceRole.Node,
				itemName: 'PpsItem2MdcMaterial',
				parent: parentDataService,
			},
		};
		super(options);
		this.processor.addProcessor([this.readonlyProcessor()]);
	}

	public override canCreate(): boolean {
		const selectedParentItem = this.getSelectedParent();
		return selectedParentItem?.PpsUpstreamTypeFk === 1 && !_.isNil(selectedParentItem.PpsItemUpstreamFk);
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override isParentFn(parentKey: IPpsUpstreamItemEntity, entity: IPpsItem2MdcMaterialEntity): boolean {
		return parentKey.PpsItemFk === entity.PpsItemFk && parentKey.Id === this.getSelectedParent()!.Id;
	}

	public override registerNodeModificationsToParentUpdate(complete: PpsUpstreamItemComplete, modified: PpsItem2MdcMaterialComplete[], deleted: IPpsItem2MdcMaterialEntity[]) {
		if (modified && modified.length > 0) {
			complete.PpsItem2MdcMaterialToSave = modified;
		}

		if (deleted && deleted.length > 0) {
			complete.PpsItem2MdcMaterialToDelete = deleted;
		}
	}

	public override createUpdateEntity(modified: IPpsItem2MdcMaterialEntity | null): PpsItem2MdcMaterialComplete {
		const complete = new PpsItem2MdcMaterialComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.PpsItem2MdcMaterial = modified;
		}

		return complete;
	}

	public override getSavedEntitiesFromUpdate(complete: PpsUpstreamItemComplete): IPpsItem2MdcMaterialEntity[]  {
		return (complete && complete.PpsItem2MdcMaterialToSave)
			? complete.PpsItem2MdcMaterialToSave.map(e => e.PpsItem2MdcMaterial)
			: [];
	}

	public override getModificationsFromUpdate(complete: PpsItem2MdcMaterialComplete): IPpsItem2MdcMaterialEntity[] {
		if (complete.PpsItem2MdcMaterial === null) {
			return [];
		}
		return [complete.PpsItem2MdcMaterial];
	}

	private readonlyProcessor() {
		return {
			process: (item: IPpsItem2MdcMaterialEntity) => {
				if (item && item.Version && item.Version > 0) {
					this.setEntityReadOnly(item, true);
				}
			},
			revertProcess() {},
		};
	}

}