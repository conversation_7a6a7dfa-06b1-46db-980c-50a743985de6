/**
 * Created by pel on 1/10/2020.
 */
(function (angular) {
	'use strict';
	/* global , globals,_ */
	const moduleName = 'documents.centralquery';
	/**
	 * @ngdoc service
	 * @name documentCentralQueryDataService
	 * @function
	 * @requireds documentCentralQueryDataService
	 *
	 * @description Provide document central query header data service
	 */

	angular.module(moduleName).factory('documentCentralQueryDataService',
		['$translate', 'platformDataServiceFactory', '$injector', 'platformContextService',
			'$q', 'cloudDesktopPinningContextService', 'documentsProjectDocumentReadonlyProcessor', 'ServiceDataProcessDatesExtension',
			'basicsCommonMandatoryProcessor', 'documentProjectDocumentFilterService', 'moment', 'platformPermissionService',
			'platformModalService', 'basicsLookupdataLookupDescriptorService', 'basicsCommonFileDownloadService', 'PlatformMessenger',
			'basicsLookupdataLookupFilterService', '$http', 'cxService', 'projectDocumentNumberGenerationSettingsService', 'platformRuntimeDataService',
			'basicsCommonServiceUploadExtension', 'documentsProjectFileActionProcessor', 'documentsProjectDocumentModuleContext', 'documentsProjectDocumentDataService',
			'platformDataServiceSelectionExtension', 'platformDataServiceActionExtension', 'cloudDesktopSidebarService', 'documentsProjectFileSizeProcessor', 'platformModuleStateService',
			'basicsLookupdataLookupDataService', 'platformDataValidationService', 'platformDataServiceConfiguredCreateExtension', 'documentsProjectDocumentFileUploadDataService', '$rootScope',
			'cloudDesktopSharePointMainService', 'cloudDesktopSharePointOpenType',
			function ($translate, dataServiceFactory, $injector, platformContextService, $q, cloudDesktopPinningContextService,
			          documentsProjectDocumentReadonlyProcessor, DatesProcessor, mandatoryProcessor, documentProjectDocumentFilterService,
			          moment, platformPermissionService, platformModalService, basicsLookupdataLookupDescriptorService,
			          basicsCommonFileDownloadService, PlatformMessenger, basicsLookupdataLookupFilterService, $http, cxService,
			          projectDocumentNumberGenerationSettingsService, platformRuntimeDataService, basicsCommonServiceUploadExtension, documentsProjectFileActionProcessor,
			          documentsProjectDocumentModuleContext, documentsProjectDocumentDataService, platformDataServiceSelectionExtension,
			          platformDataServiceActionExtension, cloudDesktopSidebarService, documentsProjectFileSizeProcessor, platformModuleStateService,
			          basicsLookupdataLookupDataService, platformDataValidationService, platformDataServiceConfiguredCreateExtension, fileUploadDataService, $rootScope,
			          sharepointMainService, sharepointOpenType) {

				let preservedFields = [
					'Code',
					'CanDeleteStatus',
					'CanWriteStatus',
					'DocumentDate',
					'DocumentType',
					'DocumentTypeFk',
					'HasDocumentRevision',
					'Id',
					'InsertedAt',
					'InsertedBy',
					'IsReadonly',
					'Revision',
					'RubricCategoryFk',
					'PrjDocumentCategoryFk',
					'PrjDocumentTypeFk',
					'PrjDocumentStatusFk',
					'IsLockedType',
					'Version',
				];

				let service = {}, serviceContainer = null;

				// var onReadSucceeded;
				// set filter parameter for this module
				let sidebarSearchOptions = {
					moduleName: moduleName,  // required for filter initialization
					enhancedSearchEnabled: true,
					pattern: '',
					pageSize: 100,
					useCurrentClient: true,
					showOptions: true,
					showProjectContext: false,
					pinningOptions: {
						isActive: true,
						showPinningContext: [{token: 'project.main', show: true}],
						setContextCallback: setCurrentPinningContext
					},
					withExecutionHints: false,
					enhancedSearchVersion: '2.0',
					includeDateSearch: true
				};

				let initialDialogService = $injector.get('documentCentralQueryCreationInitialDialogService');
				let serviceOptions = {
					flatRootItem: {
						module: angular.module(moduleName),
						serviceName: 'documentCentralQueryDataService',
						entityNameTranslationID: 'documents.centralquery.documentscentralquery',
						entityInformation: {
							module: 'Documents.CentralQuery',
							entity: 'Document',
							specialTreatmentService: initialDialogService
						},
						httpCRUD: {
							route: globals.webApiBaseUrl + 'documents/centralquery/',
							endRead: 'listDocuments',
							endDelete: 'deleteComplete',
							usePostForRead: true,
							endCreate: 'createbycontext'
						},
						dataProcessor: [documentsProjectDocumentReadonlyProcessor, documentsProjectFileSizeProcessor,
							documentsProjectFileActionProcessor,
							new DatesProcessor(['DocumentDate', 'ExpirationDate']),
							{
								processItem: processDocument,
								revertProcessItem: revertProcessDocument
							}
						],
						actions: {
							delete: true,
							create: 'flat',
							canCreateCallBackFunc: function () {
								return true;
							},
							canDeleteCallBackFunc: function () {
								let currentItem = service.getSelected();
								return !(currentItem && (currentItem.IsReadonly || (!currentItem.CanDeleteStatus && currentItem.Version !== 0)));

							}
						},
						entityRole: {
							root: {
								rootForModule: moduleName,
								itemName: 'Document',
								moduleName: 'cloud.desktop.moduleDisplayNameDocumentCentral',
								addToLastObject: true,
								lastObjectModuleName: moduleName,
								handleUpdateDone: function (updateData, response) {
									serviceContainer.data.handleOnUpdateSucceeded(updateData, response, serviceContainer.data, true);
									service.onUpdateSucceeded.fire({updateData: updateData, response: response});
								}
							}
						},
						sidebarSearch: {options: sidebarSearchOptions},
						sidebarWatchList: {active: true},
						entitySelection: {supportsMultiSelection: true},
						presenter: {
							list: {
								handleCreateSucceeded: handleCreateSucceeded,
								initCreationData: (creationData) => {
									creationData.referEntity = service.getSelected() ?? null;
									let checkSameContextDialogService = $injector.get('documentProjectDocumentUploadCheckSameContextDialogService');
									let contextDialogDto = {};
									checkSameContextDialogService.getGroupingFilterFieldKey(contextDialogDto);
									if (!_.isEmpty(contextDialogDto)) {
										let ParentEntityInfo = {};
										let ColumnConfig = [];
										for (let dtoItem in contextDialogDto) {
											ParentEntityInfo[checkSameContextDialogService.convertDataField(dtoItem)] = contextDialogDto[dtoItem];
											ColumnConfig.push({
												DocumentField: dtoItem,
												DataField: checkSameContextDialogService.convertDataField(dtoItem),
												ReadOnly: false
											});
										}
										creationData.ParentEntityInfo = ParentEntityInfo;
										creationData.ColumnConfig = ColumnConfig;
									}
								},
							}
						},
						sidebarInquiry: {
							options: {
								active: true,
								moduleName: moduleName
							}

						},
						filterByViewer: true
					}
				};

				serviceContainer = dataServiceFactory.createNewComplete(serviceOptions);
				serviceContainer.data.newEntityValidator = mandatoryProcessor.create({
					typeName: 'DocumentDto',
					moduleSubModule: 'Documents.Project',
					validationService: 'documentProjectHeaderValidationService',
					mustValidateFields: ['DocumentTypeFk']
				});

				documentProjectDocumentFilterService.registerFilters();

				service = serviceContainer.service;

				let uploadOptions = {
					uploadFilesCallBack: uploadFilesCallBack,
					canPreview: canPreview,
					uploadServiceKey: moduleName + '-' + 'documents-project-file-upload',
					uploadConfigs: {action: 'UploadWithCompress', SectionType: 'DocumentsProject'}
				};
				basicsCommonServiceUploadExtension.extendForStandard(serviceContainer, uploadOptions);

				function handleCreateSucceeded(newData) {
					if (_.isEmpty(service.uploadCreateItem)) {
						newData.DocumentDate = moment.utc(Date.now());
						if (platformDataServiceConfiguredCreateExtension.hasToUseConfiguredCreate(serviceContainer.data)) {
							newData.IsFromDialog = true;
							// if a data config is configured, the data returned by the background should clear the fields other than the data config dialog
							if (service.dialogFields.length > 0) {
								preservedFields = [...new Set([...preservedFields, ...service.dialogFields])];
							}
							Object.keys(newData).forEach(key => {
								if (!preservedFields.includes(key)) {
									newData[key] = null;
								}
							});
						}
						projectDocumentNumberGenerationSettingsService.assertLoaded().then(function () {
							platformRuntimeDataService.readonly(newData, [{
								field: 'Code',
								readonly: projectDocumentNumberGenerationSettingsService.hasToGenerateForRubricCategory(newData.RubricCategoryFk)
							}]);
							newData.Code = projectDocumentNumberGenerationSettingsService.provideNumberDefaultText(newData.RubricCategoryFk, newData.Code);
							let currentItem = service.getSelected();
							service.fireItemModified(currentItem);
						});
						if (_.isNil(newData.PrjProjectFk)) {
							let pinProjectId = getPinningProjectId();
							if (pinProjectId !== -1) {
								newData.PrjProjectFk = pinProjectId;
							}
						}
						return newData;
					} else {
						service.uploadCreateItem.forEach(e => {
							serviceContainer.data.itemList.push(e);
						});
						service.markEntitiesAsModified(serviceContainer.data.itemList);
						serviceContainer.data.doUpdate(serviceContainer.data).then((res) => {
							if (res && res.Document) {
								let readData = {
									dtos: res.Document,
									FilterResult: null
								};
								if (!service.updateRows) {
									serviceContainer.data.onReadSucceeded(readData, serviceContainer.data);
								} else {
									const uniqueRows = service.updateRows.filter((item, index, self) =>
										index === self.findIndex((t) => t.Id === item.Id)
									);
									service.setSelectedEntities(uniqueRows);
									service.refreshSelectedEntities();
									service.deselect();
									service.resetUploadData();
								}

							}
						}).finally(() => {
							let parentState = platformModuleStateService.state(service.getModule());
							if (parentState && parentState.modifications) {
								parentState.modifications.EntitiesCount = 0;
							}
							service.isConfigurationDialog = true;
							service.uploadCreateItem = {};
						});
						return [];
					}
				}

				service.handleCreateSucceeded = handleCreateSucceeded;

				service.uploadMsgDialogId = $injector.get('platformCreateUuid')();
				let checkSameContextDialogService = $injector.get('documentProjectDocumentUploadCheckSameContextDialogService');
				serviceContainer.data.onCreateSucceeded = function onCreateSucceeded(newData, data) {
					if (platformDataServiceConfiguredCreateExtension.hasToUseConfiguredCreate(serviceContainer.data)) {
						service.dataConfigData = newData;
						service.IsFromDataConfig = true;
					}
					const deffered = $q.defer();
					if (Reflect.ownKeys(service.uploadedFileData).length === 0) {
						deffered.resolve(createFunction(newData, data));
					} else {
						let contextDialogDto = {};
						let paramData = {};
						let checkSameContextDialogService = $injector.get('documentProjectDocumentUploadCheckSameContextDialogService');
						checkSameContextDialogService.getGroupingFilterFieldKey(contextDialogDto);
						if (!_.isEmpty(contextDialogDto)) {
							let ParentEntityInfo = {};
							let ColumnConfig = [];
							for (let dtoItem in contextDialogDto) {
								ParentEntityInfo[checkSameContextDialogService.convertDataField(dtoItem)] = contextDialogDto[dtoItem];
								ColumnConfig.push({
									DocumentField: dtoItem,
									DataField: checkSameContextDialogService.convertDataField(dtoItem),
									ReadOnly: false
								});
							}
							let pinProjectEntity = _.find(cloudDesktopPinningContextService.getContext(), {token: 'project.main'});
							if (pinProjectEntity) {
								ColumnConfig.push({
									DocumentField: 'ProjectFk',
									DataField: 'ProjectFk',
									ReadOnly: false
								});
							}
							paramData.ParentEntityInfo = ParentEntityInfo;
							paramData.ColumnConfig = ColumnConfig;
						}
						fileUploadDataService.asyncCreateDocumentOrUpdateRevision(service.uploadedFileData, service.extractZipOrNot, newData, paramData.ParentEntityInfo, paramData.ColumnConfig, service.IsFromDataConfig, null, service.dialogFields).then(function (res) {
							if (res?.data?.InvalidFileList?.length > 0) {
								const fileNames = res.data.InvalidFileList.join('<br/>');
								const errorInfo = $translate.instant('documents.project.FileUpload.validation.FileExtensionsErrorInfo');
								const errMsg = `<div style="height:300px"><br/>${errorInfo}:<br/>${fileNames} </div>`;
								platformModalService.showMsgBox(errMsg,'documents.project.FileUpload.validation.FileExtensionErrorMsgBoxTitle', 'warning');
							}
							if (!!res && !!res.data && !!res.data.dtos) {
								const resData = res.data.dtos;

								const docItemList = service.getList();
								let refreshItem = [];
								_.forEach(resData, function (docDto) {
									const docItem = docItemList.find(e => e.Id === docDto.Id);
									if (!docItem) {
										serviceContainer.data.itemList.push(docDto);
										serviceContainer.data.selectedItem = docDto;
									}
									refreshItem.push(docDto);
								});

								service.setSelectedEntities(refreshItem);
								service.refreshSelectedEntities().then(function () {
									service.setSelected(resData[0]);
									service.gridRefresh();
								});
								service.resetUploadData();
								service.filesClear.fire();
							}
						}, function () {
							service.filesClear.fire();
						});
					}

					return deffered.promise;
				};

				function createFunction(newData, data) {
					let newItem;
					if (checkSameContextDialogService.hasGroupingFilterFieldKey) {
						checkSameContextDialogService.hasGroupingFilterFieldKey = false;
					}
					if (service.handleCreateSucceeded) {
						newItem = service.handleCreateSucceeded(newData, data);// In case more data is send back from server it can be stripped down to the new item here.
						if (!newItem) {// Fall back, if no value is returned by handleCreateSucceeded
							newItem = newData;
						}
					} else {
						newItem = newData;
					}
					if (data.addEntityToCache) {
						data.addEntityToCache(newItem, data);
					}
					if (service.isConfigurationDialog === true) {
						service.isConfigurationDialog = false;
					} else {
						return data.handleOnCreateSucceeded(newItem, data);
					}
					return {};
				}

				const originalOnDeleteDone = serviceContainer.data.onDeleteDone;
				serviceContainer.data.onDeleteDone = function (deleteParams) {
					if (deleteParams.entities && deleteParams.entities.length > 0) {
						const selectionModelService = $injector.get('modelWdeViewerSelectionService');
						selectionModelService.deleteModels = selectionModelService.deleteModels ?? [];
						_.forEach(deleteParams.entities, function (entity) {
							selectionModelService.deleteModels.push(entity.PreviewModelFk);
						});
					}
					return originalOnDeleteDone.apply(this, arguments);
				};

				serviceContainer.data.updateOnSelectionChanging = function updateOnSelectionChanging(data) {
					if (data.doUpdate) {
						return data.doUpdate(data);
					}
					return $q.when(true);
				};
				service.canMultipleUploadFiles = canUploadFiles;
				service.uploadFiles = upload;
				// service.showUploadFilesProgress = showUploadFilesProgress;
				service.uploadSingleFile = upload;
				service.canUploadFileForSelectedPrjDocument = canUploadFileForSelectedPrjDocument;
				service.canUploadFiles = canUploadFileForSelectedPrjDocument;
				service.canDownloadFiles = canDownloadFiles;
				service.downloadSpFiles = downloadSpFiles;
				service.downloadFiles = downloadFiles;
				service.canPreviewDocument = canPreview;
				service.getPreviewConfig = getPreviewConfig;
				service.lockOrUnlockUploadBtnAndGrid = new PlatformMessenger();
				service.enableContextConfig = true;
				service.contextConfig = contextConfig;
				service.createDocuments = createDocuments;
				service.dialogFields = [];// data config fields list

				service.updatePreviewDocument = new PlatformMessenger();
				service.onUpdateSucceeded = new PlatformMessenger();
				service.onUpdateDocCreateHistory = new PlatformMessenger();
				service.onPreviewDocCreateHistory = new PlatformMessenger();
				service.onDownloadDocCreateHistory = new PlatformMessenger();
				service.filesHaveBeenUploaded = new PlatformMessenger();
				service.filesClear = new PlatformMessenger();

				service.needToLockOrUnlockDocumentRevisionGrid = function (oldReferencedDocumentId, newReferencedDocumentId) {
					if (((oldReferencedDocumentId === 0 || oldReferencedDocumentId === null) && (newReferencedDocumentId !== 0 && newReferencedDocumentId !== null)) ||
						((oldReferencedDocumentId !== 0 && oldReferencedDocumentId !== null) && (newReferencedDocumentId === 0 || newReferencedDocumentId === null))) {
						if ((oldReferencedDocumentId === 0 || oldReferencedDocumentId === null) && (newReferencedDocumentId !== 0 && newReferencedDocumentId !== null)) {
							return {needToLockOrUnlock: true, lockOrUnlock: 'lock'};
						} else {
							return {needToLockOrUnlock: true, lockOrUnlock: 'unlock'};
						}
					} else {
						return {needToLockOrUnlock: false};
					}
				};
				service.isReadOnly = function isReadOnly() {
					let currentItem = service.getSelected();
					if (currentItem === null || currentItem === undefined) {
						return true;
					}
					return currentItem.IsReadonly;
				};

				service.getCellEditable = function (item, model) {
					let editable = true;

					if (model === 'DocumentTypeFk') {
						editable = !item.FileArchiveDocFk;
					}

					return editable;
				};

				service.getSelectedProjectId = function () {
					let prjId = -1;
					let documentProject = service.getSelected();
					let project = cloudDesktopPinningContextService.getPinningItem('project.main');
					if (null !== project) {
						prjId = project.id;
					}
					if (documentProject && !_.isNull(documentProject.PrjProjectFk)) {
						prjId = documentProject.PrjProjectFk;
					}
					return prjId;
				};

				service.takeOver = function takeOver(entity) {
					let data = serviceContainer.data;
					let dataEntity = data.getItemById(entity.Id, data);
					if (!_.isNil(dataEntity)) {
						entity.CanDeleteStatus = dataEntity.CanDeleteStatus;
						entity.CanWriteStatus = dataEntity.CanWriteStatus;
						data.mergeItemAfterSuccessfullUpdate(dataEntity, entity, true, data);
						data.markItemAsModified(dataEntity, data);
					}
				};
				service.updateReadOnly = function updateReadOnly(entity, readOnlyField, value, editField) {
					if (!entity) {
						return;
					}
					if (editField) {
						entity[editField] = value;
					}
					let readOnly = !service.getCellEditable(entity, readOnlyField);
					platformRuntimeDataService.readonly(entity, [{field: readOnlyField, readonly: readOnly}]);
				};
				service.showInfoDialog = function showInfoDialog(infoData) {
					let modalOptions = {
						templateUrl: globals.appBaseUrl + 'procurement.common/partials/create-prcdocument-existinfo-dialog.html',
						backDrop: false,
						windowClass: 'form-modal-dialog',
						resizeable: true,
						headerTextKey: $translate.instant('basics.common.taskBar.info'),
						infoList: infoData,
						showYesButton: true,
						showNoButton: true
					};
					return platformModalService.showDialog(modalOptions);
				};

				service.getDocumentDataService = function () {
					return documentsProjectDocumentDataService.getService({
						moduleName: documentsProjectDocumentModuleContext.getConfig().moduleName
					});
				};

				let createItem = angular.copy(service.createItem);
				service.createItem = function () {
					if (createItem) {
						createItem(null, serviceContainer.data);
					}
				};


				service.uploadCreateItem = {};
				service.uploadedFileData = {};
				service.extractZipOrNot = false;
				service.isConfigurationDialog = false;
				service.dataConfigData = {};
				service.UploadedFileDataList = [];
				service.updateRows = []; // this is Affected array
				service.updateVerItems = []; // Update the version array
				service.IsFromDataConfig = false;
				service.clearFileInfoArray = function () {
					$rootScope.$broadcast('clearFileInfoArray');
				};

				function canUploadFiles() {
					return platformPermissionService.hasCreate('4eaa47c530984b87853c6f2e4e4fc67e');
				}

				function uploadFilesCallBack(currItem, data) {
					if (currItem === null || angular.isUndefined(currItem.Id)) {
						let args = {
							currItem: currItem,
							data: data
						};
						service.isSingle = false;
						service.filesHaveBeenUploaded.fire(null, args);
					} else {
						let arg = {
							currItem: currItem,
							data: data
						};
						service.dataList = serviceContainer.data.itemList;
						service.isSingle = true;
						service.filesHaveBeenUploaded.fire(null, arg);
					}
				}

				function upload(option) {
					let fileUploadDataService = $injector.get('documentsProjectDocumentFileUploadDataService');
					fileUploadDataService.gridFlag = '4EAA47C530984B87853C6F2E4E4FC67E';
					let uploadService = service.getUploadService();
					let currItem = service.getSelected();
					const documentTypeItems = basicsLookupdataLookupDescriptorService.getData('DocumentType');
					let value = service.getExtension(documentTypeItems, currItem.DocumentTypeFk);
					uploadService.uploadFiles(currItem, value, option);
					// uploadService.uploadFiles(currItem, service.getExtension(documentTypeItems, currItem.DocumentTypeFk));

				}


				/* function uploadFiles(scope) {
					upload(scope);
					function upload(scope) {
						var fileUploadDataService = $injector.get('documentsProjectDocumentFileUploadDataService');
						documentsProjectDocumentFileUploadDataService.gridFlag = '4EAA47C530984B87853C6F2E4E4FC67E';
						platformModalService.showDialog({
							templateUrl: globals.appBaseUrl + documentsProjectName + '/partials/file-handler-lookup.html',
							scope: scope,
							backdrop: false
						});

						var uploadService = fileUploadDataService.getUploadService();
						var basDocumentTypeArray=fileUploadDataService.getBasDocumentTypeArray();
						var allSupportedFileTypeIds=_.map(basDocumentTypeArray,function(item){
							return item.Id;
						});
						var allSupportedFileExtensions=_.map(allSupportedFileTypeIds,function(item){
							return uploadService.getExtension(basDocumentTypeArray, item);
						});
						allSupportedFileExtensions=_.filter(allSupportedFileExtensions,function(item){
							return !!item;
						});
						var fileExtensionArray=[];
						var gridFlag=fileUploadDataService.gridFlag;
						if(gridFlag === '4EAA47C530984B87853C6F2E4E4FC67E'){
							fileExtensionArray=allSupportedFileExtensions;
						}else{
							var selectedDocumentFileTypeId=fileUploadDataService.getDocumentFileType();
							var selectedDocumentFileExtension=uploadService.getExtension(basDocumentTypeArray, selectedDocumentFileTypeId);
							if(selectedDocumentFileExtension){
								fileExtensionArray.push(selectedDocumentFileExtension);
							}
						}
						fileExtensionArray=_.map(fileExtensionArray,function(item){
							return item.replace(/[*.\s]/g,'');
						});
						var finalFileExtensionArray=[];
						for(var i=0; i<fileExtensionArray.length; i++){
							if(fileExtensionArray[i].indexOf(';')!==-1){
								finalFileExtensionArray = finalFileExtensionArray.concat(fileExtensionArray[i].split(';'));
							}else if(fileExtensionArray[i].indexOf(',')!==-1){
								finalFileExtensionArray = finalFileExtensionArray.concat(fileExtensionArray[i].split(','));
							}else{
								finalFileExtensionArray.push(fileExtensionArray[i]);
							}
						}
						fileUploadDataService.getSupportedMimeTypeMapping().then(function(res){
							var supportedMimeTypeMapping=res;
							var supportedMimeTypesForAcceptAttr=_.map(finalFileExtensionArray,function(fileExtension){
								var attrValue =supportedMimeTypeMapping[fileExtension];
								if (attrValue) {
									return attrValue;
								}else{
									return null;
								}
							});
							supportedMimeTypesForAcceptAttr=_.filter(supportedMimeTypesForAcceptAttr,function(item){
								return item!==null;
							});
							if(!!supportedMimeTypesForAcceptAttr&&!!supportedMimeTypesForAcceptAttr.length&&supportedMimeTypesForAcceptAttr.length>0){
								var supportedMimeTypesForAcceptAttrString=supportedMimeTypesForAcceptAttr.join(',');
								var fileOption = {multiple: true, autoUpload: false,accept:supportedMimeTypesForAcceptAttrString};
								uploadService.selectFiles(fileOption);
							}
						});

					}
				} */

				/* function showUploadFilesProgress(scope) {
					platformModalService.showDialog({
						templateUrl: globals.appBaseUrl + documentsProjectName + '/partials/files-upload-progress.html',
						scope: scope,
						backdrop: false
					});
				} */


				function canUploadFileForSelectedPrjDocument() {

					let documentstatuss = basicsLookupdataLookupDescriptorService.getData('documentstatus');

					let currentItem = service.getSelected();
					if (_.isObject(currentItem)) {
						if (!currentItem.CanWriteStatus) {
							return false;
						}
						let currentStatus = _.find(documentstatuss, {Id: currentItem.PrjDocumentStatusFk});
						if (currentStatus !== undefined) {
							return !!currentItem && !currentStatus.IsReadonly && platformPermissionService.hasCreate('4eaa47c530984b87853c6f2e4e4fc67e');
						} else {
							return !!currentItem && platformPermissionService.hasCreate('4eaa47c530984b87853c6f2e4e4fc67e');
						}
					} else {
						return !!currentItem && platformPermissionService.hasCreate('4eaa47c530984b87853c6f2e4e4fc67e');
					}
				}

				function canDownloadFiles() {
					let selectedEntities = service.getSelectedEntities();
					if (selectedEntities.length >= 2) {
						return _.filter(selectedEntities, function (item) {
							return item.FileArchiveDocFk !== null;
						}).length > 0;
					}
					let currentItem = service.getSelected();
					if (currentItem) {
						return ($injector.get('basicsCommonDrawingPreviewDataService').checkDocumentCanPreview(service, currentItem, true) && 1000 !== currentItem.DocumentTypeFk) && platformPermissionService.hasRead('4eaa47c530984b87853c6f2e4e4fc67e');
					}
					return false;
				}

				function downloadSpFiles(selectItems) {
					let defer = $q.defer();
					sharepointMainService.msalClient.acquireTokenAsync().then(function (r) {
						if (!r) {
							return r;
						}
						sharepointMainService.readProfile().then(function (response) {
							const data = _.isArray(selectItems) ? selectItems : [selectItems];

							let request = {
								Dtos: data,
								AadUserId: response.id,
								AccessToken: r
							};

							$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/downloadspfiles', request)
								.then(function (response) {
									defer.resolve(response.data);
								});
						});
					});

					return defer.promise;
				}

				function downloadFiles() {
					const entities = service.getSelectedEntities();
					basicsCommonFileDownloadService.canDownload(service, entities);
				}

				function canPreview() {
					let currentItem = service.getSelected();
					if (currentItem) {
						return ($injector.get('basicsCommonDrawingPreviewDataService').checkDocumentCanPreview(service, currentItem) || 1000 === currentItem.DocumentTypeFk) && platformPermissionService.hasRead('4eaa47c530984b87853c6f2e4e4fc67e');
					}
					return false;
				}

				function getPreviewConfig(defaultEntity) {
					// Fixed #101548,defaultEntity, when select parent entity, it will load the documents and use the first one to preview
					let deffered = $q.defer();
					let currentItem = service.getSelected();
					if (defaultEntity !== undefined) {
						currentItem = defaultEntity;
					}
					let fileArchiveDocId = currentItem.FileArchiveDocFk;

					if (currentItem.Url) {
						if (currentItem.Url.indexOf('itwocx') > -1) {
							cxService.LoginCx().then(function (backdata) {
								let key = backdata.key;
								let url = currentItem.Url + '?k=' + key;
								deffered.resolve({
									Url: url,
									title: ''
								});
							});
						} else {
							deffered.resolve({
								Url: currentItem.Url,
								title: ''
							});
						}
					} else {
						if (fileArchiveDocId) {
							$http.get(globals.webApiBaseUrl + 'basics/common/document/preview?fileArchiveDocId=' + fileArchiveDocId).then(function (result) {
								deffered.resolve({
									src: result.data,
									documentType: currentItem.DocumentTypeFk,
									title: currentItem.OriginFileName
								});
							});
						}
					}
					return deffered.promise;
				}

				function setCurrentPinningContext(dataService) {
					function setCurrentProjectToPinnningContext(dataService) {
						let currentItem = dataService.getSelected();
						if (currentItem) {
							let projectPromise = $q.when(true);
							let pinningContext = [];
							if (angular.isNumber(currentItem.Id)) {
								if (angular.isNumber(currentItem.PrjProjectFk)) {
									projectPromise = cloudDesktopPinningContextService.getProjectContextItem(currentItem.PrjProjectFk).then(function (pinningItem) {
										pinningContext.push(pinningItem);
									});
								}
							}

							return $q.all([projectPromise]).then(
								function () {
									if (pinningContext.length > 0) {
										cloudDesktopPinningContextService.setContext(pinningContext);
									}
								});
						}
					}

					setCurrentProjectToPinnningContext(dataService);
				}

				function revertProcessDocument(item) {
					if (item.Version === 0 && projectDocumentNumberGenerationSettingsService.hasToGenerateForRubricCategory(item.RubricCategoryFk)) {
						item.Code = '';
					}
				}

				function processDocument(item) {
					let fields = [];
					if (item.Version === 0 && projectDocumentNumberGenerationSettingsService.hasToGenerateForRubricCategory(item.RubricCategoryFk)) {
						fields.push({field: 'Code', readonly: true});
					}
					if (fields.length > 0) {
						platformRuntimeDataService.readonly(item, fields);
					}
					// readOnly when the ribArchive file send/callback to itwoSite
					platformRuntimeDataService.readonly(item, [
						{field: 'PrjDocumentCategoryFk', readonly: item.IsLockedType === true},
						{field: 'PrjDocumentTypeFk', readonly: item.IsLockedType === true}]);
				}

				function getPinningProjectId() {
					let context = cloudDesktopPinningContextService.getContext();
					if (context) {
						for (let i = 0; i < context.length; i++) {
							if (context[i].token === 'project.main') {
								return context[i].id;
							}
						}
					}
					return -1;
				}

				function contextConfig() {
					let modalOptions = {
						templateUrl: globals.appBaseUrl + 'documents.centralquery/templates/context-config/documents-centralquery-context-config.html',
						backDrop: false,
						windowClass: 'form-modal-dialog',
						resizeable: false,
						headerTextKey: $translate.instant('basics.common.taskBar.info'),
						showYesButton: true,
						showNoButton: true,
						currentItem: service.getSelected() || null,
						id: '363B846A8B3E4375821EF9D2323D062C'
					};
					return platformModalService.showDialog(modalOptions);
				}

				service.canContextConfig = () => {
					return _.isNil(this.canContextConfig);
				};

				service.getServiceContainer = function () {
					return serviceContainer;
				};

				function createDocuments(docParams) {
					return $http.post(globals.webApiBaseUrl + 'documents/projectdocument/createDocument', docParams);
				}

				// begin sharepoint
				service.openShareViaSharePointDialog = async function () {
					let selections = _.filter(service.getSelectedEntities(), function (item) {
						return !_.isNil(item.PrjProjectFk);
					});

					if (selections.length === 0) {
						return;
					}
					let basDocumentTypes = await basicsLookupdataLookupDataService.getList('DocumentType');
					basicsLookupdataLookupDataService.getList('project').then(function (response) {
						let prjMap = new Map();
						_.forEach(selections, function (item) {
							const prjId = item.PrjProjectFk;
							let prj = prjMap.get(prjId);
							if (!prj) {
								prj = _.filter(response, {Id: item.PrjProjectFk})[0];
								prjMap.set(prjId, prj);
							}
							item.ProjectName = prj.ProjectName;
							item.ProjectNo = prj.ProjectNo;

							let basDocType = _.filter(basDocumentTypes, {Id: item.DocumentTypeFk})[0];
							item.FileType = basDocType.Description;
						});

						const dialogConfig = {
							templateUrl: globals.appBaseUrl + 'documents.centralquery/templates/sharepoint/share-via-sharepoint-dialog.html',
							backdrop: false,
							height: '680px',
							width: '1050px',
							resizeable: true,
							selections: selections
						};

						platformModalService.showDialog(dialogConfig);
					});
				};

				service.getFileSharePointUrl = function () {
					return this.getSharePointUrl(sharepointOpenType.file);
				};

				service.getFolderSharePointUrl = function () {
					return this.getSharePointUrl(sharepointOpenType.folder);
				};

				service.getSharePointUrl = function (openType) {
					const defer = $q.defer();
					const selected = service.getSelected();

					sharepointMainService.msalClient.acquireTokenAsync().then(function (r) {
						if (!r) {
							return r;
						}
						sharepointMainService.readProfile().then(function (response) {
							const data = {
								PrjDocId: selected.Id,
								UserExtId: response.id,
								ItemType: openType,
								AccessToken: r,
								AadUserId: response.id
							};
							$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/getsharelink', data)
								.then(function (response) {
									defer.resolve(response.data);
								});
						});
					});

					return defer.promise;
				};

				service.getSharePointMode = function () {
					return sharepointMainService.getIsShowSharePoint();
				};
				// end sharepoint

				service.resetUploadData = function resetUploadData() {
					service.uploadCreateItem = {};
					service.uploadedFileData = {};
					service.dataConfigData = {};
					service.UploadedFileDataList = [];
					service.updateRows = [];
					service.updateVerItems = [];
					service.IsFromDataConfig = false;
				};

				return service;
			}]);
})(angular);


