﻿DECLARE @verexists BIT = 0
	,@ver INT = 251999
	,@verstr NVARCHAR(10) = '25.2-0';

SELECT @verexists = dbo.FRM_CHECKVERSION_F(@ver, 'DATABASE.TRANSLATIONANDDD')

IF @verexists = 1
BEGIN
	PRINT 'Version ' + convert(NVARCHAR, @ver) + ' Module: ' + @verstr + ' already executed!'

	RETURN
END

SET XACT_ABORT ON
SET LANGUAGE english;

BEGIN TRANSACTION

	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 285 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 289 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 291 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 293 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 298 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 299 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 300 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 302 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 303 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 306 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 308 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 310 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 312 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 313 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 316 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 320 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 322 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 324 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 326 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 328 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 330 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 331 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 346 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 350 AND DESCRIPTION_TR = 638;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 225 AND DESCRIPTION_TR = 640;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 253 AND DESCRIPTION_TR = 640;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 90 AND DESCRIPTION_TR = 642;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 207 AND DESCRIPTION_TR = 642;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 214 AND DESCRIPTION_TR = 642;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 262 AND DESCRIPTION_TR = 643;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 267 AND DESCRIPTION_TR = 643;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 359 AND DESCRIPTION_TR = 644;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 363 AND DESCRIPTION_TR = 644;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 367 AND DESCRIPTION_TR = 644;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 195 AND DESCRIPTION_TR = 1363;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 213 AND DESCRIPTION_TR = 1366;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 335 AND DESCRIPTION_TR = 1370;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 341 AND DESCRIPTION_TR = 1370;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 89 AND DESCRIPTION_TR = 4410;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 217 AND DESCRIPTION_TR = 4410;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 255 AND DESCRIPTION_TR = 4411;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 241 AND DESCRIPTION_TR = 4413;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 245 AND DESCRIPTION_TR = 4413;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 249 AND DESCRIPTION_TR = 4413;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 334 AND DESCRIPTION_TR = 4416;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 340 AND DESCRIPTION_TR = 4416;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 333 AND DESCRIPTION_TR = 5335;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 339 AND DESCRIPTION_TR = 5335;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 295 AND DESCRIPTION_TR = 5341;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 338 AND DESCRIPTION_TR = 5341;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 344 AND DESCRIPTION_TR = 5341;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 358 AND DESCRIPTION_TR = 5342;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 362 AND DESCRIPTION_TR = 5342;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 366 AND DESCRIPTION_TR = 5342;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 278 AND DESCRIPTION_TR = 5344;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 287 AND DESCRIPTION_TR = 5344;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 301 AND DESCRIPTION_TR = 5344;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 348 AND DESCRIPTION_TR = 5344;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 290 AND DESCRIPTION_TR = 5346;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 296 AND DESCRIPTION_TR = 5348;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 336 AND DESCRIPTION_TR = 5348;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 342 AND DESCRIPTION_TR = 5348;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 352 AND DESCRIPTION_TR = 5360;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 356 AND DESCRIPTION_TR = 5360;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 360 AND DESCRIPTION_TR = 5360;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 364 AND DESCRIPTION_TR = 5360;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 309 AND DESCRIPTION_TR = 5362;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 317 AND DESCRIPTION_TR = 5362;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 319 AND DESCRIPTION_TR = 5362;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 318 AND DESCRIPTION_TR = 5364;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 321 AND DESCRIPTION_TR = 5364;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 280 AND DESCRIPTION_TR = 5427;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 337 AND DESCRIPTION_TR = 5427;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 343 AND DESCRIPTION_TR = 5427;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 349 AND DESCRIPTION_TR = 5427;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 208 AND DESCRIPTION_TR = 9487;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 215 AND DESCRIPTION_TR = 9489;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 226 AND DESCRIPTION_TR = 9502;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 254 AND DESCRIPTION_TR = 9502;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 231 AND DESCRIPTION_TR = 9503;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 257 AND DESCRIPTION_TR = 9503;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 229 AND DESCRIPTION_TR = 9504;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 230 AND DESCRIPTION_TR = 9505;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 256 AND DESCRIPTION_TR = 9505;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 244 AND DESCRIPTION_TR = 9509;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 248 AND DESCRIPTION_TR = 9509;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 252 AND DESCRIPTION_TR = 9509;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 243 AND DESCRIPTION_TR = 9510;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 251 AND DESCRIPTION_TR = 9511;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 266 AND DESCRIPTION_TR = 9512;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 269 AND DESCRIPTION_TR = 9512;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 357 AND DESCRIPTION_TR = 12378;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 361 AND DESCRIPTION_TR = 12378;
	UPDATE EST_RESOURCE SET DESCRIPTION_TR = NULL WHERE ID = 365 AND DESCRIPTION_TR = 12378;

COMMIT TRANSACTION
		-- last if everything is ok, we create the version entry in database.
EXEC FRM_ADDDBVERSIONENTRY_SP @version = @ver
			,@versionstr = @verStr
			,@module = 'DATABASE.TRANSLATIONANDDD'
