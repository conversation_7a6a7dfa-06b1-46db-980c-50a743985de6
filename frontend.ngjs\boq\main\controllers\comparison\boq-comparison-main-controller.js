(function (angular) {
	'use strict';

	angular.module('boq.main').controller('boqComparisonMainController', [
		'globals',
		'$scope',
		'$http',
		'$translate',
		'platformGridAPI',
		'platformObjectHelper',
		'platformModalService',
		'platformTranslateService',
		'reportingPrintService',
		'boqMainStandardConfigurationServiceFactory',
		'boqMainService',
		'resolveOptions',
		'resolveContext',
		'platformModuleNavigationService',
		function (
			globals,
			$scope,
			$http,
			$translate,
			platformGridAPI,
			platformObjectHelper,
			platformModalService,
			platformTranslateService,
			reportingPrintService,
			boqMainStandardConfigurationServiceFactory,
			boqMainService,
			resolveOptions,
			resolveContext,
			navigationService
		) {
			const cssClassHighlight = 'difference';
			const diffCells = [];
			const ignoreColumns = ['insertedat', 'insertedby', 'updatedat', 'updatedby'];
			const standardService = boqMainStandardConfigurationServiceFactory.createBoqMainStandardConfigurationService({currentBoqMainService: boqMainService});
			const baseColumns = standardService.getStandardConfigForListView().columns.filter(col => !ignoreColumns.includes(col.id));
			const additionalColumns = [{
				id: 'project',
				field: 'ContextProjectId',
				name: $translate.instant('boq.main.comparison.project'),
				width: 120,
				formatter: 'lookup',
				readonly: true,
				formatterOptions: {
					lookupType: 'project',
					displayMember: 'ProjectNo'
				}
			}].concat(resolveOptions.additionalColumns);
			const baseData = {
				References: [],
				CompareData: {}
			};
			const suffixNames = {
				baseBoq: 'baseBoq',
				compBoq: 'compBoq'
			};
			const suffixes = [{
				name: suffixNames.baseBoq,
				text: $translate.instant('boq.main.comparison.baseBoQ')
			}, {
				name: suffixNames.compBoq,
				text: $translate.instant('boq.main.comparison.compBoQ')
			}];

			$scope.loading = false;
			$scope.showDiffRowsOnly = true;
			$scope.entity = {
				BoqHeaderFk: resolveOptions.rootBoqItem.BoqHeaderFk,
				TargetProjectFk: null,
				TargetBoqHeaderFk: null,
				TargetBoq: null
			};

			$scope.modalOptions = {
				headerText: $translate.instant('boq.main.comparison.title'),
				close: function () {
					$scope.$close();
				},
				ok: function () {
					this.close();
				},
				cancel: function () {
					this.close();
				}
			};

			const buildField = function (field, suffix) {
				return field + '_' + suffix;
			};

			const sortColumns = function (columns, customColumns) {
				return customColumns ? columns.sort((a, b) => {
					return customColumns.findIndex(e => e.Id === a.id) - customColumns.findIndex(e => e.Id === b.id);
				}) : columns;
			};

			const buildColumns = function () {
				const customColumns = resolveOptions.settings?.Columns;
				const filterColumns = customColumns ? baseColumns.filter(e => customColumns.some(x => e.id === x.Id && x.Visible)) : baseColumns;
				const sortedColumns = sortColumns(filterColumns, customColumns);
				return [...additionalColumns, ...sortedColumns].reduce((result, col) => {
					const target = customColumns ? customColumns.find(e => e.Id === col.id) : null;
					suffixes.forEach((suffix) => {
						result.push({
							...col,
							id: buildField(col.id, suffix.name),
							name: (col.name$tr$ ? $translate.instant(col.name$tr$, col.name$tr$param$) : col.name) + '[' + suffix.text + ']',
							width: target && target.Width ? target.Width : 150,
							headerCssClass: target && target.Compare === true ? 'font-bold' : ''
						});
					});
					return result;
				}, []);
			};

			const equals = function (baseBoq, compBoq, col) {
				const baseValue = platformObjectHelper.getValue(baseBoq, col.field);
				const compValue = platformObjectHelper.getValue(compBoq, col.field);
				const formatter = col.domain || col.formatter;

				switch (formatter) {
					case 'translation':
						return platformObjectHelper.getValue(baseValue, 'Translated') === platformObjectHelper.getValue(compValue, 'Translated');
					default:
						return baseValue === compValue;
				}
			};

			const compare = function (baseBoq, compBoq, compareColumns) {
				return (!baseBoq || !compBoq) || !compareColumns.length || !compareColumns.every(col => {
					return equals(baseBoq, compBoq, col);
				});
			};

			const buildData = function () {
				const compareColumns = resolveOptions.settings?.Columns ? resolveOptions.settings.Columns.filter(e => e.Compare).map(e => {
					return baseColumns.find(col => col.id === e.Id);
				}) : [];

				return baseData.References.map((e, i) => {
					const compareData = baseData.CompareData[e];
					const baseTarget = compareData.find(e => e.BoqHeaderFk === $scope.entity.BoqHeaderFk);
					const compTarget = compareData.find(e => e.BoqHeaderFk === $scope.entity.TargetBoqHeaderFk);
					const baseContext = additionalColumns.reduce((result, item) => {
						return Object.defineProperty(result, item.field, {
							value: resolveContext.baseBoq[item.field],
							enumerable: true
						});
					}, {});
					return {
						Id: i,
						Reference: e,
						cssClass: baseTarget && compTarget ? '' : cssClassHighlight,
						baseBoq: baseTarget ? Object.assign(baseContext, baseTarget) : null,
						compBoq: compTarget ? Object.assign({ContextProjectId: resolveContext.compBoq.ContextProjectId}, compTarget) : null
					};
				}).filter(e => {
					return !$scope.showDiffRowsOnly || compare(e.baseBoq, e.compBoq, compareColumns);
				});
			};

			const initializeGrid = function () {
				const gridConfig = {
					columns: buildColumns(),
					data: [],
					id: '37c7aa4e91924e179f1b1946621b5f9d',
					lazyInit: true,
					isStaticGrid: true,
					options: {
						indicator: true,
						editable: false,
						idProperty: 'Id',
						skipPermissionCheck: true,
						enableConfigSave: false
					}
				};
				platformGridAPI.grids.config(gridConfig);

				$scope.gridData = {
					state: gridConfig.id
				};

				$scope.tools = {
					showImages: true,
					showTitles: true,
					cssClass: 'tools',
					items: [{
						id: 'filter_diff',
						caption: 'boq.main.comparison.showDiffRowsOnly',
						type: 'check',
						value: $scope.showDiffRowsOnly,
						iconClass: 'tlb-icons ico-filter',
						fn: function () {
							$scope.showDiffRowsOnly = !$scope.showDiffRowsOnly;
							refreshGrid(false);
						}
					}, {
						id: 'd1',
						sort: 3,
						type: 'divider'
					}, {
						id: 'print',
						sort: 1,
						caption: 'cloud.common.print',
						iconClass: 'tlb-icons ico-print-preview',
						type: 'item',
						fn: function () {
							reportingPrintService.printGrid(gridConfig.id);
						}
					}, {
						id: 'open_boq',
						sort: 2,
						caption: 'boq.main.openBoq',
						iconClass: 'tlb-icons ico-goto',
						type: 'item',
						fn: function () {
							const navigator = {moduleName: 'boq.main', forceNewTab: true};
							navigationService.navigate(navigator, $scope.entity.TargetBoq, 'BoqItemNavigator');
						},
						disabled: function () {
							return !$scope.entity.TargetBoq;
						}
					}, {
						id: 'd1',
						sort: 3,
						type: 'divider'
					}, {
						id: 'settings',
						sort: 4,
						caption: 'cloud.common.gridSettings',
						iconClass: 'tlb-icons ico-settings',
						type: 'item',
						fn: function () {
							showSettingsDialog();
						}
					}]
				};
			};

			const highlightDiffCells = function () {
				const cntCtrl = angular.element('div.boq-comparison');
				diffCells.forEach(uniqueId => {
					cntCtrl.find('#' + uniqueId).addClass(cssClassHighlight);
				});

				diffCells.length = 0;
			};

			const refreshGrid = function (rebuildColumns) {
				const visibleColumns = !rebuildColumns ? platformGridAPI.columns.getColumns($scope.gridData.state) : [];

				if (rebuildColumns) {
					platformGridAPI.columns.configuration($scope.gridData.state, buildColumns());
					visibleColumns.push(...platformGridAPI.columns.getColumns($scope.gridData.state));

					visibleColumns.forEach(col => {
						if (col.id === 'indicator') {
							return;
						}

						const originalFormatter = col.formatter;
						col.formatter = function (row, cell, value, columnDef, dataContext, plainText, uniqueId, options) {
							const isCompCol = col.id.endsWith(suffixNames.compBoq);
							const boq = isCompCol ? dataContext.compBoq : dataContext.baseBoq;

							if (!boq) {
								return '';
							}

							if (dataContext.baseBoq && dataContext.compBoq && isCompCol && !equals(dataContext.baseBoq, dataContext.compBoq, col)) {
								diffCells.push(uniqueId);
							}

							return originalFormatter(row, cell, platformObjectHelper.getValue(boq, col.field), columnDef, boq, plainText, uniqueId, options);
						};
					});
				}

				const data = buildData(visibleColumns);
				platformGridAPI.items.data($scope.gridData.state, data);
			};

			const showSettingsDialog = function () {
				const customColumns = resolveOptions.settings?.Columns;
				const sortedColumns = sortColumns(baseColumns, customColumns);
				const compareColumns = sortedColumns.map((col) => {
					const target = customColumns ? customColumns.find(e => e.Id === col.id) : null;
					return {
						Id: col.id,
						Name: col.name$tr$ ? $translate.instant(col.name$tr$, col.name$tr$param$) : col.name,
						Visible: target ? target.Visible : true,
						Width: target ? target.Width : 150,
						Compare: target ? target.Compare : true
					};
				});

				platformModalService.showDialog({
					backdrop: false,
					templateUrl: globals.appBaseUrl + 'boq.main/templates/comparison/boq-comparison-settings.html',
					controller: 'boqComparisonSettingsController',
					width: '600px',
					height: '500px',
					resizeable: true,
					resolve: {
						resolveOptions: function () {
							return {
								items: compareColumns
							};
						}
					}
				}).then((r) => {
					if (!r.ok) {
						return;
					}

					if (resolveOptions.settings) {
						resolveOptions.settings.Columns = r.value;
					} else {
						resolveOptions.settings = {Columns: r.value};
					}

					refreshGrid(true);

					return $http.post(globals.webApiBaseUrl + 'boq/main/comparison/savesettings', resolveOptions.settings).then(r => {
						return r.data;
					});
				});
			};

			const initializeForm = function () {
				$scope.containerOptions = {
					formOptions: {
						configure: {
							fid: 'boq.main.comparison.settings',
							version: '1.0.0',
							showGrouping: true,
							groups: [{
								gid: 'baseBoq',
								header$tr$: resolveOptions.groupName,
								isOpen: true,
								visible: true,
								sortOrder: 1
							}, {
								gid: 'compareBoQ',
								header$tr$: 'boq.main.comparison.compareBoQ',
								isOpen: true,
								visible: true,
								sortOrder: 2
							}],
							rows: [{
								gid: 'baseBoq',
								label$tr$: 'boq.main.comparison.boq',
								rid: 'current',
								type: 'select',
								model: 'BoqHeaderFk',
								readonly: true,
								options: {
									items: [{
										name: [resolveOptions.rootBoqItem.Reference, resolveOptions.rootBoqItem.BriefInfo?.Translated ?? ''].join(' '),
										value: resolveOptions.rootBoqItem.BoqHeaderFk
									}],
									valueMember: 'value',
									displayMember: 'name'
								}
							}, {
								gid: 'compareBoQ',
								label$tr$: 'boq.main.comparison.project',
								rid: 'targetProjectFk',
								model: 'TargetProjectFk',
								type: 'directive',
								directive: 'basics-lookup-data-project-project-dialog',
								options: {
									displayMember: 'ProjectName',
									events: [{
										name: 'onSelectedItemChanged',
										handler: function selectedBoqHeaderChanged(e, args) {
											$scope.loading = true;
											$scope.entity.TargetBoqHeaderFk = null;

											resolveContext.compBoq = {
												ContextProjectId: args.selectedItem.Id
											};

											$http.get(globals.webApiBaseUrl + 'boq/project/list?projectId=' + args.selectedItem.Id + '&filterBackups=false').then(r => {
												const targetRow = $scope.containerOptions.formOptions.configure.rows.find(e => e.rid === 'targetBoqHeaderFk');
												if (targetRow) {
													targetRow.options.items = r.data.sort(function (a, b) {
														return resolveOptions.compareBoqItemsByReferences(a.BoqRootItem, b.BoqRootItem);
													}).map(e => {
														return {
															name: [e.BoqRootItem.Reference, e.BoqRootItem.BriefInfo?.Translated ?? ''].join(' '),
															value: e.BoqRootItem.BoqHeaderFk,
															boq: e
														};
													});
												}
											}).finally(() => {
												$scope.loading = false;
											});

											baseData.References = [];
											baseData.CompareData = null;
											refreshGrid(false);
										}
									}]
								}
							}, {
								gid: 'compareBoQ',
								label$tr$: 'boq.main.comparison.boq',
								rid: 'targetBoqHeaderFk',
								type: 'select',
								model: 'TargetBoqHeaderFk',
								options: {
									items: [],
									valueMember: 'value',
									displayMember: 'name'
								},
								change: function (entity) {
									$scope.loading = true;
									$scope.entity.TargetBoq = this.options.items.find(item => item.boq.BoqHeader.Id === entity.TargetBoqHeaderFk).boq;
									$http.get(globals.webApiBaseUrl + 'boq/main/comparison/getcomparedata?headerFk=' + resolveOptions.rootBoqItem.BoqHeaderFk + '&targetHeaderFk=' + entity.TargetBoqHeaderFk).then(r => {
										baseData.References = r.data.References;
										baseData.CompareData = r.data.CompareData;
										refreshGrid(true);
									}).finally(() => {
										$scope.loading = false;
									});
								}
							}]
						}
					}
				};

				platformTranslateService.translateFormConfig($scope.containerOptions.formOptions.configure);
			};

			initializeGrid();
			initializeForm();

			platformGridAPI.events.register($scope.gridData.state, 'onRenderCompleted', highlightDiffCells);

			$scope.$on('$destroy', function destroy() {
				platformGridAPI.events.unregister($scope.gridData.state, 'onRenderCompleted', highlightDiffCells);
			});
		}
	]);
})(angular);
