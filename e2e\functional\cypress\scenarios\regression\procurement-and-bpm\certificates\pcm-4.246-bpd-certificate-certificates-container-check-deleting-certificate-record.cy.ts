import { sidebar, commonLocators, app, cnt, btn, tile } from "cypress/locators";
import { _common, _businessPartnerPage, _validate, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

let CONTAINERS_BUSINESS_PARTNER;
let CONTAINER_COLUMNS_BUSINESS_PARTNER, CONTAINER_COLUMNS_CERTIFICATE;
let BUSINESS_PARTNER_PARAMETER:DataCells

describe("PCM- 4.246 | BPD_Certificate_Certificates container_Check deleting certificate record", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/pcm-4.246-bpd-certificate-certificates-container-check-deleting-certificate-record.json')
            .then((data) => {
                this.data = data;
                CONTAINERS_BUSINESS_PARTNER = this.data.CONTAINERS.BUSINESS_PARTNER
                CONTAINER_COLUMNS_BUSINESS_PARTNER = this.data.CONTAINER_COLUMNS.BUSINESS_PARTNER
                CONTAINER_COLUMNS_CERTIFICATE = this.data.CONTAINER_COLUMNS.CERTIFICATE
            }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear();
                _common.openTab(app.TabBar.PROJECT).then(() => {
                    _common.setDefaultView(app.TabBar.PROJECT);
                    _common.waitForLoaderToDisappear();
                    _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                });
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            });  });
    after(() => {
        cy.LOGOUT();
    });

      it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });
    it("TC - Create a business partner in business partner module", function () {

        BUSINESS_PARTNER_PARAMETER = {
            [commonLocators.CommonLabels.STREET]: CONTAINERS_BUSINESS_PARTNER.STREET_NAME,
            [commonLocators.CommonLabels.CITY]: CONTAINERS_BUSINESS_PARTNER.CITY_NAME,
            [commonLocators.CommonLabels.ZIP_CODE]: CONTAINERS_BUSINESS_PARTNER.ZIP_CODE,
            [commonLocators.CommonLabels.COUNTY]: CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME,
        }

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.BUSINESS_PARTNER)
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.setDefaultView(app.TabBar.BUSINESS_PARTNERS)
            _common.select_tabFromFooter(cnt.uuid.BUSINESS_PARTNERS, app.FooterTab.BUSINESS_PARTNER, 0)
            _common.setup_gridLayout(cnt.uuid.BUSINESS_PARTNERS, CONTAINER_COLUMNS_BUSINESS_PARTNER)
        });
        _common.maximizeContainer(cnt.uuid.BUSINESS_PARTNERS)

        //1_BP
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _commonAPI.createBusinessPartner(BUSINESS_PARTNER_PARAMETER)

        //2_BP
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _commonAPI.createBusinessPartner(BUSINESS_PARTNER_PARAMETER)

        //3_BP
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _commonAPI.createBusinessPartner(BUSINESS_PARTNER_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.BUSINESS_PARTNERS)
         //4_BP
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _commonAPI.createBusinessPartner(BUSINESS_PARTNER_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.BUSINESS_PARTNERS)

    })

    it("TC - Create a record in certificates container", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CERTIFICATE);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
            _common.setup_gridLayout(cnt.uuid.CERTIFICATE, CONTAINER_COLUMNS_CERTIFICATE)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CERTIFICATE.commenttext, CONTAINER_COLUMNS_CERTIFICATE.businesspartnerfk], cnt.uuid.CERTIFICATE)
        });
        //record-1 
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.CERTIFICATE)
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.CERTIFICATE)
        _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_BP_NAME_1'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(2000)// added wait for stabilty and window to refresh
        //record-2
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.CERTIFICATE)
        _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_BP_NAME_2'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
          cy.wait(2000)// added wait for stabilty and window to refresh
        //record-3
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.CERTIFICATE)
        _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_BP_NAME_3'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
          cy.wait(2000)// added wait for stabilty and window to refresh
        //record-4
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW)
        _common.select_activeRowInContainer(cnt.uuid.CERTIFICATE)
        _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_BP_NAME_4'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CERTIFICATE)
    })

    it("TC - Delete the record in certificate container & verify record is deleted", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE_DETAIL, app.FooterTab.CERTIFICATE_DETAIL, 2)
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_BP_NAME_1'))
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        cy.REFRESH_CONTAINER()
        _common.select_rowHasValue(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.IconButtons.ICO_REC_DELETE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_BP_NAME_1'))
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _validate.verify_recordNotPresentInContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
    });

    it("TC - Delete the multiple record in certificate container & verify records are deleted", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE_DETAIL, app.FooterTab.CERTIFICATE_DETAIL, 2)
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.select_multipleRow_keyboardAction(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_2'), Cypress.env('API_BP_NAME_4'))
        _common.delete_recordFromContainer(cnt.uuid.CERTIFICATE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CERTIFICATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_4'))
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_4'))
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_3'))
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_3'))
        _common.clear_subContainerFilter(cnt.uuid.CERTIFICATE)
        _common.search_inSubContainer(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_2'))
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CERTIFICATE, Cypress.env('API_BP_NAME_2'))
        _common.waitForLoaderToDisappear()
    });
})