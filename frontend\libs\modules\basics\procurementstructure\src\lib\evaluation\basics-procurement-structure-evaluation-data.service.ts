/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import {
	DataServiceFlatLeaf,
	IDataServiceChildRoleOptions,
	IDataServiceEndPointOptions,
} from '@libs/platform/data-access';
import { ServiceRole } from '@libs/platform/data-access';
import { IDataServiceOptions } from '@libs/platform/data-access';
import { BasicsProcurementStructureDataService } from '../procurement-structure/basics-procurement-structure-data.service';
import { PrcStructureComplete } from '../model/complete-class/prc-structure-complete.class';
import { IPrcStructureEntity } from '@libs/basics/interfaces';
import { IPrcStructure2EvaluationEntity } from '../model/entities/prc-structure-2-evaluation-entity.interface';
import { BasicsSharedNewEntityValidationProcessorFactory, MainDataDto } from '@libs/basics/shared';
import { BasicsProcurementStructureEvaluationValidationService } from './basics-procurement-structure-evaluation-validation.service';


/**
 * Procurement structure evaluation entity data service
 */
@Injectable({
	providedIn: 'root',
})
export class BasicsProcurementStructureEvaluationDataService extends DataServiceFlatLeaf<IPrcStructure2EvaluationEntity, IPrcStructureEntity, PrcStructureComplete> {
	private readonly validationProcessor = inject(BasicsSharedNewEntityValidationProcessorFactory);

	public constructor(parentService: BasicsProcurementStructureDataService) {
		const options: IDataServiceOptions<IPrcStructure2EvaluationEntity> = {
			apiUrl: 'basics/procurementstructure/evaluation',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list'
			},
			roleInfo: <IDataServiceChildRoleOptions<IPrcStructure2EvaluationEntity, IPrcStructureEntity, PrcStructureComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'PrcStructure2Evaluation',
				parent: parentService
			}
		};
		super(options);
		this.processor.addProcessor([this.provideNewEntityValidationProcessor()]);
	}

	private provideNewEntityValidationProcessor() {
		return this.validationProcessor.createProcessor(BasicsProcurementStructureEvaluationValidationService, {
			moduleSubModule: 'Basics.ProcurementStructure',
			typeName: 'PrcStructure2EvaluationDto',
		});
	}

	protected override provideLoadPayload(): object {
		const parentSelection = this.getSelectedParent();
		if (parentSelection) {
			return {
				mainItemId: parentSelection.Id
			};
		} else {
			throw new Error('There should be a selected parent catalog to load the evaluation data');
		}
	}


	protected override onLoadSucceeded(loaded: object): IPrcStructure2EvaluationEntity[] {
		const dto = new MainDataDto<IPrcStructure2EvaluationEntity>(loaded);
		return dto.Main;
	}

	public override isParentFn(parentKey: IPrcStructureEntity, entity: IPrcStructure2EvaluationEntity): boolean {
		return entity.PrcStructureFk === parentKey.Id;
	}
}
