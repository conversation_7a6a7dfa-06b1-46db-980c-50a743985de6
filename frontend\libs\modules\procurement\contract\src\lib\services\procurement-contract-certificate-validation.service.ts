/*
 * Copyright(c) RIB Software GmbH
 */
import { ProcurementCommonCertificateValidationService } from '@libs/procurement/common';
import { IConHeaderEntity, IPrcCertificateEntity } from '@libs/procurement/interfaces';
import { ContractComplete } from '../model/contract-complete.class';
import { inject, Injectable } from '@angular/core';
import { ProcurementContractCertificateDataService } from './procurement-contract-certificate-data.service';
import { ProcurementContractHeaderDataService } from './procurement-contract-header-data.service';

@Injectable({
	providedIn: 'root',
})
export class ProcurementContractCertificateValidationService extends ProcurementCommonCertificateValidationService<IPrcCertificateEntity, IConHeaderEntity, ContractComplete> {
	public constructor() {
		const dataService = inject(ProcurementContractCertificateDataService);
		const parentDataService = inject(ProcurementContractHeaderDataService);
		super(dataService, parentDataService);
	}
}
