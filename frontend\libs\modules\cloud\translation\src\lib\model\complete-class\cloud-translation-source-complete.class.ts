/*
 * Copyright(c) RIB Software GmbH
 */

import { ISourceEntity } from '@libs/cloud/interfaces';
import { CompleteIdentification } from '@libs/platform/common';


/**
 * Cloud Translation Source Complete
 */
export class CloudTranslationSourceComplete implements CompleteIdentification<ISourceEntity> {
	/**
	 * Id
	 */
	public Id: number = 0;

	/**
	 * Source Entity
	 */
	public Datas: ISourceEntity[] | null = [];
}
