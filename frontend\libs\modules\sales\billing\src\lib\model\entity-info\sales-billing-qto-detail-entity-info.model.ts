/*
 * Copyright(c) RIB Software GmbH
 */
import { EntityInfo } from '@libs/ui/business-base';
import { SalesBillingQtoDetailDataService } from '../../services/qto/sales-billing-qto-detail-data.service';
import { SalesBillingQtoDetailValidationService } from '../../services/qto/validations/sales-billing-qto-detail-validation.service';
import { SalesBillingQtoDetailLayoutService } from '../../services/qto/layout/sales-billing-qto-detail-layout.service';
import { ISalesBillingQtoDetailEntity } from '../entities/sales-billing-qto-detail-entity.interface';

/**
 * Sales Billing QTO Detail Entity Info
 */
export const SALES_BILLING_QTO_DETAIL_ENTITY_INFO: EntityInfo = EntityInfo.create<ISalesBillingQtoDetailEntity>({
	grid: {
		title: { text: 'Quantity Takeoff', key: 'qto.main.detail.gridTitle' },
	},
	form: {
		containerUuid: '051c10ad93904e5abf98e31208fb7334', // TODO: Update with correct form UUID for billing QTO
		title: { text: 'Quantity Takeoff Detail', key: 'qto.main.detail.formTitle' },
	},
	dataService: (context) => context.injector.get(SalesBillingQtoDetailDataService),
	validationService: (context) => context.injector.get(SalesBillingQtoDetailValidationService),
	dtoSchemeId: { moduleSubModule: 'Qto.Main', typeName: 'QtoDetailDto' },
	permissionUuid: 'e303c8ae08b246348e6686882e17dfae', // TODO: Update with correct permission UUID
	layoutConfiguration: (context) => {
		return context.injector.get(SalesBillingQtoDetailLayoutService).generateLayout();
	},
});
