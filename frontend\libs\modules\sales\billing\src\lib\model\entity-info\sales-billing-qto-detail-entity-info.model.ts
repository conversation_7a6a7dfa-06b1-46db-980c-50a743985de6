/*
 * Copyright(c) RIB Software GmbH
 */
import { EntityInfo } from '@libs/ui/business-base';
import { SalesBillingQtoDetailDataService } from '../../services/qto/sales-billing-qto-detail-data.service';
import { SalesBillingQtoDetailLayoutService } from '../../services/qto/layout/sales-billing-qto-detail-layout.service';
import { ISalesBillingQtoDetailEntity } from '../entities/sales-billing-qto-detail-entity.interface';

/**
 * Sales Billing QTO Detail Entity Info
 */
export const SALES_BILLING_QTO_DETAIL_ENTITY_INFO: EntityInfo = EntityInfo.create<ISalesBillingQtoDetailEntity>({
	permissionUuid: 'e303c8ae08b246348e6686882e17dfae', // TODO: Update with correct permission UUID
	dataService: ctx => ctx.injector.get(SalesBillingQtoDetailDataService),
	layoutConfiguration: ctx => ctx.injector.get(SalesBillingQtoDetailLayoutService).generateLayout(),
	dtoSchemeId: { moduleSubModule: 'Qto.Main', typeName: 'QtoDetailDto' },
});
