/*
 * Copyright(c) RIB Software GmbH
 */
import { QtoShareDetailEntityInfo } from '@libs/qto/shared';
import { SalesBillingQtoDetailDataService } from '../../services/qto/sales-billing-qto-detail-data.service';
import { SalesBillingQtoDetailValidationService } from '../../services/qto/validations/sales-billing-qto-detail-validation.service';

/**
 * Sales Billing QTO Detail Entity Info
 */
export const SALES_BILLING_QTO_DETAIL_ENTITY_INFO = QtoShareDetailEntityInfo.create({
	permissionUuid: 'e303c8ae08b246348e6686882e17dfae', // TODO: Update with correct permission UUID
	formUuid: '051c10ad93904e5abf98e31208fb7334', // TODO: Update with correct form UUID for billing QTO
	dataServiceToken: SalesBillingQtoDetailDataService,
	validationServiceToken: SalesBillingQtoDetailValidationService,
	// layoutServiceToken: Use default QtoShareDetailLayoutService
});
