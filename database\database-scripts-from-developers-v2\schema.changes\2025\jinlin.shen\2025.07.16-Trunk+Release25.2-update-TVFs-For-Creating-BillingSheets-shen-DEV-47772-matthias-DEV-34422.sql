-----------------------------------------
-- Ignore Errors: Off
-- Jira Number:   DEV-47772, DEV-34422
-- Script Type:   Required Schema Change
-- Reason:        update TVFs for creating BS and minor update from task 34422
-- Install On:    All Trunk, Release25.2
-----------------------------------------


EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC 'CAL_ISEXCEPTIONDAY_F';
GO
CREATE FUNCTION dbo.CAL_ISEXCEPTIONDAY_F (
    @calendarFk INT, 
    @date DATE
)
RETURNS BIT
AS
BEGIN
    DECLARE @isException BIT = 0;
    DECLARE @dayOfWeekIndex INT = DATEPART(weekday, @date);

    --------------------------------------------------------
    -- case 1: Check CAL_EXCEPTIONDAY (Highest Priority, standard case)
    --------------------------------------------------------
    DECLARE @exceptionFound BIT = 0;
    DECLARE @isWorkdayInException BIT;

    SELECT TOP 1
        @exceptionFound = 1,
        @isWorkdayInException = ISWORKDAY
    FROM dbo.CAL_EXCEPTIONDAY
    WHERE CAL_CALENDAR_FK = @calendarFk
      AND EXCEPTDATE = @date;

    IF @exceptionFound = 1
    BEGIN
        IF @isWorkdayInException = 0
            SET @isException = 1;
        ELSE
            SET @isException = 0;

        RETURN @isException;
    END

    --------------------------------------------------------
    -- case 2: If no CAL_EXCEPTIONDAY, check CAL_WEEKDAY
    --------------------------------------------------------
    DECLARE @isWeekend BIT;

    SELECT TOP 1 @isWeekend = ISWEEKEND
    FROM dbo.CAL_WEEKDAY
    WHERE CAL_CALENDAR_FK = @calendarFk
      AND WEEKDAYINDEX = @dayOfWeekIndex;

    IF @isWeekend IS NULL
    BEGIN
        SET @isWeekend = 0; -- if no CAL_WEEKDAY defined, then 0
    END

    IF @isWeekend = 1
    BEGIN
        SET @isException = 1;
        RETURN @isException;
    END
    ELSE IF @isWeekend = 0 -- new added
    BEGIN
        SET @isException = 0;
        RETURN @isException;
    END

    --------------------------------------------------------
    -- case 3: If not an exception via CAL_EXCEPTIONDAY and not a weekend via CAL_WEEKDAY,
    --         check CAL_WORKDAY for actual work hours (last part)
    --------------------------------------------------------
    IF NOT EXISTS (
        SELECT 1
        FROM dbo.CAL_WORKDAY
        WHERE CAL_CALENDAR = @calendarFk
          AND EXCEPTDATE = @date        
          AND WORKSTART IS NOT NULL
          AND WORKEND IS NOT NULL
          AND WORKSTART < WORKEND
    )
    BEGIN
        SET @isException = 1;
    END

    RETURN @isException;
END;
GO


EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC 'LGM_GETEVALPLANTTYPEANDWOT_F';
GO

---- evaluate different use cases,LGM_GETEVALPLANTTYPEANDWOT_F ----
CREATE FUNCTION [dbo].[LGM_GETEVALPLANTTYPEANDWOT_F]
(
    @PlantTypeId INT,
    @IsBulk BIT,
    @ReservationId INT,
    @RequestedFrom DATE,
    @ReservedFrom DATE,
    @ProjectId INT,
    @WorkOperationTypeFk INT,
    @RecordDate DATE,
    @IsBALReturnCase BIT -- new added for use case BAL Return 
)
RETURNS TABLE
AS
RETURN
(
    SELECT TOP 1
        wot.ID AS WOT_ID,
        wot.ISHIRE,
        wot.ISIDLE,
        wot.BAS_UOM_FK,
        wot.ISMINOREQUIPMENT
    FROM (
        -- use Case 1: Bulk plant with active event 
        SELECT TOP 1 pe.ETM_WORKOPERATIONTYPE_FK AS WOT_ID, 1 AS Priority
        FROM dbo.ETM_PLANTEVENT pe
        INNER JOIN dbo.ETM_WOT2PLANTTYPE rel ON rel.ETM_WORKOPERATIONTYPE_FK = pe.ETM_WORKOPERATIONTYPE_FK
        WHERE @IsBulk = 1
            AND rel.ETM_PLANTTYPE_FK = @PlantTypeId
            AND pe.RES_RESERVATION_FK = @ReservationId
            AND pe.PRJ_PROJECT_FK = @ProjectId
            AND @RecordDate BETWEEN pe.EVENT_FROM AND pe.EVENT_TO
        ORDER BY pe.ID DESC 

        UNION ALL

        -- use Case 2: Non-Bulk Idle
        SELECT TOP 1 wotIdle.ID, 2 AS Priority
        FROM dbo.ETM_WOT2PLANTTYPE rel
        INNER JOIN dbo.ETM_WORKOPERATIONTYPE wotIdle ON wotIdle.ID = rel.ETM_WORKOPERATIONTYPE_FK
        WHERE @IsBulk = 0
            AND rel.ETM_PLANTTYPE_FK = @PlantTypeId
            --AND @RecordDate < ISNULL(@ReservedFrom, @RequestedFrom) ------------------modified !!!!!!!!!!!!!!!!!!!!!!!!
            AND @RecordDate >= @ReservedFrom 
            AND @RecordDate < @RequestedFrom 
            AND @ReservedFrom IS NOT NULL
            AND @RequestedFrom IS NOT NULL
            AND wotIdle.ISIDLE = 1
        ORDER BY wotIdle.ID DESC 

        UNION ALL
		--------------------------------new added!!!!!!!!!!!!!!!!!!!!!!!!----------------------------------------------
		-- use Case 2.3: Main Equipment Performance (non Bulk, nonHire from requisition)
		SELECT TOP 1 wotReq.ID, 2.3 AS Priority
		FROM dbo.ETM_WORKOPERATIONTYPE wotReq -- removed join of ETM_WOT2PLANTTYPE
		WHERE @IsBulk = 0 -- from planttype 
			-- AND rel.ETM_PLANTTYPE_FK = @PlantTypeId -- removed !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
			AND wotReq.ID = @WorkOperationTypeFk -- make sure WOT from requisition
			AND wotReq.ISHIRE = 0 --make sure wot ishire false 
		ORDER BY wotReq.ID DESC

		UNION ALL
        -- use Case 2.5: Main Equipment Performance (Non-Bulk, Non-Hire from Requisition)
        SELECT TOP 1 wotReq.ID, 2.5 AS Priority
        FROM dbo.ETM_WOT2PLANTTYPE rel
        INNER JOIN dbo.ETM_WORKOPERATIONTYPE wotReq ON wotReq.ID = rel.ETM_WORKOPERATIONTYPE_FK
        WHERE @IsBulk = 0 -- is not Bulk
            AND rel.ETM_PLANTTYPE_FK = @PlantTypeId
            AND @WorkOperationTypeFk = wotReq.ID -- evaluated from Requisition WOT
            AND wotReq.ISHIRE = 0 -- IsHire = False
        ORDER BY wotReq.ID DESC 

        UNION ALL

        -- use Case 2.8: Bulk BAL Return (new added)
        SELECT TOP 1 wotRevDep.ID, 2.8 AS Priority
        FROM dbo.ETM_WOT2PLANTTYPE rel
        INNER JOIN dbo.ETM_WORKOPERATIONTYPE wotRevDep ON wotRevDep.ID = rel.ETM_WORKOPERATIONTYPE_FK
        WHERE @IsBulk = 1
            AND @IsBALReturnCase = 1 -- only for BAL Return use case
            AND rel.ETM_PLANTTYPE_FK = @PlantTypeId
            AND wotRevDep.ISREVERSEDEPOSIT = 1
            AND wotRevDep.ISMINOREQUIPMENT = 1
        ORDER BY wotRevDep.ID DESC

        UNION ALL

        -- use Case 3: Bulk BAL Send (updated)
        SELECT TOP 1 wotBal.ID, 3 AS Priority
        FROM dbo.ETM_WOT2PLANTTYPE rel
        INNER JOIN dbo.ETM_WORKOPERATIONTYPE wotBal ON wotBal.ID = rel.ETM_WORKOPERATIONTYPE_FK
        WHERE @IsBulk = 1
            AND rel.ETM_PLANTTYPE_FK = @PlantTypeId
            AND @WorkOperationTypeFk = wotBal.ID
            AND wotBal.ISMINOREQUIPMENT = 1
            AND ISNULL(@IsBALReturnCase, 0) = 0
        ORDER BY wotBal.ID DESC 

        UNION ALL

        -- use Case 4: fallback to req.ETM_WORKOPERATIONTYPE_FK 
        SELECT TOP 1 wotReq.ID, 4 AS Priority
        FROM dbo.ETM_WOT2PLANTTYPE rel
        INNER JOIN dbo.ETM_WORKOPERATIONTYPE wotReq ON wotReq.ID = rel.ETM_WORKOPERATIONTYPE_FK
        WHERE rel.ETM_PLANTTYPE_FK = @PlantTypeId
            AND @WorkOperationTypeFk = wotReq.ID
        ORDER BY wotReq.ID DESC

    ) AS selectedWOT
    INNER JOIN dbo.ETM_WORKOPERATIONTYPE wot ON wot.ID = selectedWOT.WOT_ID
    ORDER BY selectedWOT.Priority
);
GO



------------ plant material, sundry service TVF for creating billingsheet-------------

EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC 'LGM_CREATEBILLINGSHEET_F';
GO
CREATE FUNCTION [dbo].[LGM_CREATEBILLINGSHEET_F]
(
    @projectId INT,
    @recordDate DATETIME,
    @actualDaysInMonth INT
)
RETURNS @Result TABLE
(
    PRJ_PROJECT_FK INT NOT NULL,
    RES_REQUISITION_FK INT,
    RECORDDATE DATE NOT NULL,
    ETM_PLANT_FK INT,
    LGM_JOB_FK INT,
    LGC_PRICECONDITION_FK INT,
    MDC_CONTROLLINGUNITCOST_FK INT,
    MDC_CONTROLLINGUNITREV_FK INT,
    RES_RESERVATION_FK INT,
    LGM_DISPATCH_HEADER_FK INT,
    LGM_DISPATCH_RECORD_FK INT,
	MDC_MATERIAL_FK INT NULL,
    LGM_SUNDRYSERVICE_FK INT NULL,
    CAL_CALENDAR_FK INT,
    ETM_WORKOPERATIONTYPE_FK INT,
    BAS_UOM_FK INT NOT NULL,
    PLANTQUANTITY INT NOT NULL,
    WORKHOURPERDAY NUMERIC NOT NULL,
    OPERATIONQUANTITY NUMERIC NOT NULL,
    ISREQUISITIONBASED BIT NOT NULL,
    LGM_BILLINGSHEETSTATUS_FK INT NOT NULL,
    PERCENTAGE01 NUMERIC NOT NULL,
    PERCENTAGE02 NUMERIC NOT NULL,
    PERCENTAGE03 NUMERIC NOT NULL,
    PERCENTAGE04 NUMERIC NOT NULL,
    PERCENTAGE05 NUMERIC NOT NULL,
    PERCENTAGE06 NUMERIC NOT NULL,
    PRICEPORTION01 NUMERIC NOT NULL,
    PRICEPORTION02 NUMERIC NOT NULL,
    PRICEPORTION03 NUMERIC NOT NULL,
    PRICEPORTION04 NUMERIC NOT NULL,
    PRICEPORTION05 NUMERIC NOT NULL,
    PRICEPORTION06 NUMERIC NOT NULL,
    PRICEPORTIONSUM NUMERIC NOT NULL,
    CALCPRICEPORTION01 NUMERIC NOT NULL,
    CALCPRICEPORTION02 NUMERIC NOT NULL,
    CALCPRICEPORTION03 NUMERIC NOT NULL,
    CALCPRICEPORTION04 NUMERIC NOT NULL,
    CALCPRICEPORTION05 NUMERIC NOT NULL,
    CALCPRICEPORTION06 NUMERIC NOT NULL,
    CALCPRICEPORTIONSUM NUMERIC NOT NULL,
    TOTALPRICE NUMERIC NOT NULL,
    LGM_PRICECALCTYPE_FK INT NOT NULL,
    LGM_BILLINGSHEETTYPE_FK INT NOT NULL,
    ETM_PRICINGGROUP_FK INT,
    ISREADONLYOPQUANTITY BIT NOT NULL,
	LGM_JOBPERFORMING_FK INT NULL,
	LGM_PLANTPRICE_FK INT NULL,
	LGC_PLANTPRICE_FK INT NULL,
	ETM_PLANTPRICELIST_FK INT NULL,
	ETM_PRICELIST_FK INT NULL,
	LGM_ETMCTLGPRICE_FK INT NULL,
	ETM_GROUPPRICELIST_FK INT NULL
)
AS
BEGIN

    INSERT INTO @Result
    SELECT
     	@projectId,
        req.ID AS RES_REQUISITION_FK, -- only for plant
        @recordDate AS RECORDDATE,
        COALESCE(
        rec.ETM_PLANT_FK,       --  get plant from dispRec for use cases Bulk and BAL
        resp.ETM_PLANT_FK       --  otherwise from Resource Part for other use cases
        ) AS ETM_PLANT_FK,
        ISNULL(req.LGM_JOB_FK, NULL) AS LGM_JOB_FK,
        ISNULL(job.LGC_PRICECONDITION_FK, NULL) AS LGC_PRICECONDITION_FK,
        ISNULL(req.MDC_CONTROLLINGUNIT_FK, NULL) AS MDC_CONTROLLINGUNITCOST_FK,
        COALESCE(plant2cu_rec.MDC_CONTROLLINGUNIT_FK,    
        plantgrp2cu_rec.MDC_CONTROLLINGUNIT_FK, 
        plant2cu_resp.MDC_CONTROLLINGUNIT_FK,   
        plantgrp2cu_resp.MDC_CONTROLLINGUNIT_FK ---------------------------final check !!!!!!!!!!!!!!
        ) AS MDC_CONTROLLINGUNITREV_FK,
        ISNULL(reserv.ID, NULL) AS RES_RESERVATION_FK,
        ISNULL(hdr.ID, NULL) AS LGM_DISPATCH_HEADER_FK,
        ISNULL(rec.ID, NULL) AS LGM_DISPATCH_RECORD_FK,
		NULL,
		NULL,
        ISNULL(job.CAL_CALENDAR_FK, NULL) AS CAL_CALENDAR_FK,
        ISNULL(EvalWOT.WOT_ID, NULL) AS ETM_WORKOPERATIONTYPE_FK,
        -- UOM logic with proper fallback (new modified)
        CASE
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 0 AND EvalWOT.ISHIRE = 1 THEN cal.BAS_UOMHOUR_FK
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 0 AND EvalWOT.ISHIRE = 0 THEN EvalWOT.BAS_UOM_FK
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISHIRE = 1 THEN cal.BAS_UOMHOUR_FK
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISMINOREQUIPMENT = 1 THEN NULL
        ELSE NULL
        END AS BAS_UOM_FK,
        CASE
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISHIRE = 1 THEN COALESCE(plantEvent.QUANTITY, ISNULL(dpArticleSum.dropArticleQty, 0))
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISMINOREQUIPMENT = 1 THEN ISNULL(rec.QUANTITY, 1) -- BAL send
        ELSE 1
        END AS PLANTQUANTITY, -- new modified 
        cal.WORKHOURSPERDAY AS WORKHOURPERDAY,
        op.OperatingQuantity AS OPERATIONQUANTITY,
        1 AS ISREQUISITIONBASED, --only for Plant
        defBillingSheetStatus.DEFAULT_STATUS_ID AS LGM_BILLINGSHEETSTATUS_FK,
        -- Percentages (unchanged)
        ISNULL(Price.PERCENTAGE01, 0.0) AS PERCENTAGE01,
        ISNULL(Price.PERCENTAGE02, 0.0) AS PERCENTAGE02,
        ISNULL(Price.PERCENTAGE03, 0.0) AS PERCENTAGE03,
        ISNULL(Price.PERCENTAGE04, 0.0) AS PERCENTAGE04,
        ISNULL(Price.PERCENTAGE05, 0.0) AS PERCENTAGE05,
        ISNULL(Price.PERCENTAGE06, 0.0) AS PERCENTAGE06,
        -- use helper function for all price calculations
        convertedPrices.PRICEPORTION01 AS PRICEPORTION01,
        convertedPrices.PRICEPORTION02 AS PRICEPORTION02,
        convertedPrices.PRICEPORTION03 AS PRICEPORTION03,
        convertedPrices.PRICEPORTION04 AS PRICEPORTION04,
        convertedPrices.PRICEPORTION05 AS PRICEPORTION05,
        convertedPrices.PRICEPORTION06 AS PRICEPORTION06,
        convertedPrices.PRICEPORTIONSUM AS PRICEPORTIONSUM,
        convertedPrices.CALCPRICEPORTION01 AS CALCPRICEPORTION01,
        convertedPrices.CALCPRICEPORTION02 AS CALCPRICEPORTION02,
        convertedPrices.CALCPRICEPORTION03 AS CALCPRICEPORTION03,
        convertedPrices.CALCPRICEPORTION04 AS CALCPRICEPORTION04,
        convertedPrices.CALCPRICEPORTION05 AS CALCPRICEPORTION05,
        convertedPrices.CALCPRICEPORTION06 AS CALCPRICEPORTION06,
        convertedPrices.CALCPRICEPORTIONSUM AS CALCPRICEPORTIONSUM,
        -- TOTALPRICE calculation
        convertedPrices.CALCPRICEPORTIONSUM
         * CASE
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISHIRE = 1 THEN ISNULL(dpArticleSum.dropArticleQty, 0.0)
        WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISMINOREQUIPMENT = 1 THEN ISNULL(rec.QUANTITY, 1.0)
        ELSE 1.0
        END
       * op.OperatingQuantity AS TOTALPRICE, -- new modified part!!!!!!!!!!!!!!
        ISNULL(Price.LGM_PRICECALCTYPE_FK,1) AS LGM_PRICECALCTYPE_FK,
        1 AS LGM_BILLINGSHEETTYPE_FK, --for plant
        ISNULL(job.ETM_PRICINGGROUP_FK, COALESCE(plantGroup_rec.ETM_PRICINGGROUP_FK, plantGroup_resp.ETM_PRICINGGROUP_FK)) AS ETM_PRICINGGROUP_FK,
        CASE
           WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISMINOREQUIPMENT = 1 THEN 1
        ELSE 0
        END AS ISREADONLYOPQUANTITY, -- new modified part!!!!!!!!!!!!!!

		Price.LGM_JOBPERFORMING_FK,
		Price.LGM_PLANTPRICE_FK,
		Price.LGC_PLANTPRICE_FK,
		Price.ETM_PLANTPRICELIST_FK,
		Price.ETM_PRICELIST_FK,
		Price.LGM_ETMCTLGPRICE_FK,
		Price.ETM_GROUPPRICELIST_FK

    FROM
    dbo.LGM_DISPATCH_RECORD rec -- big change!!!!!!!!!!!!!!!!!  previous basic table was requisition 
	INNER JOIN
		dbo.LGM_DISPATCH_HEADER hdr ON hdr.ID = rec.LGM_DISPATCH_HEADER_FK
	INNER JOIN
		dbo.LGM_DISPATCH_STATUS dispstat ON dispstat.ID = hdr.LGM_DISPATCH_STATUS_FK AND dispstat.ISDELIVERED = 1

	LEFT JOIN
		dbo.RES_REQUISITION req ON rec.RES_REQUISITION_FK = req.ID ---  changed!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

	LEFT JOIN
		dbo.LGM_JOB job ON job.ID = req.LGM_JOB_FK ----- needs to be final check !!!!!!
	INNER JOIN
		dbo.ETM_DIVISION division ON division.ID = job.ETM_DIVISION_FK
	INNER JOIN
		dbo.CAL_CALENDAR cal ON cal.ID = job.CAL_CALENDAR_FK

    -- all related reservation joins should be left joins 
	LEFT JOIN
		dbo.RES_RESERVATION reserv ON reserv.RES_REQUISITION_FK = req.ID AND reserv.ID = rec.RES_RESERVATION_FK
	LEFT JOIN
		dbo.RES_RESOURCE res ON res.ID = reserv.RES_RESOURCE_FK
	LEFT JOIN
		dbo.RES_RESOURCEPART resp ON resp.RES_RESOURCE_FK = res.ID AND resp.ISMAINPART = 1 

	-- case1: Plant from LGM_DISPATCH_RECORD  ----new modified!!!!!!!!!!!!!!!!!!
	LEFT JOIN
		dbo.ETM_PLANT plant_rec ON plant_rec.ID = rec.ETM_PLANT_FK
	LEFT JOIN
		dbo.ETM_PLANTTYPE planttype_rec ON planttype_rec.ID = plant_rec.ETM_PLANTTYPE_FK
	LEFT JOIN
		dbo.ETM_PLANTGROUP plantGroup_rec ON plantGroup_rec.ID = plant_rec.ETM_PLANTGROUP_FK
	LEFT JOIN
		dbo.ETM_PLANT2CONTRUNIT plant2cu_rec ON plant2cu_rec.ETM_PLANT_FK = plant_rec.ID
	LEFT JOIN
		dbo.ETM_PLANTGRP2CONTRUNIT plantgrp2cu_rec ON plantgrp2cu_rec.ETM_PLANTGROUP_FK = plant_rec.ETM_PLANTGROUP_FK

   -- case2: Plant from RES_RESOURCEPART ---- new modified!!!!!!!!!!!!!!!!!!
	LEFT JOIN
		dbo.ETM_PLANT plant_resp ON plant_resp.ID = resp.ETM_PLANT_FK
	LEFT JOIN
		dbo.ETM_PLANTTYPE planttype_resp ON planttype_resp.ID = plant_resp.ETM_PLANTTYPE_FK
	LEFT JOIN
		dbo.ETM_PLANTGROUP plantGroup_resp ON plantGroup_resp.ID = plant_resp.ETM_PLANTGROUP_FK
	LEFT JOIN
		dbo.ETM_PLANT2CONTRUNIT plant2cu_resp ON plant2cu_resp.ETM_PLANT_FK = plant_resp.ID
	LEFT JOIN
		dbo.ETM_PLANTGRP2CONTRUNIT plantgrp2cu_resp ON plantgrp2cu_resp.ETM_PLANTGROUP_FK = plant_resp.ETM_PLANTGROUP_FK


	LEFT JOIN dbo.LGC_PRICECONDITION prc ON prc.ID = job.LGC_PRICECONDITION_FK
	LEFT JOIN dbo.MDC_CONTROLLINGUNIT cu ON cu.ID = req.MDC_CONTROLLINGUNIT_FK
	LEFT JOIN dbo.ETM_PRICINGGROUP prcg ON prcg.ID = job.ETM_PRICINGGROUP_FK

    -- PlantEven, if reserv is null
    LEFT JOIN dbo.ETM_PLANTEVENT plantEvent ON
    plantEvent.PRJ_PROJECT_FK = @projectId
    AND (plantEvent.RES_RESERVATION_FK = reserv.ID OR reserv.ID IS NULL) 
    AND @recordDate BETWEEN plantEvent.EVENT_FROM AND plantEvent.EVENT_TO

  -- new modified!!!!!!!!!!!!!!!!!!!!!
	CROSS APPLY dbo.LGM_GETEVALPLANTTYPEANDWOT_F(
		COALESCE(planttype_rec.ID, planttype_resp.ID), -- first from Dispatch Record 
		COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK), 
		reserv.ID,
		req.REQUESTED_FROM,
		reserv.RESERVED_FROM,
		@projectId,
		COALESCE(req.ETM_WORKOPERATIONTYPE_FK, rec.ETM_WORKOPERATIONTYPE_FK), -- WOT could be from rec
		@recordDate,
		CAST(0 AS BIT)
	) AS EvalWOT

	-- new modified !!!!!!!!!!!!!!!!!!!!!!
	OUTER APPLY dbo.LGM_PLANTPRICECALC_F(
		hdr.LGM_JOB1_FK,
		hdr.LGM_JOB2_FK,
		COALESCE(rec.ETM_PLANT_FK, resp.ETM_PLANT_FK), --new modified!!!!!!!!!!!!!
		EvalWOT.WOT_ID,
		ISNULL(job.ETM_PRICINGGROUP_FK, COALESCE(plantGroup_rec.ETM_PRICINGGROUP_FK, plantGroup_resp.ETM_PRICINGGROUP_FK)),
		COALESCE(@recordDate, hdr.DATE_EFFECTIVE, hdr.DATE_DOCUMENTED)
	) AS Price

	-- ... (existing OUTER APPLY for covert Price)
	   CROSS APPLY dbo.LGM_CONVERTALLPRICEPORTIONS_F(
        Price.PRICEPORTION01,
        Price.PRICEPORTION02,
        Price.PRICEPORTION03,
        Price.PRICEPORTION04,
        Price.PRICEPORTION05,
        Price.PRICEPORTION06,
        Price.PERCENTAGE01,
        Price.PERCENTAGE02,
        Price.PERCENTAGE03,
        Price.PERCENTAGE04,
        Price.PERCENTAGE05,
        Price.PERCENTAGE06,
        Price.BAS_UOM_FK,
        cal.BAS_UOMHOUR_FK,
        cal.BAS_UOMWORKDAY_FK,
        cal.BAS_UOMMONTH_FK,
        cal.WORKHOURSPERDAY,
        cal.WORKHOURSPERMONTH,
        division.ISCALCBASEDONTHIRTYDAYS,
		division.ISCALCBASEDONWORKDAYS,
        @actualDaysInMonth
    ) AS convertedPrices 

	OUTER APPLY (
		SELECT SUM(dpArticle.QUANTITY) AS dropArticleQty
		FROM PRJ_DROP_POINT_ARTICLES dpArticle
		INNER JOIN PRJ_DROPPOINT dp ON dp.ID = dpArticle.PRJ_DROPPOINT_FK
		WHERE dp.PRJ_PROJECT_FK = @projectId
			AND dpArticle.ETM_PLANT_FK = COALESCE(rec.ETM_PLANT_FK, resp.ETM_PLANT_FK) -- choose the Plant FK, new modified !!!!!!!!!!!!!!
	) AS dpArticleSum

	CROSS APPLY (
		SELECT TOP 1 ID AS DEFAULT_STATUS_ID
		FROM dbo.LGM_BILLINGSHEETSTATUS
		WHERE ISDEFAULT = 1
		ORDER BY ID
	) AS defBillingSheetStatus

	CROSS APPLY (
		SELECT
			CASE
			   -- case exceptionday: exception day with 0.0 operating quantity
				WHEN dbo.CAL_ISEXCEPTIONDAY_F(cal.ID, @recordDate) = 1 THEN 0.0
				ELSE
					CASE
					    -- user input case (default 0)
						WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 0 AND EvalWOT.ISHIRE = 0 THEN 0.0
						
                        -- use case for main equipment rental (including small tools and standard)
						WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 0 AND EvalWOT.ISHIRE = 1 THEN
							CASE
								WHEN @recordDate = COALESCE(NULLIF(reserv.RESERVED_TO, NULL), req.REQUESTED_TO, rec.DATE_EFFECTIVE) --  rec.DATE_EFFECTIVE
								THEN cal.WORKHOURSPERDAY * ISNULL(prc.DEPARTURERATINGPERCENT, 1.0)
								ELSE cal.WORKHOURSPERDAY
							END
                        -- bulk plant and hire, case bulk Rental
						WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISHIRE = 1 THEN cal.WORKHOURSPERDAY
						  -- bulk plant and minor equipment case BAL ,  fixed 1 as readonly
						WHEN COALESCE(planttype_rec.ISBULK, planttype_resp.ISBULK) = 1 AND EvalWOT.ISMINOREQUIPMENT = 1 THEN 1.0
						ELSE 0.0
					END
			END AS OperatingQuantity
	) AS op


	WHERE
		req.PRJ_PROJECT_FK = @projectId -- filtered from requisition
		AND dispstat.ISDELIVERED = 1
		AND rec.MDC_MATERIAL_FK IS NULL
		AND rec.LGM_SUNDRYSERVICE_FK IS NULL
		AND COALESCE(rec.ETM_PLANT_FK, resp.ETM_PLANT_FK) IS NOT NULL

		AND (
			-- case 1: Bulk Rental (plant from dispRec, no reservation) OR BAL Send
			(
				rec.ETM_PLANT_FK IS NOT NULL -- Plant specified directly on Dispatch Record
				AND rec.RES_RESERVATION_FK IS NULL -- explicitly no reservation linked
				AND req.ID IS NOT NULL -- make sure on requisition exists 
				AND COALESCE(planttype_rec.ISBULK, 0) = 1 -- Plant type is Bulk
				AND (
					-- sub case 1.1: bulk rental (ISHIRE = 1), requires EXISTS check
					(EvalWOT.ISHIRE = 1
						AND EXISTS ( -- check if project area dp articles have quantity
						SELECT 1
						FROM PRJ_DROP_POINT_ARTICLES dpa
						INNER JOIN PRJ_DROPPOINT dp ON dp.ID = dpa.PRJ_DROPPOINT_FK
						WHERE dp.PRJ_PROJECT_FK = @projectId
							AND dpa.ETM_PLANT_FK = rec.ETM_PLANT_FK
							AND dpa.QUANTITY > 0
					))
					OR
					-- sub case 1.2: bal send (ISMINOREQUIPMENT = 1), no EXISTS check needed for filtering ---------------no BAL Return for this version
					(EvalWOT.ISMINOREQUIPMENT = 1)
				)
			)
			OR
			-- case 2: main Euuipment Rental, Main Equipment Performance, Small Tools (plant from reservation)
			(
				reserv.ID IS NOT NULL -- Explicitly associated with a Reservation
				AND resp.ETM_PLANT_FK IS NOT NULL -- Plant from Resource Part (via Reservation)
				AND rec.RES_RESERVATION_FK = reserv.ID -- Dispatch Record must link to this Reservation
			)
		)
    
	 -- date casting
     AND CAST(@recordDate AS DATE) BETWEEN 
     CAST(COALESCE(NULLIF(reserv.RESERVED_FROM, NULL), req.REQUESTED_FROM, hdr.DATE_EFFECTIVE, hdr.DATE_DOCUMENTED) AS DATE)
     AND
     CAST(COALESCE(NULLIF(reserv.RESERVED_TO, NULL), req.REQUESTED_TO, hdr.DATE_EFFECTIVE, hdr.DATE_DOCUMENTED) AS DATE)

	 -- For cases : material or sundry service	----------------------------
	 -----------------------------------------------------------------------
	UNION ALL 

    SELECT
	    job2.PRJ_PROJECT_FK, -- receiving job.PRJ_PROJECT_FK
        NULL AS RES_REQUISITION_FK, -- no requisition needed
        COALESCE(rec.DATE_EFFECTIVE, hrd.DATE_EFFECTIVE, hrd.DATE_DOCUMENTED) AS RECORDDATE,
        NULL AS ETM_PLANT_FK,
        job2.ID AS LGM_JOB_FK, -- job2.ID
        job2.LGC_PRICECONDITION_FK AS LGC_PRICECONDITION_FK, -- from job2
        job2.MDC_CONTROLLINGUNIT_FK AS MDC_CONTROLLINGUNITCOST_FK, -- from job2
        job1.MDC_CONTROLLINGUNIT_FK AS MDC_CONTROLLINGUNITREV_FK,  --from job1
        NULL AS RES_RESERVATION_FK,
        hrd.ID AS LGM_DISPATCH_HEADER_FK,
        rec.ID AS LGM_DISPATCH_RECORD_FK,
	    ISNULL(rec.MDC_MATERIAL_FK, NULL),
		ISNULL(rec.LGM_SUNDRYSERVICE_FK, NULL),
        job2.CAL_CALENDAR_FK AS CAL_CALENDAR_FK,
        NULL AS ETM_WORKOPERATIONTYPE_FK,
        rec.BAS_UOM_FK AS BAS_UOM_FK,
        0 AS PLANTQUANTITY,
        0.0 AS WORKHOURPERDAY,
        rec.QUANTITY AS OPERATIONQUANTITY, -- from LGM_DISPATCH_RECORD
        0 AS ISREQUISITIONBASED,
        defBillingSheetStatus.DEFAULT_STATUS_ID AS LGM_BILLINGSHEETSTATUS_FK,
        0.0 AS PERCENTAGE01,
        0.0 AS PERCENTAGE02,
        0.0 AS PERCENTAGE03,
        0.0 AS PERCENTAGE04,
        0.0 AS PERCENTAGE05,
        0.0 AS PERCENTAGE06,
        -- get from LGM_DISPATCH_RECORD
		-- PRICEPORTION01 for Material and Sundry Service 
		CASE
			WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN ISNULL(rec.PRICE, 0.0)
			ELSE ISNULL(rec.PRICEPORTION01, 0.0)
		END AS PRICEPORTION01,
		rec.PRICEPORTION02 AS PRICEPORTION02,
		rec.PRICEPORTION03 AS PRICEPORTION03,
		rec.PRICEPORTION04 AS PRICEPORTION04,
		rec.PRICEPORTION05 AS PRICEPORTION05,
		rec.PRICEPORTION06 AS PRICEPORTION06,
		( -- PRICEPORTIONSUM
		   CASE
				WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN ISNULL(rec.PRICE, 0.0)
				ELSE ISNULL(rec.PRICEPORTION01, 0.0)
		   END
			+ ISNULL(rec.PRICEPORTION02, 0.0) + ISNULL(rec.PRICEPORTION03, 0.0) +
			ISNULL(rec.PRICEPORTION04, 0.0) + ISNULL(rec.PRICEPORTION05, 0.0) + ISNULL(rec.PRICEPORTION06, 0.0)
		) AS PRICEPORTIONSUM,

		-- Calculated Price Portions (no percentages needed)
    
		CASE
			WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN ISNULL(rec.PRICE, 0.0)
			ELSE ISNULL(rec.PRICEPORTION01, 0.0)
		END AS CALCPRICEPORTION01,
		rec.PRICEPORTION02 AS CALCPRICEPORTION02,
		rec.PRICEPORTION03 AS CALCPRICEPORTION03,
		rec.PRICEPORTION04 AS CALCPRICEPORTION04,
		rec.PRICEPORTION05 AS CALCPRICEPORTION05,
		rec.PRICEPORTION06 AS CALCPRICEPORTION06,
		-- 4: CALCPRICEPORTIONSUM 
		(
			CASE
				WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN ISNULL(rec.PRICE, 0.0)
				ELSE ISNULL(rec.PRICEPORTION01, 0.0)
			END
			+ ISNULL(rec.PRICEPORTION02, 0.0) + ISNULL(rec.PRICEPORTION03, 0.0) +
			ISNULL(rec.PRICEPORTION04, 0.0) + ISNULL(rec.PRICEPORTION05, 0.0) + ISNULL(rec.PRICEPORTION06, 0.0)
		) AS CALCPRICEPORTIONSUM,

		-- 5: TOTALPRICE 
		rec.QUANTITY * (
			CASE
				WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN ISNULL(rec.PRICE, 0.0)
				ELSE ISNULL(rec.PRICEPORTION01, 0.0)
			END
			+ ISNULL(rec.PRICEPORTION02, 0.0) + ISNULL(rec.PRICEPORTION03, 0.0) +
			ISNULL(rec.PRICEPORTION04, 0.0) + ISNULL(rec.PRICEPORTION05, 0.0) + ISNULL(rec.PRICEPORTION06, 0.0)
		) AS TOTALPRICE,

		CASE
            WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN 10 -- Material
            WHEN rec.LGM_SUNDRYSERVICE_FK IS NOT NULL THEN 7 -- Sundry Service
            ELSE 11 -- should not happen, in case then 11
        END AS LGM_PRICECALCTYPE_FK,
        CASE
            WHEN rec.MDC_MATERIAL_FK IS NOT NULL THEN 2 -- Material
            WHEN rec.LGM_SUNDRYSERVICE_FK IS NOT NULL THEN 3 -- Sundry Service
            ELSE 0 -- should not happen, in case then 0
        END AS LGM_BILLINGSHEETTYPE_FK,
        NULL AS ETM_PRICINGGROUP_FK,
        0 AS ISREADONLYOPQUANTITY,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL
    FROM
        dbo.LGM_DISPATCH_HEADER AS hrd
    INNER JOIN
        dbo.LGM_DISPATCH_STATUS AS hrdstat ON hrdstat.ID = hrd.LGM_DISPATCH_STATUS_FK
	INNER JOIN
        dbo.LGM_DISPATCH_RECORD AS rec ON rec.LGM_DISPATCH_HEADER_FK= hrd.ID
    LEFT JOIN
        dbo.LGM_JOB AS job1 ON job1.ID = hrd.LGM_JOB1_FK
		                     AND job1.ISPRICINGNONPLANTBYSHEET = 1
                             AND job1.ISLIVE = 1

    LEFT JOIN
        dbo.LGM_JOB AS job2 ON job2.ID = hrd.LGM_JOB2_FK
                             AND job2.ISPRICINGNONPLANTBYSHEET = 1
                             AND job2.ISLIVE = 1
    CROSS APPLY (
        SELECT TOP 1 ID AS DEFAULT_STATUS_ID
        FROM dbo.LGM_BILLINGSHEETSTATUS
        WHERE ISDEFAULT = 1
        ORDER BY ID
    ) AS defBillingSheetStatus

    WHERE
        job2.PRJ_PROJECT_FK= @projectId
		AND rec.RES_REQUISITION_FK IS NULL
        AND hrd.LGM_DISPATCH_HEADER_TYPE_FK = 3
        AND hrdstat.ISDELIVERED= 1
        AND (rec.MDC_MATERIAL_FK IS NOT NULL OR rec.LGM_SUNDRYSERVICE_FK IS NOT NULL);

    RETURN;
END;
GO


--------- changes from Matthias'part-------------------------------
IF EXISTS (
    SELECT 1 
    FROM sys.foreign_keys 
    WHERE name = 'LGM_BILLINGSHEET_FK02' 
      AND parent_object_id = OBJECT_ID('dbo.LGM_PRICEORIGININFO')
)
BEGIN
    ALTER TABLE [dbo].[LGM_PRICEORIGININFO]
    DROP CONSTRAINT [LGM_BILLINGSHEET_FK02];
END
GO


ALTER TABLE [dbo].[LGM_PRICEORIGININFO]  WITH CHECK ADD  CONSTRAINT [LGM_BILLINGSHEET_FK02] FOREIGN KEY(LGM_BILLINGSHEET_FK)
REFERENCES [dbo].[LGM_BILLINGSHEET] (ID)
ON DELETE CASCADE;
GO