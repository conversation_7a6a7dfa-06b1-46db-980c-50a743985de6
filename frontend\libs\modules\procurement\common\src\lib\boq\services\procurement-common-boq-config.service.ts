import { inject, Injectable } from '@angular/core';
import { BoqCompositeConfigService, IBoqCompositeCompleteEntity, IBoqParentCompleteEntity, IBoqParentEntity } from '@libs/boq/main';
import { EntityDomainType, IConcreteEntitySchemaProperty, IEntitySchema } from '@libs/platform/data-access';
import { AllKeys, IInitializationContext, PlatformLazyInjectorService, prefixAllTranslationKeys, Translatable } from '@libs/platform/common';
import { ContainerLayoutConfiguration } from '@libs/ui/business-base';
import { IPrcBoqExtendedEntity } from '@libs/procurement/interfaces';
import { ProcurementSharedLookupOverloadProvider } from '@libs/procurement/shared';
import { ControllingSharedControllingUnitLookupProviderService } from '@libs/controlling/shared';
import { FieldOverloadSpec, ILookupContext } from '@libs/ui/common';
import { IControllingUnitLookupEntity } from '@libs/controlling/interfaces';
import { ProcurementCommonBoqDataServiceBase } from './base/procurement-common-boq.service';
import { IBoqCompositeEntity } from '@libs/boq/interfaces';
import { IPrcBoqEntityContext } from '../model';

@Injectable({ providedIn: 'root' })
export class ProcurementCommonBoqConfigService<
	PT extends IBoqParentEntity,
	PU extends IBoqParentCompleteEntity,
	T extends IPrcBoqExtendedEntity = IPrcBoqExtendedEntity,
	U extends IBoqCompositeCompleteEntity = IBoqCompositeCompleteEntity,
> extends BoqCompositeConfigService<T> {
	protected readonly lazyInjector = inject(PlatformLazyInjectorService);
	protected readonly controllingUnitLookupProvider = inject(ControllingSharedControllingUnitLookupProviderService);

	protected getPrcBoqProperties(): { [key: string]: IConcreteEntitySchemaProperty } {
		return {
			'PrcBoq.PackageFk': { domain: EntityDomainType.Integer, mandatory: false },
			'PrcBoq.MdcControllingunitFk': { domain: EntityDomainType.Integer, mandatory: false },
		};
	}

	protected properties = {
		...this.getBoqItemProperties(),
		...this.getBoqHeaderProperties(true),
		...this.getPrcBoqProperties(),
		Vat: { domain: EntityDomainType.Money, mandatory: false },
		VatOc: { domain: EntityDomainType.Money, mandatory: false },
	};

	public get labels(): { [key: string]: Translatable } {
		return this.getLabels();
	}

	protected override getBoqHeaderProperties(includeBackups: boolean = false) {
		const properties = super.getBoqHeaderProperties(includeBackups) as { [key in AllKeys<IBoqCompositeEntity>]?: IConcreteEntitySchemaProperty };

		// Remove the specific property
		delete properties['BoqHeader.IsGCBoq'];

		return properties;
	}

	protected getPrcBoqLabels() {
		return prefixAllTranslationKeys('cloud.common.', {
			'PrcBoq.PackageFk': 'entityPackageCode',
			'PrcBoq.MdcControllingunitFk': 'entityControllingUnitCode',
		});
	}

	protected override getLabels(): { [key: string]: Translatable } {
		return {
			...super.getLabels(),
			...this.getPrcBoqLabels(),
			...prefixAllTranslationKeys('procurement.common.', {
				Vat: 'entityVat',
				VatOc: 'entityVatOc',
			}),
		};
	}

	protected getPrcBoqEntityContext(entity: T): IPrcBoqEntityContext {
		return {
			controllingUnitFk: entity.PrcBoq?.MdcControllingunitFk,
		};
	}

	protected async provideControllingUnitOverload(dataService: ProcurementCommonBoqDataServiceBase<PT, PU, T, U>, ctx: IInitializationContext): Promise<FieldOverloadSpec<T>> {
		const cuOverload = await this.controllingUnitLookupProvider.generateControllingUnitLookup<T>(ctx, {
			checkIsAccountingElement: true,
			projectGetter: (e) => dataService.getHeaderContext().projectFk,
			controllingUnitGetter: (e) => this.getPrcBoqEntityContext(e).controllingUnitFk,
			lookupOptions: {
				showClearButton: true,
				serverSideFilter: {
					key: 'prc.con.controllingunit.by.prj.filterkey',
					execute: (context: ILookupContext<IControllingUnitLookupEntity, T>) => {
						return {
							ByStructure: true,
							ExtraFilter: true,
							PrjProjectFk: dataService.getHeaderContext().projectFk,
						};
					},
				},
			},
		});

		return {
			...cuOverload,
			additionalFields: [
				{
					displayMember: 'DescriptionInfo.Translated',
					label: {
						key: 'cloud.common.entityControllingUnitDesc',
					},
					column: true,
					row: false,
					singleRow: true,
				},
			],
		};
	}

	protected async getPrcBoqOverloads(
		dataService: ProcurementCommonBoqDataServiceBase<PT, PU, T, U>,
		ctx: IInitializationContext,
	): Promise<{
		[key: string]: FieldOverloadSpec<T>;
	}> {
		return {
			'PrcBoq.PackageFk': ProcurementSharedLookupOverloadProvider.providePackageReadonlyLookupOverload(false),
			'PrcBoq.MdcControllingunitFk': await this.provideControllingUnitOverload(dataService, ctx),
		};
	}

	public async getPrcBoqLayoutConfiguration(dataService: ProcurementCommonBoqDataServiceBase<PT, PU, T, U>, ctx: IInitializationContext): Promise<ContainerLayoutConfiguration<T>> {
		const prcBoqOverloads = await this.getPrcBoqOverloads(dataService, ctx);

		return {
			groups: this.getLayoutGroups(),
			labels: this.getLabels(),
			additionalOverloads: {
				...this.getOverloads(),
				...prcBoqOverloads,
			},
		};
	}

	public getBoqScheme(): IEntitySchema<T> {
		return this.getSchema('IPrcBoqExtendedEntity');
	}
}
