import { EntityInfo } from '@libs/ui/business-base';

import { MdcProductDescriptionSharedLayout } from '@libs/productionplanning/shared';
import { IPpsItem2MdcMaterialProductDescriptionEntity } from '../models';
import { PpsUpstream2MaterialProductDescriptionService } from '../../services/upstream-item/pps-upstream2-material-product-description.service';
import { PpsUpstream2MaterialProductDescriptionValidationService } from '../../services/upstream-item/pps-upstream2-material-product-description-validation.service';


export const PPS_UPSTREAM_MATERIAL_PRODUCT_DESCRIPTION_ENTITY_INFO = EntityInfo.create<IPpsItem2MdcMaterialProductDescriptionEntity>({
	grid: false,
	form: {
		containerUuid: '311110394b224dd392b69c5b60fe4e80',
		title: {key: 'productionplanning.item.upstreamItem.productTemplateDetail'},

	},
	dataService: ctx => ctx.injector.get(PpsUpstream2MaterialProductDescriptionService),
	validationService: ctx => ctx.injector.get(PpsUpstream2MaterialProductDescriptionValidationService),
	dtoSchemeId: {moduleSubModule: 'ProductionPlanning.PpsMaterial', typeName: 'MdcProductDescriptionDto'},
	permissionUuid: '211110394b224dd392b69c5b60fe4e80',
	layoutConfiguration: ctx => new MdcProductDescriptionSharedLayout().generateLayout(ctx)
});