/*
 * $Id$
 * Copyright (c) RIB Software SE
 */

(function (){
	'use strict';
	/* global globals, _ */

	const moduleName = 'awp.main';

	angular.module(moduleName).factory('awpMainPackageContainerFactoryService', [
		'$http','$q', 'platformDataServiceFactory', 'awpProjectMainListDataService', 'awpMainServicePackagesProcessorService','awpMainPackageInfoType',
		'basicsLookupdataLookupDescriptorService', 'platformModalService', 'awpMainPackageInfoProcessorService','awpPackageType',
		'awpMainMaterialPackagesProcessorService','platformGridAPI', 'procurementPackageExternalApiService', 'basicsCommonProcurementConfigurationMode',
		'awpMainItemAssignmentDataFactoryService','cloudCommonGridService', 'platformDataServiceDataProcessorExtension', 'platformDataServiceSelectionExtension',
		'platformPermissionService',
		function ($http, $q, platformDataServiceFactory, parentService, awpMainServicePackagesProcessorService, packageInfoType,
			basicsLookupdataLookupDescriptorService, platformModalService, awpMainPackageInfoProcessorService, awpPackageType,
			awpMainMaterialPackagesProcessorService, platformGridAPI, procurementPackageExternalApiService, basicsCommonProcurementConfigurationMode,
			awpMainItemAssignmentDataFactoryService, cloudCommonGridService, platformDataServiceDataProcessorExtension, platformDataServiceSelectionExtension,
			platformPermissionService){
			let factoryService = {};

			factoryService.createDataService = function (packageType){

				let lastSelectedFilterKey = 'allItems',
					scope = null;

				let containerConfig = {
					hierarchicalNodeItem: {
						module: moduleName,
						serviceName: packageType === awpPackageType.ServicePackage ? 'awpMainServicePackagesDataService' : 'awpMainMaterialPackagesDataService',
						entityNameTranslationID: packageType === awpPackageType.ServicePackage ? 'awp.main.servicePackages' : 'awp.main.materialepackage',
						httpRead: {
							route: globals.webApiBaseUrl + 'procurement/package/package/',
							endRead: 'getpackagestructure',
							usePostForRead: true,
							initReadData: function (readData) {
								let project = parentService.getSelected();
								if (project) {
									readData.ProjectId = project.Id;
									readData.IncludeNonActiveItems = true;
									readData.PackageType = packageType === awpPackageType.ServicePackage ? 1 : 2;
								}
							}
						},
						httpUpdate: {route: globals.webApiBaseUrl + 'awp/main/', endUpdate: 'update'},
						entityRole: {
							node: {
								codeField: packageType === awpPackageType.ServicePackage ? 'Reference': 'Itemno',
								descField: packageType === awpPackageType.ServicePackage ? 'BriefInfo': 'Description',
								itemName: packageType === awpPackageType.ServicePackage ? 'ServicePackages': 'MaterialPackages',
								parentService: parentService,
								moduleName: packageType === awpPackageType.ServicePackage ? 'awp.main.servicePackages': 'awp.main.materialPackages'
							}
						},
						presenter: {
							tree: {
								parentProp: 'ParentFk',
								childProp: 'Children',
								incorporateDataRead: function incorporateDataRead(readData, data){
									awpMainPackageInfoProcessorService.restoreTempId(packageType);
									readData = awpMainPackageInfoProcessorService.convertPackageInfo(readData, packageType);

									if(scope) {
										updateFilterToolbar(scope, false);
										scope.tools.update();
									}

									return data.handleReadSucceeded(readData, data);
								}
							}
						},
						actions: {
							delete: false,
							create: 'hierarchical'
						},
						dataProcessor: [packageType === awpPackageType.ServicePackage ? awpMainServicePackagesProcessorService :awpMainMaterialPackagesProcessorService],
						translation: {
							uid: packageType === awpPackageType.ServicePackage ? 'awpMainServicePackagesDataService' : 'awpMainMaterialPackagesDataService',
							title: packageType === awpPackageType.ServicePackage ? 'awp.main.servicePackages' : 'awp.main.materialPackages',
							columns: [{header: 'cloud.common.descriptionInfo', field: 'BriefInfo'}]
						},
						entitySelection: {supportsMultiSelection: false}
					}
				};

				let serviceContainer = platformDataServiceFactory.createNewComplete(containerConfig);

				let service = serviceContainer.service;

				if(packageType === awpPackageType.ServicePackage){
					serviceContainer.data.handleOnCreateSucceeded = function handleOnCreateSucceededInTree(newItem, data) {
						var newItems = [];
						data.flatten([newItem], newItems, data.treePresOpt.childProp);
						_.forEach(newItems, function (item) {
							platformDataServiceDataProcessorExtension.doProcessItem(item, data);
							data.itemList.push(item);
						});

						return platformDataServiceSelectionExtension.doSelect(newItem, data);
					};
				}

				function getContainer(){
					return serviceContainer;
				}

				service.lastSelectedFilterKey = function(filterKey){
					if(_.isString(filterKey)){
						lastSelectedFilterKey = filterKey;
					}
					return lastSelectedFilterKey;
				};

				function isFilterAllItems(){
					return !lastSelectedFilterKey ||  lastSelectedFilterKey === 'allItems';
				}

				function executeFilter(filterKey, fromToolBar){
					let filter = null;

					if(!filterKey){
						filterKey = lastSelectedFilterKey;
					}

					filterKey = filterKey || 'allItems';

					if(filterKey === 'itemWithoutAssigned') {
						filter = function (item) {
							return item.UnAssignmentCount && item.UnAssignmentCount > 0;
						};
					}else if(filterKey === 'itemWithAssigned') {
						filter = function (item) {
							return item.AssignmentCount && item.AssignmentCount > 0;
						};
					}

					service.lastSelectedFilterKey(filterKey);
					serviceContainer.data.itemFilterEnabled = filterKey !== 'allItems';
					serviceContainer.data.itemFilter = filter;
					if(fromToolBar){
						serviceContainer.data.listLoaded.fire();
					}else{
						let tree = service.getTree();
						platformGridAPI.items.data(scope.gridId, tree);
					}
				}

				function refreshAllPackage(){
					let project = parentService.getSelected();
					if(!project){
						return;
					}

					if(scope){scope.isLoading = true;}
					return $http.post(serviceContainer.data.httpReadRoute + serviceContainer.data.endRead, {
						ProjectId: project.Id,
						IncludeNonActiveItems: true,
						PackageType: packageType === awpPackageType.ServicePackage ? 1 : 2
					}).then(function (response){
						let roots = awpMainPackageInfoProcessorService.convertPackageInfo(response.data, packageType);

						let existList = _.filter(service.getList(), {TypeFk: packageInfoType.Package}),
							expandedSubPackage = [];
						if(existList && existList.length > 0){
							_.forEach(existList, oldPackage => {
								// merge same package info if it exists
								let newPackage = _.find(roots, {Id: oldPackage.Id});
								if(newPackage && newPackage.Children && newPackage.Children.length>0){
									_.forEach(newPackage.Children, newSubPackage => {
										let oldSubPackage = _.find(oldPackage.Children,{Id: newSubPackage.Id});
										if(oldSubPackage && oldSubPackage.nodeInfo && !oldSubPackage.nodeInfo.collapsed){
											expandedSubPackage.push(newSubPackage);
										}
									});
								}
							});
						}

						if(expandedSubPackage.length === 0) {
							serviceContainer.data.handleReadSucceeded(roots, serviceContainer.data);
							return [];
						}else{
							let promise = service.refreshSubPackages(expandedSubPackage);
							if(promise && promise.then){
								return awpMainItemAssignmentDataFactoryService.updateByTargetPackages(_.map(expandedSubPackage, 'PrcPackageFk')).then(() => {
									return promise.then(()=>{
										let promises = [];
										_.forEach(expandedSubPackage, sub => {
											promises.push(syncItemAssignmentStatus(sub.ParentFk, roots));
										});
										return $q.all(promises).then(() => {
											serviceContainer.data.handleReadSucceeded(roots, serviceContainer.data);
											if(service.setTargetRootBoqItemsAsync){
												let list = [];
												cloudCommonGridService.flatten(roots, list, 'Children');
												service.setTargetRootBoqItemsAsync(list);
											}

											return expandedSubPackage;
										});
									});
								});
							}else{
								serviceContainer.data.handleReadSucceeded(roots, serviceContainer.data);
								return  expandedSubPackage;
							}
						}
					}).finally(() => {
						if(scope){scope.isLoading = false;}
					});

				}

				function refreshSubPackage(subPackage){
					let project = parentService.getSelected();
					if(!project){
						return;
					}

					if(scope){scope.isLoading = true;}
					return $http.post(serviceContainer.data.httpReadRoute + serviceContainer.data.endRead, {
						ProjectId: project.Id,
						IncludeNonActiveItems: true,
						PackageType: packageType === awpPackageType.ServicePackage ? 1 : 2,
						PackageIds: [subPackage.ParentFk]
					}).then(function (response){
						let roots = _.filter(service.getList(), item => !item.ParentFk);

						let newPackage = _.find(response.data, d=>{
							return d.Packge.Id === subPackage.ParentFk;
						});
						if(newPackage && newPackage.SubPackageCompletes){
							let newSubPackage = _.find(newPackage.SubPackageCompletes, s=>{
								return s.SubPackage.Id === subPackage.Id;
							});
							if(newSubPackage && newSubPackage.PrcBoqExtendeds){
								subPackage.BoqHeaderIds = [];
								_.forEach(newSubPackage.PrcBoqExtendeds, boq => {
									subPackage.BoqHeaderIds.push(boq.BoqRootItem.BoqHeaderFk);
								});
							}
						}

						let promise = service.refreshSubPackages([subPackage]);
						if(promise && promise.then){
							return promise.then(()=>{

								let flatChild = [];
								cloudCommonGridService.flatten(subPackage.Children, flatChild, 'Children');
								serviceContainer.data.itemList = _.filter(serviceContainer.data.itemList, item=>{
									return !_.find(flatChild, c=> c.Id === item.Id);
								});

								subPackage.Children = subPackage.Children || [];
								if(subPackage.Children.length > 1 && packageType === awpPackageType.ServicePackage){
									let references = _.map(subPackage.Children, 'Reference');
									references.length = references.length -1;
									let lastOne = subPackage.Children[subPackage.Children.length - 1];
									if(references.indexOf(lastOne.Reference) >= 0){
										let reference = lastOne.Reference;
										while (references.indexOf(reference) >= 0){
											reference = incrementSuffix(reference);
										}
										lastOne.Reference = reference;

										$http.post(globals.webApiBaseUrl + 'awp/main/updateservicepackageboq', {
											Id: lastOne.Id,
											Reference: reference,
											ParentFk: lastOne.BoqHeaderFk
										});
									}
								}

								_.forEach(subPackage.Children||[], boq => {
									appendNewData(boq, subPackage);
								});

								syncItemAssignmentStatus(subPackage.ParentFk).then(() => {
									if(scope){
										service.executeFilter();
										if(isFilterAllItems()){
											platformGridAPI.rows.expandAllSubNodes(scope.gridId, subPackage);
										}else {
											let realSubPackage = getRealGridData(subPackage);
											if(realSubPackage){
												platformGridAPI.rows.expandAllSubNodes(scope.gridId, realSubPackage);
											} else{
												platformGridAPI.rows.expandNode(scope.gridId, subPackage);
											}
										}
									}
								});

								return [subPackage];
							});
						}else{
							serviceContainer.data.handleReadSucceeded(roots, serviceContainer.data);
							return  [subPackage];
						}
					}).finally(() => {
						if(scope){scope.isLoading = false;}
					});
				}

				function expandSubPackageAsync(subPackages){
					if(!subPackages || subPackages.length <= 0 || !scope){
						return;
					}

					scope.isLoading = true;
					let promise = service.refreshSubPackages(subPackages);
					if(promise && promise.then){
						promise.then(() => {
							let promises = [];
							_.forEach(subPackages, sub => {
								promises.push(syncItemAssignmentStatus(sub.ParentFk));
							});

							$q.all(promises).then(() => {
								_.forEach(subPackages, subPackage => {
									_.forEach(subPackage.Children||[], boq => {
										appendNewData(boq, subPackage);
									});
									platformGridAPI.rows.expandNode(scope.gridId, subPackage);
								});
								service.executeFilter();

								if(service.setTargetRootBoqItemsAsync){
									service.setTargetRootBoqItemsAsync();
								}

							});

						}).finally(()=>{
							scope.isLoading = false;
						});
					}else{
						scope.isLoading = false;
					}
				}

				function createItem(){
					let selected = service.getSelected();
					let type = !selected ? packageInfoType.Package : selected.TypeFk;
					createNewItem(type);
				}

				function createChildItem(){
					let selected = service.getSelected();
					if(!selected){return;}
					let type = selected.TypeFk === packageInfoType.Package ? packageInfoType.SubPackage : selected.TypeFk === packageInfoType.SubPackage ? packageInfoType.BoqHeader : null;
					if(!type){return;}

					createNewItem(type);
				}

				function deleteSelection(){

				}

				function syncItemAssignmentStatus(packageId, roots){
					return awpMainItemAssignmentDataFactoryService.getByPackages([packageId]).then(res =>{
						if(res){
							let maps = new Map();
							_.forEach(res, item=>{
								let key = getMapKey(item);
								if(!key){return;}

								if (maps.has(key)) {
									maps.get(key).push(item);
								} else {
									maps.set(key, [item]);
								}
							});

							let pack = _.find(roots || service.getList(), {Id:packageId, TypeFk: packageInfoType.Package});
							if(!pack){
								return;
							}
							let list = [];
							cloudCommonGridService.flatten([pack], list, 'Children');

							_.forEach(list, item => {
								let key = null;
								switch (item.TypeFk){
									case packageInfoType.BoqPosition:
										key = JSON.stringify([packageId, item.BoqHeaderFk, item.Id]);
										break;
									case packageInfoType.PrcItem:
										key = JSON.stringify([packageId, item.Id]);
										break;
								}

								if (key && maps.has(key)){
									let ms = maps.get(key);
									item.AssignmentCount = ms.length;
									item.UnAssignmentCount = 0;
									item.splitPackageItem = !!(_.find(ms, m=> m.PrcItemAssignmentFk));
								}else{
									item.AssignmentCount = 0;
									item.UnAssignmentCount = 1;
								}
							});
							updateParent(pack);
						}
					});

					function getMapKey(item){
						switch (packageType){
							case awpPackageType.ServicePackage:
								return item.BoqItemFk ? JSON.stringify([item.PrcPackageFk, item.BoqHeaderFk, item.BoqItemFk]) : null;
							case awpPackageType.MaterialPackage:
								return item.PrcItemFk ? JSON.stringify([item.PrcPackageFk, item.PrcItemFk]) : null;
						}
					}

					function updateParent(node){
						if (!node.Children || node.Children.length === 0) {
							return;
						}

						let totalAssignment = 0;
						let totalUnAssignment = 0;

						for (const child of node.Children) {
							updateParent(child);
							totalAssignment += child.AssignmentCount;
							totalUnAssignment += child.UnAssignmentCount;
						}
						node.AssignmentCount = totalAssignment;
						node.UnAssignmentCount = totalUnAssignment;
					}
				}

				function createNewItem(itemType){
					switch (itemType){
						case packageInfoType.Package:
							createNewPackage();
							break;
						case packageInfoType.SubPackage:
							createNewSubPackage();
							break;
						case packageInfoType.BoqHeader:
							createBoqHeader();
							break;
					}
				}

				function createNewPackage(){
					let project = parentService.getSelected();
					if(!project){
						return;
					}

					const createPackageApi = procurementPackageExternalApiService.get();
					createPackageApi.createPackageFromExternalModule({
						projectId: project.Id,
						configurationMode: packageType === awpPackageType.ServicePackage ? basicsCommonProcurementConfigurationMode.service : basicsCommonProcurementConfigurationMode.material,
						isProjectFkReadonly: true
					}).then(function(newPackage){
						if(newPackage){
							let item = {
								Packge:newPackage
							};

							if(newPackage.Package2HeaderComplete){
								newPackage.Package2HeaderComplete.SubPackage = newPackage.Package2HeaderComplete.Package2Header;
								item.SubPackageCompletes = [newPackage.Package2HeaderComplete];
								if(newPackage.Package2HeaderComplete.PrcBoqExtended){
									newPackage.Package2HeaderComplete.PrcBoqExtendeds = [newPackage.Package2HeaderComplete.PrcBoqExtended];
								}
							}

							let dto = awpMainPackageInfoProcessorService.convertPackageInfo([item], packageType)[0];
							appendNewData(dto);
							service.executeFilter();
						}
					});
				}

				function createNewSubPackage(){

				}

				function createBoqHeader(subPackage, sourceBoqHeaderFk){
					let project = parentService.getSelected(),
						parentItem = subPackage || service.getSelected();
					if(!project || !parentItem || (parentItem.TypeFk !== packageInfoType.SubPackage && parentItem.TypeFk !== packageInfoType.BoqHeader)){
						return;
					}

					if(parentItem.TypeFk === packageInfoType.BoqHeader){
						parentItem = _.find(service.getList(), {Id: parentItem.ParentFk});
					}
					if(!parentItem){
						return;
					}

					const createPackageApi = procurementPackageExternalApiService.get();
					return createPackageApi.createPackageBoqFromExternalModule({parentEntity: {
						Id: parentItem.Id,
						PrcHeaderFk: parentItem.PrcHeaderFk,
						PrcPackageFk: parentItem.ParentFk,
						CurrencyFk: parentItem.CurrencyFk},
					SourceBoqHeaderIdToCopy: sourceBoqHeaderFk}).then(function (res){
						if(res && res.BoqRootItem){
							let dtos = awpMainPackageInfoProcessorService.convertBoqInfo([res.BoqRootItem], parentItem);
							let dto = dtos[dtos.length-1];
							parentItem.BoqHeaderIds = parentItem.BoqHeaderIds || [];
							parentItem.BoqHeaderIds.push(dto.BoqHeaderFk);
							appendNewData(dto, parentItem);
							return service.addTargetRootBoqItemsAsync([dto]).then(()=>{
								return dto;
							});
						}
					});
				}

				function appendNewData(newItem, parent){
					if(parent) {
						parent.Children = parent.Children || [];
						parent.Children = _.filter(parent.Children, c => c.Id !== newItem.Id);
						if(parent.BoqItems){
							parent.BoqItems = parent.Children;
						}
					}
					if (serviceContainer.data.onCreateSucceeded) {
						serviceContainer.data.onCreateSucceeded(newItem, serviceContainer.data, {parent: parent});
					}
				}

				function appendToolbar($scope, loadTreeChildrenAsync){
					$scope.tools.items.push({
						id: 'itemFilterTypes',
						sort: 199,
						filterIconsGroup: 'itemFilterTypes',
						type: 'sublist',
						list: {
							cssClass: 'radio-group',
							showTitles: true,
							items: [
								{
									id: 'all_item',
									caption: 'awp.main.allItems',
									type: 'radio',
									value: 'allItems',
									iconClass: 'tlb-icons ico-line-item-filter-off',
									fn: function () {
										service.executeFilter('allItems', true);
									},
									disabled: function () {
										return false;
									}
								},
								{
									id: 'with_package',
									caption: 'awp.main.itemWithAssignment',
									type: 'radio',
									value: 'itemWithPackage',
									iconClass: 'tlb-icons ico-line-item-filter',
									fn: function () {
										service.executeFilter('itemWithAssigned', true);
									},
									disabled: function () {
										return false;
									}
								},
								{
									id: 'without_package',
									caption: 'awp.main.itemWithoutAssignment',
									type: 'radio',
									value: 'itemWithoutPackage',
									iconClass: 'tlb-icons ico-line-item-not-linked',
									fn: function () {
										service.executeFilter('itemWithoutAssigned', true);
									},
									disabled: function () {
										return false;
									}
								}
							]
						}
					});

					$scope.tools.items.push({
						id: 'd3',
						sort: 199,
						type: 'divider'
					});

					$scope.tools.items.push({
						id: 't10',
						sort: 199,
						caption: 'cloud.common.toolbarRefresh',
						type: 'item',
						iconClass: 'tlb-icons ico-refresh',
						disabled: function () {
							return false;
						},
						fn: function refresh() {
							let permission = service.refreshAllPackage();
							if(permission && permission.then){
								permission.then(function (res){
									if(res && res.length>0){
										_.forEach(res, p=> {
											if(!p.nodeInfo){
												return;
											}
											platformGridAPI.rows.expandNode($scope.gridId, p);
										});
									}
								});
							}
						}
					});

					$scope.tools.items.push({
						id: 't11',
						sort: 199,
						caption: 'awp.main.refreshSelection',
						type: 'item',
						iconClass: 'tlb-icons ico-refresh-one',
						disabled: function () {
							let selected = service.getSelected();

							return !selected || selected.TypeFk !== packageInfoType.SubPackage;
						},
						fn: function refresh() {
							let selected = service.getSelected();
							if(selected && selected.TypeFk === packageInfoType.SubPackage) {

								let parent = getOriginalItem(selected);

								let flatChild = [];
								cloudCommonGridService.flatten(selected.Children, flatChild, 'Children');
								serviceContainer.data.itemList = _.filter(serviceContainer.data.itemList, item=>{
									return !_.find(flatChild, c=> c.Id === item.Id);
								});

								parent.Children = parent.BoqItems = [];
								loadTreeChildrenAsync(null, {item: parent});
							}
						}
					});

					$scope.tools.items.push({
						id: 'd5',
						sort: 199,
						type: 'divider'
					});

					$scope.tools.items.push({
						id: 'd4',
						sort: 45,
						type: 'divider'
					});

					$scope.tools.items.push({
						id: 't9',
						sort: 45,
						caption: 'cloud.common.toolbarCollapseAll',
						type: 'item',
						iconClass: 'tlb-icons ico-tree-collapse-all',
						fn: function collapseAll() {
							platformGridAPI.rows.collapseAllSubNodes($scope.gridId);
						}
					});

					$scope.tools.items.push({
						id: 't10',
						sort: 45,
						caption: 'cloud.common.toolbarExpandAll',
						type: 'item',
						iconClass: 'tlb-icons ico-tree-expand-all',
						fn: function expandAll() {
							platformGridAPI.rows.expandAllSubNodes($scope.gridId);

							let allList = service.getList();
							let selected = service.getSelected();
							let unLazyLoadSubPackages = _.filter(allList, d => {
								let result = d.TypeFk === packageInfoType.SubPackage && d.HasChildren && (!d.Children || d.Children.length <= 0);
								if(!result){ return result;}

								if(selected){
									// only expand the sub package which is under the selected package
									result = selected.TypeFk === packageInfoType.Package ? d.PrcPackageFk === selected.Id : d.PrcPackageFk === selected.PrcPackageFk;
								}

								return result;
							});
							expandSubPackageAsync(unLazyLoadSubPackages);
						}
					});
				}

				function updateFilterToolbar($scope,disabled){
					let filterToolbar = _.find($scope.tools.items, {id: 'itemFilterTypes'});
					_.forEach(filterToolbar.list.items, tool=>{
						tool.disabled = function (){
							return disabled;
						};
					});
				}

				function getOriginalItem(item){
					if(!item || !item.Id){
						return null;
					}

					if(isFilterAllItems()){
						// this case is for the item in the tree, which is not filtered
						return item;
					}

					return _.find(serviceContainer.data.itemList, {Id: item.Id});
				}

				function setScope($scope){
					scope = $scope;
				}

				function getRealGridData(item){
					if(!item || !item.Id){
						return null;
					}

					let list = platformGridAPI.items.filtered(scope.gridId) || [];

					return _.find(list, {Id: item.Id});
				}

				function hasDragDropPermission(){
					let permissionServiceId = '9e09290f6b7941078372ebefc9380229';
					let permissionMaterialId = '1ad7b75a2b7549659c948cfb7822a05e';
					if( packageType === awpPackageType.ServicePackage){
						return platformPermissionService.hasCreate(permissionServiceId)
							&& platformPermissionService.hasWrite(permissionServiceId);
					}
					if(packageType === awpPackageType.MaterialPackage) {
						return platformPermissionService.hasCreate(permissionMaterialId)
							&& platformPermissionService.hasWrite(permissionMaterialId);
					}
					return false;
				}


				function incrementSuffix(A) {
					const regex = /\((\d+)\)$/;
					const match = A.match(regex);

					if (match) {
						const number = parseInt(match[1], 10);
						const newNumber = number + 1;
						return A.replace(regex, `(${newNumber})`);
					} else {
						return `${A}(1)`;
					}
				}

				angular.extend(service, {
					getContainer: getContainer,
					executeFilter: executeFilter,
					refreshAllPackage: refreshAllPackage,
					refreshSubPackage:refreshSubPackage,
					createItem:createItem,
					createChildItem:createChildItem,
					deleteSelection: deleteSelection,
					syncItemAssignmentStatus: syncItemAssignmentStatus,
					appendToolbar: appendToolbar,
					setScope:setScope,
					appendNewData: appendNewData,
					getOriginalItem: getOriginalItem,
					isFilterAllItems: isFilterAllItems,
					getRealGridData: getRealGridData,
					hasDragDropPermission: hasDragDropPermission
				});

				return service;
			};

			return factoryService;
		}
	]);
})();