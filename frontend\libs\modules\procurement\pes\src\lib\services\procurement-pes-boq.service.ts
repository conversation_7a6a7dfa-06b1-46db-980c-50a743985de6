import { Injectable } from '@angular/core';
import { IDataServiceEndPointOptions, IDataServiceOptions, IDataServiceRoleOptions, ServiceRole } from '@libs/platform/data-access';
import { IPesBoqEntity } from '../model/entities/pes-boq-entity.interface';
import { ProcurementPesHeaderDataService } from './procurement-pes-header-data.service';
import { IPesHeaderEntity } from '../model/entities';
import { PesCompleteNew } from '../model/complete-class/pes-complete-new.class';
import { ProcurementCommonBoqDataServiceBase } from '@libs/procurement/common';
import { IPropertyChangedEvent } from '@libs/procurement/shared';
import { MainDataDto, skipNullMap } from '@libs/basics/shared';
import { PesBoqComplete } from '../model/complete-class/pes-boq-complete.class';

/** Prc Pes boq list data service */
@Injectable({ providedIn: 'root' })
export class ProcurementPesBoqDataService extends ProcurementCommonBoqDataServiceBase<IPesHeaderEntity, PesCompleteNew, IPesBoqEntity, PesBoqComplete> {
	public constructor(private headerDataService: ProcurementPesHeaderDataService) {
		const options: IDataServiceOptions<IPesBoqEntity> = {
			apiUrl: 'procurement/pes/boq',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: false,
			},
			roleInfo: <IDataServiceRoleOptions<IPesBoqEntity>>{
				role: ServiceRole.Node,
				itemName: 'PesBoq',
				parent: headerDataService,
			},
		};
		super(options, headerDataService);
	}

	protected override provideLoadPayload(): object {
		const pesHeader = this.headerDataService.getSelectedEntity();

		if (!pesHeader) {
			throw new Error('No pes header is selected!');
		}

		return {
			mainItemId: pesHeader.Id,
		};
	}

	protected override onLoadSucceeded(loaded: IPesBoqEntity[]): IPesBoqEntity[] {
		return new MainDataDto<IPesBoqEntity>(loaded).Main;
	}

	public override isParentFn(pesHeader: IPesHeaderEntity, pesBoq: IPesBoqEntity): boolean {
		return pesHeader.Id == pesBoq.PesHeaderFk;
	}

	public override createUpdateEntity(modified: IPesBoqEntity): PesBoqComplete {
		return {
			MainItemId: modified?.Id ?? 0,
			EntitiesCount: modified ? 1 : 0,
			PesBoq: modified,
		};
	}

	public override registerNodeModificationsToParentUpdate(complete: PesCompleteNew, modified: PesBoqComplete[], deleted: IPesBoqEntity[]) {
		complete.PesBoqToSave = modified;
		complete.PesBoqToDelete = deleted;
	}

	public override getSavedEntitiesFromUpdate(complete: PesCompleteNew): IPesBoqEntity[] {
		return skipNullMap(complete.PesBoqToSave, (e) => e.PesBoq);
	}

	public override getSavedCompletesFromUpdate(parentUpdate: PesCompleteNew): PesBoqComplete[] {
		return parentUpdate.PesBoqToSave ?? [];
	}

	protected onHeaderControllingUnitChanged(e: IPropertyChangedEvent<IPesHeaderEntity>) {
		this.getList().forEach((entity) => {
			entity.ControllingUnitFk = e.newValue as number;
			this.setModified(entity);
			this.updateEntities([entity]);
		});
	}
}
