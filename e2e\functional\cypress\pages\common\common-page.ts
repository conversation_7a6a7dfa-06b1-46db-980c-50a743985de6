/// <reference types="cypress" />

import { _mainView, _sidebar, _modalView, _validate, _common } from '../index';
import { cnt, btn, app, commonLocators, sidebar } from '../../locators';
import { DataCells } from '../interfaces';
import { forIn } from 'cypress/types/lodash';
import CommonLocators from 'cypress/locators/common-locators';
import { wrap } from 'module';
import { match } from 'assert';

export class CommonPage {
	element: string = '';

	//! Container related common method

	generateRandomString(length: number) {
		let result = '';
		const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		const charactersLength = characters.length;
		for (let i = 0; i < length; i++) {
			result += characters.charAt(Math.floor(Math.random() * charactersLength));
		}
		let finalString = result.toUpperCase();
		finalString += Math.floor(Math.random() * 1000); // Append a random number between 0 and 999
		return finalString;
	}

	generateRandomString_withExactLength(exactStringLength: number) {
		//returns exact length of string which is asked.
		let result = '';
		const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
		const charactersLength = characters.length;
		for (let i = 0; i < exactStringLength; i++) {
			result += characters.charAt(Math.floor(Math.random() * charactersLength));
		}
		return result;
	}

	appendStringWithNumber_randomData(data:string){
		return 	(data +"-" + Math.floor(Math.random() * 1000)); // Append a random number between 0 and 999;
	}

	waitForLoaderToDisappear() {
		cy.get('#loading-bar')
			.should('not.exist', { timeout: 50000, interval: 1000 })
			.then(() => {
				cy.log('Loader has disappeared successfully.');
			});

		cy.get('body').then(($body) => {
			if ($body.find('#loading-bar').length > 0) {
				throw new Error('Loader did not disappear within the expected time.');
			}
		});
	}

	toggleSidebar(sideBarClass: string) {
		cy.wait(3000)
		cy.get(`#${app.Layouts.MAIN_CONTAINER}`).then(($ele) => {
			this.waitForLoaderToDisappear();
			if ($ele.find(commonLocators.CommonElements.SIDEBAR).length > 0) {
				cy.log('Sidebar already expanded....');
				cy.get('body').then(($body) => {
					if ($body.find(`[id='${sideBarClass}']` + commonLocators.CommonElements.CLASS_SELECTED).length > 0) {
						cy.log('Sidebar is present');
					} else {
						_sidebar.findSidebar().sidebarIndicator(sideBarClass).clickIn();
					}
				});
			} else {
				cy.log('Sidebar not expanded. Expanding...');
				_sidebar.findSidebar().sidebarIndicator(sideBarClass).clickIn();
			}
		});
		return this;
	}

	clear_searchInSidebar(): void {
		cy.wait(1000); // This wait is required as the script was getting failed

		cy.get(`#${sidebar.sideBarList.SIDEBAR_SEARCH}`)
			.find(`#${sidebar.fields.GOOGLE_SEARCH_INPUT}`)
			.clear()
			.then(($input) => {
				if ($input.val() !== '') {
					throw new Error('Search input field was not cleared'); // Explicit error
				}
				cy.log('Successfully cleared the search input in the sidebar.');
			});

		cy.wait(1000); // This wait is required as the script was getting failed

		cy.get(`#${sidebar.sideBarList.SIDEBAR_SEARCH}`)
			.find(`[class*="${sidebar.icoButtons.SEARCH}"]`)
			.click()
			.then(() => {
				cy.log('Successfully clicked the search button in the sidebar.');
			});
	}

	pinnedItem() {
		_mainView.clear_ObjectElement();
		cy.wait(2000)
		_mainView.toolbar(app.ContainerElements.SUBVIEW_HEADER_TOOLBAR).findButton(btn.ToolBar.ICO_SET_PRJ_CONTEXT).clickIn();
		return this;
	}

	delete_pinnedItem() {
		_sidebar.delete_pinnedItem();
		return this;
	}

	maximizeContainer(containerUUID: string) {
		cy.wait(1000);
		cy.get(commonLocators.CommonElements.BODY).then(($body) => {
			if ($body.find('.cid_' + containerUUID + ` [class*='${app.Layouts.SUB_VIEW_HEADER_TOOLBAR}'] [class*='${btn.ToolBar.ICO_MINIMIZED_2}']`).length > 0) {
				cy.log('Container already maximized');
			} else {
				_mainView.findModuleClientArea().findAndShowContainer(containerUUID).toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR).findButton(btn.ToolBar.ICO_MAXIMIZED_2).clickIn();
			}
		});
	}

	minimizeContainer(containerUUID: string) {
		cy.wait(1000);
		cy.get('body').then(($body) => {
			if ($body.find('.cid_' + containerUUID + ` [class*='${app.Layouts.SUB_VIEW_HEADER_TOOLBAR}'] [class*='${btn.ToolBar.ICO_MAXIMIZED_2}']`).length > 0) {
				cy.log('Container already minimized');
			} else {
				_mainView.findModuleClientArea().findAndShowContainer(containerUUID).toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR).findButton(btn.ToolBar.ICO_MINIMIZED_2).clickIn();
			}
		});
	}

	search_fromSidebar(searchType: string, searchValue: string) {
		cy.wait(5000)
		_sidebar.searchBySidebar(searchType, searchValue);
		return this;
	}

	add_projectFavorites(inputTextClass: string, projectNumber: string) {
		cy.get('#sidebar-prjNavi')
			.find(` button[title='Add Project to Navigator']`)
			.click()
			.then(() => {
				_modalView.findModal().findTextInput(inputTextClass).first().clear().type(projectNumber);
				_modalView.findModal().findButton(btn.IconButtons.ICO_SEARCH).wrapElements().first().click();
				_modalView.findModal().wrapElements().find(`[class*='${app.SubContainerLayout.COLUMN_ID}']`).contains(projectNumber).click();
				_modalView.findModalBody().wrapElements().contains(projectNumber).first().click({ force: true });
				_modalView.findModal().acceptButton(btn.ButtonText.OK);
				_common.waitForLoaderToDisappear();
			});
	}

	selectData_fromProjectFavorites(project: string, type: string, value: string) {
		cy.get('#sidebar-prjNavi')
			.find(`button[class*='nodetitle']`)
			.contains(project)
			.closest(`[class*='treelist']`)
			.within(($element) => {
				// Check for ico-tree-expand and click if it exists
				cy.wrap($element).then(($icon) => {
					if ($icon.find(`[class*='ico-arrow-down']`).length > 0) {
						cy.wrap($icon).click();
					}
				});
				cy.get(`[class*='ico-arrow-right']`).click();

				cy.wrap($element)
					.closest(`[class*='treelist']`)
					.find(`button[class*='nodetitle']`)
					.contains(type)
					.closest(`[class*='navielement projectname']`)
					.within(($element) => {
						cy.wrap($element).then(($icon) => {
							if ($icon.find(`[class*='ico-arrow-down']`).length > 0) {
								cy.wrap($icon).click();
							}
						});
						//cy.get(`[class*='ico-arrow-right']`).click();
					});
			});
			cy.wait(1000)
		cy.get('#sidebar-prjNavi').find(`[class*='title']`).contains(value).click({force:true});
		_common.waitForLoaderToDisappear();
	}

	openTab(tabID: string): Cypress.Chainable<any> {
		cy.wait(2000);
		Cypress.env('groupContainerArray', []);
		const chain = cy.wrap(_mainView.findModuleClientArea().tabBar().findTab(tabID).clickIn());
		cy.wait(5000);
		return chain;
	}

	openDesktopTile(tileClass: string) {
		Cypress.env('groupContainerArray', []);
		_mainView.findModuleClientArea().findTile(tileClass).clickIn();
	}

	clickOn_toolbarButton(containerUUID: string, buttonClass: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR).findButton(buttonClass).clickIn();
		this.waitForLoaderToDisappear();
	}

	clickOn_expandCollapseMenu_columnById(containerUUID: string, buttonClass: string,id:string, containerPosition?: number) {
		_common.select_allContainerData(containerUUID)
		_common.waitForLoaderToDisappear()
		_mainView.findModuleClientArea()
					.findAndShowContainer(containerUUID, containerPosition)
					.wrapElements()
					.within(()=>{
						cy.get(`button[class*='${buttonClass}']`)
							.click()
					})
					.then(()=>{
						cy.get(`.popup-container `)
							.find(`#${id}`)
							.click()

					})

		this.waitForLoaderToDisappear();
	}

	clickOn_expandCollapseMenu_columnByLevels(containerUUID: string, buttonClass: string,level:string, containerPosition?: number) {
		_common.select_allContainerData(containerUUID)
		_common.waitForLoaderToDisappear()
		_mainView.findModuleClientArea()
					.findAndShowContainer(containerUUID, containerPosition)
					.wrapElements()
					.within(()=>{
						cy.get(`button[class*='${buttonClass}']`)
							.click()
					})
					.then(()=>{
						cy.get(`.popup-container `)
							.find(`.${level}`)
							.click()

					})

		this.waitForLoaderToDisappear();
	}

	create_newRecord(containerUUID: string, containerPosition?: number) {
		if (containerUUID === cnt.uuid.PROJECTS) {
			_common.waitForLoaderToDisappear();
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.waitForLoaderToDisappear();
			cy.wait(5000); // Added with as sometime page takes time to render
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar();
			_common.waitForLoaderToDisappear();
			_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR).findButton(btn.ToolBar.ICO_REC_NEW).clickIn();
			this.waitForLoaderToDisappear();
		} else {
			_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR).findButton(btn.ToolBar.ICO_REC_NEW).clickIn();
			this.waitForLoaderToDisappear();
		}
	}

	create_newSubRecord(containerUUID: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
				 .findButton(btn.IconButtons.ICO_SUB_FLD_NEW)
				 .clickIn();
		this.waitForLoaderToDisappear();
	}

	delete_recordFromContainer(containerUUID: string) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID)
				 .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
				 .findButton(btn.IconButtons.ICO_REC_DELETE)
				 .wrapElements()
				 .click({ force: true })
				 .then(() => {
					cy.wait(1000);
					cy.get('body').then(($body) => {
						if ($body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS}`).length > 0) {
							if ($body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS} ${commonLocators.CommonModalElements.DELETE}`).length > 0) {
								_modalView.acceptButton(btn.ButtonText.DELETE);
							}else if($body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS} ${commonLocators.CommonModalElements.YES}`).length > 0){
								_modalView.acceptButton(btn.ButtonText.YES);
							}
							else if($body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS} ${commonLocators.CommonModalElements.YES_DATA_NG}`).length > 0){
								_modalView.acceptButton(btn.ButtonText.YES);
							}
						}
					});
				 });
	}

	select_rowInContainer(containerUUID: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findCell(app.SubContainerLayout.INDICATOR);
		_common.waitForLoaderToDisappear();
	}

	select_firstRowInContainer(containerUUID: string, cellClass: string, containerPosition?: number) {
		cy.get(`#mainContent [class*='cid_${containerUUID}'] [class*='grid-container'] div.${app.SubContainerLayout.COLUMN_ID}_${cellClass}`).first().click();
	}

	select_rowInSubContainer(containerUUID: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowSubContainer_InContainer(containerUUID, containerPosition).findCell(app.SubContainerLayout.INDICATOR);
	}


	//!Updated this pom by adding click on search on button under filter panel
	search_inSubContainer(containerUUID: string, searchValue: string, containerPosition?: number) {
		cy.wait(1000);
		_mainView.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH_ALL)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					cy.log('----> DIRECTLY SEARCHING <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
						.then(() => {
							_mainView.findModuleClientArea()
								.findAndShowContainer(containerUUID)
								.findGrid()
								.findTextInput(app.InputFields.FILTER_INPUT)
								.type(`{selectAll}{backspace}${searchValue}`)
								.then(() => {
									_mainView.findModuleClientArea()
										.findAndShowContainer(containerUUID)
										.findGrid()
										.wrapElements()
										.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
										.click({ force: true })
								})
						})


				} else {
					cy.wait(1000);
					cy.log('----> CLICK ON SEARCH AND SEARCHING <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.wrapElements()
						.find(`button[class*='${btn.ToolBar.ICO_SEARCH_ALL}']`)
						.click();

					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
						.then(() => {
							_mainView.findModuleClientArea()
								.findAndShowContainer(containerUUID)
								.findGrid()
								.findTextInput(app.InputFields.FILTER_INPUT)
								.type(`{selectAll}{backspace}${searchValue}`)
								.then(() => {
									_mainView.findModuleClientArea()
										.findAndShowContainer(containerUUID)
										.findGrid()
										.wrapElements()
										.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
										.click({ force: true })
								})
						})

				}
			});
	}

	select_dataFromSubContainer(containerUUID: string, data: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .findGrid()
				 .wrapElements()
				 .contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, data)
				 .click({ force: true });
	}

	select_dataFromSubContainer_lastValue(containerUUID: string, data: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .findGrid()
				 .wrapElements()
				 .contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, data)
				 .last()
				 .click({ force: true });
	}

	select_tabFromFooter(containerUUID: string, footerClass: string, containerPosition?: number) {
		cy.wait(2000); // This wait is added as container takes time to load
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).selectTabInSubContainerView(footerClass);
	}

	enterRecord_inNewRow(containerUUID: string, gridCellClass: string, inputFieldClass: string, inputData: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().getCell(gridCellClass, recordType).clickIn();
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.findCell(gridCellClass, recordType)
			.findTextInput(inputFieldClass)
			.clear()
			.type(inputData, { force: true, parseSpecialCharSequences: false });
	}

	enterRecord_inNewRow_byInputTag(containerUUID: string, gridCellClass: string, inputData: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().getCell(gridCellClass, recordType).clickIn();
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.findCell(gridCellClass, recordType)
			.findTextInput_byInputTag()
			.clear()
			.type(inputData, { force: true, parseSpecialCharSequences: false });
	}

	clear_subContainerFilter(containerUUID: string) {
		this.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH_ALL)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> Search field is present and direct searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
				} else {
					console.log('----> Clicking on search icon and searching <----');
					cy.wait(1000);
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.wrapElements()
						.find(`button[class*='${btn.ToolBar.ICO_SEARCH_ALL}']`)
						.click({ force: true });

					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
				}
			});
	}

	select_allContainerData(containerUUID: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).selectAllContainerData();
	}

	clickOn_cellHasValue(containerUUID: string, gridCell: string, cellValue: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findCellhasValue(gridCell, cellValue, recordType).click();
	}

	clickOn_cellHasUniqueValue(containerUUID: string, cellClass: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.getCell(cellClass, recordType)
			.wrapElements()
			.each(($el) => {
				const val = $el.text().trim();
				if (val === value) {
					cy.wrap($el).click({ force: true });
					return false;
				}
			});
	}

	clickOn_activeRowCell(containerUUID: string, gridCell: string, containerPosition?: number, recordType?: string) {
		cy.wait(1000);
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(gridCell, recordType);
		this.waitForLoaderToDisappear();
	}

	clickOn_goToButton_toSelectModule(containerUUID: string, moduleName: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.IconButtons.ICO_GO_TO)
			.wrapElements()
			.click()
			.then(() => {
				_mainView.select_popupItem('span', moduleName);
			});
	}

	clickOn_actionButton_toSelectAction(containerUUID: string, actionName: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ACTIONS)
			.wrapElements()
			.click()
			.then(() => {
				_mainView.select_popupItem('span', actionName);
			});
	}

	edit_containerCell(containerUUID: string, gridCells: string, inputTextClass: string, cellValue: any, recordType?) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findCell(gridCells, recordType).findTextInput(inputTextClass).clear({ force: true }).type(cellValue);
	}

	setDefaultView(tabBarID: any, value?: string) {
		_mainView
			.findModuleClientArea()
			.tabBar()
			.wrapElements()
			.find(`#${tabBarID}`)
			.within(() => {
				cy.get(`[class*='${btn.IconButtons.ICO_MENU}']`).click();
			})
			.then(() => {
				cy.wait(500);
				if (value) {
					cy.get(`${commonLocators.CommonElements.VIEW_DESCRIPTION}`).contains(value).click({ force: true });
				} else {
					cy.get(`${commonLocators.CommonElements.VIEW_DESCRIPTION}`).contains('RIB').click({ force: true });
				}
			});

	}
	setNewView(tabBarID: any,data:DataCells) {
		_mainView
			.findModuleClientArea()
			.tabBar()
			.wrapElements()
			.find(`#${tabBarID}`)
			.within(() => {
				cy.get(`[class*='${btn.IconButtons.ICO_MENU}']`).click();
			})
			.then(() => {
				cy.wait(500);
				cy.get(`div.popup-content ul>li>button`)
				  .contains(btn.ButtonText.SAVE_VIEW)
				  .click();
				cy.wait(500);
				if (data[commonLocators.CommonLabels.DESCRIPTION]) {
					cy.get(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS)
					  .find(`[class*='${app.ModalInputFields.DESCRIPTION}'] input`)
					  .clear({force:true})
					  .type(data[commonLocators.CommonLabels.DESCRIPTION]);
				}
				cy.wait(1000)
				.then(() => {
					_common.clickOn_modalFooterButton(btn.ButtonText.OK)
				})
				
			});
	}

	clickOn_cellInSubContainer(containerUUID: string, cellClass: string, recordType?: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findCell(cellClass, recordType);
	}

	clickOn_cellHasIcon(containerUUID: string, cellClass: string, iconClass: string, recordType?: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().getCell(cellClass, recordType).wrapElements().then(($cell)=>{
			cy.wrap($cell).find(`i.${iconClass}`).click({ force: true });
		})
	}

	clickOn_cellHasIconWithIndex(containerUUID: string, cellClass: string, iconClass: string, index: number, recordType?: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().getCell(cellClass, recordType).wrapElements().get(`i.${iconClass}`).eq(index).click({ force: true });
	}

	clickOn_cellHasIconWithIndex_forContainer(containerUUID: string, cellClass: string, iconClass: string, index: number, recordType?: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().getCell(cellClass, recordType).wrapElements().eq(index).find(`i.${iconClass}`).click({ force: true });
	}

	select_activeRowInContainer(containerUUID: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(app.SubContainerLayout.INDICATOR, recordType);
		_common.waitForLoaderToDisappear();
	}

	setup_gridLayout(containerUUID: string, requiredColumn: { [key: string]: any }) {
		var requiredColumns = requiredColumn;
		console.log('GRID LAYOUT COLUMN==>', requiredColumns);
		_mainView.configure_containerColumn(containerUUID, requiredColumns);
	}

	select_rowHasValue_byId(containerUUID: string, value: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGridById(containerUUID)
			.wrapElements()
			.within(() => {
				cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, value).then(($cell) => {
					if ($cell) {
						cy.wrap($cell).click({ multiple: true, force: true });
						cy.wait(1000);
						cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).click();
					}
				});
			});
	}

	select_rowHasValue_onIndexBased(containerUUID: string, value: string, index?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGridById(containerUUID)
			.wrapElements()
			.within(() => {
				cy.get(`[class*='${app.SubContainerLayout.COLUMN_ID}']:contains('${value}')`)
					.eq(index)
					.then(($cell) => {
						if ($cell) {
							cy.wrap($cell).click({ force: true });
							cy.wait(1000);
							cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).click();
						}
					});
			});
	}

	select_rowHasValue(containerUUID: string, value: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, value).then(($cell) => {
					if ($cell) {
						cy.wrap($cell).click({ multiple: true, force: true });
						cy.wait(1000);
						cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).click();
					}
				});
			});
	}

	create_newRecordIfDataNotFound(containerUUID: string, gridCells: string, value: string) {
		_common.waitForLoaderToDisappear();
		_common.clear_subContainerFilter(containerUUID);
		let returnValue: string = '';
		cy.get('body').then(($body) => {
			if ($body.find(`div.${app.SubContainerLayout.COLUMN_ID}_${gridCells} `).length <= 0) {
				cy.wait(1000);
				_common.create_newRecord(containerUUID);
				returnValue = 'Data does not exist';
				cy.wrap(returnValue).as('CHECK_DATA');
			} else {
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(containerUUID)
					.findGrid()
					.getCell(gridCells)
					.wrapElements()
					.each(($ele) => {
						var GetTextValue = $ele.text();
						if (GetTextValue == value) {
							console.log('Already Present');
							cy.log('VALUE => ' + GetTextValue);
							returnValue = 'Data already exist';
							cy.wrap(returnValue).as('CHECK_DATA');
						}
					})
					.then(() => {
						if (returnValue != 'Data already exist') {
							_common.create_newRecord(containerUUID);
							returnValue = 'Data does not exist';
							cy.wrap(returnValue).as('CHECK_DATA');
						}
					});
			}
		});
	}

	getText_fromCell(containerUUID: string, cellClass: string, recordType?) {
		return _mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().getCell(cellClass, recordType).wrapElements();
	}

	edit_dropdownCellWithInput(containerUUID: string, cellType: string, popUpType: string, cellInputType: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(cellType, recordType);
		_common.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.findCell(cellType, recordType)
			.findTextInput(cellInputType)
			.first()
			.clear({ force: true })
			.type(value)
			.then(() => {
				cy.wait(2000); // This wait is necessary
				cy.get('body').then(($body) => {
					if ($body.find(`${commonLocators.CommonElements.POPUP_FOOTER} .${btn.ToolBar.ICO_REFRESH}`).length > 0) {
						cy.get(`${commonLocators.CommonElements.POPUP_FOOTER} .${btn.ToolBar.ICO_REFRESH}`).click();
						cy.wait(2000); // This wait is necessary
					}
				});
			})
			.then(() => {
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(containerUUID, containerPosition)
					.findGrid()
					.findActiveRow()
					.findCell(cellType, recordType)
					.findTextInput(cellInputType)
					.first()
					.clear({ force: true })
					.type(value)
					.then(() => {
						cy.wait(4000); // This wait is necessary
						_mainView.select_popupItem(popUpType, value);
					});
			});
	}

	edit_dropdownCellWithCaret(containerUUID: string, cellType: string, popUpType: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .findGrid()
				 .findActiveRow()
				 .findCell(cellType, recordType)
				 .caret()
		
		cy.wait(2000).then(() => {
		_mainView.select_popupItem(popUpType, value);

		})
	}

	edit_dropdownCellWithCaret_scroll(containerUUID: string, cellType: string, popUpType: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(cellType, recordType).caret();
		cy.wait(2000);
		_mainView.select_popupItem_scroll(popUpType, value);
	}

	edit_dropdownCellWithCaret_withRefresh(containerUUID: string, cellType: string, popUpType: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(cellType, recordType).caret();
		cy.wait(2000)
			.then(() => {
				cy.wait(2000); // This wait is necessary
				cy.get('body').then(($body) => {
					if ($body.find(`${commonLocators.CommonElements.POPUP_FOOTER} .${btn.ToolBar.ICO_REFRESH}`).length > 0) {
						cy.get(`${commonLocators.CommonElements.POPUP_FOOTER} .${btn.ToolBar.ICO_REFRESH}`).click();
						cy.wait(2000); // This wait is necessary
					}
				});
			})
			.then(() => {
				cy.wait(4000); // This wait is necessary
				_mainView.select_popupItem(popUpType, value);
			});
	}

	edit_ModaldropdownCellWithCaret(cellType: string, popUpType: string, value: string, recordType?: string, containerPosition?: number) {
		_modalView.findModal().findActiveRow().findCell(cellType, recordType).caret();
		cy.wait(2000);
		_mainView.select_popupItem(popUpType, value);
	}

	edit_dropdownCellWith_Input_without_ref(containerUUID: string, cellType: string, popUpType: string, cellInputType: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(cellType, recordType);
		_common.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.findCell(cellType, recordType)
			.findTextInput(cellInputType)
			.first()
			.clear({ force: true })
			.type(value)
			.then(() => {
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(containerUUID, containerPosition)
					.findGrid()
					.findActiveRow()
					.findCell(cellType, recordType)
					.findTextInput(cellInputType)
					.first()
					.clear({ force: true })
					.type(value)
					.then(() => {
						cy.wait(4000); // This wait is necessary
						_mainView.select_popupItem(popUpType, value);
					});
			});
	}

	create_newRecordInContainer_ifNoRecordExists(containerUUID: string, index: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.find(`${commonLocators.CommonElements.GRID_CANVAS_RIGHT}`)
			.eq(index)
			.then(($ele) => {
				if ($ele.find(`${commonLocators.CommonElements.UI_WIDGET_CONTENT}`).length > 0) {
					_common.select_rowInContainer(containerUUID);
				} else {
					_common.create_newRecord(containerUUID);
				}
			});
	}

	create_newRecordInSubContainer_ifNoRecordExists(containerUUID: string, index: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.find(`${commonLocators.CommonElements.GRID_CANVAS_RIGHT}`)
			.eq(index)
			.then(($ele) => {
				if ($ele.find(`${commonLocators.CommonElements.UI_WIDGET_CONTENT}`).length > 0) {
					_common.select_rowInContainer(containerUUID);
				} else {
					_common.create_newRecord(containerUUID);
				}
			});
	}

	delete_recordInContainer_ifRecordExists(containerUUID: string) {
		cy.wait(3000); //Added this wait as data is getting loaded after sometime
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.find(`${commonLocators.CommonElements.GRID_CANVAS_RIGHT}`)
			.then(($ele) => {
				if ($ele.find(`${commonLocators.CommonElements.UI_WIDGET_CONTENT}`).length > 0) {
					_common.select_allContainerData(containerUUID);
					_common.delete_recordFromContainer(containerUUID);
				} else {
					cy.log('No record is available in container');
				}
			});
	}

	saveCellDataToEnv(containerUUID: string, cellClass: string, cypressEnvName: string, recordType?: string, containerPosition?: number) {
		cy.wait(5000)
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.getCell(cellClass, recordType)
			.wrapElements()
			.then(($value) => {
				Cypress.env(cypressEnvName, $value.text());
			});
	}

	saveCellDataToEnvFromModalRow(cellClass: string, cypressEnvName: string, recordType?: string) {
		_modalView
			.findModal()
			.findActiveRow()
			.findCell(cellClass, recordType)
			.wrapElements()
			.then(($value) => {
				cy.log("value",$value)
				cy.log("text",$value.text())
				Cypress.env(cypressEnvName, $value.text());
		
			});
	}

	saveNumericCellDataToEnv(containerUUID: string, cellClass: string, cypressEnvName: string, recordType?: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.getCell(cellClass, recordType)
			.wrapElements()
			.then(($value) => {
				Cypress.env(cypressEnvName, parseInt($value
					.text()));
			});
	}

	set_cellCheckboxValue(containerUUID: string, checkboxCell: string, checkBoxValue: string, containerPosition?: number, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check();
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck();
					}
				}
			});
	}

	set_cellCheckboxValue_forColumn(containerUUID: string, checkboxCell: string, checkBoxValue: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.wrapElements()
			.then(($ele) => {
				cy.wrap($ele)
					.find(`[class*='slick-column-name'] input[id*="${checkboxCell}"]`)
					.as('check')
					.invoke('is', ':checked')
					.then((checked) => {
						if (checkBoxValue == 'check') {
							if (!checked) {
								cy.get('@check').check();
							}
						}
						if (checkBoxValue == 'uncheck') {
							if (checked) {
								cy.get('@check').uncheck();
							}
						}
					});
			});
	}

	collapse_activeRecord(containerUUID: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findIconTreeCollapse();
	}

	filterCurrentEstimate(containerUUID: string, type: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.wrapElements()
			.then(($el) => {
				if (type === 'noFilter') {
					if ($el.find(`[class*='${btn.ToolBar.ICO_FILTER}'].${app.SubContainerLayout.ACTIVE}`).length > 0) {
						cy.wrap($el).find(`[class*='${btn.ToolBar.ICO_FILTER}'].${app.SubContainerLayout.ACTIVE}`).click();
					}
				}
				if (type === 'filter') {
					if ($el.find(`[class*='${btn.ToolBar.ICO_FILTER}'].${app.SubContainerLayout.ACTIVE}`).length > 0) {
						cy.log('Already Filtered');
					} else {
						cy.wrap($el).find(`[class*='${btn.ToolBar.ICO_FILTER}']`).click();
					}
				}
			});
	}

	returnArrayForMultipleCell(containerUUID: string, cellClass: string, recordType?: string, containerPosition?: number): string[] {
		let arrayData: string[] = [];
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.getCell(cellClass, recordType)
			.wrapElements()
			.each(($el, index) => {
				arrayData.push($el.text());
			});
		return arrayData;
	}

	set_containerLayoutUnderEditView(layout_name: string) {
		cy.get('#tabbar', { timeout: 3000 }).within((li) => {
			cy.wrap(li).get('li[class*="active"] button.ico-menu').click();
		});
		cy.get("[class*='popup-content flex-box']", { timeout: 3000 }).contains('Edit View').click();
		cy.get(`[class*='modal-content'] [name='${layout_name}']`).click();
		_common.clickOn_modalFooterButton(btn.ButtonText.OK);
	}

	clickOn_activeContainerButton(containerUUID: string, btnClass: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findButton(btnClass).clickIn();
	}

	clickOn_toolBarButtonWithTitle(containerUUID: string, buttonTitle: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR).findButtonWithTitle(buttonTitle).clickIn();
	}

	lookUpButtonInCell(containerUUID: string, cellClass: string, lookUpBtnClass: string, index: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findCell(cellClass).findInputLookup(lookUpBtnClass, index);
	}

	findLookUpInputInContainerByLabel(uuid: string, labelName: string): Cypress.Chainable<any> {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`[class*="ico-input-lookup"]`).eq(0).click();
			});
		this.element = '';
		return div;
	}

	checkSearchOption_inSideBar(data: DataCells) {
		cy.get(data[commonLocators.CommonKeys.CLASS])
			.contains(data[commonLocators.CommonElements.LABEL])
			.click({ force: true })
			.closest(commonLocators.CommonElements.ROW)
			.within(($ele) => {
				cy.wrap($ele)
					.find(commonLocators.CommonElements.CHECKBOX_INPUT)
					.as('checkbox')
					.invoke('is', ':checked')
					.then((checked) => {
						if (data[commonLocators.CommonKeys.VALUE] === commonLocators.CommonKeys.CHECK) {
							if (!checked) {
								cy.get('@checkbox').check();
							}
						} else if (data[commonLocators.CommonKeys.VALUE] === commonLocators.CommonKeys.UNCHECK) {
							if (checked) {
								cy.get('@checkbox').uncheck();
							}
						}
					});
			});
	}

	select_numberRangeSetting() {
		_mainView
			.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
			.findCheckBox_byLabel('Check Number', 'checkbox')
			.then((check_number) => {
				if (check_number.hasClass('ng-empty')) {
					_common.create_newRecord_forNumberRange(cnt.uuid.NUMBER_RANGES);
					_mainView
						.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
						.findCaretByLabel('Sequence Type')
						.then(() => {
							_mainView.select_popupItem('list', 'Continues Sequence');
						});
					_modalView
						.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
						.findCheckBox_byLabel('Check Number', 'checkbox')
						.click()
						.then(() => {
							_mainView.findInputInContainerByLabel(cnt.uuid.NUMBER_RANGES, 'Minimal Length').clear().type('1');
							_mainView.findInputInContainerByLabel(cnt.uuid.NUMBER_RANGES, 'Maximum Length').clear().type('999');
						});
					_mainView
						.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
						.findCaretByLabel('Number Sequence')
						.then(() => {
							_mainView.select_popupItem('list', '999_Vertrag');
						});
					cy.SAVE();
				} else {
					cy.log('auto code generated setting is active');
				}
			});
	}

	create_newRecordInCurrencyConversion_ifRecordNotExists(containerUUID: string, gridCells: string, value: string, rate: string, index: number) {
		const DAYS = require('dayjs');
		let date = parseInt(DAYS().format('DD'));
		let month = DAYS().format('MM');
		let year = DAYS().format('YYYY');
		let currentDate = date + '/' + month + '/' + year;
		let status: any;
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.find("[class*='grid-canvas grid-canvas-top grid-canvas-right']")
			.eq(index)
			.then(($ele) => {
				if ($ele.find("[class*='ui-widget-content']").length > 0) {
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.getCell(app.SubContainerLayout.INDICATOR)
						.wrapElements()
						.each(($ele) => {
							cy.wrap($ele).click();
							_mainView
								.findAndShowContainer(containerUUID)
								.findGrid()
								.getCell(gridCells)
								.wrapElements()
								.each(($ele) => {
									var GetTextValue = $ele.text();
									cy.log(GetTextValue);
									if (GetTextValue.match(value)) {
										console.log('Already Present');
										status = 'true';
										_mainView
											.findModuleClientArea()
											.findAndShowContainer(cnt.uuid.EXCHANGE_RATES)
											.findGrid()
											.getCell(app.GridCells.RATE_DATE)
											.wrapElements()
											.each(($el, index, $list) => {
												if ($list.length == 0) {
													_common.create_newRecord(cnt.uuid.EXCHANGE_RATES);
													_common.edit_containerCell(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, app.InputFields.INPUT_GROUP_CONTENT, rate);
													cy.SAVE();
													cy.wait(2000);
													_common.saveCellDataToEnv(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, 'EXCHANGE-RATE');
												}
												if ($el.text().match(currentDate)) {
													cy.wrap($el).click();
													cy.wait(2000);
													_common.saveCellDataToEnv(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, 'EXCHANGE-RATE');
												}
											});
										return false;
									} else {
										status = 'false';
									}
								});
						});
				} else {
					_common.create_newRecord(containerUUID);
					_common.edit_dropdownCellWithInput(containerUUID, gridCells, 'grid', app.InputFields.INPUT_GROUP_CONTENT, value);
					_common.create_newRecord(cnt.uuid.EXCHANGE_RATES);
					_common.edit_containerCell(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, app.InputFields.INPUT_GROUP_CONTENT, rate);
					cy.SAVE();
					cy.wait(2000);
					this.saveCellDataToEnv(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, 'EXCHANGE-RATE');
				}

				if (status == 'false') {
					_common.create_newRecord(containerUUID);
					_common.edit_dropdownCellWithInput(containerUUID, gridCells, 'grid', app.InputFields.INPUT_GROUP_CONTENT, value);
					_common.create_newRecord(cnt.uuid.EXCHANGE_RATES);
					_common.edit_containerCell(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, app.InputFields.INPUT_GROUP_CONTENT, rate);
					cy.SAVE();
					cy.wait(2000);
					this.saveCellDataToEnv(cnt.uuid.EXCHANGE_RATES, app.GridCells.RATE, 'EXCHANGE-RATE');
				}
			});
	}

	set_cellCheckboxValueForAllRowCell(containerUUID: string, checkboxCell: string, checkBoxValue: string, containerPosition?: number, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.each(($checkbox) => {
				const isChecked = $checkbox.prop('checked');

				if (checkBoxValue === 'check' && !isChecked) {
				cy.wrap($checkbox).check({ force: true });
				}

				if (checkBoxValue === 'uncheck' && isChecked) {
				cy.wrap($checkbox).uncheck({ force: true });
				}
			});
	  }

	enterRecord_toChangeLanguage(data: DataCells) {
		cy.get(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS).find(commonLocators.CommonElements.MASTER_ITEM).contains(commonLocators.CommonLabels.LANGUAGE).click({ force: true });
		_modalView
			.findModal()
			.findCaretInsideModal(commonLocators.CommonLabels.UI_LANGUAGE)
			.then(() => {
				_modalView.select_popupItem('grid1', data[commonLocators.CommonLabels.UI_LANGUAGE]);
			});
		_common.waitForLoaderToDisappear();
		cy.wait(1000);
		_modalView
			.findModal()
			.findCaretInsideModal(commonLocators.CommonLabels.USER_DATA_LANGUAGE)
			.then(() => {
				_modalView.select_popupItem('grid1', data[commonLocators.CommonLabels.USER_DATA_LANGUAGE]);
			});
	}

	enterRecord_toChangeLanguage1(data: DataCells) {
		cy.get(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS).find(commonLocators.CommonElements.MASTER_ITEM).contains(commonLocators.CommonLabels.SPRACHE).click({ force: true });
		_modalView
			.findModal()
			.findCaretInsideModal(commonLocators.CommonLabels.OBERFLÄCHENSPRACHE)
			.then(() => {
				_modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.OBERFLÄCHENSPRACHE]);
			});
		_common.waitForLoaderToDisappear();
		cy.wait(1000); //Wait required to load data
		_modalView
			.findModal()
			.findCaretInsideModal(commonLocators.CommonLabels.BENUTZERDATENSPRACHE)
			.then(() => {
				_modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.BENUTZERDATENSPRACHE]);
			});
	}

	assign_clerkForLoggedInUser(containerUUID: string, data: DataCells) {
		_common.search_inSubContainer(cnt.uuid.CLERKS, Cypress.env('USER_NAME'));
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.find("[class*='grid-canvas grid-canvas-top grid-canvas-right']")
			.then(($ele) => {
				if ($ele.find("[class*='ui-widget-content']").length > 0) {
					let clerkStatus;
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`[class*='${app.SubContainerLayout.COLUMN_ID}_userfk']`)
						.each(($value, index) => {
							let clerkName = $value.text();
							if (clerkName === Cypress.env('USER_NAME')) {
								clerkStatus = 'true';
								return false;
							} else {
								clerkStatus = 'false';
							}
						})
						.then(() => {
							if (clerkStatus === 'true') {
								_common.select_rowHasValue(containerUUID, Cypress.env('USER_NAME'));
								cy.wait(1000);
								_common.saveCellDataToEnv(containerUUID, app.GridCells.CODE, 'CLERK_LOGGED_USER');
							} else {
								_common.search_inSubContainer(containerUUID, data[app.GridCells.CODE]);
								_common.clickOn_cellHasUniqueValue(containerUUID, app.GridCells.CODE, data[app.GridCells.CODE]);
								_common.edit_dropdownCellWithInput(containerUUID, app.GridCells.USER_FK, 'grid', app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('USER_NAME'));
								_common.select_activeRowInContainer(containerUUID);
								this.waitForLoaderToDisappear();
								cy.SAVE();
								this.waitForLoaderToDisappear();
								cy.wait(1000);
								_common.saveCellDataToEnv(containerUUID, app.GridCells.CODE, 'CLERK_LOGGED_USER');
							}
						});
				} else {
					_common.search_inSubContainer(containerUUID, data[app.GridCells.CODE]);
					_common.clickOn_cellHasUniqueValue(containerUUID, app.GridCells.CODE, data[app.GridCells.CODE]);
					_common.edit_dropdownCellWithInput(containerUUID, app.GridCells.USER_FK, 'grid', app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('USER_NAME'));
					_common.clickOn_activeRowCell(containerUUID, app.GridCells.CODE);
					cy.SAVE();
					this.waitForLoaderToDisappear();
					cy.wait(1000);
					_common.saveCellDataToEnv(containerUUID, app.GridCells.CODE, 'CLERK_LOGGED_USER');
				}
			});
	}

	clickOn_containerFooterButton(containerUUID: string, footerClass: string, buttonClass: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).containerFooter(footerClass).findButton(buttonClass).clickIn();
	}

	columnFilter_inSubContainer(containerUUID: string, columnClass: string, searchValue: string, recordType?: string) {
		this.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH_COLUMN)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> column filter is present and direct searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`.item-field_${columnClass}`).first().as('column').click();
							cy.get('@column').find('input').clear({ force: true });
							cy.wait(500);
							cy.get(`.item-field_${columnClass}`).first().as('column').click();
							cy.get('@column').find('input').type(searchValue).type('{enter}');
						});
				} else if (recordType) {
					console.log('----> Clicking on column filter icon with header and searching <----');
					this.waitForLoaderToDisappear();
					cy.wait(1000);
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.wrapElements()
						.find(`button[class*='${btn.ToolBar.ICO_SEARCH_COLUMN}']`)
						.click();
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`[class*='item-field_${recordType}.${columnClass}']`).first().as('column').click();
							cy.get('@column').find('input').clear({ force: true });
							cy.wait(500);
							cy.get(`[class*='item-field_${recordType}.${columnClass}']`).first().as('column').click();
							cy.get('@column').find('input').type(searchValue).type('{enter}');
						});
				}
				else {
					console.log('----> Clicking on column filter icon and searching <----');
					this.waitForLoaderToDisappear();
					cy.wait(1000);
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.wrapElements()
						.find(`button[class*='${btn.ToolBar.ICO_SEARCH_COLUMN}']`)
						.click();
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`.item-field_${columnClass}`).as('column').click();
							cy.get('@column').find('input').clear({ force: true });
							cy.wait(500);
							cy.get(`.item-field_${columnClass}`).as('column').click();
							cy.get('@column').find('input').type(searchValue).type('{enter}');
						});
				}
			});
	}

	edit_inTextAreaOfContainer(containerUUID: string, inputText: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findTextAreaInModal(app.InputFields.K_CONTENT_FRAME).type(inputText);
	}

	toggle_radioFiledInContainer(checkBoxStatus: string, containerUUID: string, gridCell: string) {
		switch (checkBoxStatus) {
			case 'selectRadioButton':
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(containerUUID)
					.findGrid()
					.findActiveRow()
					.getCell(gridCell)
					.wrapElements()
					.then(($ele) => {
						if ($ele.find("[checked='checked']").length > 0) {
							cy.log('radio button is already selected');
						} else {
							_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findCell(gridCell);
						}
					});
				break;
			case 'deSelectRadioButton':
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(containerUUID)
					.findGrid()
					.findActiveRow()
					.getCell(gridCell)
					.wrapElements()
					.then(($ele) => {
						if ($ele.find("[checked='checked']").length < 1) {
							cy.log('radio button is already not selected');
						} else {
							_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findCell(gridCell);
						}
					});
				break;
			default:
				cy.log('Search type not supported');
		}
	}

	getDate(type: string, numberOfDays?: number, dateFetched?: string) {
		if (type === 'year') {
			let date = new Date();
			let year = date.getFullYear();
			let fullYear = year;
			return fullYear;
		}
		if (type === 'current') {
			let date = new Date();
			let year = date.getFullYear();
			let month: any = date.getMonth() + 1;
			let dates: any = date.getDate();
			if (dates < 10) {
				dates = '0' + date.getDate();
			}
			if (month < 10) {
				month = '0' + (date.getMonth() + 1);
			}
			let currentFullDate = dates + '/' + month + '/' + year;

			return currentFullDate;
		}
		if (type === 'incremented') {
			let date = new Date();
			date.setDate(date.getDate() + numberOfDays);
			let year = date.getFullYear();
			let month: any = date.getMonth() + 1;
			let dates: any = date.getDate();
			if (dates < 10) {
				dates = '0' + date.getDate();
			}
			if (month < 10) {
				month = '0' + (date.getMonth() + 1);
			}
			let incFullDate = dates + '/' + month + '/' + year;

			return incFullDate;
		}
		if (type === 'fetchedDateIncrement') {
			var datearray = dateFetched.split('/');
			var newdate = datearray[1] + '/' + datearray[0] + '/' + datearray[2];
			let date = new Date(newdate);
			date.setDate(date.getDate() + numberOfDays);
			let year = date.getFullYear();
			let month: any = date.getMonth() + 1;
			let dates: any = date.getDate();
			if (dates < 10) {
				dates = '0' + date.getDate();
			}
			if (month < 10) {
				month = '0' + (date.getMonth() + 1);
			}
			let incFullDate = dates + '/' + month + '/' + year;

			return incFullDate;
		}
		if (type === 'fetchedDateDecrement') {
			var datearray = dateFetched.split('/');
			var newdate = datearray[1] + '/' + datearray[0] + '/' + datearray[2];
			let date = new Date(newdate);
			date.setDate(date.getDate() - numberOfDays);
			let year = date.getFullYear();
			let month: any = date.getMonth() + 1;
			let dates: any = date.getDate();
			if (dates < 10) {
				dates = '0' + date.getDate();
			}
			if (month < 10) {
				month = '0' + (date.getMonth() + 1);
			}
			let incFullDate = dates + '/' + month + '/' + year;

			return incFullDate;
		}
		if (type === 'extraDayBetweenDates') {
			let startdate: any = new Date();
			let endDate: any = new Date();
			endDate.setDate(endDate.getDate() + numberOfDays);
			let differenceTime: any = Math.abs(endDate - startdate);
			let differenceDay: any = Math.floor(differenceTime / (1000 * 60 * 60 * 24));
			let a: any = Math.floor(differenceDay / 7);
			let b: any = a * 7;
			let c: any = differenceDay - b;
			return c;
		}
		if (type === 'DayBetweenDates') {
			let startdate: any = new Date();
			let endDate: any = new Date();
			endDate.setDate(endDate.getDate() + numberOfDays);
			let differenceTime: any = Math.abs(endDate - startdate);
			let differenceDay: any = Math.floor(differenceTime / (1000 * 60 * 60 * 24));
			let c: any = differenceDay;
			return c;
		}
		if (type === 'lastDateOfCurrentMonth') {
			let currentDate = new Date();
			let lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
			let c: any = `${lastDayOfMonth.getDate().toString().padStart(2, '0')}/${(lastDayOfMonth.getMonth() + 1).toString().padStart(2, '0')}/${lastDayOfMonth.getFullYear()}`;
			return c;
		}
		if (type === 'lastDateOfNextMonth') {
			let currentDate = new Date();
			let firstDayOfNextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
			let lastDayOfNextMonth = new Date(firstDayOfNextMonth.getFullYear(), firstDayOfNextMonth.getMonth() + 1, 0);
			let c: any = `${lastDayOfNextMonth.getDate().toString().padStart(2, '0')}/${(lastDayOfNextMonth.getMonth() + 1).toString().padStart(2, '0')}/${lastDayOfNextMonth.getFullYear()}`;
			return c;
		}
		if (type === 'getCurrentYearAndMonth') {
			let currentDate = new Date();
			let lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
			let c: any = `${lastDayOfMonth.getFullYear()}${(lastDayOfMonth.getMonth() + 1).toString().padStart(2, '0')}`;
			return c;
		}
		if (type === 'currentFormatYMD') {
			let date = new Date();
			let year = date.getFullYear();
			let month: any = date.getMonth() + 1;
			let dates: any = date.getDate();
			if (dates < 10) {
				dates = '0' + date.getDate();
			}
			if (month < 10) {
				month = '0' + (date.getMonth() + 1);
			}
			let currentFullDate = year + '-' + month + '-' + dates;

			return currentFullDate;
		}
		if (type === 'incrementedFormatYMD') {
			let date = new Date();
			date.setDate(date.getDate() + numberOfDays);
			let year = date.getFullYear();
			let month: any = date.getMonth() + 1;
			let dates: any = date.getDate();
			if (dates < 10) {
				dates = '0' + date.getDate();
			}
			if (month < 10) {
				month = '0' + (date.getMonth() + 1);
			}
			let incFullDate = year + '-' + month + '-' + dates;

			return incFullDate;
		}
		if (type === 'currentYearAndMonth') {
			let currentDate = new Date();
			let year: any = currentDate.getFullYear();
			let month: any = currentDate.getMonth();
			if (month < 10) {
				month = '0' + (currentDate.getMonth() + 1)
			}
			let yearAndMonth = year + '' + month;
			return yearAndMonth;
		}
	}

	//!Updated this pom by adding click on search on button under filter panel
	search_dataInNumberRangeContainer(containerUUID: string, searchValue: string, containerPosition?: number) {
		cy.wait(1000);
		_mainView.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.toolbar(app.Layouts.SUB_VIEW_CONTENT)
			.findButton(btn.ToolBar.ICO_SEARCH_ALL)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> Search field is present and direct searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
						.then(() => {
							_mainView.findModuleClientArea()
								.findAndShowContainer(containerUUID)
								.findGrid()
								.findTextInput(app.InputFields.FILTER_INPUT)
								.type(`{selectAll}{backspace}${searchValue}`)
								.then(() => {
									_mainView.findModuleClientArea()
										.findAndShowContainer(containerUUID)
										.findGrid()
										.wrapElements()
										.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
										.click({ force: true })
								})
						})

				} else {
					cy.wait(1000);
					console.log('----> Clicking on search icon and searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID, containerPosition)
						.toolbar(app.Layouts.SUB_VIEW_CONTENT)
						.findButton(btn.ToolBar.ICO_SEARCH_ALL)
						.clickIn();

					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
						.then(() => {
							_mainView.findModuleClientArea()
								.findAndShowContainer(containerUUID)
								.findGrid()
								.findTextInput(app.InputFields.FILTER_INPUT)
								.type(`{selectAll}{backspace}${searchValue}`)
								.then(() => {

									_mainView.findModuleClientArea()
										.findAndShowContainer(containerUUID)
										.findGrid()
										.wrapElements()
										.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
										.click({ force: true })
								})
						})
				}
			});
	}

	create_newRecord_forNumberRange(containerUUID: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).toolbar(app.Layouts.SUB_VIEW_CONTENT).findButton(btn.ToolBar.ICO_REC_NEW).clickIn();
		this.waitForLoaderToDisappear();
	}

	//! Modal related common methods

	select_dataFromLookups_fromModal(container_UUID: string, cellClass: string, searchValue: string, inputTextClass: string) {
		_common.waitForLoaderToDisappear();
		_mainView.findModuleClientArea()
				 .findAndShowContainer(container_UUID)
				 .findGrid()
				 .findActiveRow()
				 .findCell(cellClass)
				 .findInputLookup(app.InputFields.ICO_INPUT_LOOKUP, 0);
		cy.wait(2000); //  Added this wait as data take time to load
		_common.waitForLoaderToDisappear();
		_modalView.findModal()
				  .acceptButton(btn.ButtonText.REFRESH);
		_common.waitForLoaderToDisappear();
		_modalView.findModal()
		          .findTextInput(inputTextClass)
				  .first()
				  .clear()
				  .type(searchValue);
		_modalView.findModal()
				  .findButton(btn.IconButtons.ICO_SEARCH)
				  .wrapElements()
				  .first().
				  click();
		_modalView.findModal()
				  .wrapElements()
				  .find(`[class*='${app.SubContainerLayout.COLUMN_ID}']`)
				  .contains(searchValue)
				  .click();
		_modalView.findModal()
				  .acceptButton(btn.ButtonText.OK);
		_common.waitForLoaderToDisappear();
	}

	clickOn_modalFooterButton(buttonLabel: string) {
		_modalView.findModal().acceptButton(buttonLabel);
	}

	clickOn_modalFooterButton_ifExists(buttonLabel: string) {
		cy.wait(1000);
		cy.get('body').then(($body) => {
			if ($body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS}`).length > 0) {
				_modalView.acceptButton(buttonLabel);
			}
		});
	}

	assert_modalShouldNotExists() {
		cy.wait(1000);
		cy.get(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS}`)
		  .should('not.exist')
	}

	clickOn_modalFooterCheckbox_doNotAskAgain(checkBoxValue: string) {
		_modalView.findModal()
				  .wrapElements()
				  .find(`[class*='modal-footer-deactivate-option'] ${commonLocators.CommonElements.CHECKBOX_TYPE}`)
				  .as('check')
				  .invoke('is', ':checked')
				  .then((checked) => {
					if (checkBoxValue == 'check') {
						if (!checked) {
							cy.get('@check').check();
						}
					}
					if (checkBoxValue == 'uncheck') {
						if (checked) {
							cy.get('@check').uncheck();
						}
					}
				  });
	}

	clickOn_validationModalFooterButton_ifExists(buttonLabel: string) {
		cy.wait(1000);
		cy.get('body').then(($body) => {
			if ($body.find(`${commonLocators.CommonModalElements.ALERT_WARNING}`).length > 0) {
				_modalView.acceptButton(buttonLabel);
			}
		});
	}

	select_multipleRow_fromModal() {
		_modalView.findModal().findMultipleCell(app.SubContainerLayout.INDICATOR);
	}

	clickOn_multipleCellHasICON(containerUUID: string, cellClass: string, iconClass: string, recordType?: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().getCell(cellClass, recordType).wrapElements().get(`i.${iconClass}`).as('boq_Icon');
		cy.get('@boq_Icon').then((els) => {
			cy.wrap(els).click({ multiple: true, ctrlKey: true });
		});
	}

	clickOn_cellHasValue_fromModal(gridCell: string, data: string) {
		_modalView.findModal().findModalBody().findCellhasValue(gridCell, data).scrollIntoView().click({ force: true });
	}

	clickOn_cellHasValueWithIndex_fromModal(cellClass: string, value: string, index) {
		_modalView.findModal().getCell(cellClass).wrapElements().contains(value).eq(index).click({ force: true });
	}

	select_ItemFromPopUpList(popupType: string, value: string) {
		_modalView.select_popupItem(popupType, value);
	}

	/**
	 * @deprecated This implementation is no longer supported.
	 * Please use the `edit_caretDropdown_fromModal_byClass` method instead.
	*/
	editModalDropdown_WithCaret(label: string) {
		_modalView.findModal().findCaretInsideModal(label);
	}
	edit_caretDropdown_fromModal_byClass(className: string) {
		_modalView.findModal().findCaretInsideModal_byClass(className);
	}

	/**
	 * @deprecated This implementation is no longer supported.
	 * Please use the `edit_inputField_fromModal_byClass or edit_dropDownWithInput_fromModal_byClass` method instead.
	*/
	edit_dropDownWithInput_fromModal(label: string, value: string, popUpType: string) {
		_modalView.findModal()
				  .wrapElements()
				  .find(`${commonLocators.CommonElements.PLATFORM_FORM_LABEL}`)
				  .contains(label)
				  .closest(`${commonLocators.CommonElements.ROW}`)
				  .within(() => {
					cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`).within(() => {
						cy.get(`[class*='${app.InputFields.INPUT_GROUP_CONTENT}']`)
							.first()
							.clear({ force: true })
							.type(value)
							.then(() => {
								cy.document()
									.its('body')
									.find('.popup-container')
									.within(() => {
										switch (popUpType) {
											case 'list'.toLowerCase():
												cy.contains('li', value).click({ force: true });
												break;
											case 'grid':
												cy.get('div.popup-content').within(() => {
													cy.wait(1000);
													cy.get(`div[class*='${app.SubContainerLayout.COLUMN_ID}']`).each(($cell) => {
														const cellField: string = $cell.text();
														if (value === cellField) {
															cy.wait(1000);
															cy.wrap($cell).click({ force: true });
															cy.wait(1000);
															return false;
														}
													});
												});
												break;
											case 'grid1'.toLowerCase():
												cy.contains('button', value).click({ force: true });
												break;
											case 'span'.toLowerCase():
												cy.contains('span', value).click({ force: true });
												break;
											case 'listexact'.toLowerCase():
												cy.contains('li', new RegExp('^' + value + '$', 'g')).click({ force: true });
												break;
											default:
												cy.log('Search type not supported');
										}
									});
							});
					});
				  });
	}

	/**
	 * @deprecated This implementation is no longer supported. Use the class-based POM approach for modal inputs and dropdowns with inputs.
	*/
	edit_inputField_fromModal(label: string, inputClass: string, value: string) {
		_modalView
			.findModal()
			.wrapElements()
			.find(`${commonLocators.CommonElements.PLATFORM_FORM_LABEL}`)
			.contains(label)
			.closest(`${commonLocators.CommonElements.ROW}`)
			.within(() => {
				cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`).within(() => {
					cy.get(`[class*='${inputClass}']`).first().type(`{selectall}{backspace}${value}`);
				});
			});
	}

	edit_inputField_fromModal_byClass(inputClass: string, value: string) {
		_modalView.findModal()
				  .wrapElements()
				  .find(`.${inputClass}`)
				  .closest(`${commonLocators.CommonElements.ROW}`)
				  .within(() => {
					cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`).within(() => {
						cy.get(`[class*='${inputClass}']`).first().type(`{selectall}{backspace}${value}`);
					});
				  });
	}

	edit_dropDownWithInput_fromModal_byClass(inputClass: string, value: string, popUpType: string) {
		_modalView.findModal()
				  .wrapElements()
				  .find(`.${inputClass}`)
				  .closest(`${commonLocators.CommonElements.ROW}`)
				  .within(() => {
					cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`)
					  .within(() => {
						cy.get(`[class*='${app.InputFields.INPUT_GROUP_CONTENT}']`)
							.first()
							.clear({ force: true })
							.type(value)
							.then(() => {
								cy.document()
									.its('body')
									.find('.popup-container')
									.within(() => {
										switch (popUpType) {
											case 'list'.toLowerCase():
												cy.contains('li', value).click({ force: true });
												break;
											case 'grid':
												cy.get('div.popup-content').within(() => {
													cy.wait(1000);
													cy.get(`div[class*='${app.SubContainerLayout.COLUMN_ID}']`).each(($cell) => {
														const cellField: string = $cell.text();
														if (value === cellField) {
															cy.wait(1000);
															cy.wrap($cell).click({ force: true });
															cy.wait(1000);
															return false;
														}
													});
												});
												break;
											case 'grid1'.toLowerCase():
												cy.contains('button', value).click({ force: true });
												break;
											case 'span'.toLowerCase():
												cy.contains('span', value).click({ force: true });
												break;
											case 'listexact'.toLowerCase():
												cy.contains('li', new RegExp('^' + value + '$', 'g')).click({ force: true });
												break;
											default:
												cy.log('Search type not supported');
										}
									});
							});
					});
				  });
	}

	/**
	 * @deprecated This implementation is no longer supported.
	 * Please use the `edit_dropDownWithCaret_fromModal_byClass` method instead.
	*/
	edit_dropDownWithCaret_fromModal(label: string, value: string, popUpType: string) {
		_modalView
			.findModal()
			.wrapElements()
			.find(`${commonLocators.CommonElements.PLATFORM_FORM_LABEL}`)
			.contains(label)
			.closest(`${commonLocators.CommonElements.ROW}`)
			.within(() => {
				cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`).within(() => {
					cy.get(`[class*='${commonLocators.CommonElements.CARET_MODAL}']`)
						.first()
						.click()
						.then(() => {
							cy.document()
								.its('body')
								.find('.popup-container')
								.within(() => {
									switch (popUpType) {
										case 'list'.toLowerCase():
											cy.contains('li', value).click({ force: true });
											break;
										case 'grid':
											cy.get('div.popup-content').within(() => {
												cy.wait(1000);
												cy.get(`div[class*='${app.SubContainerLayout.COLUMN_ID}']`).each(($cell) => {
													const cellField: string = $cell.text();
													if (value === cellField) {
														cy.wait(1000);
														cy.wrap($cell).click({ force: true });
														cy.wait(1000);
														return false;
													}
												});
											});
											break;
										case 'grid1'.toLowerCase():
											cy.contains('button', value).click({ force: true });
											break;
										case 'span'.toLowerCase():
											cy.contains('span', value).click({ force: true });
											break;
										case 'listexact'.toLowerCase():
											cy.contains('li', new RegExp('^' + value + '$', 'g')).click({ force: true });
											break;
										default:
											cy.log('Search type not supported');
									}
								});
						});
				});
			});
	}

	edit_dropDownWithCaret_fromModal_byClass(className: string, value: string, popUpType: string) {
		_modalView.findModal()
				  .wrapElements()
				  .find(`.${className} .caret`)
				  .parent(`button`)
				  .first()
				  .should('exist')
				  .click({force:true})
				  .then(() => {
					  cy.document()
						.its('body')
						.find('.popup-container')
						.within(() => {
							switch (popUpType) {
								case 'list'.toLowerCase():
									cy.contains('li', value).click({ force: true });
									break;
								case 'grid':
									cy.get('div.popup-content').within(() => {
										cy.wait(1000);
										cy.get(`div[class*='${app.SubContainerLayout.COLUMN_ID}']`).each(($cell) => {
											const cellField: string = $cell.text();
											if (value === cellField) {
												cy.wait(1000);
												cy.wrap($cell).click({ force: true });
												cy.wait(1000);
												return false;
											}
										});
									});
									break;
								case 'grid1'.toLowerCase():
									cy.contains('button', value).click({ force: true });
									break;
								case 'span'.toLowerCase():
									cy.contains('span', value).click({ force: true });
									break;
								case 'listexact'.toLowerCase():
									cy.contains('li', new RegExp('^' + value + '$', 'g')).click({ force: true });
									break;
								default:
									cy.log('Search type not supported');
							}
						});
			});
	}

	enterRecord_inActiveRow_fromModal(gridCellClass: string, inputFieldClass: string, inputData: string, recordType?: string) {
		_modalView.findModal()
				  .findActiveRow()
				  .findCell(gridCellClass, recordType)
				  .findTextInput(inputFieldClass)
				  .clear({ force: true })
				  .type(inputData);
	}

	select_rowHasValue_fromModal(cellValue: string, bodyIndex?: number) {
		_modalView
			.findModal()
			.findModalBody()
			.wrapElements()
			.within(() => {
				if (bodyIndex!) {
					cy.get(commonLocators.CommonElements.GRID_CONTAINER).eq(bodyIndex);
					cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, cellValue).then(($cell) => {
						if ($cell) {
							cy.wrap($cell).click({ force: true });
							cy.wait(1000);
							cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).click();
						} else {
							cy.get(commonLocators.CommonElements.GRID_CONTAINER);
							cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, cellValue).then(($cell) => {
								if ($cell) {
									cy.wrap($cell).click({ force: true });
									cy.wait(1000);
									cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).click();
								}
							});
						}
					});
				}
			});
	}

	set_cellCheckboxValue_fromModal(checkboxCell: string, checkBoxValue: string, recordType?: string) {
		_modalView
			.findModal()
			.findActiveRow()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check();
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck();
					}
				}
			});
	}

	set_cellCheckboxValue_fromModal_byLabelName(checkboxCell: string, checkBoxValue: string, recordType?: string) {
		_modalView
			.findModal()
			.findActiveRow()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check();
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck();
					}
				}
			});
	}

	set_checkboxValueForAllRowCell_fromModal(checkboxCell: string, checkBoxValue: string, recordType?: string) {
		_modalView
			.findModal()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check({ force: true });
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck({ force: true });
					}
				}
			});
	}

	getText_fromCell_fromModal(cellClass: string, recordType?) {
		return _modalView.findModal().findActiveRow().getCell(cellClass, recordType).wrapElements();
	}
	findRadio_byLabel_Index_fromModal(labelName: string, inputClass: string, classIndex:number,index: number, radioClass: string) {
		_modalView.findModal().findRadio_byLabel_Index_InModal(labelName ,inputClass,classIndex,index,radioClass);
	}
/**
	 * @deprecated This implementation is no longer supported.
	 * Please use the `findRadio_underModal_byClass` method instead.
	*/
	findRadio_byLabel_fromModal(labelName: string, inputClass: string, index: number, radioClass: string) {
		_modalView.findModal().findRadio_byLabel_InModal(labelName, inputClass, index, radioClass);
	}

	findRadio_fromModal_byClass(className: string, id:string) {
        _modalView.findModal()
                  .findRadio_underModal_byClass(className, id);
    }

	changeStatus_ofMultipleRecord_fromModal(option: string, option2?: string) {
		_modalView.findModal().containsValue(option).click();
		_modalView.findModal().acceptButton('Next');

		if (option2) {
			_modalView.findModal().containsValue(option2).click();
			_modalView.findModal().acceptButton('Next');
		}
		cy.wait(2000); //Required wait
		_modalView.findModal().acceptButton(btn.ButtonText.CLOSE);
	}

	changeStatus_fromModal(option: string, remark?: string) {
		_modalView.findModal()
				  .containsValue(option)
			      .click()
				  .then(() => {
						if (remark) {
							_modalView.findModal()
									  .findModalBody()
									  .findTextAreaInModal(app.InputFields.DOMAIN_TYPE_REMARK)
									  .type(remark);
						}
						cy.wait(2000);
						_modalView.findModal()
								  .acceptButton(btn.ButtonText.OK);
				   });
	}
	/**
	 * @deprecated This implementation is no longer supported.
	 * Please use the `clickOn_checkboxUnderModal_byClass` method instead.
	*/
	clickOn_checkboxByLabel_fromModal(rowInputClass: string, labelName: string, index: number,checkboxValue?:string) {
		_modalView.findModal().findCheckbox_byLabelnModal(rowInputClass, labelName, index,checkboxValue);
	}

	clickOn_checkboxUnderModal_byClass(className:string,checkBoxValue:string) {
		_modalView.findModal()
				  .findCheckbox_underModal_byClass(className,checkBoxValue);
	}

	select_cellHasValueWithIndexBySearch_fromModal(modalLabel: string, searchValue: string, popUpGridCell: string) {
		_modalView.findModal().searchInModal_byDataNG_Selector(modalLabel, searchValue);
		cy.wait(1000);
		_modalView.findModal().findButton(btn.IconButtons.ICO_SEARCH).clickIn();
		cy.wait(1000);
		_common.clickOn_cellHasValueWithIndex_fromModal(popUpGridCell, searchValue, 0);
		_common.clickOn_modalFooterButton(btn.ButtonText.OK);
	}

	findCaret_withDynamicInputClass_fromModal(inputClass: string, labelName: string) {
		this.element = this.element.concat(`[class*='modal-dialog'] ${inputClass}`);
		cy.wait(2000);
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`.caret`).eq(0).should('be.visible').click();
			});
		this.element = '';
		return div;
	}

	clickOn_modalButtonByClass(btnClass: string) {
		_modalView.findModal().findModalBody().findButton(btnClass).clickIn();
	}

	handledErrorPopUp_IfExist() {
		// Waiting for modal to appear as it is taking time to load
		_modalView
			.findModal()
			.wrapElements()
			.then(($err) => {
				if ($err.find(`[class*='icon tlb-icons ico-error']`).length > 0) {
					_common.clickOn_modalFooterButton(btn.ButtonText.OK);
				} else {
					cy.log('No error PopUp Exist');
				}
			});
	}

	selectValue_fromModal(value: string) {
		_modalView.findModalBody().wrapElements().contains(value).eq(0).click({ force: true });
	}

	goToButton_inActiveRow(containerUUID: string, gridCell: string, buttonClass?: string, btnName?: string, recordType?: string) {
		_mainView.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.findActiveRow()
			.getCell(gridCell, recordType)
			.wrapElements()
			.rightclick({ force: true })
			.then(() => {
				cy.wait(1000);
				if (buttonClass && !btnName) {
					cy.get('body')
						.then($body => {
							if ($body.find('.popup-content .ico-goto').length > 0) {
								cy.get(`.popup-content .ico-goto`)
									.click()
									.then(() => {
										if(buttonClass == "ico-goto")
										{
											cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
											.last()
											.within(() => {
												cy.get(`button.${buttonClass}`).last().click({ force: true });
											});
										}
										else{
										cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
											.last()
											.within(() => {
												cy.get(`button.${buttonClass}`).first().click({ force: true });
											});
										}	
									})
							}

						})
				}
				if (buttonClass && btnName) {
					
					cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
						.last()
						.within(() => {
							cy.get(`button.${buttonClass}`).contains(btnName).first().click({ force: true });
						});
				}

			});
		cy.wait(2000);
	}


	clickOn_gotToButton_inActiveRow_fromModal(gridCell: string) {
		cy.get('.popup-container')
			.within(() => {
				_mainView.findGrid().findActiveRow().getCell(gridCell).wrapElements().rightclick({ force: true });
			})
			.then(() => {
				cy.get('.popup-content')
					.first()
					.within(() => {
						cy.get('button.ico-goto').click({ force: true });
					});
				cy.wait(2000);
			});
	}

	set_columnAtTop(columnName: string[], container_UUID: string) {
		_common.waitForLoaderToDisappear();
		cy.REFRESH_CONTAINER();
		_common.waitForLoaderToDisappear();
		cy.get(`.cid_${container_UUID} button.${btn.ToolBar.ICO_MAXIMIZED_2}`).click();
		cy.get(`.cid_${container_UUID} .toolbar button.${btn.ToolBar.ICO_SETTINGS}`).click();
		cy.wait(1000);
		cy.get('body').then(($body) => {
			//! New UI changed introduced on Grid Layout
			// TODO: Unique locator will be added later
			if ($body.find(`[class*='popup-menu'] button[title='Configure Table']`).length > 0) {
				cy.wait(500);
				cy.get(`[class*='popup-menu'] button[title='Configure Table']`).click();
			}
		});
		cy.wait(1000);
		for (let index = 0; index < columnName.length; index++) {
			const colName = columnName[index];
			cy.get(`${commonLocators.CommonElements.GRID_CONFIGURATOR_ID} .${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
			.click();
			cy.wait(1000)
			cy.get(commonLocators.CommonElements.GRID_CONFIGURATOR_ID + ' ' + `input.${app.InputFields.FILTER_INPUT}`)
				.type(colName)
				.then(()=>{
							_modalView.findModal()
								.wrapElements()
								.find(`${commonLocators.CommonElements.GRID_CONFIGURATOR_ID} .${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
								.click({force:true})
						})
			cy.wait(2000);
			cy.get(commonLocators.CommonModalElements.MODAL_DIALOG_CLASS + ' ' + commonLocators.CommonElements.GRID_CONFIGURATOR_ID + ' .' + commonLocators.CommonElements.COLUMN_ID + app.GridCells.FIELD_NAME).each(($el, nameIndex, $list) => {
				const ActVal = $el.text();
				cy.log(ActVal);
				if (ActVal == colName) {
					cy.wait(2000);
					cy.wrap($el).click();
				}
			});
			cy.wait(1000);
			cy.get(`${commonLocators.CommonElements.GRID_CONFIGURATOR_ID} .${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`).click();
			cy.wait(1000);
			cy.get("[class*='" + btn.GridButtons.ICO_GRID_ROW_START + "']").click({ force: true });
			cy.wait(1000);
		}
		cy.wait(2000);
		_modalView.findModal().acceptButton(btn.ButtonText.OK);
		cy.wait(2000);
		cy.get(`.cid_${container_UUID} button.${btn.ToolBar.ICO_MINIMIZED_2}`).click();
	}

	set_columnAtTop_withoutRefresh(columnName: string[], container_UUID: string) {
		_common.waitForLoaderToDisappear();
		_common.waitForLoaderToDisappear();
		cy.get(`[class*='${container_UUID}'] ` + commonLocators.CommonContainerElements.MAXIMIZE).click();
		cy.get(`[class*='${container_UUID}'] .toolbar` + commonLocators.CommonElements.ICON_SETTING).click();
		cy.wait(1000);
		cy.get('body').then(($body) => {
			//! New UI changed introduced on Grid Layout
			// TODO: Unique locator will be added later
			if ($body.find(`[class*='popup-menu'] button[title='Configure Table']`).length > 0) {
				cy.wait(500);
				cy.get(`[class*='popup-menu'] button[title='Configure Table']`).click();
			}
		});
		cy.wait(1000);
		for (let index = 0; index < columnName.length; index++) {
			const colName = columnName[index];
			cy.get(`${commonLocators.CommonElements.GRID_CONFIGURATOR_ID} .${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`).click();
			cy.wait(1000);
			cy.get(commonLocators.CommonElements.GRID_CONFIGURATOR_ID + ' ' + `input.${app.InputFields.FILTER_INPUT}`)
				.type(colName)
				.then(()=>{
							_modalView.findModal()
								.wrapElements()
								.find(`${commonLocators.CommonElements.GRID_CONFIGURATOR_ID} .${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
								.click({force:true})
						})
			cy.wait(2000);
			cy.get(commonLocators.CommonModalElements.MODAL_DIALOG_CLASS + ' ' + commonLocators.CommonElements.GRID_CONFIGURATOR_ID + ' .' + commonLocators.CommonElements.COLUMN_ID + app.GridCells.FIELD_NAME).each(($el, nameIndex, $list) => {
				const ActVal = $el.text();
				cy.log(ActVal);
				if (ActVal == colName) {
					cy.wait(2000);
					cy.wrap($el).click();
				}
			});
			cy.wait(1000);
			cy.get(`${commonLocators.CommonElements.GRID_CONFIGURATOR_ID} .${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`).click();
			cy.wait(1000);
			cy.get("[class*='" + btn.GridButtons.ICO_GRID_ROW_START + "']").click({ force: true });
			cy.wait(1000);
		}
		cy.wait(2000);
		_modalView.findModal().acceptButton(btn.ButtonText.OK);
		cy.wait(2000);
		cy.get(`[class*='${container_UUID}'] ` + commonLocators.CommonContainerElements.MINIMIZE).click();
	}

	/**
    * @deprecated This implementation is no longer supported. Use the class-based POM approach for modal inputs and dropdowns with inputs.
    */

	inputField_fromModal(rowClass: string, labelName: string, Index: number, inputClass: string): Cypress.Chainable<any> {
		this.element = this.element.concat(`[class*='modal-dialog'] ${rowClass}`);
		cy.wait(2000);
		const div = cy
			.get(this.element)
			.contains(this.element, new RegExp(`^\\s*${labelName}\\s*$`))
			.eq(Index)
			.then((ele) => {
				cy.wrap(ele).find(`[class*='${inputClass}']`).eq(0);
			});
		this.element = '';
		return div;
	}

	//! Assertions related common method

	assert_forNumericValues(containerUUID: string, gridCellClass: string, expectedValue: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				if (recordType) {
					cy.get(`[class*='active'] [class*='${app.SubContainerLayout.COLUMN_ID}_${recordType}.${gridCellClass}']`).then(($el) => {
						const ActVal = $el.text().replace(/,/g, '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(/,/g, '')).toFixed(2)).to.equals(parseFloat(ActVal).toFixed(2));
					});
				} else {
					cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
						const ActVal = $el.text().replace(/,/g, '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(/,/g, '')).toFixed(2)).to.equals(parseFloat(ActVal).toFixed(2));
					});
				}
			});
	}

	assert_cellData(containerUUID: string, gridCellClass: string, expectedValue: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.get(`div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`)
					.first()
					.then(($el) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(expectedValue).to.equals(ActVal.trim());
					});
			});
	}

	assert_cellData_insideActiveRow(containerUUID: string, gridCellClass: string, expectedValue: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				if (recordType) {
					cy.get(`[class*='active'] [class*='${app.SubContainerLayout.COLUMN_ID}_${recordType}.${gridCellClass}']`).then(($el) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(expectedValue).to.equals(ActVal.trim());
					});
				} else {
					cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(expectedValue).to.equals(ActVal.trim());
					});
				}
			});
	}

	assert_forNumericValues_notEqualCondition(containerUUID: string, gridCellClass: string, expectedValue: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				if (recordType) {
					cy.get(`[class*='active'] [class*='${app.SubContainerLayout.COLUMN_ID}_${recordType}.${gridCellClass}']`).then(($el) => {
						const ActVal = $el.text().replace(',', '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(',', '')).toFixed(2)).to.not.equals(parseFloat(ActVal).toFixed(2));
					});
				} else {
					cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
						const ActVal = $el.text().replace(',', '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(',', '')).toFixed(2)).to.not.equals(parseFloat(ActVal).toFixed(2));
					});
				}
			});
	}

	assert_cellData_whereRecordIsNotEqual(containerUUID: string, gridCellClass: string, actualValue: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				if (recordType) {
					cy.get(`[class*='active'] [class*='${app.SubContainerLayout.COLUMN_ID}_${recordType}.${gridCellClass}']`).each(($el, index, $list) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(ActVal).to.not.equals(actualValue);
					});
				} else {
					cy.get(`[class*='active'] div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass} `).each(($el, index, $list) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(ActVal).to.not.equals(actualValue);
					});
				}
			});
	}

	assert_getText_fromContainerForm(containerUUID: string, labelName: string, expectedValue: string) {
		_mainView.getTextFromContainerFormByLabel(containerUUID, labelName);
		cy.wait(500).then(() => {
			expect(Cypress.env('getTextInContainerForm')).to.contain(expectedValue);
		});
	}

	assert_cellDataByContent_inContainer(containerUUID: string, gridCellClass: string, expectedValue: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.get(`div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`)
					.eq(0)
					.then(($el) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(ActVal).to.contain(expectedValue);
					});
			});
	}

	assert_activeRow_cellDataByContent_inContainer(containerUUID: string, gridCellClass: string, expectedValue: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.get(`[class*='active'] div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`)
					.eq(0)
					.then(($el) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(ActVal).to.contain(expectedValue);
					});
			});
	}

	assert_cellDataByContent_fromModal(gridCellClass: string, expectedValue: string) {
		_modalView
			.findModal()
			.findActiveRow()
			.getCell(gridCellClass)
			.wrapElements()
			.then(($el) => {
				const ActVal = $el.text();
				cy.log(ActVal);
				expect(ActVal).to.contain(expectedValue);
			});
	}

	assert_forNumericValues_fromModal(gridCellClass: string, expectedValue: string) {
		_modalView
			.findModal()
			.wrapElements()
			.within(() => {
				cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
					const ActVal = $el.text().replace(',', '');
					cy.log(ActVal);
					var ActVAlue = parseInt(ActVal).toFixed(0);
					expect(expectedValue).to.equals(ActVAlue);
				});
			});
	}

	getText_fromPopUpContent_fromCaret(containerUUID: string, cellType: string, envVariableName: string, containerPosition?, recordType?) {
		var Column_Data: string[] = [];

		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(cellType, recordType).caret();
		cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`).within(() => {
			cy.get(`[class*='${commonLocators.CommonElements.LOOKUP_ITEM}']`).each(($lookUpItem) => {
				$lookUpItem.text();
				var data = $lookUpItem.text();
				Column_Data.push(data);
				cy.log('columData ' + Column_Data);
				Cypress.env(envVariableName, Column_Data);
			});
		});
	}

	verify_activeRow_fromModal(gridCell: string, expectedValue: string) {
		_modalView
			.findModal()
			.findActiveRow()
			.getCell(gridCell)
			.wrapElements()
			.eq(0)
			.then(($ele) => {
				var ccQuantity = $ele.text();
				cy.log(ccQuantity);
				Cypress.env('GetTextValueFromPopUp', ccQuantity);
				expect(ccQuantity).to.contain(expectedValue);
			});
	}

	getText_fromCell_storeInArray(containerUUID: string, gridCells: string, envVariableName: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.getCell(app.SubContainerLayout.INDICATOR)
			.wrapElements()
			.then(($ele) => {
				var NumberOfRows = $ele.length;
				var Column_Data: string[] = [];
				for (var i = 0; i <= NumberOfRows - 1; i++) {
					cy.get(`[class*='cid_${containerUUID}'] div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).eq(i).click();
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.findActiveRow()
						.getCell(gridCells)
						.wrapElements()
						.then(($value) => {
							$value.text();
							var data = $value.text();
							Column_Data.push(data);
							var Column_Data1 = Column_Data.toString();
							cy.log('columData ' + Column_Data1);
							Cypress.env(envVariableName, Column_Data1);
						});
				}
			});
	}

	getText_storeIntoArray(containerUUID: string, cellClass: string, arrEnvName: string, recordType?: string) {
		const Arr: string[] = [];
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.getCell(cellClass, recordType)
			.wrapElements()
			.each((record) => {
				const description: string = record.text();
				Arr.push(description);
			})
			.then(() => {
				Cypress.env(arrEnvName, Arr);
				Arr.forEach((description, index) => {
					cy.log(`Description[${index}]: ${description}`);
				});
			});
	}

	verify_cellHasValue_fromModal(cellClass: string, value: string) {
		_modalView.findModal().getCell(cellClass).wrapElements().should('include.text', value);
	}

	Verify_cellNotIncludeText_fromModal(cellClass: string, text: any) {
		_modalView.findModalBody().findActiveRow().getCell(cellClass).wrapElements().should('not.include.text', text);
	}

	assert_labelValues_fromModalWithPanelGroup(labelName: string, inputClass: string, panelGroup: string, expectedValue: string) {
		_modalView
			.findModal()
			.wrapElements()
			.get(commonLocators.CommonElements.PLATFORM_FORM_GROUP_HEADER_TEXT)
			.contains(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS + ' ' + commonLocators.CommonElements.PLATFORM_FORM_GROUP_HEADER_TEXT, panelGroup)
			.closest(commonLocators.CommonElements.PANEL_GROUP)
			.within(($val) => {
				cy.wrap($val)
					.contains(commonLocators.CommonElements.PLATFORM_FORM_LABEL, labelName)
					.closest(commonLocators.CommonElements.ROW)
					.within((ele) => {
						cy.wrap(ele)
							.find(`[class*='${inputClass}']`)
							.invoke('val')
							.then((val) => {
								var actualValue = val;
								expect(actualValue).to.equal(expectedValue);
							});
					});
			});
	}

	assert_labelValues_fromModal(genericRowClass: string, labelName: string, inputClass: string, expectedValue: string, invokeType?: string) {
		if (invokeType === 'text') {
			_modalView
				.findModalBody()
				.wrapElements()
				.within((ele) => {
					cy.wrap(ele)
						.get(genericRowClass)
						.contains(genericRowClass, labelName)
						.then((ele) => {
							cy.wrap(ele)
								.find("[class*='" + inputClass + "']")
								.eq(0)
								.invoke('text')
								.then((val) => {
									var actualValue = val;
									expect(actualValue).to.equals(expectedValue);
								});
						});
				});
		} else {
			_modalView
				.findModalBody()
				.wrapElements()
				.within((ele) => {
					cy.wrap(ele)
						.get(genericRowClass)
						.contains(genericRowClass, labelName)
						.then((ele) => {
							cy.wrap(ele)
								.find("[class*='" + inputClass + "']")
								.eq(0)
								.invoke('val')
								.then((val) => {
									var actualValue = val;
									expect(actualValue).to.equals(expectedValue);
								});
						});
				});
		}
	}

	getText_ofCartField(containerUUID: string, rowClass: string, inputField: string, index: number, envVariableNumber?) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.wrapElements()
			.find("[class*='" + rowClass + "'] [class*='" + inputField + "']")
			.eq(index)
			.then(($Value) => {
				let ValueNumber = $Value.text();
				Cypress.env(envVariableNumber, parseFloat(ValueNumber));
				Cypress.env('stringVariable', ValueNumber);
			});
	}

	getText_ofCartFieldWithInvoke(containerUUID: string, rowClass: string, inputField: string, index: number, envVariableNumber?) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.wrapElements()
			.find("[class*='" + rowClass + "'] [class*='" + inputField + "']")
			.eq(index)
			.invoke('val')
			.then((valueNumber) => {
				cy.log(typeof valueNumber);
				Cypress.env(envVariableNumber, valueNumber);
				Cypress.env('stringVariable', valueNumber);
			});
	}

	getText_CellData_fromModal(genericRowClass: string, labelName: string, inputClass: string, envName: string) {
		_modalView
			.findModalBody()
			.wrapElements()
			.within((ele) => {
				cy.wrap(ele)
					.get(genericRowClass)
					.contains(genericRowClass, labelName)
					.then((ele) => {
						cy.wrap(ele)
							.find("[class*='" + inputClass + "']")
							.eq(0)
							.invoke('val')
							.then((val) => {
								Cypress.env(envName, val);
							});
					});
			});
	}

	verify_gridCellHasNoText(containerUUID: string, gridCell: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).getCell(gridCell).wrapElements().should('not.contain', 'text');
	}

	perform_additionOfCellData(UUID1: string, cellClass: string) {
		let sum = 0;
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(UUID1)
			.getCell(cellClass)
			.wrapElements()
			.each(($cell) => {
				const value = $cell.text().replace(/,/g, '').trim();
				const numericValue = parseFloat(value);
				sum += numericValue;
				Cypress.env('AdditionOfColumnValues', sum);
			});
	}

	// NA
	getTextfromCell_with_Header_inRecord(uuid: string, gridCells_1, recordType?: string, gridCells_2?, gridcelss_3?) {
		if (gridCells_1 && recordType != null) {
			_mainView
				.findModuleClientArea()
				.findAndShowContainer(uuid)
				.findGrid()
				.findActiveRow()
				.getCell(gridCells_1, recordType)
				.wrapElements()
				.then(($ele1) => {
					cy.log($ele1.text());
					Cypress.env('Text', $ele1.text());
				});
		}
		cy.wait(2000).then(() => {
			if (gridCells_2 != null) {
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(uuid)
					.findGrid()
					.findActiveRow()
					.getCell(gridCells_2)
					.wrapElements()
					.then(($ele2) => {
						cy.log($ele2.text());
						Cypress.env('gridcell_2_Text', $ele2.text());
					});
			}
			if (gridcelss_3 != null) {
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(uuid)
					.findGrid()
					.findActiveRow()
					.getCell(gridcelss_3)
					.wrapElements()
					.then(($ele1) => {
						cy.log($ele1.text());
						Cypress.env('gridcelss_3_text', $ele1.text());
					});
			}
		});
	}

	getTextfromCell(uuid: string, gridCells_1, gridCells_2?, gridcelss_3?) {
		if (gridCells_1 != null) {
			_mainView
				.findModuleClientArea()
				.findAndShowContainer(uuid)
				.findGrid()
				.findActiveRow()
				.getCell(gridCells_1)
				.wrapElements()
				.then(($ele1) => {
					cy.log($ele1.text());
					Cypress.env('Text', $ele1.text());
				});
		}
		cy.wait(2000).then(() => {
			if (gridCells_2 != null) {
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(uuid)
					.findGrid()
					.findActiveRow()
					.getCell(gridCells_2)
					.wrapElements()
					.then(($ele2) => {
						cy.log($ele2.text());

						Cypress.env('gridcell_2_Text', $ele2.text());
					});
			}
			cy.wait(2000).then(() => {
				if (gridcelss_3 != null) {
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(uuid)
						.findGrid()
						.findActiveRow()
						.getCell(gridcelss_3)
						.wrapElements()
						.then(($ele1) => {
							cy.log($ele1.text());
							Cypress.env('gridcell_3_Text', $ele1.text());
						});
				}
			});
		});
	}
	set_checkBoxValue_basedOnString(uuid: string, gridCellClass: string, checkboxGridCellClass: string, data: any) {
		Object.keys(data).forEach(function (key) {
			_common.waitForLoaderToDisappear();
			_common.waitForLoaderToDisappear();
			_common.clear_subContainerFilter(uuid);
			_common.waitForLoaderToDisappear();
			_common.waitForLoaderToDisappear();
			_common.search_inSubContainer(uuid, key);
			_common.waitForLoaderToDisappear();
			_common.waitForLoaderToDisappear();
			_common.waitForLoaderToDisappear();
			_mainView.findModuleClientArea().findAndShowContainer(uuid).findGrid().findCellhasValue(gridCellClass, key).click();
			_mainView
				.findModuleClientArea()
				.findAndShowContainer(uuid)
				.findGrid()
				.findActiveRow()
				.getCell(checkboxGridCellClass)
				.wrapElements()
				.find(commonLocators.CommonElements.CHECKBOX_TYPE)
				.as('checkbox')
				.invoke('is', ':checked')
				.then((checked) => {
					if (data[key] == 'check') {
						if (!checked) {
							cy.get('@checkbox').check();
						}
					} else if (data[key] == 'uncheck') {
						if (checked) {
							cy.get('@checkbox').uncheck();
						}
					}
				});
		});
	}

	clearOrType_inPlainTextArea_inContainer(container_UUID: string, textAreaClass: string, value?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.wrapElements()
			.within(() => {
				cy.get(textAreaClass).clear({ force: true });
				if (value != null) {
					cy.get(textAreaClass).clear({ force: true }).type(value, { force: true });
				}
			});
	}

	addRecord_inSubContainer_ifNotExist(container_UUID, index) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.findGrid()
			.wrapElements()
			.find("[class*='grid-canvas grid-canvas-top grid-canvas-left']")
			.eq(index)
			.then(($ele) => {
				if ($ele.find("[class*='ui-widget-content']").length > 0) {
				} else {
					_common.create_newRecord(container_UUID);
				}
			});
	}
	/*
	 * This is used to select_dropdownCellWithInput_basedOnIndex_forDiv
	 * Updated Date: 2/15/2024
	 * Author : Anupama G
	 */

	select_dropdownCellWithInput_basedOnIndex_forDiv(containerUUID: string, cellType: string, indexValue: number, cellInputType: string, value: string, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID, containerPosition).findGrid().findActiveRow().findCell(cellType, recordType);
		_common.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.findCell(cellType, recordType)
			.findTextInput(cellInputType)
			.clear({ force: true })
			.type(value)
			.then(() => {
				cy.wait(2000); // This wait is necessary

				cy.get('div.popup-content').within(() => {
					cy.wait(1000);
					cy.get(`div[class*='column-id']`).each(($cell, index) => {
						if (index === indexValue) {
							// Index 1 corresponds to the second element (index is zero-based)
							cy.wait(1000);
							cy.wrap($cell).click({ force: true });
							cy.wait(2000);
							return false; // Stop the loop after clicking the second element
						}
					});
				});
			});
	}
	/*
	 * This is used to wait for progress loads to 100 %
	 * Updated Date: 01/03/2024
	 * Author : Harshal Shinkar
	 */
	waitForProgressBarToLoad() {
		cy.get('[class="progress-bar"] > span').should('have.text', '100%', { timeout: 10000, interval: 1000 });
	}

	edit_dropdownCellWithButton(container_UUID: string, cellClass: string, popUpType: string, value: string) {
		_common.waitForLoaderToDisappear();
		_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findActiveRow().findCell(cellClass).findInputLookup(app.InputFields.ICO_DOWN, 0);
		cy.wait(2000); //  Added this wait as data take time to load
		_mainView.select_popupItem(popUpType, value);
	}

	assert_forNumericValues_mathsAbs(containerUUID: string, gridCellClass: string, expectedValue: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				if (recordType) {
					cy.get(`[class*='active'] [class*='${app.SubContainerLayout.COLUMN_ID}_${recordType}.${gridCellClass}']`).then(($el) => {
						const ActVal = $el.text().replace(',', '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(',', '')).toFixed(2)).to.equals(Math.abs(parseFloat(ActVal)).toFixed(2));
					});
				} else {
					cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
						const ActVal = $el.text().replace(',', '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(',', '')).toFixed(2)).to.equals(Math.abs(parseFloat(ActVal)).toFixed(2));
					});
				}
			});
	}
	/*
	 * This is used to create caluclate the percentage
	 * Date: 20/3/2024
	 * Author : Anupama G
	 */

	calculatePercentage_inContainer(container_UUID, value1, value2, cellClass) {
		return _mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.findGrid()
			.findActiveRow()
			.getCell(cellClass)
			.wrapElements()
			.invoke('text')
			.then((cellText) => {
				// Remove any non-numeric characters from the text (if needed)
				const cellValue = parseFloat(cellText.replace(/[^\d.]/g, ''));

				// Calculate the expected value using calculatePercentage function
				const percentage = value1;
				const number = value2;
				let expectedValue = calculatePercentage(percentage, number);
				function calculatePercentage(percentage, number) {
					return (percentage / 100) * number;
				}

				expectedValue = parseFloat(expectedValue.toFixed(2));
				expect(cellValue).to.equal(expectedValue);
			});
	}

	search_dataFromLookups_fromModal(container_UUID: string, cellClass: string, searchValue: string, inputTextClass: string, gridCell: string, value: string) {
		_common.waitForLoaderToDisappear();
		_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findActiveRow().findCell(cellClass).findInputLookup(app.InputFields.ICO_INPUT_LOOKUP, 0);
		cy.wait(2000); //  Added this wait as data take time to load
		_common.waitForLoaderToDisappear();
		_modalView.findModal().findTextInput(inputTextClass).last().clear().type(searchValue);
		_modalView.findModal().findButton(btn.IconButtons.ICO_SEARCH).wrapElements().first().click();
		_modalView.findModal().wrapElements().find(`[class*='${app.SubContainerLayout.COLUMN_ID}']`).contains(searchValue).click();
		_modalView.findModal().findModalBody().findCell(gridCell).findButton(btn.IconButtons.ICO_TREE_COLLAPSE).clickIn();
		_modalView.findModalBody().wrapElements().contains(value).eq(0).click({ force: true });
		_modalView.findModal().acceptButton(btn.ButtonText.OK);
		_common.waitForLoaderToDisappear();
	}

	select_reportPeriod(periodDate: string) {
		cy.get("div[class*='right-side flex-element']").contains(commonLocators.CommonLabels.REPORT_PERIOD).click();
		cy.get("ul[class*='popup-menu']").contains(periodDate).click();
	}

	select_dataFromContainersForm_caret(container_UUID: string, labelName: string, popUpType: string, value: string) {
		cy.get(`[class*='cid_${container_UUID}'] ` + commonLocators.CommonElements.ROW)
			.contains(`[class*='cid_${container_UUID}'] ` + commonLocators.CommonElements.ROW, labelName)
			.then((ele) => {
				_mainView
					.findModuleClientArea()
					.findCaretByLabel(labelName)
					.then(() => {
						cy.document()
							.its('body')
							.within(() => {
								_modalView.select_popupItem(popUpType, value);
								cy.wait(1000);
							});
					});
			});
	}

	clear_inTextAreaOfContainer(containerUUID: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findTextAreaInModal(app.InputFields.K_CONTENT_FRAME).clear();
	}

	verify_gridCellWithClass_InModal() {
		_modalView
			.findModalBody()
			.wrapElements()
			.then(($cell) => {
				cy.get("[class*='invalid-cell']").should('exist');
			});
	}

	verify_gridCellWithInvalidClass(containerUUID: string, gridCell: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.findActiveRow()
			.getCell(gridCell)
			.wrapElements()
			.then(($cell) => {
				cy.get("[class*='invalid-cell']").should('exist');
			});
	}

	find_dynamicInput_inContainerByLabel(uuid: string, inputClass: string, labelName: string): Cypress.Chainable<any> {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`[class*="${inputClass}"]`).eq(0).should('be.visible');
			});
		this.element = '';
		return div;
	}

	clickOn_buttonWithText_inContainer(container_UUID: string, buttonText: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.wrapElements()
			.then(() => {
				cy.contains('button', buttonText).click({ force: true });
			});
	}

	groupingAndUngroupingRecordsUsingColumns_inContainer(container_UUID: string, columnId: string[]) {
		//This POM can be used to group Items in container having Ico-Group-Column Button. This POM is used to Group Data as per columns, User can drag and drop column header in droppable area to Group data records.
		//Created By - Rushikesh Mahale (20/06/2024)
		cy.wait(1000); //Required wait
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_GROUP_COLUMNS)
			.assertClass('active')
			.then((activeToolbarButton) => {
				if (activeToolbarButton) {
					cy.wait(2000); //Required wait to load droppable area in container after adding new one.
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(container_UUID)
						.wrapElements()
						.then(($el) => {
							let len = $el.find(`[class*='${CommonLocators.CommonElements.UI_DROPPABLE}'] [class*='${btn.IconButtons.ICO_CLOSE}']`).length;
							if (len > 0) {
								for (let index = 0; index < len; index++) {
									cy.get(`[class*='${CommonLocators.CommonElements.UI_DROPPABLE}'] [class*='${btn.IconButtons.ICO_CLOSE}']`).first().click();
								}
							}
						});
					_common.waitForLoaderToDisappear();
					for (let index = 0; index < columnId.length; index++) {
						const colName = columnId[index];
						cy.wait(2000); //Required wait to load droppable area in container after adding new one.
						cy.get(`.cid_${container_UUID} [class*='${CommonLocators.CommonElements.SLICK_PANE_HEADER}'] [id*='${colName}']`).first().as('Drag_location');
						_common.waitForLoaderToDisappear();
						cy.get(`.cid_${container_UUID} [class*='${CommonLocators.CommonElements.UI_DROPPABLE}']`).first().as('Drop_location');
						_common.waitForLoaderToDisappear();
						_mainView.dragAndDrop('@Drag_location', '@Drop_location');
					}
				} else {
					cy.wait(2000); //Required wait to load droppable area in container after adding new one.
					_common.clickOn_toolbarButton(container_UUID, btn.ToolBar.ICO_GROUP_COLUMNS);
					_common.waitForLoaderToDisappear();
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(container_UUID)
						.wrapElements()
						.then(($el) => {
							let len = $el.find(`[class*='${CommonLocators.CommonElements.UI_DROPPABLE}'] [class*='${btn.IconButtons.ICO_CLOSE}']`).length;
							if (len > 0) {
								for (let index = 0; index < len; index++) {
									cy.get(`[class*='${CommonLocators.CommonElements.UI_DROPPABLE}'] [class*='${btn.IconButtons.ICO_CLOSE}']`).first().click();
								}
							}
						});
					for (let index = 0; index < columnId.length; index++) {
						const colName = columnId[index];
						cy.wait(2000); //Required wait to load droppable area in container after adding new one.
						cy.get(`.cid_${container_UUID} [class*='${CommonLocators.CommonElements.SLICK_PANE_HEADER}'] [id*='${colName}']`).first().as('Drag_location');
						_common.waitForLoaderToDisappear();
						cy.get(`.cid_${container_UUID} [class*='${CommonLocators.CommonElements.UI_DROPPABLE}']`).first().as('Drop_location');
						_common.waitForLoaderToDisappear();
						_mainView.dragAndDrop('@Drag_location', '@Drop_location');
					}
				}
			});
	}
	select_multipleRow_keyboardAction(containerUUID, startValue, endValue) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, startValue).click({ force: true }).type('{shift}', { release: false });

				cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, endValue).click({ force: true }).type('{shift}', { release: true });
			});
	}

	/*This method use to click on toolbar button Update Price in Package Item Assignment container and select option */
	clickOn_updatePackage_toSelectOption(containerUUID: string, option: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.IconButtons.ICO_PRICE_UPDATE)
			.wrapElements()
			.click()
			.then(() => {
				_mainView.select_popupItem('span', option);
			});
	}

	activateToolbarButton_inContainer(container_UUID: string, buttonClass: string) {
		//This POM can be used to activate a Toolbar button, regardless of whether it is inactive or active.
		//Created By - Rushikesh Mahale (24/06/2024)
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(buttonClass)
			.assertClass('active')
			.then((activeToolbarButton) => {
				if (activeToolbarButton) {
					cy.log('Toolbar Button is Active');
				} else {
					_common.clickOn_toolbarButton(container_UUID, buttonClass);
					_common.waitForLoaderToDisappear();
				}
			});
	}

	clickOnSlickColumnButton(slickColumnName) {
		cy.get("[class*='cid_07b7499a1f314f16a94edddc540c55d4'] [class*='ui-sortable-handle']").contains(slickColumnName).click();
	}

	select_dataFromDropDown_typeInput_withoutGrid(containerUUID: string, popUpType: string, inputType: string, value: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findTextInput(inputType)
			.first()
			.clear({ force: true })
			.type(value)
			.then(() => {
				cy.wait(4000); // This wait is necessary
				_mainView.select_popupItem(popUpType, value);
			});
	}

	clearColumnFilter_inSubContainer(containerUUID: string, columnClass: string, recordType?:string) {
		this.waitForLoaderToDisappear();
		cy.wait(1000); //required wait
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH_COLUMN)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> column filter is present and direct searching <----');
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`.item-field_${columnClass} input`).clear({ force: true });
						});
				}
				else if (recordType){
					console.log('----> Clicking on column filter icon with header and searching <----');
					this.waitForLoaderToDisappear();
					cy.wait(1000); //required wait
					_mainView.findModuleClientArea().findAndShowContainer(containerUUID).wrapElements().find(`button[class*='${btn.ToolBar.ICO_SEARCH_COLUMN}']`).click();
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`[class*='item-field_${recordType}.${columnClass}'] input`).clear({ force: true });
						});
				}
				else  {
					console.log('----> Clicking on column filter icon and searching <----');
					this.waitForLoaderToDisappear();
					cy.wait(1000); //required wait
					_mainView.findModuleClientArea().findAndShowContainer(containerUUID).wrapElements().find(`button[class*='${btn.ToolBar.ICO_SEARCH_COLUMN}']`).click();
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`.item-field_${columnClass} input`).clear({ force: true });
						});
				}
			});
	}

	enterRecord_underContainerForm(uuid: string, label: string, labelclass: string, value: any) {
		_mainView.findInputInContainerByLabelClass(uuid, label, labelclass).wait(1000).clear({ force: true }).type(value, { force: true });
	}

	search_recordInSourceBoqContainer(container_UUID: string, data: DataCells) {
		switch (data[commonLocators.CommonKeys.CASE_TYPE]) {
			case 'WIC BoQ':
				_common.waitForLoaderToDisappear();
				_common.clear_subContainerFilter(container_UUID);
				_common.waitForLoaderToDisappear();
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(container_UUID)
					.findCaretByLabel(commonLocators.CommonLabels.COPY_FROM)
					.then(() => {
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.COPY_FROM]);
					});
				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.WIC_GROUP)
					.clear()
					.type(data[CommonLocators.CommonLabels.WIC_GROUP])
					.then(() => {
						_common.waitForLoaderToDisappear();
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.WIC_GROUP]);
					});
				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.BOQ_SELECTION)
					.clear()
					.type(data[CommonLocators.CommonLabels.BOQ_SELECTION])
					.then(() => {
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.BOQ_SELECTION]);
					});
				_common.waitForLoaderToDisappear();
				_common.clear_subContainerFilter(container_UUID);
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);

				_common.clear_subContainerFilter(container_UUID);
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				_common.clickOn_expandCollapseButton(container_UUID, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL);
				_common.waitForLoaderToDisappear();
				_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findCellhasValue(app.GridCells.BRIEF_INFO_SMALL, data[commonLocators.CommonKeys.VALUE]).click({ force: true });

				break;
			case 'Project BoQ':
				_mainView
					.findModuleClientArea()
					.findAndShowContainer(container_UUID)
					.findCaretByLabel(commonLocators.CommonLabels.COPY_FROM)
					.then(() => {
						_common.waitForLoaderToDisappear();
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.COPY_FROM]);
					});
				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.PROJECT)
					.click()
					.clear({ force: true })
					.type(data[CommonLocators.CommonLabels.PROJECT], { force: true })
					.then(() => {
						cy.wait(2000); // This wait is necessary
						cy.get('body').then(($body) => {
							if ($body.find(`${commonLocators.CommonElements.POPUP_FOOTER} .${btn.ToolBar.ICO_REFRESH}`).length > 0) {
								cy.get(`${commonLocators.CommonElements.POPUP_FOOTER} .${btn.ToolBar.ICO_REFRESH}`).click();
								cy.wait(2000); // This wait is necessary
							}
						});
					})
					.then(() => {
						_mainView
							.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.PROJECT)
							.clear()
							.type(data[CommonLocators.CommonLabels.PROJECT])
							.then(() => {
								_common.waitForLoaderToDisappear();
								_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.PROJECT]);
							});
					});

				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.BOQ_SELECTION)
					.clear()
					.type(data[CommonLocators.CommonLabels.BOQ_SELECTION])
					.then(() => {
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.BOQ_SELECTION]);
					});
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findCellhasValue(app.GridCells.BRIEF_INFO_SMALL, data[commonLocators.CommonKeys.VALUE]).click({ force: true });
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				break;
			case 'Package BOQ':
				_mainView.findCaretByLabel(commonLocators.CommonLabels.COPY_FROM).then(() => {
					_mainView.select_popupItem(CommonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.COPY_FROM]);
				});

				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.PROJECT)
					.clear()
					.type(data[CommonLocators.CommonLabels.PROJECT])
					.then(() => {
						_common.waitForLoaderToDisappear();
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.PROJECT]);
					});

				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.BOQ_SELECTION)
					.clear()
					.type(data[CommonLocators.CommonLabels.BOQ_SELECTION])
					.then(() => {
						_common.waitForLoaderToDisappear();
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.BOQ_SELECTION]);
					});

				_common.waitForLoaderToDisappear();
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findCellhasValue(app.GridCells.BRIEF_INFO_SMALL, data[commonLocators.CommonKeys.VALUE]).click({ force: true });
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				break;
			case 'Procurement Contract BoQ':
				_mainView.findCaretByLabel(commonLocators.CommonLabels.COPY_FROM).then(() => {
					_mainView.select_popupItem(CommonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.COPY_FROM]);
				});

				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.PROJECT)
					.clear()
					.type(data[CommonLocators.CommonLabels.PROJECT])
					.then(() => {
						_common.waitForLoaderToDisappear();
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.PROJECT]);
					});

				_mainView
					.findInputInContainerByLabel(container_UUID, CommonLocators.CommonLabels.BOQ_SELECTION)
					.clear()
					.type(data[CommonLocators.CommonLabels.BOQ_SELECTION])
					.then(() => {
						_mainView.select_popupItem(CommonLocators.CommonKeys.GRID, data[CommonLocators.CommonLabels.BOQ_SELECTION]);
					});

				_common.maximizeContainer(container_UUID);
				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findCellhasValue(app.GridCells.BRIEF_INFO_SMALL, data[commonLocators.CommonKeys.VALUE]).click({ force: true });

				_common.search_inSubContainer(container_UUID, data[commonLocators.CommonKeys.VALUE]);
				_common.waitForLoaderToDisappear();
				_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findCellhasValue(app.GridCells.BRIEF_INFO_SMALL, data[commonLocators.CommonKeys.VALUE]).click({ force: true });
				break;
			default:
				cy.log('Copy Value not present');
		}
	}

	set_numberMask(numberMask: string, checkMask: string, inputClass: string, value: string) {
		_mainView.findInputInContainerByLabel_inputClass(cnt.uuid.NUMBER_RANGES, numberMask, inputClass).clear().type(value);
		_mainView.findInputInContainerByLabel_inputClass(cnt.uuid.NUMBER_RANGES, checkMask, inputClass).clear().type(value);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
	}

	checkSearchOption_inSideBar_contractModule(data: DataCells) {
		cy.get(data[commonLocators.CommonKeys.CLASS])
			.contains(data[commonLocators.CommonElements.LABEL])
			.click({ force: true })
			.closest(`[class*='${app.InputFields.DOMAIN_TYPE_BOOLEAN}']`)
			.within(($ele) => {
				cy.wrap($ele)
					.find(commonLocators.CommonElements.CHECKBOX_INPUT)
					.as('checkbox')
					.invoke('is', ':checked')
					.then((checked) => {
						if (data[commonLocators.CommonKeys.VALUE] === commonLocators.CommonKeys.CHECK) {
							if (!checked) {
								cy.get('@checkbox').check();
							}
						} else if (data[commonLocators.CommonKeys.VALUE] === commonLocators.CommonKeys.UNCHECK) {
							if (checked) {
								cy.get('@checkbox').uncheck();
							}
						}
					});
			});
	}
	columnFilter_inSubContainer_dropDownWithCaret(containerUUID: string, columnClass: string, popUpType: string, value: string) {
		this.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH_COLUMN)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> column filter is present and direct searching <----');
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`.item-field_${columnClass}`).find(`[class*='input-group-content']`).click({ force: true });
						})
						.then(() => {
							cy.wait(4000); // popup data is taking time load so added wait
							_mainView.select_popupItem(popUpType, value);
						});
				} else {
					console.log('----> Clicking on column filter icon and searching <----');
					this.waitForLoaderToDisappear();
					cy.wait(1000);
					_mainView.findModuleClientArea().findAndShowContainer(containerUUID).wrapElements().find(`button[class*='${btn.ToolBar.ICO_SEARCH_COLUMN}']`).click();
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.within(() => {
							cy.get(`.item-field_${columnClass}`).find(`[class*='input-group-content']`).click({ force: true });
						})
						.then(() => {
							cy.wait(4000); // popup data is taking time load so added wait
							_mainView.select_popupItem(popUpType, value);
						});
				}
			});
	}
	getText_CellData_fromLabel(containerUUID: string, panelName: string, labelName: string, index: number, envName: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.wrapElements()
			.within((ele) => {
				cy.contains(commonLocators.CommonElements.PLATFORM_FORM_GROUP_HEADER_TEXT, panelName)
					.closest(`${commonLocators.CommonElements.PANEL_GROUP}`)
					.within(() => {
						cy.contains(commonLocators.CommonElements.PLATFORM_FORM_LABEL, labelName)
							.closest("[class*='platform-form-row']")
							.within((ele) => {
								cy.get(commonLocators.CommonElements.PLATFORM_FORM_COL)
									.find(commonLocators.CommonElements.DOMAIN_TYPE_MONEY_INPUT_GROUP_CONTENT)
									.eq(index)
									.invoke('val')
									.then((val) => {
										Cypress.env(envName, val);
									});
							});
					});
			});
	}

	assert_textInContainerForm_usingLabelAndInputClass(uuid: string, labelName: string, inputClass: string, expectedValue: string) {
		this.element = `[class*='cid_${uuid}'] ${CommonLocators.CommonElements.ROW} `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele)
					.find(`[class*="${inputClass}"]`)
					.first()
					.invoke('val')
					.then(function (codeVal: string) {
						const extractedVal = codeVal;
						cy.log(extractedVal);
						Cypress.env('getTextInContainerForm', extractedVal);
					});
				cy.wait(500).then(() => {
					expect(Cypress.env('getTextInContainerForm')).to.contain(expectedValue);
				});
			});
	}

	/*This method use to click checkbox value for selected row */
	set_cellCheckboxValueForSelectedRow(containerUUID: string, checkboxCell: string, checkBoxValue: string, index?: number, containerPosition?: number, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.first()
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check();
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck();
					}
				}
			});
	}

	/*This method use to select all modal data*/
	select_allModalData() {
		_modalView.findModal().selectAllContainerData();
	}

	/*This method use to click checkbox value for selected row in modal*/
	set_cellCheckboxValueForSelectedRowInModal(checkboxCell: string, checkBoxValue: string, index?: number, containerPosition?: number, recordType?: string) {
		_modalView
			.findModal()
			.getCell(checkboxCell, recordType)
			.wrapElements()
			.find(commonLocators.CommonElements.CHECKBOX_TYPE)
			.first()
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check();
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck();
					}
				}
			});
	}

	/*This method use to validate text in header of application */
	verify_textInDesktopHeader(expectedValue: string) {
		cy.get("[class='middle-container'] [class*='content'] [class*='ellipsis ng-binding']").then(($el) => {
			const ActVal = $el.text().trim()
			expect(expectedValue).to.equal(ActVal);
		});
	}

	/*This method use to type inside dynamic input field with label in container */
	edit_containerLabelCell_withDynamicInput(containerUUID: string, cellClass: string, labelName: string, value: string) {
		_common.find_dynamicInput_inContainerByLabel(containerUUID, cellClass, labelName).clear().type(value);
	}
	handledErrorPopUp_IfExist_indexBased(index: number) {
		cy.wait(8000); // Waiting for modal to appear as it is taking time to load
		cy.get('body').then(($body) => {
			if ($body.find(`[class*='modal-dialog'] `).eq(index).length > 0) {
				_modalView
					.findModal()
					.wrapElements()
					.eq(index)
					.then(($err) => {
						if ($err.find(`[class*='icon tlb-icons ico-error']`).length > 0) {
							cy.get("[class*='modal-footer']").eq(index).contains(btn.ButtonText.OK).click();
						} else {
							cy.log('No error PopUp Exist');
						}
					});
			}
		});
	}

	columnSorting_inSubContainer(containerUUID: string, titleLabel: string, value: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.get('[title=' + titleLabel + ']')
					.click({ force: true })
					.within(() => {
						cy.get("[class*='sort-indicator']")
							.as('sort')
							.invoke('is', 'asc')
							.then((asc) => {
								if (value == 'ascending') {
									if (asc) {
										cy.get('@sort').click();
									}
								}
								if (value == 'decending') {
									if (!asc) {
										cy.get('@sort').click();
									}
								}
							});
					});
			});
	}

	selectValue_fromCodeLookupModal_inCharateristics(containerUUID: string, data: DataCells) {
		//created by : Ashwini Awate 27/08/2024
		//description : This POM is used to select value from popup appering after clicking lookup button in code grid of characteristic container.
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findCell(app.GridCells.CHARACTERISTIC_FK).findButton(btn.IconButtons.BLOCK_IMAGE).clickIn();
		_common.waitForLoaderToDisappear();
		_modalView
			.findModal()
			.wrapElements()
			.last()
			.within(() => {
				cy.get(commonLocators.CommonElements.CHARACTERISTIC_LABEL)
					.contains(data[CommonLocators.CommonKeys.CHARACTERISTIC_GROUP])
					.wait(1000) //mandatory wait needed to reload data
					.click({ force: true })
					.wait(1000); //mandatory wait needed to reload data
				_modalView.findCellhasValue(data[CommonLocators.CommonKeys.GRID], data[CommonLocators.CommonKeys.VALUE]).click({ force: true });
			});
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnvFromModalRow(app.GridCells.CODE,"CHAR_CODE")
		_common.saveCellDataToEnvFromModalRow(app.GridCells.DESCRIPTION,"CHAR_DESC")
		_common.clickOn_modalFooterButton(btn.ButtonText.OK);
	}

	assert_containerFormData_byInvokeText(uuid: string, labelName: string, inputClass: string, envName: string, expectedValue: string) {
		this.element = `[class*='cid_${uuid}'] ${CommonLocators.CommonElements.ROW} `;
		cy.get(this.element) // Get the container element
			.contains(this.element, labelName) // Find the label
			.then((ele) => {
				cy.wrap(ele)
					.find(`[class*="${inputClass}"]`)
					.first()
					.invoke('text')
					.then(function (codeVal: string) {
						const extractedVal = codeVal;
						cy.log(`Extracted value: ${extractedVal}`);

						// Save the value in Cypress environment variable for further assertions
						Cypress.env(envName, extractedVal);

						// Perform the assertion after a short wait
						cy.wait(500).then(() => {
							expect(Cypress.env(envName)).to.contain(expectedValue);
						});
					});
			});
	}

	dateConversion_toNumberFormat(value: string, envVariable: string) {
		//created by : Ashwini Awate 09/09/2024
		//description : This POM is used to convert date to numeric value without any special symbol
		const dateParts = value.split('/');
		const [day, month, year] = dateParts;
		Cypress.env(envVariable, dateParts);
		const formattedMonth = month.padStart(2, '0');
		const formattedDay = day.padStart(2, '0');
		const formattedYear = year.padStart(4, '0');
		const formattedDate = `${formattedYear}${formattedMonth}${formattedDay}`;
		Cypress.env(envVariable, formattedDate);
		return formattedDate;
	}

	assert_errorMessage_underContainerForm(containerUUID: string, expectedValue: string, labelName?: string) {
		if (labelName) {
			cy.get(`[class*='${containerUUID}'] .${commonLocators.CommonElements.PLATFORM_FORM_ROW}`)
				.contains(`[class*='${containerUUID}'] .${commonLocators.CommonElements.PLATFORM_FORM_ROW}`, labelName)
				.then((ele) => {
					cy.wrap(ele)
						.find(`[class*='invalid-cell']`)
						.invoke('text')
						.then((val) => {
							var actualValue = val;
							expect(actualValue).to.equals(expectedValue);
						});
				});
		} else {
			cy.get('.cid_' + containerUUID)
				.find(commonLocators.CommonElements.PLATFORM_FORM_COL)
				.then((ele) => {
					cy.wrap(ele)
						.find(commonLocators.CommonElements.INVALID_CELL_CLASS)
						.invoke('text')
						.then((val) => {
							var actualValue = val;
							expect(actualValue).to.equals(expectedValue);
						});
				});
		}
	}

	getText_fromLabel_containerForm(containerUUID: string, labelName: string, inputClass, envName: string) {
		cy.get(`[class*='${containerUUID}'] ` + commonLocators.CommonElements.ID_PAYMENT_SCHEDULE_TOTAL_SETTING_BOX)
			.find(commonLocators.CommonElements.ROW)
			.contains(labelName)
			.closest(commonLocators.CommonElements.ROW)
			.within(() => {
				cy.get(`[class*='${inputClass}']`)
					.invoke('val')
					.then((val) => {
						Cypress.env(envName, val);
					});
			});
	}

	edit_inputFieldWithDropdown_fromModal(data: DataCells) {
		//Created by : Ashwini Awate
		//Date : 29/08/2024
		//This POM can be use to edit input field with the dropdown inside modal where everything is handle dynamically
		_modalView
			.findModal()
			.wrapElements()
			.find(`${commonLocators.CommonElements.PLATFORM_FORM_LABEL}`)
			.contains(data[CommonLocators.CommonKeys.LABEL])
			.closest(`${commonLocators.CommonElements.ROW}`)
			.within(() => {
				cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`).within(() => {
					cy.get(`[class*='${data[CommonLocators.CommonKeys.INPUT_CLASS]}']`).first().type(`{selectall}{backspace}${data[CommonLocators.CommonKeys.VALUE]}`);
				});
			});
		_common.waitForLoaderToDisappear();
		_common.select_ItemFromPopUpList(data[CommonLocators.CommonKeys.POPUP_TYPE], data[CommonLocators.CommonKeys.VALUE]);
	}

	clear_containerCell(containerUUID: string, gridCells: string, inputTextClass: string, recordType?: string) {
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().findActiveRow().findCell(gridCells, recordType).findTextInput(inputTextClass).clear({ force: true });
	}

	collapseContainerRecords_withLevel(data: DataCells, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(data[CommonLocators.CommonKeys.CONTAINER_UUID], containerPosition)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.wrapElements()
			.within(() => {
				_common.waitForLoaderToDisappear();
				cy.get(`[class*="${btn.ToolBar.ACTION_BTN}"] [class*="${btn.ToolBar.ACTIONS}"] `).click({ force: true });
			});
		cy.wait(2000); //wait needed for popup to load
		_common.select_ItemFromPopUpList(data[CommonLocators.CommonKeys.POPUP_TYPE], data[CommonLocators.CommonKeys.VALUE]);
	}
	findCaretByLabel(labelName: string): Cypress.Chainable<any> {
		this.element = this.element.concat("[class^='platform-form-row'] ");
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`.caret`).eq(0).should('be.visible').click({ force: true });
			});
		this.element = '';
		return div;
	}
	findInputInContainerByLabel(uuid: string, labelName: string, value: string): Cypress.Chainable<any> {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`[class*="input-group-content"]`).first().clear().type(value);
			});
		this.element = '';
		return div;
	}
	enterRecord_inputInContainerByLabel(uuid: string, inputClass: string, labelName: string, value: string): Cypress.Chainable<any> {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`[class*="${inputClass}"]`).first().clear().type(value);
			});
		this.element = '';
		return div;
	}

	clickOn_lableDownArrow_inContainer(label: string) {
		cy.contains(commonLocators.CommonElements.PLATFORM_FORM_GROUP_HEADER_TEXT, label).click();
	}

	assert_forValues_with_singleDigitAfterDecimal(containerUUID: string, gridCellClass: string, expectedValue: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				if (recordType) {
					cy.get(`[class*='active'] [class*='${app.SubContainerLayout.COLUMN_ID}_${recordType}.${gridCellClass}']`).then(($el) => {
						const ActVal = $el.text().replace(',', '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(',', '')).toFixed(1)).to.equals(parseFloat(ActVal).toFixed(1));
					});
				} else {
					cy.get(`.${app.SubContainerLayout.ACTIVE} div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
						const ActVal = $el.text().replace(',', '');
						cy.log(ActVal);
						expect(parseFloat(expectedValue.replace(',', '')).toFixed(1)).to.equals(parseFloat(ActVal).toFixed(1));
					});
				}
			});
	}
	dragDrop_dataToContainer(sourceContainerId: string, destinationContainerId: string, gridCell: string, value: string) {
		cy.get('.cid_' + sourceContainerId + ' .grid-container .column-id_' + gridCell)
			.contains(value)
			.as('Drag_Source');

		cy.get('.cid_' + destinationContainerId + " .grid-container [id*='indicator']").click();

		cy.get('.cid_' + destinationContainerId + " .grid-container [id*='indicator']")
			.first()
			.as('Drop_Destination');

		_mainView.dragAndDrop('@Drag_Source', '@Drop_Destination');
	}

	getText_fromCell_storeInArray2(containerUUID: string, gridCells: string, envVariableName: string) {
		let Column_Data: string[] = []; // Initialize array to store data
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.getCell(app.SubContainerLayout.INDICATOR)
			.wrapElements()
			.then(($ele) => {
				var NumberOfRows = $ele.length;
				for (var i = 0; i < NumberOfRows; i++) {
					cy.get(`[class*='cid_${containerUUID}'] div.${app.SubContainerLayout.COLUMN_ID}_${app.SubContainerLayout.INDICATOR}`).eq(i).click();

					_mainView
						.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.findActiveRow()
						.getCell(gridCells)
						.wrapElements()
						.then(($value) => {
							let data = $value.text();
							Column_Data.push(data); // Push the cell data to the array

							// Log and store the array in Cypress environment variable
							cy.log('Column Data: ' + Column_Data);
							Cypress.env(envVariableName, Column_Data); // Store the array (not string) directly
						});
				}
			});
	}
	assert_textInContainerForm_usingLabelAndInputClass_Form_caret(uuid: string, labelName: string, inputClass: string, expectedValue: string) {
		this.element = `[class*='cid_${uuid}'] ${CommonLocators.CommonElements.ROW} `;
		cy.get(this.element) // Get the container element
			.contains(this.element, labelName) // Find the label
			.then((ele) => {
				cy.wrap(ele)
					.find(`[class*="${inputClass}"]`)
					.first()
					.invoke('text')
					.then(function (codeVal: string) {
						const extractedVal = codeVal;
						cy.log(`Extracted value: ${extractedVal}`);

						// Save the value in Cypress environment variable for further assertions
						Cypress.env('getTextInContainerForm', extractedVal);

						// Perform the assertion after a short wait
						cy.wait(500).then(() => {
							expect(Cypress.env('getTextInContainerForm')).to.contain(expectedValue);
						});
					});
			});
	}

	search_dataFromLookupsUsingRefreshButton_fromModal(container_UUID: string, cellClass: string, searchValue: string, inputTextClass: string, value: string) {
		_common.waitForLoaderToDisappear();
		_mainView.findModuleClientArea().findAndShowContainer(container_UUID).findGrid().findActiveRow().findCell(cellClass).findInputLookup(app.InputFields.ICO_INPUT_LOOKUP, 0);
		cy.wait(2000); //  Added this wait as data take time to load
		_common.waitForLoaderToDisappear();
		_modalView.findModal().findTextInput(inputTextClass).first().clear().type(searchValue);
		_modalView.findModal().findButton(btn.IconButtons.ICO_SEARCH).wrapElements().first().click();
		_common.clickOn_modalFooterButton(btn.ButtonText.REFRESH);
		_common.waitForLoaderToDisappear();
		_validate.verify_recordNotPresentInmodal(searchValue);
	}

	click_popupItemRefresh() {
		cy.wait(1000);
		cy.get('.popup-container').within(() => {
			cy.get('.ico-refresh').click();
		});
	}

	assert_breadCrumb(value: string) {
		cy.get(`#infoView`)
			.find(`#infoTitle [class*='subtitle']`)
			.within(() => {
				cy.get(`label`).contains(value).should('exist');
			});
	}

	goToButton_inActiveRowWithText(containerUUID: string, gridCell: string, btnName?: string, recordType?: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.findActiveRow()
			.getCell(gridCell, recordType)
			.wrapElements()
			.rightclick({ force: true })
			.then(() => {
				cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
					.first()
					.within(() => {
						cy.get(`button.${btn.IconButtons.ICO_GO_TO}`).click({ force: true });
					})
					.then(() => {
						cy.wait(1000);

						cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
							.last()
							.within(() => {
								cy.contains(btnName).first().click({ force: true });
							});
						cy.wait(2000);
					});
			});
	}

	scrollIntoView_inContainerForm(labelName: string) {
		cy.get('.platform-form-label')
			.contains(labelName, { timeout: 10000 }) // wait up to 10 seconds
			.scrollIntoView()
			.should('be.visible');
	}
	// TODO: This method is under test mode, hardcoded locators will be fixed after trail run

	/*
	 * This is used to expand and collapse record in container and prepared based on new changes in 24.3
	 * Note : if data is present in container select data first
	 * button text can be get from buttons.ts file
	 * Updated Date: 14/11/2024
	 * Author : Harshal Sakure
	 */
	clickOn_expandCollapseButton(containerUUID: string, buttonClass: string, value: string, buttonText?: string) {
		// Click the button to open the popup
		_mainView.findModuleClientArea().findAndShowContainer(containerUUID).findButton(buttonClass).clickIn();
		this.waitForLoaderToDisappear();
		cy.wait(1000);
		cy.get('.popup-container').within(() => {
			// Determine action based on the `value` parameter
			switch (value) {
				case 'EXPAND ALL':
					cy.get(`[class*='horizontal-list'] li button`).last().click({ force: true });
					cy.wait(1000);
					break;
				case 'COLLAPSE ALL':
					cy.get(`[class*='horizontal-list'] li button`).first().click({ force: true });
					break;
				case 'Expand Selected':
					cy.get(`button[class*='expandnode']`).click({ force: true });
					break;
				case 'Collapse Selected':
					cy.get(`button[class*='collapsenode']`).click({ force: true });
					break;
				default:
					cy.log('Button text not provided or button not present');
			}
		});
	}


	clickOn_modalFooterButtonWithTitle(buttonTitle: string) {
		_modalView.findModal().findButtonWithTitle(buttonTitle).clickIn();
	}

	//!Updated this pom by adding click on search on button under filter panel
	clear_containerFilter(containerUUID: string) {
		this.waitForLoaderToDisappear();
		cy.wait(1000);
		_mainView.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> Search field is present and direct searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
				} else {
					console.log('----> Clicking on search icon and searching <----');
					cy.wait(1000);
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
						.findButton(btn.ToolBar.ICO_SEARCH)
						.clickIn()
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
				}
			});
	}

	//!Updated this pom by adding click on search on button under filter panel
	search_inContainer(containerUUID: string, searchValue: string, containerPosition?: number) {
		cy.wait(1000);
		_mainView.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_SEARCH)
			.assertClass('active')
			.then((searchField) => {
				if (searchField) {
					console.log('----> Search field is present and direct searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
						.then(() => {
							_mainView.findModuleClientArea()
								.findAndShowContainer(containerUUID)
								.findGrid()
								.findTextInput(app.InputFields.FILTER_INPUT)
								.type(`{selectAll}{backspace}${searchValue}`)
								.then(() => {
									_mainView.findModuleClientArea()
										.findAndShowContainer(containerUUID)
										.findGrid()
										.wrapElements()
										.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
										.click({ force: true })
								})
						})

				} else {
					cy.wait(1000);
					console.log('----> Clicking on search icon and searching <----');
					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.wrapElements()
						.find(`button[class*='${btn.ToolBar.ICO_SEARCH}']`)
						.click();

					_mainView.findModuleClientArea()
						.findAndShowContainer(containerUUID)
						.findGrid()
						.wrapElements()
						.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_INPUT_DELETE}`)
						.click({ force: true })
						.then(() => {
							_mainView.findModuleClientArea()
								.findAndShowContainer(containerUUID)
								.findGrid()
								.findTextInput(app.InputFields.FILTER_INPUT)
								.type(`{selectAll}{backspace}${searchValue}`)
								.then(() => {
									_mainView.findModuleClientArea()
										.findAndShowContainer(containerUUID)
										.findGrid()
										.wrapElements()
										.find(`.${app.Layouts.FILTER_PANEL} .${btn.IconButtons.ICO_SEARCH}`)
										.click({ force: true })
								})
						})

				}
			});
	}

	clickOnToolbarButton_inPopup(btnClass: string) {
		cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
			.within(() => {
				cy.get(`[class*="${commonLocators.CommonElements.TOOLS}"] [class*="${btnClass}"]`)
					.click({ force: true })
			})
	}

	columnFilter_inModal(columnField: string, inputClass: string, value: string) {
		cy.get(`${commonLocators.CommonElements.POPUP_CONTAINER}`)
			.then(() => {
				cy.get(`.item-field_${columnField} [class*="${inputClass}"]`)
					.clear({ force: true })
					.type(value, { force: true })
					.type('{enter}')
			})
	}
	select_dataFromContainersForm_Inputcaret(container_UUID: string, labelName: string,labelclass:string,popUpType: string, value: string) {
		_mainView.findInputInContainerByLabelClass(container_UUID, labelName,labelclass)
			.wait(1000)
			.clear({ force: true })
			.type(value,{ force: true })
			.then((ele) => {
						cy.document()
							.its('body')
							.within(() => {
								_modalView.select_popupItem(popUpType, value);
								cy.wait(1000)
							})
			});
	}
	getTextFromContainerFormByLabelWithIndex(uuid: string, labelName: string,inputClass:string,index:any,envName: string) {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele)
					.find(`[class*='${inputClass}']`)
					.eq(index)
					.invoke('val')
					.then(function (codeVal: string) {
						const Value = codeVal;
						cy.log(Value);
						Cypress.env(envName, Value);
					});
			});
	}
	clickOn_modalFooter_goToButton() {
		_modalView.findModal().goToButton();
	}

	numberRangeSetting_forAPI(data:DataCells,type:string){
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.COMPANY);
		_common.waitForLoaderToDisappear()
		cy.wait(8000) // This wait is added as script was getting failed to loading
		  .then(()=>{
			cy.reload();
		  })
		cy.wait(5000) // This wait is added as script was getting failed to loading
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.NUMBER_SERIES).then(() => {
			_common.setDefaultView(app.TabBar.NUMBER_SERIES)
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.NUMBER_RANGES, app.FooterTab.NUMBERRANGE, 1);
			_common.waitForLoaderToDisappear()
		});
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		cy.wait(2000) // This wait is added as script was getting failed to loading
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.NUMBER_SERIES).then(() => {
			_common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_2)
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.COMPANIES, app.FooterTab.COMPANIES, 0);
		});
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.COMPANIES)
		_common.clear_subContainerFilter(cnt.uuid.COMPANIES)
		_common.search_inSubContainer(cnt.uuid.COMPANIES, data[app.GridCells.CODE]);
		_common.select_rowHasValue(cnt.uuid.COMPANIES, data[app.GridCells.CODE]);
		_common.minimizeContainer(cnt.uuid.COMPANIES)
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.NUMBER_SERIES).then(() => {
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.NUMBER_RANGES, app.FooterTab.NUMBERRANGE, 0);
		});
		_common.waitForLoaderToDisappear()
		_common.search_dataInNumberRangeContainer(cnt.uuid.NUMBER_RANGES,data[app.GridCells.DESCRIPTION_INFO]);
		_common.waitForLoaderToDisappear()
		_common.select_dataFromSubContainer(cnt.uuid.NUMBER_RANGES,data[app.GridCells.DESCRIPTION_INFO]);
		if (data[app.GridCells.DESCRIPTION_INFO]=='WIP') {
			_common.select_allContainerData(cnt.uuid.NUMBER_RANGES)
		}
		_common.waitForLoaderToDisappear()
		cy.wait(2000) // This wait is added as script was getting failed to loading

		if (type==="CreateNew") {
			cy.get(`.cid_${cnt.uuid.NUMBER_RANGES} button[title="New Record (Ctrl+Alt+N)"]`)
			  .then(($button) => {
				// Check if the button is not disabled
				if (!$button.prop('disabled')) {
					cy.wrap($button).click(); // Perform click if not disabled
					cy.log('Button was clicked because it was enabled.');
				} else {
					cy.log('Button is disabled. No action performed.');
				}
			  });
			_mainView.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
				 .findCaretByLabel(commonLocators.CommonLabels.SEQUENCE_TYPE)
				 .then(() => {
					_mainView.select_popupItem(commonLocators.CommonKeys.LIST, data[commonLocators.CommonLabels.SEQUENCE_TYPE]);
				 });

			_mainView.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
					.findCheckBox_byLabel(commonLocators.CommonLabels.GENERATE_NUMBER, commonLocators.CommonKeys.CHECKBOX_SMALL)
					.as('checkbox')
					.invoke('is', ':checked')
					.then((checked) => {
						if (data[commonLocators.CommonLabels.GENERATE_NUMBER] == 'check') {
							if (!checked) {
								cy.get('@checkbox').check();
							}
						} else if (data[commonLocators.CommonLabels.GENERATE_NUMBER] == 'uncheck') {
							if (checked) {
								cy.get('@checkbox').uncheck();
							}
						}
					});

			_mainView.findInputInContainerByLabel_inputClass(cnt.uuid.NUMBER_RANGES, commonLocators.CommonLabels.NUMBER_MASK, app.InputFields.DOMAIN_TYPE_DESCRIPTION)
					.clear()
					.type(data[commonLocators.CommonLabels.NUMBER_MASK]);

			_mainView.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
					.findCheckBox_byLabel(commonLocators.CommonLabels.CHECK_NUMBER, commonLocators.CommonKeys.CHECKBOX_SMALL)
					.as('checkbox')
					.invoke('is', ':checked')
					.then((checked) => {
						if (data[commonLocators.CommonLabels.CHECK_NUMBER] == 'check') {
							if (!checked) {
								cy.get('@checkbox').check();
							}
						} else if (data[commonLocators.CommonLabels.CHECK_NUMBER] == 'uncheck') {
							if (checked) {
								cy.get('@checkbox').uncheck();
							}
						}
					});

			_mainView.findInputInContainerByLabel_inputClass(cnt.uuid.NUMBER_RANGES, commonLocators.CommonLabels.CHECK_MASK, app.InputFields.DOMAIN_TYPE_DESCRIPTION)
					.clear()
					.type(data[commonLocators.CommonLabels.CHECK_MASK]);

			_mainView.findInputInContainerByLabel_inputClass(cnt.uuid.NUMBER_RANGES, commonLocators.CommonLabels.MINIMAL_LENGTH, app.InputFields.INPUT_GROUP_CONTENT)
					.clear()
					.type(data[commonLocators.CommonLabels.MINIMAL_LENGTH]);

			_mainView.findInputInContainerByLabel_inputClass(cnt.uuid.NUMBER_RANGES, commonLocators.CommonLabels.MAXIMUM_LENGTH, app.InputFields.INPUT_GROUP_CONTENT)
					.clear()
					.type(data[commonLocators.CommonLabels.MAXIMUM_LENGTH]);

			_mainView.findAndShowContainer(cnt.uuid.NUMBER_RANGES)
					.findCaretByLabel(commonLocators.CommonLabels.NUMBER_SEQUENCE)
					.then(() => {
						_mainView.select_popupItem(commonLocators.CommonKeys.LIST, data[commonLocators.CommonLabels.NUMBER_SEQUENCE]);
					});

			_common.waitForLoaderToDisappear()
			cy.SAVE()
			_common.waitForLoaderToDisappear()
			cy.SAVE()
			_common.waitForLoaderToDisappear()
		}else if(type==="DeleteRecord"){
			cy.get(`.cid_${cnt.uuid.NUMBER_RANGES} [class*="${btn.IconButtons.ICO_REC_DELETE}"]`)
			  .click()
			_common.waitForLoaderToDisappear()
			cy.SAVE()
			_common.waitForLoaderToDisappear()
			cy.SAVE()
			_common.waitForLoaderToDisappear()
			cy.REFRESH_CONTAINER()
			.wait(2000) // This wait is added as script was getting failed to loading
		}
	}

	maximize_recordPresentInTasklist(searchValue) {
		cy.get(`#${commonLocators.CommonElements.WORKFLOW_TASKS}`).within(() => {
		  cy.get(`${CommonLocators.CommonElements.RW_CONTENT} ${CommonLocators.CommonElements.TITLE_SMALL}`).first().each(($label) => {
			  const module_name: string = $label.text().trim();
			  cy.log('Module Name ==> ', module_name);
			  cy.log('Module Compare ==> ', module_name === searchValue);
			  cy.wrap($label)
				.invoke("text")
				.then((text) => {
				  cy.wrap($label).should("have.text", text).click({force:true});
				})
		  });
		});
		cy.get(`[class*="sidebar-panel"] [class*='ico-maximized']`).click({force:true});
	}

	select_recordIncharacteristicsSections(container_UID: string, data: any) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(container_UID)
		  		 .wrapElements()
		  		 .find("[class*='tree-classic'] [class*='tree-label']")
		  		 .each(($el) => {
					cy.wrap($el).should('be.visible');
					cy.wrap($el).then(($element) => {
			  			const elementText = $element.text().trim();
			  			if (elementText.includes(data)) {
							cy.wrap($element).click();
			  			} else {
							cy.log(`Element with text "${data}" not found.`);
			  			}
					});
				});
	}

	clear_dataFromDropDown_typeInput_withoutGrid(containerUUID: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findButton(btn.ToolBar.CONTROL_ICONS_ICO_INPUT_DELETE)
			.clickIn()
	}


	getText_fromLookup_modal(uuid: string, gridCells: string, searchValue: string, inputTextClass: string, genericRowClass: string, cellClass: string, envVariableName: string) {
		_mainView.findModuleClientArea()
			.findAndShowContainer(uuid)
			.findGrid()
			.findActiveRow()
			.findCell(gridCells)
			.findInputLookup(app.InputFields.ICO_INPUT_LOOKUP, 0)
		_modalView.findModal()
			.acceptButton(btn.ButtonText.REFRESH);
		_common.waitForLoaderToDisappear();
		_modalView.findModal()
			.findTextInput(inputTextClass)
			.first()
			.clear()
			.type(searchValue);
		_modalView.findModal()
			.findButton(btn.IconButtons.ICO_SEARCH)
			.wrapElements()
			.first()
			.click();
		_modalView.findModal()
		          .selectAllContainerData();
		_modalView.findModal()
		          .wrapElements()
				  .within((ele) => {
					cy.wrap(ele)
				      .get(genericRowClass)
					   .then((ele) => {
						  cy.wrap(ele)
							.find("div[class*='" + cellClass + "']")
							.invoke('text')
							.then((text) => {
								Cypress.env(envVariableName, text);
							});

						})
					});
	}
	searchOrClear_columnFilter_fromlookupinModal(inputTextClass: string, columnField: string, inputClass: string, value?: string) {
		_modalView.findModal()
			.findTextInput(inputTextClass)
			.first()
			.clear()
		_modalView.findModal()
			.findButton(btn.IconButtons.ICO_SEARCH)
			.wrapElements()
			.first()
			.click();
		_modalView.findModal()
			.wrapElements()
			.find(`.item-field_${columnField}`)
			.first()
			.as('column')
			.click()
			.within(() => {
				cy.get("[class*='" + inputClass + "']").clear({ force: true });
				if (value != null) {
					cy.get("[class*='" + inputClass + "']").clear({ force: true }).type(value, { force: true });
				}
			});
	}

	edit_dropdownCellWithCaretIconClass(containerUUID: string, cellType: string, iconClass: string, index?: number, recordType?: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .findGrid()
				 .findActiveRow()
				 .findCell(cellType, recordType)
				 .caret()
		cy.wait(2000);//required wait to open popup
		cy.contains(CommonLocators.CommonKeys.LI)
		  .get(`[class*='${iconClass}']`)
		  .click({ force: true });
	}

	clickOn_modalFooterButton_indexBased(buttonLabel: string, index: number) {
        _modalView.findModal()
                  .wrapElements()
                  .eq(index)
                  .find(`[class*='modal-footer'] `)
                  .contains('button', buttonLabel)
                  .click({ force: true });
    }

	clickOn_modalFooterButton_indexBased_ifExists_multipleModal(buttonLabel: string, index: number) {
		cy.wait(1000);
		cy.get('body').then(($body) => {
			if ($body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS}`).length > 1) {
				_modalView.findModal()
						.wrapElements()
						.eq(index)
						.find(`[class*='modal-footer'] `)
						.contains('button', buttonLabel)
						.click({ force: true });
			}
		});
	}

	selectAllData_fromSplitContainer_westLayout(containerUUID: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .wrapElements()
				 .find(`[id='ui-layout-west'] [class*='grid-container'] [class*='slick-header-column indicator']`)
				 .click({ force: true });
	}

	selectAllData_fromSplitContainer_eastLayout(containerUUID: string, containerPosition?: number) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(containerUUID, containerPosition)
				 .wrapElements()
				 .find(`[id='ui-layout-east'] [class*='grid-container'] [class*='slick-header-column indicator']`)
				 .click({ force: true });
	}

	edit_dropdownCellWithCaretIconClassInFormContainer(containerUUID: string, labelName: string, iconClass: string) {
		cy.get(`[class*='cid_${containerUUID}'] ` + commonLocators.CommonElements.ROW)
      	  .contains(`[class*='cid_${containerUUID}'] ` + commonLocators.CommonElements.ROW, labelName)
		  .find("[title='Edit']").click({force:true})
		cy.wait(2000)//required wait to open popup
		.then((ele) => {
			cy.contains(CommonLocators.CommonKeys.LI)
		  	.get(`[class*='${iconClass}']`)
		  	.click({ force: true });
		});
	}

	getText_storeIntoArray_inSubContainer(containerUUID: string, cellClass: string, index: number, arrEnvName: string) {
		const Arr: string[] = [];
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.wrapElements()
			.within(() => {
				cy.get(`${CommonLocators.CommonElements.GRID_CONTAINER}`).eq(index)
					.within(() => {
						cy.get(`div.${app.SubContainerLayout.COLUMN_ID}_${cellClass}`)
							.each((record) => {
								const description: string = record.text();
								Arr.push(description);
							})
							.then(() => {
								Cypress.env(arrEnvName, Arr);
								Arr.forEach((description, index) => {
									cy.log(`Description[${index}]: ${description}`);
								});
							});
					})
			})
	}

	clickOn_containerDesignArea(uuid: string) {
		_mainView.findModuleClientArea()
				 .findAndShowContainer(uuid)
				 .wrapElements().find(`[class*='workflowItem action']`).click({force:true})
	}

	add_filterHierachicalStructure(container_UUID: string,structureInputs:string) {
		cy.wait(2000);
		cy.get(`[class*="${container_UUID}"]`).then(($ele) => {
			cy.wait(2000);
			if ($ele.find(`[class*='${app.GridCellIcons.ICO_INPUT_DELETE}']`).length > 0) {
				cy.get(`[class*="${container_UUID}"] [class*='${app.GridCellIcons.ICO_INPUT_DELETE}']`).eq(0).click({ multiple: true });
				_mainView.findModuleClientArea()
				         .findAndShowContainer(container_UUID)
						 .findButton(btn.IconButtons.ICO_INPUT_ADD)
						 .clickIn();
				cy.get(commonLocators.CommonElements.POPUP_CONTAINER_GENERIC_POPUP_FORM_CONTROL)
				  .type(structureInputs);
				_mainView.select_popupItem(commonLocators.CommonKeys.GRID_1, structureInputs);
				_mainView.findModuleClientArea()
				         .findAndShowContainer(container_UUID)
						 .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
						 .findButton(btn.ToolBar.ICO_REFRESH)
						 .clickIn();
			} else {
				_mainView.findModuleClientArea()
				         .findAndShowContainer(container_UUID)
						 .findButton(btn.IconButtons.ICO_INPUT_ADD)
						 .clickIn();
				cy.get(commonLocators.CommonElements.POPUP_CONTAINER_GENERIC_POPUP_FORM_CONTROL).type(structureInputs);
				_mainView.select_popupItem(commonLocators.CommonKeys.GRID_1, structureInputs);
				_mainView.findModuleClientArea()
				         .findAndShowContainer(container_UUID)
						 .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
						 .findButton(btn.ToolBar.ICO_REFRESH)
						 .clickIn();
			}
		});
	}

	clickOn_closeIconUnderContainer(container_UUID: string){
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(container_UUID)
			.toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
			.findButton(btn.ToolBar.ICO_GROUP_COLUMNS)
			.assertClass('active')
			.then((activeToolbarButton) => {
				if (activeToolbarButton) {
					cy.wait(2000); //Required wait to load droppable area in container after adding new one.
					_mainView
						.findModuleClientArea()
						.findAndShowContainer(container_UUID)
						.wrapElements()
						.then(($el) => {
							let len = $el.find(`[class*='${CommonLocators.CommonElements.UI_DROPPABLE}'] [class*='${btn.IconButtons.ICO_CLOSE}']`).length;
							if (len > 0) {
								for (let index = 0; index < len; index++) {
									cy.get(`[class*='${CommonLocators.CommonElements.UI_DROPPABLE}'] [class*='${btn.IconButtons.ICO_CLOSE}']`).first().click();
								}
							}
						});
	}

})
	}

	getNumericValue(containerUUID: string, gridCellClass: string) {
        _mainView.findModuleClientArea().findAndShowContainer(containerUUID).findGrid().wrapElements().within(() => {
            cy.get(`.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`).then(($el) => {
                cy.wait(2000)//required wait
                const regex = /\((\d+)\)/;
                let match = $el.text().match(regex);
                const count = parseInt(match[1], 10);
                Cypress.env("number", count)
            });
        })
	}

	setButtonState(containerUUID: string, buttonClass: string, titleClass: string, containerPosition?: number) {
		_mainView
				.findModuleClientArea()
				.findAndShowContainer(containerUUID, containerPosition)
				.findGrid()
				.findActiveRow()
				.wrapElements()
				.find(`[class*='${buttonClass}'] button`)
				.as('button');
		// Check if the specific element exists
		cy.get("body").then(($body) => {
			if (!$body.find(`[title*='${titleClass}']`).length) {
				// The element is present, proceed with clicking
				cy.get('@button')
					.should('exist')
					.and('not.be.disabled')
					.click();
			}
		});
	}

	getPartialTextFromContainerFormByLabelWithIndex(uuid: string, index:any,envName: string) {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.then((ele) => {
				cy.wrap(ele)
					.find(`[class*='${app.ModalInputFields.NUMBER_MASK}']`)
					.eq(index)
					.invoke('val')
					.then(function (codeVal: string) {
						const Value = codeVal.substring(0,(codeVal.length-5));
						cy.log(Value);
						Cypress.env(envName, Value);
					});
			});
	}

	handledTreeGrid_IfExistInModal(action: btn.ButtonText.EXPAND_ALL | btn.ButtonText.COLLAPSE_ALL, gridCell?: string, descriptionStatus?: string) {
		cy.wait(2000); // Waiting for modal to appear as it is taking time to load

		_modalView.findModalBody().wrapElements().then(($modalBody) => {
			const performActionOnTree = () => {
				cy.wrap($modalBody).then(($body) => {
					let actionIcons;
					switch (action) {
						case btn.ButtonText.EXPAND_ALL:
							actionIcons = $body.find(`[class*='${app.GridCellIcons.ICO_TREE_COLLAPSE}']`);
							break;
						case btn.ButtonText.COLLAPSE_ALL:
							actionIcons = $body.find(`[class*='${app.GridCellIcons.ICO_TREE_EXPAND}']`);
							break;
					}

					if (actionIcons.length > 0) {
						cy.wrap(actionIcons[0])
							.click({ force: true })
							.wait(500) // Give time for DOM to update
							.then(() => {
								performActionOnTree(); // Recursively call again
							});
					} else {
						cy.log(`${action.replace('_', ' ')} action completed`);
					}
				});
			};
			// Start performing the action
			performActionOnTree();
		});

		// If descriptionStatus is provided, click the row and expand its tree
		if (descriptionStatus) {
			_common.clickOn_cellHasValue_fromModal(gridCell, descriptionStatus);
			cy.get(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS)
				.find(`[class*='${app.GridCells.SELECTED}'] [class*='${app.GridCellIcons.ICO_TREE_COLLAPSE}']`)
				.click();
		} else if (!descriptionStatus) {
			cy.log('No specific tree action required');
		}
	}

	verify_isRecordNotEditableInContainerForm(container_UUID: string, labelName: string) {

		this.element = `[class*='cid_${container_UUID}'] ${CommonLocators.CommonElements.ROW} `;
		cy.get(this.element)  // Get the container element
		  .contains(this.element, labelName)  // Find the label
		  .then((ele) => {
			cy.wrap(ele).should("not.have.class", app.InputFields.INPUT_GROUP_CONTENT)

		  });
	}

	search_fromSidebar_withIncludesFlag(data:DataCells) {
		cy.wait(100)

		cy.get(`#${sidebar.sideBarList.SIDEBAR_SEARCH}`)
		   .find(`[class*='${app.ModalInputFields.INCLUDE_REFERNCE_LINE_ITEMS}']`)
		   .find(commonLocators.CommonElements.CHECKBOX_INPUT)
		   .as('checkbox')
		   .invoke('is', ':checked')
		   .then((checked) => {
			   if (data[app.ModalInputFields.INCLUDE_REFERNCE_LINE_ITEMS]=== commonLocators.CommonKeys.CHECK) {
				if (!checked) {
					   cy.get('@checkbox').check();
				}
			   } else if (data[app.ModalInputFields.INCLUDE_REFERNCE_LINE_ITEMS] === commonLocators.CommonKeys.UNCHECK) {
				if (checked) {
					   cy.get('@checkbox').uncheck();
				}
			   }

		})


	}

	saveCellDataWithNumberToEnv(containerUUID: string, cellClass: string, cypressEnvName: string, recordNumber?: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.getCellWithNumber(cellClass, recordNumber)
			.wrapElements()
			.then(($value) => {
				Cypress.env(cypressEnvName, $value.text());
			});
	}

	clickOn_modalFooterCheckbox_multipleSelection(checkboxClass: string,checkBoxValue: string) {
		_modalView.findModal()
				  .wrapElements()
				  .find(`[class*='${checkboxClass}']`+" "+ commonLocators.CommonElements.CHECKBOX_TYPE)
				  .as('check')
				  .invoke('is', ':checked')
				  .then((checked) => {
					if (checkBoxValue == 'check') {
						if (!checked) {
							cy.get('@check').check();
						}
					}
					if (checkBoxValue == 'uncheck') {
						if (checked) {
							cy.get('@check').uncheck();
						}
					}
				  });
	}

	select_multipleRow_inModal_keyboardAction(startValue, endValue) {
		_modalView
		.findModal()
		.findModalBody()
		.wrapElements()
		.within(() => {
			cy.wait(500)
				cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, startValue).click({ force: true }).type('{shift}', { release: false });

				cy.contains(`[class*='${app.SubContainerLayout.COLUMN_ID}']`, endValue).click({ force: true }).type('{shift}', { release: true });
			});
	}
	findbuttonInContainerFormByLabel(uuid: string, labelName: string, cssSelector: string): Cypress.Chainable<any> {
		this.element = `[class*='cid_${uuid}'] [class*='platform-form-row '] `;
		const div = cy
			.get(this.element)
			.contains(this.element, labelName)
			.then((ele) => {
				cy.wrap(ele).find(`[class*='${cssSelector}']`).eq(0).click();
			});
		this.element = '';
		return div;
	}



	search_dataAndSelectFromLookups_fromModal_basedOnIndex(container_UUID: string, cellClass: string, searchValue: string, inputTextClass: string, modalCellClass:string,index: number) {
		_common.waitForLoaderToDisappear();
		_mainView.findModuleClientArea()
				 .findAndShowContainer(container_UUID)
				 .findGrid()
				 .findActiveRow()
				 .findCell(cellClass)
				 .findInputLookup(app.InputFields.ICO_INPUT_LOOKUP, 0);
		cy.wait(2000); // Added wait for data loading
		_common.waitForLoaderToDisappear();
		_modalView.findModal()
				  .findTextInput(inputTextClass)
				  .clear()
				  .type(searchValue);
		_modalView.findModal()
				  .findButton(btn.IconButtons.ICO_SEARCH)
				  .wrapElements()
				  .first()
				  .click();
		_modalView.findModal()
				  .wrapElements()
				  .find(`[class*='column-id_${modalCellClass}']`)
				  .eq(index) // Select by index
				  .contains(searchValue)
				  .click({ force: true });
		_modalView.findModal().acceptButton(btn.ButtonText.OK);
		_common.waitForLoaderToDisappear();
	}

	saveCheckboxStatusToEnv(containerUUID: string, cellClass: string, cypressEnvName: string,recordType?: string, containerPosition?: number) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID, containerPosition)
			.findGrid()
			.findActiveRow()
			.getCell(cellClass, recordType)
			.wrapElements()
			.find('input[type="radio"]')
			.invoke('is', ':checked')
			.then((isChecked: boolean) => {
				cy.log(`Checkbox status for ${cellClass}: ${isChecked}`);
				Cypress.env(cypressEnvName, isChecked);
			});
	}

	searchDataUsingRefreshButton_fromModal(inputTextClass: string, searchValue: string) {
		cy.WaitUntilLoaderComplete_Trial()
		_modalView.findModal().findTextInput(inputTextClass).first().clear().type(searchValue)
		cy.WaitUntilLoaderComplete_Trial()
		_modalView.findModal().findButton(btn.IconButtons.ICO_SEARCH).wrapElements().first().click()
		cy.WaitUntilLoaderComplete_Trial()
		_common.clickOn_modalFooterButton(btn.ButtonText.REFRESH);
		cy.WaitUntilLoaderComplete_Trial()
		_common.select_rowHasValue_fromModal(searchValue)
	}


	clickRadioInModalByClass(radioClass: string, index: number = 0): void {
    cy.get(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS)
      .find(`input[type="radio"][class*=${radioClass}]`)
      .eq(index)
      .should('be.visible')
      .click({ force: true });
}
	saveCellDataToEnvFromModalRow_ByInvokeMethod(cellClass: string, cypressEnvName: string) {
	cy.get(`.modal-content [class*='active']`)
	.find(`[class*=${cellClass}]`)
	.invoke('text')
		.then(($value) => {
			cy.log("value",$value)
			Cypress.env(cypressEnvName, $value);
		});
	}

	assert_cellData_ByInclude(containerUUID: string, gridCellClass: string, expectedValue: string) {
		_mainView
			.findModuleClientArea()
			.findAndShowContainer(containerUUID)
			.findGrid()
			.wrapElements()
			.within(() => {
				cy.get(`div.${app.SubContainerLayout.COLUMN_ID}_${gridCellClass}`)
					.first()
					.then(($el) => {
						const ActVal = $el.text();
						cy.log(ActVal);
						expect(ActVal.trim()).to.include(expectedValue);
					});
			});
	}

	clickonModalButton_insideInputByClass(inputClass: string, btnClass: string) {
		_modalView.findModal()
			.wrapElements()
			.find(`.${inputClass}`)
			.closest(`${commonLocators.CommonElements.ROW}`)
			.within(() => {
				cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL}`).within(() => {
					cy.get(`[class*='${btnClass}']`).first().click();
				});
			});
	}
	edit_inputFieldWithMultipleClass_fromModal_byClass(inputClass1: string, inputClass2: string, value: string) {
		_modalView.findModal()
			.wrapElements()
			.find(`.${inputClass1}`)
			.closest(`${commonLocators.CommonElements.ROW}`)
			.within(() => {
				cy.get(`[class*='${inputClass2}']`).first().type(`{selectall}{backspace}${value}`);
			});
	}
	setCheckBoxValueUnderModal_byLabel(labelName: string, checkBoxValue: string) {
		_modalView.findModal()
			.checkBox_hasLabel(labelName)
			.as('check')
			.invoke('is', ':checked')
			.then((checked) => {
				if (checkBoxValue == 'check') {
					if (!checked) {
						cy.get('@check').check({ force: true });
					}
				}
				if (checkBoxValue == 'uncheck') {
					if (checked) {
						cy.get('@check').uncheck({ force: true });
					}
				}
			});
	}

	clickOnButtonInModal_byTextContains(value: string) {
		_modalView.findModal()
			.wrapElements()
			.within(() => {
				cy.get(`${commonLocators.CommonElements.PLATFORM_FORM_COL} ${commonLocators.CommonElements.BUTTON}`)
					.contains(value)
					.click({ force: true })
			})
		cy.WaitUntilLoaderComplete_Trial()
	}
		
}



