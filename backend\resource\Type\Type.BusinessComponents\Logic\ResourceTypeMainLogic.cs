using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Transactions;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Platform.Core;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.BusinessComponents;

using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using NLS = RIB.Visual.Resource.Type.Localization.Properties.Resources;
using CCB = RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents;
using RVPBC = RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Resource.Type.BusinessComponents
{
	/// <summary>
	/// Reservation Business Logic should be placed here
	/// 
	/// </summary>
	[Export(typeof(IResTypeLogic))]
	public class ResourceTypeMainLogic : EntityProvidingUpdateLogic<ResourceTypeEntity, IdentificationData>, IUpdateCompleteData, IResTypeLogic, IEntityAggregator, IEntityDeleteAgent
	{
		/// <summary>
		/// The singleton identifier instance for the <see cref="ResourceTypeEntity"/> type.
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<ResourceTypeEntity>> IdentifierInstance =
			IdentifierFactory.Create<ResourceTypeEntity>("Id");

		/// <summary>
		/// ResourceTypeMainLogic constructor
		/// </summary>
		public ResourceTypeMainLogic()
		{
			Identifier = IdentifierInstance.Value;
			PermissionGUID = "b881141e03c14ddfb1aa965c0cb9ea2c";
			CompleteUpdater = this;
			GetTranslatedProperties = new List<Func<ResourceTypeEntity, DescriptionTranslateType>>() { e => e.DescriptionInfo }.ToArray();
			SetRelationInfoIdentifier("resource.type.type");
			OrderByExpressions = new[]
			{
				OrderTerm.Create(e => e.Id)
			};
			OrderByKey = e => Identifier.GetEntityIdentification(e);

			ParentIdFn = e => e.ResourceTypeFk;
			GetChildren = e => GetResourceTypeChildren(e);
			GetChildrenFromParents = e => GetResourceTypeChildren(e);
			IsRootEntity = e => !e.ResourceTypeFk.HasValue;

			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
		}

		#region IResTypeLogic

		/// <summary>
		/// </summary>
		IEnumerable<IResTypeEntity> IResTypeLogic.GetTypesById(IEnumerable<int> typeIds)
		{
			if (typeIds.IsNullOrEmpty())
			{
				return Array.Empty<IResTypeEntity>();
			}
			var identifications = typeIds.Select(id => new IdentificationData() { Id = id }).ToArray();
			return this.GetByIds(identifications);
		}

		IEnumerable<IResTypeEntity> IResTypeLogic.GetResourceTypesByCompanyStructured()
		{
			return this.GetResourceTypesByCompanyStructured(Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.ClientId);
		}

		IEnumerable<IResTypeEntity> IResTypeLogic.GetResourceTypesByCompanyFlat()
		{
			return this.GetResourceTypesByCompanyFlat(Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.ClientId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		/// \since 2019-01-02 (15:48), by zov
		IEnumerable<IResTypeEntity> IResTypeLogic.GetResourceTypesOfCrane()
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			var ctxId = GetCurrentResourceContext();
			return GetListByFilter(e => e.ResourceContextFk == ctxId && e.IsCrane).ToList();
		}
		#endregion


		/// <summary>
		/// Gets DbModel.
		/// </summary>
		/// <returns/>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Method to get one sequence id
		/// </summary>
		/// <returns/>
		protected override string GetEntityTableName()
		{
			return "RES_TYPE";
		}

		/// <summary>
		/// Gets search context for flat list.
		/// </summary>
		/// <param name="request">The filter request</param>
		/// <param name="response">The filter response</param>
		/// <returns>The SearchSpecification instance</returns>
		protected override SearchSpecification<ResourceTypeEntity, IdentificationData> GetListSearchContext(FilterRequest request, out FilterResponse response)
		{
			var id = GetCurrentResourceContext();
			var context = new SearchSpecification<ResourceTypeEntity, IdentificationData>(request, OrderByKey, OrderTerm.CreateQuerySorter(OrderByExpressions));
			//context.UseCurrentClientPredicate = e => e.ResourceContextFk == id;
			context.CustomPredicate = e => e.ResourceContextFk == id;
			response = context.FilterOut;

			return context;
		}

		/// <summary>
		/// EvaluatePatternSearch
		/// </summary>
		protected override IQueryable<ResourceTypeEntity> EvaluatePatternSearch(SearchSpecification<ResourceTypeEntity, IdentificationData> searchContext, DbContext dbContext, IQueryable<ResourceTypeEntity> query)
		{
			if (!string.IsNullOrWhiteSpace(searchContext.FilterIn.Pattern))
			{
				var option = new QueryFilterOptions<ResourceTypeEntity>();
				option.Add(new List<string>() { "DescriptionInfo" });
				option.IgnoreSearchPattern = true;
				option.SearchText = searchContext.FilterIn.Pattern;
				query = query.JoinTrAndFilter<ResourceTypeEntity, CCB.TranslationEntity>(dbContext.Set<CCB.TranslationEntity>(), option);
			}
			return query;
		}






		/// <summary>
		/// Create an entity via creation data.
		/// </summary>
		/// <param name="creationData"/>
		/// <returns/>
		public override ResourceTypeEntity Create(IdentificationData creationData)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);
			var entity = new ResourceTypeEntity();
			entity.Id = this.SequenceManager.GetNext(GetEntityTableName());
			if (creationData.PKey1.HasValue)
			{
				entity.ResourceTypeFk = creationData.PKey1.Value;
			}

			entity.ResourceContextFk = GetCurrentResourceContext();
			RelationDefaultValueSetter.Handle(entity, new List<Tuple<string, Action<ResourceTypeEntity, int>, int?>>() {
				new Tuple<string, Action<ResourceTypeEntity, int>, int?>("basics.customize.logisticsdispatchergroup", (e, i) => e.DispatcherGroupFk = i, null)
			});

			return entity;
		}

		/// <summary>
		/// Gets children of a resource type, if there any.
		/// </summary>
		/// <param name="parent">Possible parent of other resource types</param>
		/// <returns>The children of the passed cost group</returns>
		protected IEnumerable<ResourceTypeEntity> GetResourceTypeChildren(ResourceTypeEntity parent)
		{
			return GetListByFilter(e => e.ResourceTypeFk == parent.Id);
		}

		/// <summary>
		/// Gets children of resource types, if there any.
		/// </summary>
		/// <param name="parents">Possible parents of other resource types</param>
		/// <returns>The children of the passed cost group</returns>
		protected IEnumerable<ResourceTypeEntity> GetResourceTypeChildren(IEnumerable<ResourceTypeEntity> parents)
		{
			var parentIds = parents.CollectIds(p => p.Id).ToArray();

			return GetListByFilter(e => e.ResourceTypeFk.HasValue && parentIds.Contains(e.ResourceTypeFk.Value));
		}

		/// <summary>
		/// update types
		/// </summary>
		/// <param name="scheduleUpdates"></param>
		/// <returns></returns>
		public IEnumerable<ResourceTypeUpdateEntity> DoUpdate(IEnumerable<ResourceTypeUpdateEntity> scheduleUpdates)
		{
			var res = new List<ResourceTypeUpdateEntity>();
			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				foreach (var item in scheduleUpdates)
				{
					if (item.ResourceTypes != null)
					{
						item.ResourceTypes = Save(item.ResourceTypes);
					}

					EntityUpdateDispatcher.Handle(item);

					res.Add(item);
				}
				transaction.Complete();
			}

			return res;
		}

		IEnumerable<IIdentifyable> IUpdateCompleteData.HandleUpdate(IEnumerable<IIdentifyable> completeData)
		{
			var resTypes2Update = completeData.OfType<ResourceTypeUpdateEntity>().ToList();

			return this.DoUpdate(resTypes2Update);
		}

		private int GetCurrentResourceContext(int? client = null)
		{
			if (client == null)
			{
				var CurrentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
				client = CurrentContext.ClientId;
			}
			var CompanyInfoProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ICompanyInfoProvider>();
			var CompanyInfo = CompanyInfoProvider.GetInstanceInfo(client.Value);

			var nullableId = CompanyInfo.GetResourceContext();

			if (nullableId.HasValue)
			{
				return nullableId.Value;
			}

			throw new BusinessLayerException
			{
				ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError,
				ErrorMessage = NLS.ERR_TitleNoCompanyResourceContext,
				ErrorDetail = NLS.ERR_CompanyNeedsResourceContext
			};
		}

		/// <summary>
		/// Return resource-types hierarchical structured Parent->Children
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceTypeEntity> GetResourceTypesStructured()
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			var id = GetCurrentResourceContext();
			
			return GetByFilter(e => e.ResourceContextFk == id).ToList(); ;
		}
		
		/// <summary>
		/// Return resource-types hierarchical structured Parent->Children
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceTypeEntity> GetResourceTypesAlternativeStructured(int idAlternative)
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			
			var reqTypeLogic = new AlternativeResTypeLogic();
			var alternativeTypes = reqTypeLogic.GetByFilter(e => e.ResTypeFk == idAlternative).ToList();		
			var requestedTypeIds = alternativeTypes.Select(e => e.ResAlterTypeFk).ToHashSet();
			
			var resourceTypes = GetByFilter(e => requestedTypeIds.Contains(e.Id)).ToList();
			var id = GetCurrentResourceContext();
			var items = resourceTypes.Where(e => e.ResourceContextFk == id).ToList();		

			return items;
		}

		/// <summary>
		/// Return resource-types hierarchical structured Parent->Children
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceTypeEntity> GetResourceTypeSmallToolsStructured()
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			var id = GetCurrentResourceContext();

			return GetByFilter(e => e.ResourceContextFk == id && e.IsSmallTools).ToList();
		}

		/// <summary>
		/// Get Item by given Id
		/// </summary>
		/// <param name="id"></param>
		/// <returns>Resource Type entity</returns>
		public int? GetResourceContextFkByTypeId(int id)
		{
			var list = GetListByFilter(e => e.Id == id);
			if (list != null && list.Any())
			{
				return list.First().ResourceContextFk;
			}
			return null;
		}


		/// <summary>
		/// Get Topmost ParentId by given Id
		/// </summary>
		/// <param name="id"></param>
		/// <returns>Resource Type entity</returns>
		public int? GetRootResourceId(int id)
		{
			var resType = GetById(new IdentificationData(id));
			if (resType != null)
			{
				while (!IsRootEntity(resType))
				{
					resType = GetById(new IdentificationData(ParentIdFn(resType).GetValueOrDefault()));
				}
				return resType.Id;
			}
			return null;
		}

		/// <summary>
		/// Save process of entity.
		/// </summary>
		/// <param name="entity"/><param name="dbContext"/>
		protected override void SavePostProcessing(ResourceTypeEntity entity, RVPBizComp.DbContext dbContext)
		{
			dbContext.ExecuteStoredProcedure("RES_TYPE_PROC", entity.Id);
		}

		/// <summary>
		/// Save process of entity.
		/// </summary>
		/// <param name="entities"/><param name="dbContext"/>
		protected override void SavePostProcessing(IEnumerable<ResourceTypeEntity> entities, RVPBizComp.DbContext dbContext)
		{
			foreach (var entity in entities)
			{
				SavePostProcessing(entity, dbContext);
			}
		}

		/// <summary>
		/// Performs some post-processing on retrieved entities before they are returned.
		/// </summary>
		/// <param name="entities"></param>
		protected override void PostProcess(IEnumerable<ResourceTypeEntity> entities)
		{
			base.PostProcess(entities);

			var resTypeLogic = new PlantGroup2ResTypeLogic();
			var reqSkillLogic = new RequiredSkillLogic();
			var reqTypeLogic = new RequestedTypeLogic();
			var entityIds = entities.Select(e => e.Id);

			var plantGroups = resTypeLogic.GetPlantGroupIdsByResourceTypes(entityIds);
			var type2reqSkills = reqSkillLogic.GetByFilter(e => entityIds.Contains(e.TypeFk)).Select(e=>e.TypeFk);
			var type2reqTypes = reqTypeLogic.GetByFilter(e => entityIds.Contains(e.TypeFk)).Select(e => e.TypeFk);

			foreach (var entity in entities)
			{
				var plantGroup = plantGroups.FirstOrDefault(e => e.ResTypeFk == entity.Id);
				if (plantGroup != null)
				{
					entity.PlantGroupFk = plantGroup.PlantGroupFk;
				}

				entity.HR = type2reqSkills.Contains(entity.Id);
				entity.Has2ndDemand = type2reqTypes.Contains(entity.Id);
			}
		}

		/// <summary/>
		/// <param name="children">children looking out for their parents</param>
		/// <param name="parents">parents wanting to take care for their children</param>
		protected override void LinkChildrenToParents(IEnumerable<ResourceTypeEntity> children, IEnumerable<ResourceTypeEntity> parents)
		{
			foreach (var parent in parents)
			{
				parent.SubResources = children.Where(e => e.ResourceTypeFk == parent.Id).ToArray();
			}
		}

		private static void VerifyLoginCompany(int companyId)
		{
			var companyLogic = new BasicsCompanyLogic();
			var company = companyLogic.GetItemByKey(companyId);
			CompanyCheckResult result = null;
			if (company != null)
			{
				result = companyLogic.CheckValidCompany(company.Code);
			}
			if (result == null || !result.IsValid)
			{
				throw new SecurityException(NLS.ERR_InvalidCompanyForLookup)
				{
					ErrorCode = (Int32)ExceptionErrorCodes.GeneralPermission,
					ErrorDetail = string.Format(NLS.ERR_CanNotSwitchToCompany + " \"{0}\"", company)
				};
			}
		}

		/// <summary>
		/// Return resource-types hierarchical structured Parent->Children
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceTypeEntity> GetResourceTypesByCompanyStructured(int companyId)
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			// check login
			VerifyLoginCompany(companyId);
			// get context for given company

			var id = GetCurrentResourceContext(companyId);

			return GetByFilter(e => e.ResourceContextFk == id).ToList();
		}

		/// <summary>
		/// Return resource-types as a flat list
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceTypeEntity> GetResourceTypesByCompanyFlat(int companyId)
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			// check login
			VerifyLoginCompany(companyId);
			// get context for given company

			var id = GetCurrentResourceContext(companyId);

			return GetListByFilter(e => e.ResourceContextFk == id).ToList();
		}

		/// <summary>
		/// Return resource-types as a flat list by plant group
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ResourceTypeEntity> GetResourceTypesByPlantGroupFlat(int plantGroupId)
		{
			Permission.Ensure(PermissionGUID, Permissions.Read);
			var id = GetCurrentResourceContext();

			return GetListByFilter(e => e.ResourceContextFk == id).Where(e =>  e.PlantGroupFk == plantGroupId).ToList();
		}

		#region Aggregator

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			var updateEntity = to as ResourceTypeUpdateEntity;
			if (updateEntity != null && toBeAggregated.Any())
			{
				DoAggregate(updateEntity, relInfo, toBeAggregated);
			}
		}

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IIdentifyable toBeAggregated)
		{
			var updateEntity = to as ResourceTypeUpdateEntity;
			if (updateEntity != null && toBeAggregated != null)
			{
				DoAggregate(updateEntity, relInfo, new List<IIdentifyable>() { toBeAggregated });
			}
		}

		IIdentifyable IEntityAggregator.ProvideCompleteEntity()
		{
			return new ResourceTypeUpdateEntity();
		}

		/// <summary>
		/// Gives access to the instance being responsible for creating complete entities. base implmentation returns null, which is appropriate return for all leave entities
		/// </summary>
		/// <returns>
		/// null
		/// </returns>
		protected override IEntityAggregator ProvideAggregator()
		{
			return this;
		}

		/// <summary>
		/// Takes over a list of entities and appends it to the passed update entity
		/// </summary>
		/// <param name="updateEntity">Complete entity to build up</param>
		/// <param name="relInfo">Information about content of toBeAggregated</param>
		/// <param name="toBeAggregated">Entities which need to be added to updateEntity</param>
		/// protected virtual IEntityAggregator ProvideAggregator(
		protected void DoAggregate(ResourceTypeUpdateEntity updateEntity, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			switch (relInfo.GetIdentifier())
			{
				case "resource.type.type":
					updateEntity.ResourceTypes = toBeAggregated.OfType<ResourceTypeEntity>().FirstOrDefault();
					updateEntity.ResourceTypeId = updateEntity.ResourceTypes.Id;
					break;
				case "required.skill.skill":
					updateEntity.RequiredSkillsToSave = toBeAggregated.OfType<RequiredSkillEntity>();
					break;
				case "required.type.requestedtype":
					updateEntity.RequestedTypesToSave = toBeAggregated.OfType<RequestedTypeUpdateEntity>();
					break;
			}
		}

		#endregion


		#region IEntityDeleteAgent
		bool IEntityDeleteAgent.CheckAggregatedCanBeDeleted(IEntityRelationInfo candidate, IEnumerable<IIdentifyable> entities, IEntityDeleteGuard guard)
		{
			return true;
		}

		bool IEntityDeleteAgent.CheckAggregatedCanBeDeleted(IEntityRelationInfo candidate, IIdentifyable entity, IEntityDeleteGuard guard)
		{
			return true;
		}

		bool IEntityDeleteAgent.CheckUsersAllowDelete(IEntityRelationInfo candidate, IEnumerable<IIdentifyable> entities, IEntityDeleteGuard guard)
		{
			return true;
		}

		bool IEntityDeleteAgent.CheckUsersAllowDelete(IEntityRelationInfo candidate, IIdentifyable entity, IEntityDeleteGuard guard)
		{
			return true;
		}

		void IEntityDeleteAgent.DeleteAggregated(IEntityRelationInfo candidate, IEnumerable<IIdentifyable> entities, IEntityDeleteGuard guard)
		{
			DoDeleteAggregated(candidate, entities);
		}

		void IEntityDeleteAgent.DeleteAggregated(IEntityRelationInfo candidate, IIdentifyable entity, IEntityDeleteGuard guard)
		{
			DoDeleteAggregated(candidate, new[] { entity });
		}

		void IEntityDeleteAgent.PrepareAggregatedForDelete(IEntityRelationInfo candidate, IEnumerable<IIdentifyable> entities, IEntityDeleteGuard guard)
		{
			
		}

		void IEntityDeleteAgent.PrepareAggregatedForDelete(IEntityRelationInfo candidate, IIdentifyable entity, IEntityDeleteGuard guard)
		{
			
		}

		private void DoDeleteAggregated(IEntityRelationInfo candidate, IEnumerable<IIdentifyable> entities)
		{
			String sql;
			switch (candidate.GetIdentifier())
			{
				case "resource.type.type":
					sql = SQLExpressionBuilder.CreateDeleteStatement(GetEntityTableName(), "ID", entities);
					break;
				default:
					return;
			}

			using (var dbCtx = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				dbCtx.ExecuteSqlCommand(sql);
			}

		}

		#endregion
	}
}
