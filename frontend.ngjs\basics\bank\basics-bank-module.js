(function (angular) {
	/* global globals */
	'use strict';

	const moduleName = 'basics.bank';

	angular.module(moduleName, []);
	globals.modules.push(moduleName);

	angular.module(moduleName).config(['mainViewServiceProvider',
		function (platformLayoutService) {
			let wizardData = [{
				serviceName: 'basicsBankWizardService',
				wizardGuid: '09d06dc2449e4596bebdd14672956e52',
				methodName: 'disableBank',
				canActivate: true
			}, {
				serviceName: 'basicsBankWizardService',
				wizardGuid: '7fc78eafa5344243abb0ccc643ec1ec9',
				methodName: 'enableBank',
				canActivate: true
			}
			];

			let options = {
				'moduleName': moduleName,
				'resolve': {
					'loadDomains': ['platformSchemaService', 'basicsConfigWizardSidebarService',
						function (platformSchemaService, basicsConfigWizardSidebarService) {
						basicsConfigWizardSidebarService.registerWizard(wizardData);
						return platformSchemaService.getSchemas([
							{typeName: 'BankDto', moduleSubModule: 'Basics.Bank'}
						]);
					}]
				}
			};
			platformLayoutService.registerModule(options);
		}
	]);
})(angular);