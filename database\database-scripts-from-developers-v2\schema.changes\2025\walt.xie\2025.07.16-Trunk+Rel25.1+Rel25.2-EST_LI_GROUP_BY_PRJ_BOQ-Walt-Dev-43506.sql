-----------------------------------------
-- Ignore Errors:    Off
-- Jira Ticket:      DEV-43506
-- Script Type:      Required schema change
-- Reason:           Add SP for get project boq structure by lineitem
-- Install On:       trunk + 25.1 + 25.2
-----------------------------------------

CREATE OR ALTER PROCEDURE EST_LI_GROUP_BY_PRJ_BOQ
	@EstHeaderId INT
AS
BEGIN
SET NOCOUNT ON;

WITH 
--lineItems filter by estHeaderId
LI AS (
	SELECT 
		ID,
		EST_HEADER_FK,
		BOQ_HEADER_FK,
		BOQ_ITEM_FK,
		COST_TOTAL,
		GRAND_COST_UNIT_TARGET,
		GRAND_TOTAL
	FROM EST_LINE_ITEM WHERE EST_HEADER_FK = @EstHeaderId AND ISTEMP = 0 AND ISDISABLED = 0
)
,PRCI AS (
	SELECT * FROM PRC_ITEMASSIGNMENT WHERE EST_HEADER_FK = @EstHeaderId
)
--boq tree using in lineItem
,PrimaryTree AS (
    SELECT 
        BOQ_HEADER_FK,
        ID,
        BOQ_ITEM_FK,
        REFERENCE,
        BRIEF,
        BRIEF_TR,
        BOQ_LINE_TYPE_FK,
		BOQ_ITEM_BASIS_FK,
        QUANTITY,
        BAS_UOM_FK,
        PRICE,
        FINALPRICE
    FROM BOQ_ITEM
    WHERE EXISTS (
        SELECT 1 
        FROM LI 
        WHERE BOQ_HEADER_FK = BOQ_ITEM.BOQ_HEADER_FK AND BOQ_ITEM_FK = BOQ_ITEM.ID
    )
    UNION ALL
    SELECT 
        Parent.BOQ_HEADER_FK,
        Parent.ID,
        Parent.BOQ_ITEM_FK,
        Parent.REFERENCE,
        Parent.BRIEF,
        Parent.BRIEF_TR,
        Parent.BOQ_LINE_TYPE_FK,
		Parent.BOQ_ITEM_BASIS_FK,
        Parent.QUANTITY,
        Parent.BAS_UOM_FK,
        Parent.PRICE,
        Parent.FINALPRICE
    FROM BOQ_ITEM Parent
    JOIN PrimaryTree Child 
      ON Parent.ID = Child.BOQ_ITEM_FK 
     AND Parent.BOQ_HEADER_FK = Child.BOQ_HEADER_FK
),
--attach text node
FullTree AS (
    SELECT * FROM PrimaryTree
    UNION ALL
    SELECT 
        t.BOQ_HEADER_FK,
        t.ID,
        t.BOQ_ITEM_FK,
        t.REFERENCE,
        t.BRIEF,
        t.BRIEF_TR,
        t.BOQ_LINE_TYPE_FK,
		t.BOQ_ITEM_BASIS_FK,
        t.QUANTITY,
        t.BAS_UOM_FK,
        t.PRICE,
        t.FINALPRICE
    FROM BOQ_ITEM t
    JOIN FullTree ft 
      ON t.BOQ_ITEM_FK = ft.ID 
     AND t.BOQ_HEADER_FK = ft.BOQ_HEADER_FK
    WHERE t.BOQ_LINE_TYPE_FK IN (105, 106, 107, 110)
),
--lineItem summary
LI_SUMMARY AS (
	SELECT 
		ISNULL(R.BOQ_HEADER_FK, -1) AS BOQ_HEADER_FK, 
		ISNULL(R.BOQ_ITEM_FK, -1) AS BOQ_ITEM_FK, 
		SUM(R.COST_TOTAL) AS COST_TOTAL, 
		SUM(R.GRAND_COST_UNIT_TARGET) AS GRAND_COST_UNIT_TARGET, 
		SUM(R.GRAND_TOTAL) AS GRAND_TOTAL
	FROM LI R 
	GROUP BY R.BOQ_HEADER_FK, R.BOQ_ITEM_FK
),
--LINE ITEM WITH PRC ITEM ASSIGNMENT
BOQ_WITH_ASSIGNMENT AS (
	SELECT 
		ISNULL(LP.BOQ_HEADER_FK, -1) AS BOQ_HEADER_FK,
		ISNULL(LP.BOQ_ITEM_FK, -1) AS BOQ_ITEM_FK,
		COUNT(1) AS ASSIGNMENT_COUNT,
		STUFF((
			SELECT ',' + CAST(LI2.ID AS VARCHAR(MAX))
			FROM (
				SELECT DISTINCT 
					LI.ID
				FROM LI
				INNER JOIN PRCI ON LI.EST_HEADER_FK = PRCI.EST_HEADER_FK AND LI.ID = PRCI.EST_LINE_ITEM_FK
				WHERE (LI.BOQ_HEADER_FK = LP.BOQ_HEADER_FK AND LI.BOQ_ITEM_FK = LP.BOQ_ITEM_FK) OR (LP.BOQ_ITEM_FK IS NULL AND LI.BOQ_ITEM_FK IS NULL)
			) AS LI2
			ORDER BY LI2.ID
			FOR XML PATH(''), TYPE
		).value('.', 'VARCHAR(MAX)'), 1, 1, '') AS LINE_ITEM_IDS
	FROM (
		SELECT DISTINCT 
			LI.BOQ_HEADER_FK,
			LI.BOQ_ITEM_FK,
			LI.ID
		FROM LI
		JOIN PRCI
			ON LI.EST_HEADER_FK = PRCI.EST_HEADER_FK 
			AND LI.ID = PRCI.EST_LINE_ITEM_FK
	) AS LP
	GROUP BY LP.BOQ_HEADER_FK, LP.BOQ_ITEM_FK
),
--LINE ITEM WITHOUT PRC ITEM ASSIGNMENT
BOQ_WITHOUT_ASSIGNMENT AS (
	SELECT 
		ISNULL(LI.BOQ_HEADER_FK, -1) AS BOQ_HEADER_FK,
		ISNULL(LI.BOQ_ITEM_FK, -1) AS BOQ_ITEM_FK,
		COUNT(1) AS UN_ASSIGNMENT_COUNT,
		STUFF((
			SELECT ',' + CAST(LI2.ID AS VARCHAR(MAX))
			FROM (
				SELECT DISTINCT 
					LI1.ID
				FROM LI AS LI1
				WHERE ((LI1.BOQ_HEADER_FK = LI.BOQ_HEADER_FK AND LI1.BOQ_ITEM_FK = LI.BOQ_ITEM_FK) OR (LI1.BOQ_ITEM_FK IS NULL AND LI.BOQ_ITEM_FK IS NULL))
				AND NOT EXISTS (
					SELECT 1 
					FROM PRCI 
					WHERE EST_HEADER_FK = LI1.EST_HEADER_FK 
					  AND EST_LINE_ITEM_FK = LI1.ID
				)
			) AS LI2
			ORDER BY LI2.ID
			FOR XML PATH(''), TYPE
		).value('.', 'VARCHAR(MAX)'), 1, 1, '') AS LINE_ITEM_IDS
	FROM LI
	WHERE NOT EXISTS (
		SELECT 1 
		FROM PRCI 
		WHERE EST_HEADER_FK = LI.EST_HEADER_FK 
		  AND EST_LINE_ITEM_FK = LI.ID
	)
	GROUP BY LI.BOQ_HEADER_FK, LI.BOQ_ITEM_FK
),
ASSIGN_BOQ AS 
(
SELECT DISTINCT 
    FT1.BOQ_HEADER_FK AS EntityHeaderFK, 
	FT1.ID AS Id,
	FT1.ID AS EntityId, 
	FT1.BOQ_ITEM_FK As EntityParentFk,
    FT1.REFERENCE AS Code, 
	FT1.BRIEF AS Description, 
	FT1.BRIEF_TR AS DescriptionTr,
	FT1.BOQ_LINE_TYPE_FK AS LineTypeFk,
	FT1.BOQ_ITEM_BASIS_FK AS BoqItemBasisFk,
    FT1.QUANTITY AS Quantity, 
	FT1.BAS_UOM_FK AS BasUomFk, 
	FT1.PRICE AS UnitRate, 
	FT1.FINALPRICE AS FinalPrice,
	LS1.COST_TOTAL As CostTotal,
	LS1.GRAND_COST_UNIT_TARGET AS GrandCostUnitTarget,
	LS1.GRAND_TOTAL AS GrandTotal,
	ISNULL(LPA.ASSIGNMENT_COUNT, 0) AS AssignmentCount,
	LPA.LINE_ITEM_IDS AS AssignmentLineItemIds,
	ISNULL(LWPA.UN_ASSIGNMENT_COUNT, 0) as UnAssignmentCount,
	LWPA.LINE_ITEM_IDS as UnAssignmentLineItemIds
FROM FullTree FT1
LEFT JOIN LI_SUMMARY LS1 ON FT1.BOQ_HEADER_FK = LS1.BOQ_HEADER_FK AND FT1.ID = LS1.BOQ_ITEM_FK
LEFT JOIN BOQ_WITH_ASSIGNMENT LPA ON FT1.BOQ_HEADER_FK = LPA.BOQ_HEADER_FK AND FT1.ID = LPA.BOQ_ITEM_FK
LEFT JOIN BOQ_WITHOUT_ASSIGNMENT LWPA ON FT1.BOQ_HEADER_FK = LWPA.BOQ_HEADER_FK AND FT1.ID = LWPA.BOQ_ITEM_FK
),
UN_ASSIGN_BOQ AS(
SELECT DISTINCT 
    -1 AS EntityHeaderFK, 
	-1 AS Id,
	-1 AS EntityId, 
	null As EntityParentFk,
    'Unassigned' AS Code, 
	'Unassigned' AS Description, 
	null AS DescriptionTr,
	-1 AS LineTypeFk,
	NULL AS BoqItemBasisFk,
    NULL AS Quantity, 
	null AS BasUomFk, 
	null AS UnitRate, 
	null AS FinalPrice,
	LS1.COST_TOTAL As CostTotal,
	LS1.GRAND_COST_UNIT_TARGET AS GrandCostUnitTarget,
	LS1.GRAND_TOTAL AS GrandTotal,
	ISNULL(LPA.ASSIGNMENT_COUNT, 0) AS AssignmentCount,
	LPA.LINE_ITEM_IDS AS AssignmentLineItemIds,
	ISNULL(LWPA.UN_ASSIGNMENT_COUNT, 0) as UnAssignmentCount,
	LWPA.LINE_ITEM_IDS as UnAssignmentLineItemIds
FROM LI_SUMMARY LS1
LEFT JOIN BOQ_WITH_ASSIGNMENT LPA ON LS1.BOQ_HEADER_FK = LPA.BOQ_HEADER_FK AND LS1.BOQ_ITEM_FK = LPA.BOQ_ITEM_FK
LEFT JOIN BOQ_WITHOUT_ASSIGNMENT LWPA ON LS1.BOQ_HEADER_FK = LWPA.BOQ_HEADER_FK AND LS1.BOQ_ITEM_FK = LWPA.BOQ_ITEM_FK
WHERE LS1.BOQ_HEADER_FK = -1 AND LS1.BOQ_ITEM_FK = -1
)
SELECT
	*
FROM ASSIGN_BOQ
UNION
SELECT
	*
FROM UN_ASSIGN_BOQ
OPTION (MAXRECURSION 128);

END;