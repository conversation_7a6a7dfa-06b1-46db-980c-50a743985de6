/*
 * Copyright(c) RIB Software GmbH
 */
import { QtoShareDetailGridComplete } from '@libs/qto/shared';
import { ISalesBillingQtoDetailEntity } from '../entities/sales-billing-qto-detail-entity.interface';

/**
 * Sales Billing QTO Detail Complete Class
 * Extends the shared QTO detail complete class for billing-specific functionality
 */
export class SalesBillingQtoDetailComplete extends QtoShareDetailGridComplete<ISalesBillingQtoDetailEntity> {
	public constructor(entity: ISalesBillingQtoDetailEntity) {
		super(entity);
	}

	// Add any billing-specific complete class methods here if needed
}
