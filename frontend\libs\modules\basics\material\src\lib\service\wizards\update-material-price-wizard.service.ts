/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import { cloneDeep, get, uniq } from 'lodash';
import { PlatformHttpService, PlatformTranslateService } from '@libs/platform/common';
import { CustomStep, IDialogResult, MultistepDialog, MultistepTitleFormat, StandardDialogButtonId, UiCommonMessageBoxService, UiCommonMultistepDialogService } from '@libs/ui/common';
import { BasicsMaterialRecordDataService } from '../../material/basics-material-record-data.service';
import { BasicsMaterialMaterialCatalogDataService } from '../../material-catalog/basics-material-material-catalog-data.service';
import { BasicsMaterialMaterialGroupDataService } from '../../material-group/basics-material-material-group-data.service';
import { BasicsMaterialUpdatePriceWizardOption } from '../../model/enums/update-material-price-wizard-option.enum';
import { IMaterialEntity } from '@libs/basics/interfaces';
import { IBasicsMaterialQtnCon2PrcItemEntity, IBasicsMaterialUpdatePriceParam, IUpdatePriceDataComplete } from '../../model/entities/basics-material-update-price-entity.interface';
import { UpdateMaterialPriceSelectScopeComponent } from '../../update-material-price/components/update-material-price-select-scope/update-material-price-select-scope.component';
import { UpdateMaterialPriceFromQtnAndContractComponent } from '../../update-material-price/components/update-material-price-from-qtn-and-contract/update-material-price-from-qtn-and-contract.component';

@Injectable({
	providedIn: 'root'
})
export abstract class UpdateMaterialPriceWizardService {
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	private readonly translateService = inject(PlatformTranslateService);
	private readonly dataService = inject(BasicsMaterialRecordDataService);
	private readonly catalogDataService = inject(BasicsMaterialMaterialCatalogDataService);
	private readonly groupDataService = inject(BasicsMaterialMaterialGroupDataService);
	private readonly http = inject(PlatformHttpService);
	private multistepService = inject<UiCommonMultistepDialogService>(UiCommonMultistepDialogService);
	private readonly materialDataService = inject(BasicsMaterialRecordDataService);

	public async onStartWizard() {
		const currentCatalog = this.catalogDataService.getSelectedEntity();
		const materials = this.dataService.getList();
		const selectedMaterials = this.dataService.getSelection();
		const groups = this.groupDataService.getList();
		if (!currentCatalog && materials.length == 0 && selectedMaterials.length == 0) {
			return this.messageBoxService.showMsgBox('basics.material.updatePriceWizard.updateMaterialPriceNoMaterialCatalog', 'cloud.common.informationDialogHeader',
				'ico-info');
		} else if (currentCatalog && currentCatalog.IsInternetCatalog && materials.length == 0 && selectedMaterials.length == 0) {
			return this.messageBoxService.showMsgBox('basics.material.updatePriceWizard.cannotUpdateInternetMaterial', 'cloud.common.informationDialogHeader',
				'ico-info');
		} else {
			let option = BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial;
			if (selectedMaterials.length === 0) {
				option = BasicsMaterialUpdatePriceWizardOption.MaterialResultSet;
				if (materials.length === 0) {
					option = BasicsMaterialUpdatePriceWizardOption.HighlightedMaterialCatalog;
					if (groups.length === 0) {
						return this.messageBoxService.showMsgBox('basics.material.updatePriceWizard.noMaterials', 'cloud.common.informationDialogHeader',
							'ico-info');
					} else if (currentCatalog) {
						const resp = await this.http.get('basics/material/wizard/updatematerialprice/hasmaterialincatalog', {
							params: {
								catalogId: currentCatalog.Id
							}
						});
						if (resp) {
							return this.showUpdatePriceDialog(option);
						} else {
							return this.messageBoxService.showMsgBox('basics.material.updatePriceWizard.noMaterials', 'cloud.common.informationDialogHeader',
								'ico-info');
						}
					}
				}
			}
			return this.showUpdatePriceDialog(option);
		}
	}

	public async showUpdatePriceDialog(option: number) {
		const dataItem: IUpdatePriceDataComplete = {
			basicOption: option,
			updatePriceParam: {
				priceForm: {
					scopeOption: BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial,
					quoteStatusFks: [],
					contractStatusFks: [],
					isCheckQuote: true,
					isCheckContract: true,
					priceVersionFk: 0,
					priceResultSet: []
				}
			}
		};
		const stepTitle = this.translateService.instant('basics.material.updatePriceWizard.updateMaterialPriceTitle');
		const basicSetting = new CustomStep('basicSetting', stepTitle, UpdateMaterialPriceSelectScopeComponent, [], 'basicOption');
		const searchSetting = new CustomStep('searchSetting', stepTitle, UpdateMaterialPriceFromQtnAndContractComponent, [], 'updatePriceParam');
		const multistepDialog = new MultistepDialog(dataItem, [
			basicSetting, searchSetting
		]);
		multistepDialog.titleFormat = MultistepTitleFormat.StepTitle;
		multistepDialog.dialogOptions.width = '1200px';
		multistepDialog.dialogOptions.minWidth = '1200px';
		multistepDialog.onChangingStep = async (dialog, nextIndex) => {
			if (dialog.stepIndex === 0) {
				const scopeOption = dialog.dataItem.basicOption;
				dialog.dataItem.updatePriceParam.priceForm.scopeOption = scopeOption;
				const catalog = this.catalogDataService.getSelectedEntity();
				const materialList = this.materialDataService.getList();
				const selectMaterials = this.materialDataService.getSelection();
				if (scopeOption === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterialCatalog && catalog) {
					dialog.dataItem.updatePriceParam.priceForm.catalogId = catalog.Id;
				} else if (scopeOption === BasicsMaterialUpdatePriceWizardOption.MaterialResultSet && materialList.length > 0) {
					dialog.dataItem.updatePriceParam.priceForm.catalogId = materialList[0].MaterialCatalogFk;
				} else if (scopeOption === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial && materialList.length > 0) {
					dialog.dataItem.updatePriceParam.priceForm.catalogId = selectMaterials[0].MaterialCatalogFk;
				} else {
					dialog.dataItem.updatePriceParam.priceForm.catalogId = null;
				}
			}
		};
		multistepDialog.dialogOptions.buttons = [{
			id: 'previousStep', caption: {key: 'cloud.common.previousStep'},
			isVisible: (info) => {
				return info.dialog.value?.stepIndex !== 0;
			},
			fn: (event, info) => {
				info.dialog.value?.goToPrevious();
			}
		}, {
			id: 'nextBtn', caption: {key: 'basics.common.button.nextStep'},
			isVisible: (info) => {
				return info.dialog.value?.stepIndex === 0;
			},
			isDisabled: (info) => {
				//todo require control by form
				//return info.dialog.value?this.nextBtnDisabled(info.dialog.value):true;
				return false;
			},
			fn: (event, info) => {
				info.dialog.value?.goToNext();
			}
		}, {
			id: 'updateInsert', caption: {key: 'basics.material.updatePriceWizard.updateOrInsertButton'},
			isVisible: (info) => {
				return info.dialog.value?.stepIndex === 1;
			},
			isDisabled: (info) => {
				return info.dialog.value && info.dialog.value.stepIndex === 1 ? !this.canUpdateOrInsert(info.dialog.value.dataItem.updatePriceParam) : true;
			},
			fn: (event, info) => {
				if (info.dialog.value) {
					this.updateInsert(info.dialog.value.dataItem.updatePriceParam)?.then(resultDialog => {
						if (resultDialog && resultDialog.closingButtonId == StandardDialogButtonId.Ok) {
							info.dialog.close();
						}
					});
				}
			}
		}, {
			id: 'closeWin', caption: {key: 'basics.common.button.cancel'}, autoClose: true
		}];
		const result = await this.multistepService.showDialog(multistepDialog);
		return result?.value;
	}

	private canUpdateOrInsert(priceRequestParam: IBasicsMaterialUpdatePriceParam) {
		return priceRequestParam.priceForm.priceResultSet.some(item => item.Children.some(child => child.Selected));
	}

	public async updateInsert(priceRequestParam: IBasicsMaterialUpdatePriceParam): Promise<IDialogResult | null> {
		const materialList: IBasicsMaterialQtnCon2PrcItemEntity[] = cloneDeep(
			priceRequestParam.priceForm.priceResultSet
		).filter(item =>
			item.Children.some(child => child.Selected)
		).map(item => ({
			...item,
			Children: item.Children.filter(child => child.Selected)
		}));
		const resp = await this.http.post('basics/material/wizard/updatematerialprice/updateinsert',
			{
				PriceVersionFk: priceRequestParam.priceForm.priceVersionFk,
				Materials: materialList
			});
		if (resp) {
			const failedMaterialCodes = get(resp, 'failedMaterialCodes');
			const successMaterialCodes = get(resp, 'successMaterialCodes');
			if (failedMaterialCodes || successMaterialCodes) {
				const failedMaterials = failedMaterialCodes ? failedMaterialCodes as string[] : [];
				const successMaterials = successMaterialCodes ? successMaterialCodes as string[] : [];
				let msg = '';
				if (failedMaterials.length > 0) {
					msg = this.translateService.instant({
						key: 'basics.material.updatePriceWizard.partialUpdated',
						params: {'p_0': failedMaterials.length}
					}).text + failedMaterials.join(',');

				} else {
					msg = this.translateService.instant({
						key: 'basics.material.updatePriceWizard.allUpdated',
						params: {'p_0': successMaterials.length}
					}).text;
				}
				return this.messageBoxService.showMsgBox(msg, 'cloud.common.informationDialogHeader', 'ico-info');
			}
		}
		return null;
	}

	public IsMaterialsInSameCatalog(option: number) {
		const materials = this.dataService.getList();
		const selectedMaterials = this.dataService.getSelection();
		const checkUniqueCatalogIds = (materials: IMaterialEntity[], option: number) => {
			const ids = materials.map(item => item.MaterialCatalogFk);
			const materialCatalogIds = uniq(ids);
			return materialCatalogIds.length === 1 && option !== BasicsMaterialUpdatePriceWizardOption.HighlightedMaterialCatalog;
		};
		if ((option === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial && selectedMaterials.length <= 1) || (option === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial && materials.length <= 1)) {
			return true;
		} else {
			return checkUniqueCatalogIds(option === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial ? selectedMaterials : materials, option);
		}
	}

	public checkIfInternetCatalog(option: number) {
		/* angularjs get IsInternetCatalog from catalog by http,here use get catalog IsInternetCatalog by getList*/
		const materials = this.dataService.getList();
		const selectedMaterials = this.dataService.getSelection();
		const materialCatalogs = this.catalogDataService.getList();
		let firstMaterial: IMaterialEntity | null = null;
		if (option === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial) {
			firstMaterial = selectedMaterials[0];
		} else if (option === BasicsMaterialUpdatePriceWizardOption.MaterialResultSet) {
			firstMaterial = materials[0];
		}
		if (firstMaterial != null) {
			const catalogId = firstMaterial.MaterialCatalogFk;
			const materialCatalog = materialCatalogs.find(item => {
				return item.Id == catalogId;
			});
			if (materialCatalog) {
				return materialCatalog.IsInternetCatalog;
			}
		}
		return true;
	}
}