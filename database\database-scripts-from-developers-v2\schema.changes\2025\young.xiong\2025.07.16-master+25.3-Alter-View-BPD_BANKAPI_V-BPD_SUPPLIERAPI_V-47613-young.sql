-------------------------------------------------------
-- <PERSON><PERSON> Ticket (REQUIRED): DEV-47613
-- Script Type (REQUIRED): Required Schema Change 
-- Reason (REQUIRED): BPM fields for synchronisation with RIB Finance   
-- Install On (OPTIONAL): trunk,25.3
-------------------------------------------------------

--BPD_BANKAPI_V
EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC BPD_BANKAPI_V
GO
CREATE VIEW [dbo].[BPD_BANKAPI_V]
AS
SELECT                   BPD_BANK.ID, 
                         BPD_BUSINESSPARTNER.ID AS BPD_BUSINESSPARTNER_ID, 
                         BAS_BANK.ID AS BAS_BANK_ID, 
                         BAS_BANK.BANK_NAME AS BAS_BANK_NAME, 
                         BAS_BANK.CITY AS BAS_BANK_CITY, 
                         BAS_BANK.BIC AS BAS_BANK_BIC,
						 BAS_BANK.SORTCODE AS BAS_BANK_SORTCODE, 
						 BPD_BANK_TYPE.ID AS BPD_BANK_TYPE_ID, 
						 ISNULL
                             ((SELECT        DESCRIPTION
                                 FROM            BAS_TRANSLATION AS t
                                 WHERE        (BPD_BANK_TYPE.DESCRIPTION_TR = ID) AND (BAS_LANGUAGE_FK = lang.ID)), BPD_BANK_TYPE.DESCRIPTION) AS BPD_BANK_TYPE_DESC, BAS_COMPANY.ID AS BAS_COMPANY_ID, 
                         BAS_COMPANY.COMPANY_NAME AS BAS_COMPANY_NAME, 
						 BAS_COMPANY.CODE AS BAS_COMPANY_CODE, 
						 BAS_COUNTRY.ID AS BAS_COUNTRY_ID, 
						 BAS_COUNTRY.ISO2 AS BAS_COUNTRY_ISO2, 
                         ISNULL
                             ((SELECT        DESCRIPTION
                                 FROM            BAS_TRANSLATION AS t
                                 WHERE        (BAS_COUNTRY.DESCRIPTION_TR = ID) AND (BAS_LANGUAGE_FK = lang.ID)), BAS_COUNTRY.DESCRIPTION) AS BAS_COUNTRY_DESC, BPD_BANKSTATUS.ID AS BPD_BANKSTATUS_ID, 
                         ISNULL
                             ((SELECT        DESCRIPTION
                                 FROM            BAS_TRANSLATION AS t
                                 WHERE        (BPD_BANKSTATUS.DESCRIPTION_TR = ID) AND (BAS_LANGUAGE_FK = lang.ID)), BPD_BANKSTATUS.DESCRIPTION) AS BPD_BANKSTATUS_DESC, 
                         BPD_BANKSTATUS.CODE AS BPD_BANKSTATUS_CODE, 
						 BPD_BANK.IBAN AS IBAN,
						 BPD_BANK.ACCOUNTNO AS ACCOUNTNO,
						 BPD_BANK.ISLIVE AS ISLIVE,
						 BPD_BANK.ISDEFAULT AS ISDEFAULT, 
						 BPD_BANK.USERDEFINED1 AS USERDEFINED1, 
                         BPD_BANK.USERDEFINED2 AS USERDEFINED2, 
						 BPD_BANK.USERDEFINED3 AS USERDEFINED3,
						 BPD_BANK.IS_DEFAULT_CUSTOMER,
						 BPD_BANK.DIFFERENT_ACCOUNT_HOLDER,
						 BPD_BANK.INSERTED AS INSERTED, 
						 BPD_BANK.WHOISR AS WHOISR, 
						 BPD_BANK.UPDATED AS UPDATED,
						 BPD_BANK.WHOUPD AS WHOUPD, 
						 BPD_BANK.VERSION AS VERSION, 
                         lang.ID AS LANGUAGE_ID
FROM            BPD_BANK INNER JOIN
                         BPD_BANK_TYPE ON BPD_BANK.BPD_BANK_TYPE_FK = BPD_BANK_TYPE.ID INNER JOIN
                         BPD_BANKSTATUS ON BPD_BANK.BPD_BANKSTATUS_FK = BPD_BANKSTATUS.ID LEFT OUTER JOIN
                         BAS_COMPANY ON BPD_BANK.BAS_COMPANY_FK = BAS_COMPANY.ID INNER JOIN
                         BAS_COUNTRY ON BPD_BANK.BAS_COUNTRY_FK = BAS_COUNTRY.ID LEFT OUTER JOIN
                         BAS_BANK ON BPD_BANK.BAS_BANK_FK = BAS_BANK.ID INNER JOIN
                         BPD_BUSINESSPARTNER ON BPD_BANK.BPD_BUSINESSPARTNER_FK = BPD_BUSINESSPARTNER.ID CROSS JOIN
                         BAS_LANGUAGE AS lang
GO

--BPD_SUPPLIERAPI_V
EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC BPD_SUPPLIERAPI_V
GO
CREATE VIEW [dbo].[BPD_SUPPLIERAPI_V]
AS
SELECT supplier.ID AS ID
    ,businesspartner.ID AS BPD_BUSINESSPARTNER_ID
    ,supplierstatus.ID AS BPD_SUPPLIERSTATUS_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE supplierstatus.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE supplierstatus.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), supplierstatus.DESCRIPTION)) AS BPD_SUPPLIERSTATUS_DESC
    ,supplier.CODE AS CODE
    ,supplier.DESCRIPTION AS DESCRIPTION
    ,supplier.DESCRIPTION2 AS DESCRIPTION2
    ,payment_term_pa.ID AS BAS_PAYMENT_TERM_PA_ID
    ,payment_term_pa.CODE AS BAS_PAYMENT_TERM_PA_CODE
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE payment_term_pa.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE payment_term_pa.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), payment_term_pa.DESCRIPTION)) AS BAS_PAYMENT_TERM_PA_DESC
    ,payment_term_fi.ID AS BAS_PAYMENT_TERM_FI_ID
    ,payment_term_fi.CODE AS BAS_PAYMENT_TERM_FI_CODE
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE payment_term_fi.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE payment_term_fi.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), payment_term_fi.DESCRIPTION)) AS BAS_PAYMENT_TERM_FI_DESC
    ,supplier_ledger_group.ID AS BPD_SUPPLIER_LEDGER_GROUP_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE supplier_ledger_group.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE supplier_ledger_group.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), supplier_ledger_group.DESCRIPTION)) AS BPD_SUPPLIER_LEDGER_GROUP_DESC
    ,billing_schema.ID AS MDC_BILLING_SCHEMA_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE billing_schema.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE billing_schema.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), billing_schema.DESCRIPTION)) AS MDC_BILLING_SCHEMA_DESC
    ,supplier.CUSTOMERNO AS CUSTOMERNO
    ,vatgroup.ID AS BPD_VATGROUP_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE vatgroup.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE vatgroup.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), vatgroup.DESCRIPTION)) AS BPD_VATGROUP_DESC
    ,subsidiary.ID AS BPD_SUBSIDIARY_ID
    ,subsidiary.DESCRIPTION AS BPD_SUBSIDIARY_DESC
    ,supplier.USERDEFINED1 AS USERDEFINED1
    ,supplier.USERDEFINED2 AS USERDEFINED2
    ,supplier.USERDEFINED3 AS USERDEFINED3
    ,supplier.USERDEFINED4 AS USERDEFINED4
    ,supplier.USERDEFINED5 AS USERDEFINED5
    ,businesspostinggroup.ID AS BPD_BUSINESSPOSTINGGROUP_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE businesspostinggroup.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE businesspostinggroup.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), businesspostinggroup.DESCRIPTION)) AS BPD_BUSINESSPOSTINGGROUP_DESC
    ,bank.ID AS BPD_BANK_ID
    ,supplier.INSERTED AS INSERTED
    ,supplier.WHOISR AS WHOISR
    ,supplier.UPDATED AS UPDATED
    ,supplier.WHOUPD AS WHOUPD
    ,supplier.VERSION AS VERSION
    ,paymentmethod.ID AS BAS_PAYMENTMETHOD_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE paymentmethod.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE paymentmethod.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), paymentmethod.DESCRIPTION)) AS BAS_PAYMENTMETHOD_DESC
    ,businesspostgrp_wht.ID AS BPD_BUSINESSPOSTGRP_WHT_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE businesspostgrp_wht.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE businesspostgrp_wht.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), businesspostgrp_wht.DESCRIPTION)) AS BPD_BUSINESSPOSTGRP_WHT_DESC
    ,blockingreason.ID AS BPD_BLOCKINGREASON_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE blockingreason.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE blockingreason.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), blockingreason.DESCRIPTION)) AS BPD_BLOCKINGREASON_DESC
	,supplier_ledger_group_ic.ID AS BPD_SUPPLIER_LEDGER_GROUP_IC_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE supplier_ledger_group_ic.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE supplier_ledger_group_ic.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), supplier_ledger_group_ic.DESCRIPTION)) AS BPD_SUPPLIER_LEDGER_GROUP_IC_DESC
	,rubric_category.ID AS BAS_RUBRIC_CATEGORY_ID
    ,ISNULL((
            SELECT [DESCRIPTION]
            FROM BAS_TRANSLATION AS t1
            WHERE rubric_category.DESCRIPTION_TR = t1.ID
                AND BAS_LANGUAGE_FK = lang.ID
            ), ISNULL((
                SELECT [DESCRIPTION]
                FROM BAS_TRANSLATION AS t2
                WHERE rubric_category.DESCRIPTION_TR = t2.ID
                    AND BAS_LANGUAGE_FK = lang.FB_LANGUAGE_FK
                ), rubric_category.DESCRIPTION)) AS BAS_RUBRIC_CATEGORY_DESC
    ,supplier.IS_EMAIL_NOTIFICATION 
	,supplier.NOTIFICATION_EMAIL
	,supplier.POST_AVIS
	,supplier.IS_BP_SETTLEMENT
	,supplier.ITEM_PER_TRANSFER
	,lang.ID AS LANGUAGE_ID
FROM BPD_SUPPLIER supplier
INNER JOIN BPD_BUSINESSPARTNER businesspartner ON businesspartner.ID = supplier.BPD_BUSINESSPARTNER_FK
LEFT JOIN BPD_SUPPLIERSTATUS supplierstatus ON supplierstatus.ID = supplier.BPD_SUPPLIERSTATUS_FK
LEFT JOIN BAS_PAYMENT_TERM payment_term_pa ON payment_term_pa.ID = supplier.BAS_PAYMENT_TERM_PA_FK
LEFT JOIN BAS_PAYMENT_TERM payment_term_fi ON payment_term_fi.ID = supplier.BAS_PAYMENT_TERM_FI_FK
INNER JOIN BPD_SUPPLIER_LEDGER_GROUP supplier_ledger_group ON supplier_ledger_group.ID = supplier.BPD_SUPPLIER_LEDGER_GROUP_FK
LEFT JOIN BPD_SUPPLIER_LEDGER_GROUP supplier_ledger_group_ic ON supplier_ledger_group_ic.ID = supplier.BPD_SUPPLIER_LEDGER_GROUP_IC_FK
LEFT JOIN MDC_BILLING_SCHEMA billing_schema ON billing_schema.ID = supplier.MDC_BILLING_SCHEMA_FK
LEFT JOIN BPD_VATGROUP vatgroup ON vatgroup.ID = supplier.BPD_VATGROUP_FK
LEFT JOIN BPD_SUBSIDIARY subsidiary ON subsidiary.ID = supplier.BPD_SUBSIDIARY_FK
INNER JOIN BPD_BUSINESSPOSTINGGROUP businesspostinggroup ON businesspostinggroup.ID = supplier.BPD_BUSINESSPOSTINGGROUP_FK
LEFT JOIN BPD_BANK bank ON bank.ID = supplier.BPD_BANK_FK
LEFT JOIN BAS_PAYMENTMETHOD paymentmethod ON paymentmethod.ID = supplier.BAS_PAYMENTMETHOD_FK
LEFT JOIN BPD_BUSINESSPOSTGRP_WHT businesspostgrp_wht ON businesspostgrp_wht.ID = supplier.BPD_BUSINESSPOSTGRP_WHT_FK
LEFT JOIN BPD_BLOCKINGREASON blockingreason ON blockingreason.ID = supplier.BPD_BLOCKINGREASON_FK
LEFT JOIN BAS_RUBRIC_CATEGORY rubric_category ON rubric_category.ID = supplier.BAS_RUBRIC_CATEGORY_FK
CROSS JOIN BAS_LANGUAGE lang
GO