

import { BaseValidationService, IEntityRuntimeDataRegistry, IUniqueValidationHttpOptions, IValidationFunctions, ValidationInfo } from '@libs/platform/data-access';
import { IPpsItem2MdcMaterialProductDescriptionEntity } from '../../model/models';
import { Injectable } from '@angular/core';
import { PpsUpstream2MaterialProductDescriptionService } from './pps-upstream2-material-product-description.service';

@Injectable({
	providedIn: 'root'
})
export class PpsUpstream2MaterialProductDescriptionValidationService extends BaseValidationService<IPpsItem2MdcMaterialProductDescriptionEntity> {

	public constructor(private dataService: PpsUpstream2MaterialProductDescriptionService) {
		super();
	}

	protected override generateValidationFunctions(): IValidationFunctions<IPpsItem2MdcMaterialProductDescriptionEntity> {
		return {
			Code: [this.validateIsRequired, this.validateIsLocalUnique, this.validateIsRemoteUnique],
			UomFk: [this.validateIsMandatory],
			BasUomLengthFk: [this.validateIsMandatory],
			BasUomWidthFk: [this.validateIsMandatory],
			BasUomHeightFk: [this.validateIsMandatory],
			BasUomWeightFk: [this.validateIsMandatory],
			BasUomAreaFk: [this.validateIsMandatory],
			BasUomVolumeFk: [this.validateIsMandatory],
			EngDrawingFk:[this.validateIsMandatory],
		};
	}

	protected override getLoadedEntitiesForUniqueComparison = (info: ValidationInfo<IPpsItem2MdcMaterialProductDescriptionEntity>): IPpsItem2MdcMaterialProductDescriptionEntity[] => {
		const itemList = this.dataService.getList();
		return itemList.filter((item) => {
			return (item as never)[info.field] === info.value && item.Id !== info.entity.Id;
		});
	};

	protected override createUniqueValidationHttpOptions = async (info: ValidationInfo<IPpsItem2MdcMaterialProductDescriptionEntity>): Promise<IUniqueValidationHttpOptions> => {
		switch (info.field) {
			case 'Code':
				return {
					usePost: false,
					route: 'productionplanning/ppsmaterial/mdcproductdescription/isuniquecode',
					params: {
						currentId: info.entity.Id,
						materialId: info.entity.MaterialFk,
						code: info.value as string,
					},
				};
		}
		throw new Error('Unsupported field');
	};

	protected override getEntityIdentification = (entity: IPpsItem2MdcMaterialProductDescriptionEntity) => entity;

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPpsItem2MdcMaterialProductDescriptionEntity> {
		return this.dataService;
	}
}
