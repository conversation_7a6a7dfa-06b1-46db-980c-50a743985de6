/*
 * $Id$
 * Copyright (c) RIB Software AG
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Http;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Dtos;
using RIB.Visual.Platform.Core;
using RIB.Visual.Resource.Requisition.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Final;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using Newtonsoft.Json;
using RIB.Visual.Resource.Requisition.Common;

namespace RIB.Visual.Resource.Requisition.ServiceFacade.WebApi
{
	/// <summary>
	/// class for lookup filter service
	/// </summary>
	public class LookupFilter
	{
		/// <summary>
		/// Id of the job to look for
		/// </summary>
		[JsonProperty("jobFk")]
		public int JobFk { get; set; }

		/// <summary>
		/// Id of the resource to look for
		/// </summary>
		[JsonProperty("resourceFk")]
		public int? ResourceFk { get; set; }

		/// <summary>
		/// Id of the project to look for
		/// </summary>
		[JsonProperty("projectFk")]
		public int? ProjectFk { get; set; }

		///<summary>
		///Id of the resourceTypefk to look for
		///</summary>
		[JsonProperty("resourceTypeFk")]
		public int? ResourceTypeFk { get; set; }

		/// <summary>
		/// date of the requested from to look for
		/// </summary>
		[JsonProperty("requestedFrom")]
		public DateTime? RequestedFrom { get; set; }

		/// <summary>
		/// date of the requested to to look for
		/// </summary>
		[JsonProperty("requestedTo")]
		public DateTime? RequestedTo { get; set; }
	}

	/// <summary>
	/// Please add a comment here and change RoutePrefix and Name of controller to your module's and submodule's name
	/// </summary>
	[RoutePrefix("resource/requisition")]
	// or 
	// [RoutePrefix("module/submodule[/localname]")]
	public class ResourceRequisitionController : RootEntityController<ResourceRequisitionMainLogic, RequisitionDto, RequisitionEntity, RequisitionUpdateDto, RequisitionUpdateEntity, IdentificationData>
	{
		/// <summary>
		/// Controller for requisition WEB Api, doing some crucial initialisation
		/// </summary>
		public ResourceRequisitionController()
		{
			CreateDto = e => new RequisitionDto(e);
			CopyDto = dto => dto.Copy();

			CreateCompleteDto = e => new RequisitionUpdateDto(e);
			CopyCompleteDto = dto => dto.Copy();
		}

		/// <summary>
		/// list for lookup
		/// </summary>
		[HttpGet]
		[Route("lookuplist")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> LookupList(RequisitionDto dto)
		{
			return Logic.GetListByFilter(null).Select(e => new RequisitionDto(e));
		}

		/// <summary>
		/// list for lookup
		/// </summary>
		[HttpPost]
		[Route("requiredby")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> RequiredBy(IdentificationData reserver)
		{
			Expression<Func<RequisitionEntity, bool>> filter = null;

			if (reserver.PKey1.HasValue)
			{
				int nID = reserver.PKey1.Value;

				switch (reserver.Id)
				{
					case 1: filter = req => req.ActivityFk == nID; break;
				}
			}

			return filter != null ? Logic.GetListByFilter(filter).ToList().Select(e => new RequisitionDto(e)) : null;
		}

		/// <summary>
		/// list for lookup
		/// </summary>
		[HttpGet]
		[Route("byJob")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> RequiredByJob(int job)
		{
			return Logic.GetListByFilter(req => req.JobFk == job).ToArray().Select(CreateDto);
		}

		/// <summary>
		/// list for lookup
		/// </summary>
		[HttpGet]
		[Route("listForMntActivity")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> GetRequisitionByMntActivity(int PpsEventId)
		{
			var dtos = Logic.GetListByFilter(e => e.PpsEventFk == PpsEventId).ToList().Select(e => new RequisitionDto(e));
			return dtos;
		}

		/// <summary>
		/// Load filtered Requisitions for the planningboard
		/// </summary>
		[HttpPost]
		[Route("getForPlanningBoard")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> GetForPlanningBoard(PlanningBoardFilter filter)
		{
			return Logic.GetForPlanningBoard(filter).Select(e => new RequisitionDto(e));
		}

		/// <summary>
		/// Load filtered Requisitions for the planningboard
		/// </summary>
		[HttpPost]
		[Route("merge")]
		[Permission(Permissions.Write, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public string Merge(IEnumerable<RequisitionDto> requisitions)
		{
			return Logic.Merge(requisitions.Select(r => r.Copy()));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="trsRequisitionId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("listForTrsRequisition")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> GetRequisitionByTrsRequisition(int trsRequisitionId)
		{
			var dtos = Logic
				.GetListByFilter(e => e.TrsRequisitionFk == trsRequisitionId)
				.Select(e => new RequisitionDto(e));
			return dtos;
		}

		/// <summary>
		/// return resource requisitions by trs-requisition's Id or trs-requisition's PpsEventFk
		/// </summary>
		/// <remarks>
		/// Actually, trs-requisition is also a special Pps Event. 
		/// At the moment, there are lots of res-requisition records that are created by trs-requisition's Id or trs-requisition's PpsEventFk in database.
		/// To be compatible with such situation, we need to provide this function.
		/// </remarks>
		/// <param name="trsRequisitionId"></param>
		/// <param name="ppsEventId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("listbytrsrequisition")]
		[Permission(Permissions.Read, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> GetRequisitionByTrsRequisition(int trsRequisitionId, int ppsEventId)
		{
			var dtos = Logic.GetListByFilter(e => (e.TrsRequisitionFk == trsRequisitionId || e.PpsEventFk == ppsEventId) && !e.IsDeleted)
								 .Select(e => new RequisitionDto(e));
			return dtos;
		}

		/// <summary>
		/// Returns a list 
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("lookuplistbyfilter")]
		public IEnumerable<RequisitionDto> GetLookupListByFilter(LookupFilter filter)
		{
			var list = Logic.GetLookupListByFilter(filter.JobFk, filter.ProjectFk, filter.ResourceFk, filter.RequestedFrom,
				filter.RequestedTo, filter.ResourceTypeFk);
			return list.Select(e => new RequisitionDto(e)).ToList();
		}

		/// <summary>	
		/// change status
		/// </summary>
		/// <returns></returns>
		[Route("changestatus")]
		[HttpPost]
		public RequisitionDto ChangeRequisitionStatus(ChangeStatusDto dto)
		{
			var entity = Logic.SetRequisitionStatus(dto.EntityId, dto.NewStatusId);
			return entity == null ? null : new RequisitionDto(entity);
		}

		/// <summary>	
		/// change status to isfullycovered
		/// </summary>
		/// <returns></returns>
		[Route("changestatustoisfullycovered")]
		[HttpPost]
		public string ChangeReqStatusToIsFullyCovered(IEnumerable<int> resIds)
		{
			try
			{
				Logic.SetReqStatusToIsFullyCovered(resIds);
				return "";

			} catch (Exception e)
			{
				return e.Message;
			}

		}

		/// <summary>
		/// Get Requisition by Id
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getById")]
		public RequisitionDto GetById(int Id)
		{
			var entity = Logic.GetById(new IdentificationData() { Id = Id });
			return entity == null ? null : new RequisitionDto(entity);
		}

		/// <summary>
		/// get by jobs
		/// </summary>
		/// <returns>The number of records for which there have been made reservations</returns>
		[HttpPost]
		[Route("reservematerial")]
		public IEnumerable<RequisitionDto> ReserveMaterial(RequisitionMaterialReservationDto reservationInfo)
		{
			return Logic.ReserveMaterialsInStockAndAdjustState(reservationInfo.Copy()).Select(CreateDto);
		}

		/// <summary>
		/// Handle command
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("execute")]
		public RequisitionUpdateDto HandleCommand(ResourceRequisitionActionDto resourceRequisitionAction)
		{
			ResourceRequisitionActionEntity enti = resourceRequisitionAction.Copy();
			var logicActionLogic = new ResourceRequisitonActionLogic();

			return new RequisitionUpdateDto(logicActionLogic.Execute(enti));
		}

		/// <summary>
		/// create dispatching notes from given job and job card
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("dispatchingformaterialres")]
		public string CreateDispatchNoteForMaterialReservation(CreateDispatchNoteFromMaterialReservationData data)
		{
			return Logic.CreateDispatchingForMaterialReservation(data);
		}

		/// <summary>
		/// delete requisition
		/// </summary>
		/// <returns>saved header</returns>
		[HttpPost]
		[Route("deleterequisition")]
		public IEnumerable<RequisitionDto> DeleteRequisition(IEnumerable<RequisitionDto> dtos)
		{
			var entities = dtos.Select(e => e.Copy()).ToList();

			Logic.DeleteRequisition(entities);

			return dtos;
		}

		/// <summary>
		/// delete requisition
		/// </summary>
		/// <returns>saved header</returns>
		[HttpPost]
		[Route("changerequesteddate")]
		public IEnumerable<RequisitionDto> ChangeRequestedDate(RequisitionChangeRequestedDateReq request)
		{
			return Logic.ChangeRequestedDate(request).Select(e => new RequisitionDto(e));
		}

		/// <summary>
		/// IsCodeUnique
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("isCodeUnique")]
		public bool IsCodeUnique(string code)
		{
			var list = Logic.GetResCode(code);
			return !list.Any();
		}

		/// <summary>
		/// Create an Resource Requisition from initial data
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("createrequisitiondata")]
		[Permission(Permissions.Write, "291a21ca7ab94d549d2d0c541ec09f5d")]
		public IEnumerable<RequisitionDto> CreateRequisitionData(RequisitionEntity data)
		{
			var dtos = Logic.CreateRequisitionData(data).Select(e => new RequisitionDto(e)).ToArray();
			return dtos;
		}

		/// <summary>
		/// fetch default logistic job for the given project
		/// </summary>
		/// <param name="projectfk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("defaultlogisticjobislive")]
		public int? DefaultLogisticJobIsLive(int projectfk)
		{
			var jobfk = Logic.DefaultLogisticJobIsLive(projectfk);
			return jobfk;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns>saved header</returns>
		[HttpPost]
		[Route("createalternative")]
		public IEnumerable<RequisitionDto> CreateAlternativeFor(IEnumerable<RequisitionDto> dtos)
		{
			var item = Logic.CreateAlternativeFor(dtos.Select(e => e.Copy()).ToList()).Select(e => new RequisitionDto(e)).ToArray();			
			return item;
		}

	}
}
