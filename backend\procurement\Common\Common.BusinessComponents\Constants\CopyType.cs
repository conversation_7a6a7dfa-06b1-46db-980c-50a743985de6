using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Procurement.Common.Localization.Properties;

namespace RIB.Visual.Procurement.Common.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public enum CopyType
	{
		/// <summary>
		/// 
		/// </summary>
		WicBoq = 1,
		/// <summary>
		/// 
		/// </summary>
		ProjectBoq,
		/// <summary>
		/// 
		/// </summary>
		PackageBoq,
		/// <summary>
		/// 
		/// </summary>
		Material,
		/// <summary>
		/// 
		/// </summary>
		ContractBoq
	}

	/// <summary>
	/// 
	/// </summary>
	public static class CopyTypeLogic
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="copyType"></param>
		/// <returns></returns>
		public static string GetCopyTypeText(int copyType)
		{
			switch (copyType)
			{
				case (int)CopyType.WicBoq: // Wic BoQ
					return Resources.CopyType_WICBoQ;
				case (int)CopyType.ProjectBoq: // Project BoQ
					return Resources.CopyType_ProjectBoQ;
				case (int)CopyType.PackageBoq: // Package BoQ
					return Resources.CopyType_PackageBoQ;
				case (int)CopyType.Material: // Material
					return Resources.CopyType_Material;
				case (int)CopyType.ContractBoq: // Procurement Contract BoQ
					return Resources.CopyType_ContractBoQ;
				default:
					throw new BusinessLayerException(string.Format("Argument {0} ({1}) is not valid.", nameof(copyType), copyType));
			}
		}
	}
}
