/// <reference types="cypress" />
import { apiConstants } from 'cypress/constantData';
import apiConstantData from 'cypress/constantData/apiConstantData';
import { apiParameters, app, btn, cnt, commonLocators, sidebar } from 'cypress/locators';
import { _common, _mainView } from 'cypress/pages';
import { getCode, getProjectCode, getProjectName } from 'cypress/pages/data-generator';
import { DataCells } from 'cypress/pages/interfaces';
import { isParameter } from 'typescript';

export class CommonAPI {
	private token: string;
	private tokenType: string;

	public getAccessToken(): Cypress.Chainable<{ token: string; tokenType: string }> {
		return cy.fixture('api-config/login-headers.json').then((data) => {
			const input = data;
			return cy
				.request({
					method: 'POST',
					url: Cypress.env('Base_URL') + 'identityservercore/core/connect/token',
					headers: input.Login,
					body: input.Body,
				})
				.then((response) => {
					expect(response.status).to.eq(200);
					this.token = response.body.access_token;
					this.tokenType = response.body.token_type;
					Cypress.env('TokenType', this.tokenType);
					Cypress.env('LoginToken', this.token);
					cy.log(`${this.tokenType} ${this.token}`);

					return cy.wrap({ token: this.token, tokenType: this.tokenType });
				});
		});
	}
	public createProject(): Cypress.Chainable<Cypress.Response<any>> {
		const projectNo = _common.generateRandomString(5);
		const projectName = _common.generateRandomString(5);
		let projectCounter = Cypress.env('projectCounter') || 0; // Start from 0 if not defined
		projectCounter += 1;
		Cypress.env('projectCounter', projectCounter); // Update counter in env globally
		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/project/publicapi/project/3.0',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json, text/plain, */*',
					'Accept-Encoding': 'gzip, deflate, br',
					Connection: 'keep-alive',
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {
					ProjectStatusId: 4,
					ProjectStatusDesc: 'Sales',
					ProjectTypeId: 5,
					ProjectTypeDesc: 'iTWO Cloud Project',
					ProjectNo: projectNo,
					ProjectIndex: 0,
					ProjectName: projectName,
					ProjectName2: projectName,
					CurrencyId: 1,
					CurrencyDesc: 'Euro',
					CompanyId: 1000005,
					CompanyCode: '999',
					ClerkId: 2,
					ClerkCode: 'SmiJ',
					ClerkDesc: 'Smith, John',
					StartDate: '2018-11-20T00:00:00Z',
					MainProject: 0,
					IsLive: true,
					IsTemplate: false,
					RubricCategoryId: 1,
					ProjectGroupId: 1000002,
					CompanyResponsibleId: 1000005,
					CompanyResponsibleCode: '999',
					ProjectIndexAlpha: '0',
					CheckPermission: false,
					IsAdministration: false,
					IsInterCompany: true,
					DateEffective: '2018-11-20T00:00:00Z',
					CatalogConfigurationTypeId: 2000003,
					CatalogConfigurationId: 2000005,
					ProjectQuantityControlId: 6,
				},
			})
			.then((response) => {
				expect(response.status).to.equal(200);

				Cypress.env(`API_PROJECT_ID_${projectCounter}`, response.body.Id);
				Cypress.env(`API_PROJECT_NUMBER_${projectCounter}`, response.body.ProjectNo);
				Cypress.env(`API_PROJECT_NAME_${projectCounter}`, response.body.ProjectName);

				cy.log(`API_PROJECT_ID_${projectCounter}: ${response.body.Id}`);
				cy.log(`API_PROJECT_NUMBER_${projectCounter}: ${response.body.ProjectNo}`);
				cy.log(`API_PROJECT_NAME_${projectCounter}: ${response.body.ProjectName}`);
			});
	}


	public createBoQHeaderWithItems(data: DataCells, positionCount?: number): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/boq/publicapi/project/1.0/putprojectboq',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {
					CompanyCode: '901',
					ProjectNumber: data[app.GridCells.PROJECT_CODE],
					ImportMode: 1,
					BoqReference: '',
					BoqOutlineSpecification: data[app.GridCells.BRIEF_INFO_SMALL],

					// BoqItems array starts with the static items
					BoqItems: [
						{
							ParentReference: '',
							Reference: '10.',
							Culture: 'en',
							Description: null,
							BoqlineType: 1,
							URBreakdown: false,
							Lumpsum: false,
							Uom: null,
							Quantity: 2.0,
							QuantityAQ: 0.0,
							UnitRate: 0.0,
							CorrectedUR: 0.0,
							Correction: 0.0,
							LumpsumPrice: 0.0,
							Factor: 1.0,
							ItemTypeStandOpt: null,
							ItemTypeBaseAlt: null,
							FinalPrice: 0.0,
							CorrectedURGross: 0.0,
							OutlineSpecification: {
								Description: null,
								OtherLanguages: [],
							},
						},
						{
							ParentReference: '10.',
							Reference: '10.10.',
							Culture: 'en',
							Description: null,
							BoqlineType: 2,
							URBreakdown: false,
							Lumpsum: false,
							Uom: null,
							Quantity: 3.0,
							QuantityAQ: 0.0,
							UnitRate: 0.0,
							CorrectedUR: 0.0,
							Correction: 0.0,
							LumpsumPrice: 0.0,
							Factor: 1.0,
							ItemTypeStandOpt: null,
							ItemTypeBaseAlt: null,
							FinalPrice: 0.0,
							CorrectedURGross: 0.0,
							OutlineSpecification: {
								Description: null,
								OtherLanguages: [],
							},
						},

						// Add dynamic position items if positionCount > 0 Conditional spread
						...(positionCount > 0
							? Array.from({ length: positionCount }, (_, i) => ({
									ParentReference: '10.10.',
									Reference: `10.10.${(i + 1) * 10}.`, // Dynamic reference
									Culture: 'en',
									Description: data[app.GridCells.BRIEF_INFO][i],
									BoqlineType: 'Position',
									URBreakdown: false,
									Lumpsum: false,
									Uom: data[app.GridCells.BAS_UOM_FK][i],
									Quantity: data[app.GridCells.QUANTITY_SMALL][i],
									QuantityAQ: data[app.GridCells.QUANTITY_SMALL][i],
									UnitRate: data[app.GridCells.PRICE_SMALL][i],
									CorrectedUR: 0.0,
									Correction: 0.0,
									LumpsumPrice: 0.0,
									Factor: 1.0,
									ItemTypeStandOpt: null,
									ItemTypeBaseAlt: null,
									FinalPrice: 0.0,
									CorrectedURGross: 0.0,
									OutlineSpecification: {
										Description: data[app.GridCells.BRIEF_INFO][i],
										OtherLanguages: [],
									},
							  }))
							: [
									{
										ParentReference: '10.10.',
										Reference: '10.10.10.',
										Culture: 'en',
										Description: data[app.GridCells.BRIEF_INFO],
										BoqlineType: 'Position',
										URBreakdown: false,
										Lumpsum: false,
										Uom: data[app.GridCells.BAS_UOM_FK],
										Quantity: data[app.GridCells.QUANTITY_SMALL],
										QuantityAQ: data[app.GridCells.QUANTITY_SMALL],
										UnitRate: data[app.GridCells.PRICE_SMALL],
										CorrectedUR: 0.0,
										Correction: 0.0,
										LumpsumPrice: 0.0,
										Factor: 1.0,
										ItemTypeStandOpt: null,
										ItemTypeBaseAlt: null,
										FinalPrice: 0.0,
										CorrectedURGross: 0.0,
										OutlineSpecification: {
											Description: data[app.GridCells.BRIEF_INFO],
											OtherLanguages: [],
										},
									},
							  ]),
					],
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(response.body);
			});
	}

	public createEstimateHeader(projectId: string): Cypress.Chainable<Cypress.Response<any>> {
		let estimateCounter = Cypress.env('estimateCounter') || 0; // Start from 0 if not defined
		estimateCounter += 1;
		Cypress.env('estimateCounter', estimateCounter); // Update counter in env globally

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/estimate/publicapi/estimate/header/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {
					Code: getCode(),
					PrjProjectFk: projectId,
					IsActive: true,
					Description: _common.generateRandomString(5),
					EstTypeId: 2,
					RubricCategoryId: 443,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);

				Cypress.env(`API_EST_ID_${estimateCounter}`, response.body.Id);
				Cypress.env(`API_EST_CODE_${estimateCounter}`, response.body.Code);
				Cypress.env(`API_EST_DESCRIPTION_${estimateCounter}`, response.body.Description);

				cy.log(`API_EST_ID_${estimateCounter}: ${response.body.Id}`);
				cy.log(`API_EST_CODE_${estimateCounter}: ${response.body.Code}`);
				cy.log(`API_EST_DESCRIPTION_${estimateCounter}: ${response.body.Description}`);
			});
	}

	public createEstimateLineItems(EST_HEADER_ID, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let lineItemCounter = Cypress.env('lineItemCounter') || 0; // Start from 0 if not defined
		lineItemCounter += 1;
		Cypress.env('lineItemCounter', lineItemCounter); // Update counter in env globally
		const body: any = {
			EstHeaderId: EST_HEADER_ID,
			Code: getCode(),
		};
		if (data[app.GridCells.DESCRIPTION_INFO]) {
			body.Description = data[app.GridCells.DESCRIPTION_INFO];
		}
		if (data[app.GridCells.QUANTITY_SMALL]) {
			body.Quantity = data[app.GridCells.QUANTITY_SMALL];
		}
		if (data[app.GridCells.BAS_UOM_FK]) {
			body.BasUomId = data[app.GridCells.BAS_UOM_FK];
		}
		if (data[app.GridCells.QUANTITY_TARGET]) {
			body.QuantityTarget = data[app.GridCells.QUANTITY_TARGET];
		}
		if (data[app.GridCells.WQ_QUANTITY_TARGET]) {
			body.WbsQuantity = data[app.GridCells.WQ_QUANTITY_TARGET];
		}
		if (data[app.GridCells.ACTIVITY_FK]) {
			body.PsdActivityId = data[app.GridCells.ACTIVITY_FK];
		}
		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/estimate/publicapi/estimate/lineitem/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);

				Cypress.env(`API_LINE_ITEM_ID_${lineItemCounter}`, response.body.Id);
				Cypress.env(`API_LINE_ITEM_CODE_${lineItemCounter}`, response.body.Code);
				Cypress.env(`API_LINE_ITEM_DESCRIPTION_${lineItemCounter}`, response.body.Description);

				cy.log(`API_LINE_ITEM_ID_${lineItemCounter}: ${response.body.Id}`);
				cy.log(`API_LINE_ITEM_CODE_${lineItemCounter}: ${response.body.Code}`);
				cy.log(`API_LINE_ITEM_DESCRIPTION_${lineItemCounter}: ${response.body.Description}`);
			});
	}

	public setCostGroupCodeToLineItem(EstID:any,LineID:any): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/estimate/main/lineitem/update',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json, text/plain, */*',
					'Accept-Encoding': 'gzip, deflate, br',
					Connection: 'keep-alive',
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {
					CostGroupCatFk: 1000026,
					CostGroupFk: 1004148,
					RootItemId: EstID,
					MainItemId: LineID,
				},
			})
			.then((response) => {
				expect(response.status).to.equal(200);

			});
	}

	public getBoQHeaderList(projectId: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + `services/boq/project/list?projectId=${projectId}&filterBackups=false`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const boqIds = response.body.map((item) => item.Boq.BoqHeaderFk);
				Cypress.env('API_BOQ_HEADER_ID', boqIds);
				cy.log(`Boq Ids are: ${boqIds}`);
			});
	}

	public generateBOQFromLeadingStructure(estimateId: string, boqHeaderId: string, projectId: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/estimate/main/lineitem/generatefromleadingstructure',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {
					StructureName: apiConstants.CONSTANT.BOQ,
					RootItemId: boqHeaderId,
					CreateOnlyNewLineItem: true,
					EstHeaderFk: estimateId,
					ProjectFk: projectId,
					CopyLeadingStructrueDesc: true,
					EstQtyTelAotFk: 3,
					CopyControllingUnit: true,
					CopyProcStructure: true,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
			});
	}

	public generateActivityScheduleFromLeadingStructure(estimateId: string, activityId: string, projectId: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/estimate/main/lineitem/generatefromleadingstructure',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {
					StructureName: apiConstants.CONSTANT.SCHEDULE,
					RootItemId: activityId,
					CreateOnlyNewLineItem: true,
					EstHeaderFk: estimateId,
					ProjectFk: projectId,
					CopyLeadingStructrueDesc: true,
					EstQtyTelAotFk: 3,
					CopyControllingUnit: true,
					CopyProcStructure: true,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
			});
	}

	public createControllingUnit(projectId: string, noOfItems: number, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let controllingUnitCounter = Cypress.env('controllingUnitCounter') || 0; // Start from 0 if not defined
		Cypress.env('controllingUnitCounter', controllingUnitCounter); // Update counter in env globally

		const body: any = {
			Code: _common.generateRandomString(2),
			Description: _common.generateRandomString(2),
			ProjectId: projectId,
			CompanyId: apiConstants.ID.COMPANY_999,
			ContextId: apiConstants.ID.CONTEXT,
		};

		if (data[app.GridCells.BAS_UOM_FK]?.[0]) {
			body.UomId = data[app.GridCells.BAS_UOM_FK]?.[0];
		}

		if (data[app.GridCells.QUANTITY_SMALL]?.[0]) {
			body.Quantity = data[app.GridCells.QUANTITY_SMALL]?.[0];
		}

		if (data[app.GridCells.IS_BILLING_ELEMENT]?.[0]) {
			body.IsBillingElement = data[app.GridCells.IS_BILLING_ELEMENT]?.[0];
		}

		if (data[app.GridCells.ISA_ACCOUNTING_ELEMENT]?.[0]) {
			body.IsAccountingElement = data[app.GridCells.ISA_ACCOUNTING_ELEMENT]?.[0];
		}

		if (data[app.GridCells.IS_PLANNING_ELEMENT]?.[0]) {
			body.IsPlanningElement = data[app.GridCells.IS_PLANNING_ELEMENT]?.[0];
		}

		if (data[app.GridCells.IS_STOCK_MANAGEMENT]?.[0]) {
			body.IsStockManagement = data[app.GridCells.IS_STOCK_MANAGEMENT]?.[0];
		}

		if (data[app.GridCells.IS_ASSET_MANAGEMENT]?.[0]) {
			body.IsAssetManagement = data[app.GridCells.IS_ASSET_MANAGEMENT]?.[0];
		}

		if (data[app.GridCells.IS_PLANTMANAGEMENT]?.[0]) {
			body.IsPlantManagement = data[app.GridCells.IS_PLANTMANAGEMENT]?.[0];
		}

		if (data[app.GridCells.BUDGET]?.[0]) {
			body.Budget = data[app.GridCells.BUDGET]?.[0];
		}

		if (data[app.GridCells.IS_FIXED_BUDGET]?.[0]) {
			body.IsFixedBudget = data[app.GridCells.IS_FIXED_BUDGET]?.[0];
		}

		if (data[app.GridCells.IS_DEFAULT]?.[0]) {
			body.IsDefault = data[app.GridCells.IS_DEFAULT]?.[0];
		}

		if (data[app.GridCells.IS_INTER_COMPANY]?.[0]) {
			body.IsIntercompany = data[app.GridCells.IS_INTER_COMPANY]?.[0];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/controlling/publicapi/controllingunit/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_CNT_ID`, response.body.Id);

				const controllingUnitId = response.body.Id;

				for (let index = 1; index < noOfItems; index++) {
					const body: any = {
						Code: getCode(),
						Description: _common.generateRandomString(2),
						ProjectId: projectId,
						CompanyId: apiConstants.ID.COMPANY_999,
						ContextId: apiConstants.ID.CONTEXT,
					};

					if (data[app.GridCells.BAS_UOM_FK]?.[index]) {
						body.UomId = data[app.GridCells.BAS_UOM_FK]?.[index];
					}

					if (controllingUnitId !== null) {
						body.ControllingUnitId = controllingUnitId;
					}

					if (data[app.GridCells.QUANTITY_SMALL]?.[index]) {
						body.Quantity = data[app.GridCells.QUANTITY_SMALL]?.[index];
					}

					if (data[app.GridCells.IS_BILLING_ELEMENT]?.[index]) {
						body.IsBillingElement = data[app.GridCells.IS_BILLING_ELEMENT]?.[index];
					}

					if (data[app.GridCells.ISA_ACCOUNTING_ELEMENT]?.[index]) {
						body.IsAccountingElement = data[app.GridCells.ISA_ACCOUNTING_ELEMENT]?.[index];
					}

					if (data[app.GridCells.IS_PLANNING_ELEMENT]?.[index]) {
						body.IsPlanningElement = data[app.GridCells.IS_PLANNING_ELEMENT]?.[index];
					}

					if (data[app.GridCells.IS_STOCK_MANAGEMENT]?.[index]) {
						body.IsStockManagement = data[app.GridCells.IS_STOCK_MANAGEMENT]?.[index];
					}

					if (data[app.GridCells.IS_ASSET_MANAGEMENT]?.[index]) {
						body.IsAssetManagement = data[app.GridCells.IS_ASSET_MANAGEMENT]?.[index];
					}

					if (data[app.GridCells.IS_PLANTMANAGEMENT]?.[index]) {
						body.IsPlantManagement = data[app.GridCells.IS_PLANTMANAGEMENT]?.[index];
					}

					if (data[app.GridCells.BUDGET]?.[index]) {
						body.Budget = data[app.GridCells.BUDGET]?.[index];
					}

					if (data[app.GridCells.IS_FIXED_BUDGET]?.[index]) {
						body.IsFixedBudget = data[app.GridCells.IS_FIXED_BUDGET]?.[index];
					}

					if (data[app.GridCells.IS_DEFAULT]?.[index]) {
						body.IsDefault = data[app.GridCells.IS_DEFAULT]?.[index];
					}

					if (data[app.GridCells.IS_INTER_COMPANY]?.[index]) {
						body.IsIntercompany = data[app.GridCells.IS_INTER_COMPANY]?.[index];
					}

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + 'services/controlling/publicapi/controllingunit/2.0',
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						Cypress.env(`API_CNT_ID_${controllingUnitCounter}`, response.body.Id);
						Cypress.env(`API_CNT_DESCRIPTION_${controllingUnitCounter}`, response.body.Description);
						Cypress.env(`API_CNT_CODE_${controllingUnitCounter}`, response.body.Code);
						cy.log(`API_CNT_ID_${controllingUnitCounter} : ${response.body.Id}`);
						cy.log(`API_CNT_DESCRIPTION_${controllingUnitCounter} : ${response.body.ControllingUnitDesc}`);
						cy.log(`API_CNT_CODE_${controllingUnitCounter} : ${response.body.Code}`);

						controllingUnitCounter += 1;
						Cypress.env('controllingUnitCounter', controllingUnitCounter);
					});
				}
			});
	}

	public createSchedulingAndSchedulingActivity(projectId: string, noOfItems: number, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		const body: any = {
			Code: getCode(),
			Description: _common.generateRandomString(5),
			ProjectId: projectId,
			CompanyId: apiConstants.ID.COMPANY_999,
			IsLive: true,
			IsActive: true,
		};

		if (data[app.GridCells.BAS_UOM_FK]?.[0]) {
			body.QuantityUoMId = data[app.GridCells.BAS_UOM_FK]?.[0];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/scheduling/publicapi/schedule/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_SCH_ACT_ID`, response.body.Id);
				Cypress.env(`API_SCH_DESC`, response.body.Description);

				cy.log(`API_SCH_ACT_ID ${response.body.Id}`);
				cy.log(`API_SCH_DESC ${response.body.Description}`);

				const schedulingId = response.body.Id;

				for (let index = 1; index < noOfItems; index++) {
					const body: any = {
						Code: getCode(),
						Description: _common.generateRandomString(5),
						ProjectId: projectId,
						CompanyId: apiConstants.ID.COMPANY_999,
					};

					if (schedulingId !== null) {
						body.ScheduleId = schedulingId;

						if (data[app.GridCells.BAS_UOM_FK]?.[index]) {
							body.QuantityUoMId = data[app.GridCells.BAS_UOM_FK]?.[index];
						}

						if (data[app.GridCells.QUANTITY_SMALL]?.[index]) {
							body.Quantity = data[app.GridCells.QUANTITY_SMALL]?.[index];
						}

						if (data[app.GridCells.PLANNED_START]?.[index]) {
							body.PlannedStart = data[app.GridCells.PLANNED_START]?.[index];
						}

						if (data[app.GridCells.PLANNED_END]?.[index]) {
							body.PlannedFinish = data[app.GridCells.PLANNED_END]?.[index];
						}

						if (data[app.GridCells.PROGRESS_REPORT_METHOD_FK]?.[index]) {
							body.ProgressReportMethodId = data[app.GridCells.PROGRESS_REPORT_METHOD_FK]?.[index];
						}

						
					}

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + 'services/scheduling/publicapi/activity/2.0',
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						Cypress.env(`API_SCH_ACT_ID_${index}`, response.body.Id);
						Cypress.env(`API_SCH_ACT_DESCRIPTION_${index}`, response.body.Description);
						Cypress.env(`API_SCH_ACT_CODE_${index}`, response.body.Code);

						cy.log(`API_SCH_ACT_ID_${index} ${response.body.Id}`);
						cy.log(`API_SCH_ACT_DESCRIPTION_${index} ${response.body.Description}`);
						cy.log(`API_SCH_ACT_CODE_${index} ${response.body.Code}`);

					});
				}
			});
	}

	public createMaterialCatalog(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let materialCatalogCounter = Cypress.env('materialCatalogCounter') || 0; // Start from 0 if not defined
		materialCatalogCounter += 1;
		Cypress.env('materialCatalogCounter', materialCatalogCounter); // Update counter in env globally

		const body: any = {
			Code: getCode(),
			Description: _common.generateRandomString(5),
			CompanyId: apiConstants.ID.COMPANY_999,
			IsLive: true,
			IsActive: true,
		};

		if (data[app.GridCells.VALID_FROM]) {
			body.ValidFrom = data[app.GridCells.VALID_FROM];
		}

		if (data[app.GridCells.VALID_TO]) {
			body.ValidTo = data[app.GridCells.VALID_TO];
		}

		if (data[app.GridCells.BUSINESS_PARTNER_FK]) {
			body.BpdBusinessPartnerId = data[app.GridCells.BUSINESS_PARTNER_FK];
		}

		if (data[app.GridCells.PAYMENT_TERM_FI_FK]) {
			body.BasPaymentTermFiId = data[app.GridCells.PAYMENT_TERM_FI_FK];
		}

		if (data[app.GridCells.PAYMENT_TERM_AD_FK]) {
			body.BasPaymentTermAdId = data[app.GridCells.PAYMENT_TERM_AD_FK];
		}

		if (data[app.GridCells.BAS_PAYMENT_TERM_ID]) {
			body.BasPaymentTermId = data[app.GridCells.BAS_PAYMENT_TERM_ID];
		}

		if (data[app.GridCells.BAS_RUBRIC_CATEGORY_FK]) {
			body.BasRubricCategoryId = data[app.GridCells.BAS_RUBRIC_CATEGORY_FK];
		}

		if (data[app.GridCells.IS_NEUTRAL]) {
			body.IsNeutral = data[app.GridCells.IS_NEUTRAL];
		}

		if (data[app.GridCells.IS_TICKET_SYSTEM]) {
			body.IsTicketSystem = data[app.GridCells.IS_TICKET_SYSTEM];
		}

		if (data[app.GridCells.IS_INTERNET_CATALOG]) {
			body.IsInternetCatalog = data[app.GridCells.IS_INTERNET_CATALOG];
		}

		if (data[app.GridCells.CONTEXT_FK]) {
			body.MdcContextId = data[app.GridCells.CONTEXT_FK];
		}

		if (data[app.GridCells.SUBSIDIARY_FK]) {
			body.BpdSubsidiaryId = data[app.GridCells.SUBSIDIARY_FK];
		}

		if (data[app.GridCells.SUPPLIER_FK]) {
			body.BpdSupplierId = data[app.GridCells.SUPPLIER_FK];
		}

		if (data[app.GridCells.MATERIAL_CATALOG_TYPE_FK]) {
			body.MdcMaterialCatalogTypeId = data[app.GridCells.MATERIAL_CATALOG_TYPE_FK];
		}

		if (data[app.GridCells.CLERK_FK]) {
			body.BasClerkId = data[app.GridCells.CLERK_FK];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/material/catalog/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_MATERIAL_CATALOG_ID_${materialCatalogCounter}`, response.body.Id);
				Cypress.env(`API_MATERIAL_CATALOG_CODE_${materialCatalogCounter}`, response.body.Code);

				cy.log(`API_MATERIAL_CATALOG_ID_${materialCatalogCounter}:  ${response.body.Id}`);
				cy.log(`API_MATERIAL_CATALOG_CODE_${materialCatalogCounter}: ${response.body.Code} `);
			});
	}

	public createMaterialGroups(materialCatalogId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let materialGroupCounter = Cypress.env('materialGroupCounter') || 0; // Start from 0 if not defined
		materialGroupCounter += 1;
		Cypress.env('materialGroupCounter', materialGroupCounter); // Update counter in env globally
		const body: any = {
			Code: getCode(),
			Description: _common.generateRandomString(5),
			MdcMaterialCatalogId: materialCatalogId,
		};

		if (data[app.GridCells.STRUCTURE_FK]) {
			body.PrcStructureId = data[app.GridCells.STRUCTURE_FK];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/material/group/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);

				Cypress.env(`API_MATERIAL_GROUP_ID_${materialGroupCounter}`, response.body.Id);
				Cypress.env(`API_MATERIAL_GROUP_CODE_${materialGroupCounter}`, response.body.Code);

				cy.log(`API_MATERIAL_GROUP_ID_${materialGroupCounter}:  ${response.body.Id}`);
				cy.log(`API_MATERIAL_GROUP_CODE_${materialGroupCounter}: ${response.body.Code} `);
			});
	}

	public createMaterialRecord(materialCatalogId: string, materialGroupId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let materialRecordCounter = Cypress.env('materialRecordCounter') || 0; // Start from 0 if not defined
		materialRecordCounter += 1;
		Cypress.env('materialRecordCounter', materialRecordCounter); // Update counter in env globally
		const body: any = {
			Code: getCode(),
			Description1: _common.generateRandomString(5),
			BasCurrencyId: apiConstants.ID.CURRENCY_EUR,
			MdcMaterialGroupId: materialGroupId,
			MaterialCatalogId: materialCatalogId,
		};

		if (data[app.GridCells.UOM_FK]) {
			body.BasUomId = data[app.GridCells.UOM_FK];
		}

		if (data[app.GridCells.RETAIL_PRICE]) {
			body.RetailPrice = data[app.GridCells.RETAIL_PRICE];
		}

		if (data[app.GridCells.LIST_PRICE]) {
			body.ListPrice = data[app.GridCells.LIST_PRICE];
		}

		if (data[app.GridCells.ESTIMATE_PRICE]) {
			body.EstimatePrice = data[app.GridCells.ESTIMATE_PRICE];
		}

		if (data[app.GridCells.MIN_QUANTITY]) {
			body.MinQuantity = data[app.GridCells.MIN_QUANTITY];
		}

		if (data[app.GridCells.MATERIAL_TYPE_FK]) {
			body.MaterialTypeId = data[app.GridCells.MATERIAL_TYPE_FK];
		}

		if (data[app.GridCells.CO2_PROJECT]) {
			body.Co2Project = data[app.GridCells.CO2_PROJECT];
		}

		if (data[app.GridCells.NEUTRAL_MATERIAL_CATALOG_FK]) {
			body.NeutralMaterialCatalogFk = data[app.GridCells.NEUTRAL_MATERIAL_CATALOG_FK];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/material/5.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);

				Cypress.env(`API_MATERIAL_RECORD_ID_${materialRecordCounter}`, response.body.Id);
				Cypress.env(`API_MATERIAL_RECORD_CODE_${materialRecordCounter}`, response.body.Code);
				Cypress.env(`API_MATERIAL_RECORD_DESC_${materialRecordCounter}`, response.body.Description1);

				cy.log(`API_MATERIAL_RECORD_ID_${materialRecordCounter}:  ${response.body.Id}`);
				cy.log(`API_MATERIAL_RECORD_CODE_${materialRecordCounter}: ${response.body.Code} `);
				cy.log(`API_MATERIAL_RECORD_DESC_${materialRecordCounter}: ${response.body.Description1} `);
			});
	}

	public createBusinessPartner(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let addressCounter = Cypress.env('addressCounter') || 0; // Start from 0 if not defined
		addressCounter += 1;
		Cypress.env('addressCounter', addressCounter); // Update counter in env globally

		let bpCounter = Cypress.env('bpCounter') || 0; // Start from 0 if not defined
		bpCounter += 1;
		Cypress.env('bpCounter', bpCounter); // Update counter in env globally

		const body: any = {};

		if (data[commonLocators.CommonLabels.COUNTRY]) {
			body.CountryId = data[commonLocators.CommonLabels.COUNTRY];
		}

		if (data[commonLocators.CommonLabels.STREET]) {
			body.Street = data[commonLocators.CommonLabels.STREET];
		}

		if (data[commonLocators.CommonLabels.CITY]) {
			body.City = data[commonLocators.CommonLabels.CITY];
		}

		if (data[commonLocators.CommonLabels.ZIP_CODE]) {
			body.Zipcode = data[commonLocators.CommonLabels.ZIP_CODE];
		}

		if (data[commonLocators.CommonLabels.COUNTY]) {
			body.County = data[commonLocators.CommonLabels.COUNTY];
		}

		if (data[commonLocators.CommonLabels.ADDRESS]) {
			body.Address = data[commonLocators.CommonLabels.ADDRESS];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/address/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_ADDRESS_ID_${addressCounter}`, response.body.Id);
				cy.log(`API_ADDRESS_ID_${addressCounter} : ${response.body.Id}`);

				const addressId = response.body.Id;

				const body: any = {
					Code: getCode(),
					CompanyId: apiConstants.ID.COMPANY_999,
					IsLive: true,
					BusinessPartnerName1: _common.generateRandomString(5),
				};

				if (addressId !== null) {
					body.SubsidiaryAddressId = addressId;

					if (data[app.GridCells.CLERK_FK]) {
						body.ClerkId = data[app.GridCells.CLERK_FK];
					}
					if (data[apiParameters.Keys.BUSINESS_PARTNER_NAME_1]) {
						body.BusinessPartnerName1 = data[apiParameters.Keys.BUSINESS_PARTNER_NAME_1];
					}
				}

				cy.request({
					method: 'POST',
					url: Cypress.env('Base_URL') + 'services/businesspartner/publicapi/businesspartner/3.0',
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
					Cypress.env(`API_BP_ID_${bpCounter}`, response.body.Id);
					Cypress.env(`API_BP_NAME_${bpCounter}`, response.body.BusinessPartnerName1);
					Cypress.env(`API_BP_CODE_${bpCounter}`, response.body.Code);

					cy.log(`API_BP_ID_${bpCounter} : ${response.body.Id}`);
					cy.log(`API_BP_NAME_${bpCounter} : ${response.body.BusinessPartnerName1}`);
					cy.log(`API_BP_CODE_${bpCounter} : ${response.body.Code}`);
				});
			});
	}

	public createBranches(businessPartnerId: string, addressId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let branchesCounter = Cypress.env('branchesCounter') || 0; // Start from 0 if not defined
		branchesCounter += 1;
		Cypress.env('branchesCounter', branchesCounter); // Update counter in env globally

		const body: any = {
			BusinessPartnerId: businessPartnerId,
			AddressId: addressId,
			Description: _common.generateRandomString(4),
		};

		if (data[app.GridCells.ADDRESS_TYPE_FK]) {
			body.AddressTypeId = data[app.GridCells.ADDRESS_TYPE_FK];
		}

		if (data[app.GridCells.IS_MAIN_ADDRESS]) {
			body.IsMainAddress = data[app.GridCells.IS_MAIN_ADDRESS];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/businesspartner/publicapi/subsidiary/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_BRANCHES_ID_${branchesCounter}`, response.body.Id);
				cy.log(`API_BRANCHES_ID_${branchesCounter}:  ${response.body.Id}`);
				cy.log(`API_BRANCHES_Description_${branchesCounter}:  ${response.body.Description}`);
			});
	}

	public createBank(businessPartnerId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let bankCounter = Cypress.env('bankCounter') || 0; // Start from 0 if not defined
		bankCounter += 1;
		Cypress.env('bankCounter', bankCounter); // Update counter in env globally

		const body: any = {
			BusinessPartnerId: businessPartnerId,
			CompanyId: apiConstants.ID.COMPANY_999,
		};

		if (data[app.GridCells.BANK_TYPE_FK]) {
			body.BankTypeId = data[app.GridCells.BANK_TYPE_FK];
		}

		if (data[app.GridCells.IBAN]) {
			body.Iban = data[app.GridCells.IBAN];
		}

		if (data[app.GridCells.ACCOUNT_NO]) {
			body.AccountNo = data[app.GridCells.ACCOUNT_NO];
		}

		if (data[app.GridCells.IS_DEFAULT]) {
			body.IsDefault = data[app.GridCells.IS_DEFAULT];
		}

		if (data[app.GridCells.IS_LIVE]) {
			body.IsLive = data[app.GridCells.IS_LIVE];
		}

		if (data[app.GridCells.IS_DEFAULT_BASELINE]) {
			body.IsDefaultCustomer = data[app.GridCells.IS_DEFAULT_BASELINE];
		}

		if (data[app.GridCells.BANK_FK]) {
			body.BankId = data[app.GridCells.BANK_FK];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/businesspartner/publicapi/bank/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_BANK_ID_${bankCounter}`, response.body.Id);
				cy.log(`API_BANK_ID_${bankCounter}:  ${response.body.Id}`);
			});
	}

	public createSuppliers(businessPartnerId:string,data?:DataCells):Cypress.Chainable<Cypress.Response<any>>{
		let suppliersCounter = Cypress.env('suppliersCounter') || 0; // Start from 0 if not defined
		suppliersCounter += 1;
		Cypress.env('suppliersCounter', suppliersCounter); // Update counter in env globally

		const body: any = {
			BusinessPartnerId: businessPartnerId,
			CompanyId: apiConstants.ID.COMPANY_999,
			Code: getCode(),
			Description: _common.generateRandomString(5),
		};

		if (data[app.GridCells.PAYMENT_TERM_PA_FK]) {
			body.PaymentTermPaId = data[app.GridCells.PAYMENT_TERM_PA_FK];
		}

		if (data[app.GridCells.PAYMENT_TERM_FI_FK]) {
			body.PaymentTermFiId = data[app.GridCells.PAYMENT_TERM_FI_FK];
		}

		if (data[app.GridCells.VAT_GROUP_FK]) {
			body.VatgroupId = data[app.GridCells.VAT_GROUP_FK];
		}

		if (data[app.GridCells.SUBSIDIARY_FK]) {
			body.SubsidiaryId = data[app.GridCells.SUBSIDIARY_FK];
		}

		if (data[app.GridCells.BANK_FK]) {
			body.BankId = data[app.GridCells.BANK_FK];
		}

		if (data[app.GridCells.PAYMENT_TERM_FK]) {
			body.PaymentMethodId = data[app.GridCells.PAYMENT_TERM_FK];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/businesspartner/publicapi/supplier/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_SUPPLIER_ID_${suppliersCounter}`, response.body.Id);
				Cypress.env(`API_SUPPLIER_CODE_${suppliersCounter}`, response.body.Code);

				cy.log(`API_SUPPLIER_ID_${suppliersCounter}:  ${response.body.Id}`);
				cy.log(`API_SUPPLIER_CODE_${suppliersCounter}:  ${response.body.Code}`);
			});
	}

	public createCustomer(businessPartnerId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let customerCounter = Cypress.env('customerCounter') || 0; // Start from 0 if not defined
		customerCounter += 1;
		Cypress.env('customerCounter', customerCounter); // Update counter in env globally

		const body: any = {
			BusinessPartnerId: businessPartnerId,
			SupplierNo: _common.generateRandomString(4),
		};

		if (data[app.GridCells.CUSTOMER_STATUS_ID]) {
			body.CustomerStatusId = data[app.GridCells.CUSTOMER_STATUS_ID];
		}

		if (data[app.GridCells.PAYMENT_TERM_PA_FK]) {
			body.PaymentTermPaId = data[app.GridCells.PAYMENT_TERM_PA_FK];
		}

		if (data[app.GridCells.PAYMENT_TERM_FI_FK]) {
			body.PaymentTermFiId = data[app.GridCells.PAYMENT_TERM_FI_FK];
		}

		if (data[app.GridCells.CUSTOMER_LEDGER_GROUP_ID]) {
			body.CustomerLedgerGroupId = data[app.GridCells.CUSTOMER_LEDGER_GROUP_ID];
		}

		if (data[app.GridCells.BUSINESS_UNIT_ID]) {
			body.BusinessUnitId = data[app.GridCells.BUSINESS_UNIT_ID];
		}

		if (data[app.GridCells.CUSTOMER_BRANCH_ID]) {
			body.CustomerBranchId = data[app.GridCells.CUSTOMER_BRANCH_ID];
		}

		if (data[app.GridCells.VAT_GROUP_FK]) {
			body.VatGroupId = data[app.GridCells.VAT_GROUP_FK];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/businesspartner/publicapi/customer/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_CUSTOMER_SUPPLIER_NO_${customerCounter}`, response.body.SupplierNo);
				Cypress.env(`API_CUSTOMER_CODE_${customerCounter}`, response.body.Code);

				cy.log(`API_CUSTOMER_SUPPLIER_NO_${customerCounter}:  ${response.body.SupplierNo}`);
				cy.log(`API_CUSTOMER_CODE_${customerCounter}:  ${response.body.Code}`);
			});
	}

	public createProcurementStructure(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let procurementStructureCounter = Cypress.env('procurementStructureCounter') || 0; // Start from 0 if not defined
		procurementStructureCounter += 1;
		Cypress.env('procurementStructureCounter', procurementStructureCounter); // Update counter in env globally

		const body: any = {
			Code: getCode(),
			Description: _common.generateRandomString(5),
			IsLive: true,
		};

		if (data[app.GridCells.PRC_CONFIGURATION_FK]) {
			body.PrcConfigheaderId = data[app.GridCells.PRC_CONFIGURATION_FK];
		}

		if (data[app.GridCells.PRC_STRUCTURE_FK]) {
			body.PrcStructureTypeId = data[app.GridCells.PRC_STRUCTURE_FK];
		}

		if (data[app.GridCells.CLERK_RESPONSIBLE_FK]) {
			body.BasClerkRoleReqId = data[app.GridCells.CLERK_RESPONSIBLE_FK];
		}

		if (data[app.GridCells.CLERK_PRC_FK]) {
			body.BasClerkRolePrcId = data[app.GridCells.CLERK_PRC_FK];
		}

		if (data[app.GridCells.ALLOW_ASSIGNMENT]) {
			body.AllowAssignment = data[app.GridCells.ALLOW_ASSIGNMENT];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/procurementstructure/4.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_PRC_STRUCTURE_ID_${procurementStructureCounter}`, response.body.Id);
				Cypress.env(`API_PRC_STRUCTURE_CODE_${procurementStructureCounter}`, response.body.Code);
				Cypress.env(`API_PRC_STRUCTURE_DESCRIPTION_${procurementStructureCounter}`, response.body.Description);

				cy.log(`API_PRC_STRUCTURE_ID_${procurementStructureCounter}:  ${response.body.Id}`);
				cy.log(`API_PRC_STRUCTURE_CODE_${procurementStructureCounter}:  ${response.body.Code}`);
				cy.log(`API_PRC_STRUCTURE_DESCRIPTION_${procurementStructureCounter}:  ${response.body.Description}`);
			});
	}

	public createProjectStockLocation(projectId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let projectStockLocationCounter = Cypress.env('projectStockLocationCounter') || 0; // Start from 0 if not defined
		projectStockLocationCounter += 1;
		Cypress.env('projectStockLocationCounter', projectStockLocationCounter); // Update counter in env globally

		const body: any = {
			Code: getCode(),
			Description: _common.generateRandomString(5),
			ProjectId: projectId,
			IsDefault: true,
		};

		if (data[app.GridCells.ADDRESS]?.[0]) {
			body.AddressId = data[app.GridCells.ADDRESS]?.[0];
		}

		if (data[app.GridCells.IS_PROVISION_ALLOWED]?.[0]) {
			body.IsProvisionAllowed = data[app.GridCells.IS_PROVISION_ALLOWED]?.[0];
		}

		if (data[app.GridCells.IS_LOCATION_MANDATORY]?.[0]) {
			body.IsLocationMandatory = data[app.GridCells.IS_LOCATION_MANDATORY]?.[0];
		}

		if (data[app.GridCells.STOCK_FK]?.[0]) {
			body.StockTypeId = data[app.GridCells.STOCK_FK]?.[0];
		}

		if (data[app.GridCells.CURRENCY]?.[0]) {
			body.CurrencyId = data[app.GridCells.CURRENCY]?.[0];
		}

		if (data[app.GridCells.CLERK_FK]?.[0]) {
			body.ClerkId = data[app.GridCells.CLERK_FK]?.[0];
		}

		if (data[app.GridCells.CONTROLLING_UNIT_FK]?.[0]) {
			body.ControllingUnitId = data[app.GridCells.CONTROLLING_UNIT_FK]?.[0];
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/project/publicapi/project/stock/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_CNT_ID`, response.body.Id);

				Cypress.env(`API_PROJECT_STOCK_LOCATION_ID_${projectStockLocationCounter}`, response.body.Id);
				Cypress.env(`API_PROJECT_STOCK_LOCATION_CODE_${projectStockLocationCounter}`, response.body.Code);
				Cypress.env(`API_PROJECT_STOCK_LOCATION_DESCRIPTION_${projectStockLocationCounter}`, response.body.Description);

				cy.log(`API_PROJECT_STOCK_LOCATION_ID_${projectStockLocationCounter}:  ${response.body.Id}`);
				cy.log(`API_PROJECT_STOCK_LOCATION_CODE_${projectStockLocationCounter}:  ${response.body.Code}`);
				cy.log(`API_PROJECT_STOCK_LOCATION_DESCRIPTION_${projectStockLocationCounter}:  ${response.body.Description}`);
			});
	}

	public updateRequisitionStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/reqstatus/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);
				const { Id, Version } = status;

				const body: any = { Version };

				const assignIfProvided = (fieldName, cellType) => {
					if (data[app.GridCells[cellType]] !== undefined) {
						body[fieldName] = data[app.GridCells[cellType]];
					}
				};

				assignIfProvided('Ispublished', 'IS_PUBLISHED');
				assignIfProvided('Isquoted', 'IS_QUOTED');
				assignIfProvided('Isordered', 'IS_ORDERED');

				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/reqstatus/3.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
				});
			});
	}

	public updatePesStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/pesstatus/4.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) throw new Error('Status description not found');

				const { Id, Version } = status;

				// Set initial default values from the status data
				const body: any = { Version };

				const assignIfProvided = (fieldName, cellType) => {
					if (data[app.GridCells[cellType]] !== undefined) {
						body[fieldName] = data[app.GridCells[cellType]];
					}
				};

				// Update only if specific fields are provided in data
				assignIfProvided('IsReadOnly', 'IS_READONLY');
				assignIfProvided('Iscanceled', 'IS_CANCELED');
				assignIfProvided('Isprotected', 'IS_PROTECTED');
				assignIfProvided('Isstock', 'IS_STOCK');
				assignIfProvided('Isdelivered', 'IS_DELIVERED');
				assignIfProvided('Isvirtual', 'IS_VIRTUAL');
				assignIfProvided('Isinvoiced', 'IS_INVOICED');

				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/pesstatus/4.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
				});
			});
	}

	public changeActivityStatus(description, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let versionCounter = Cypress.env('versionCounter') || 0; // Start from 0 if not defined
		versionCounter += 1;
		Cypress.env('versionCounter', versionCounter); // Update counter in env globally

		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/activitystate/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log('GET API request was successful');
				const responseData = response.body;
				cy.log(JSON.stringify(responseData));
				let selectedValue = null;
				const targetDescription = description;

				for (let i = 0; i < responseData.length; i++) {
					if (response.body[i].Description === targetDescription) {
						selectedValue = responseData[i];
						break;
					}
				}
				if (selectedValue) {
					const body: any = {
						Id: selectedValue.Id,
						Description: selectedValue.Description,
						LanguageId: selectedValue.LanguageId,
						Version: selectedValue.Version,
					};

					if (data[app.GridCells.IS_LIVE]) {
						body.IsLive = data[app.GridCells.IS_LIVE];
					}

					if (data[app.GridCells.IS_AUTOMATIC]) {
						body.Isautomatic = data[app.GridCells.IS_AUTOMATIC];
					}

					if (data[app.GridCells.IS_STARTED]) {
						body.Isstarted = data[app.GridCells.IS_STARTED];
					}

					if (data[app.GridCells.IS_DELAYED]) {
						body.Isdelayed = data[app.GridCells.IS_DELAYED];
					}

					if (data[app.GridCells.IS_AHEAD]) {
						body.Isahead = data[app.GridCells.IS_AHEAD];
					}

					if (data[app.GridCells.IS_FINISHED]) {
						body.Isfinished = data[app.GridCells.IS_FINISHED];
					}

					if (data[app.GridCells.IS_FINISHED_DELAYED]) {
						body.Isfinisheddelayed = data[app.GridCells.IS_FINISHED_DELAYED];
					}

					if (data[app.GridCells.SORTING]) {
						body.Sorting = data[app.GridCells.SORTING];
					}

					if (data[app.GridCells.IS_DEFAULT]) {
						body.IsDefault = data[app.GridCells.IS_DEFAULT];
					}

					if (data[app.GridCells.IS_PLANNING_FINISHED]) {
						body.Isplanningfinished = data[app.GridCells.IS_PLANNING_FINISHED];
					}

					if (data[app.GridCells.IS_READONLY]) {
						body.IsReadOnly = data[app.GridCells.IS_READONLY];
					}

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/activitystate/3.0',
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						cy.log('PATCH API request was successful');
					});
				} else {
					cy.log('Description not found.');
				}
			});
	}

	public updateConstraintType(description, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let versionCounter = Cypress.env('versionCounter') || 0;
		versionCounter += 1;
		Cypress.env('versionCounter', versionCounter);

		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/constrainttype/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log('GET API request was successful');
				const responseData = response.body;
				cy.log(JSON.stringify(responseData));
				let selectedConstraint = null;
				const targetDescription = description;

				for (let i = 0; i < responseData.length; i++) {
					if (response.body[i].Description === targetDescription) {
						selectedConstraint = responseData[i];
						break;
					}
				}
				if (selectedConstraint) {
					const body: any = {
						Id: selectedConstraint.Id,
						Version: selectedConstraint.Version,
					};
					if (data[app.GridCells.IS_DEFAULT]) {
						body.IsDefault = data[app.GridCells.IS_DEFAULT];
					}
					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/constrainttype/1.0',
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.eq(200);
						cy.log('PATCH API request was successful');
					});
				} else {
					cy.log('Constraint with the given description not found.');
				}
			});
	}

	public updatePackageStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/packagestatus/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) throw new Error('Status description not found');

				const { Id, Version } = status;

				// Set initial default values from the status data
				const body: any = { Version };

				const assignIfProvided = (fieldName, cellType) => {
					if (data[app.GridCells[cellType]] !== undefined) {
						body[fieldName] = data[app.GridCells[cellType]];
					}
				};

				// Update only if specific fields are provided in data
				assignIfProvided('IsEstimate', 'IS_ESTIMATE');

				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/packagestatus/3.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				});
	}

	public updatePackageStatusUnderCustomizing_forAllGivenCheckBox(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy.request({
			method: 'GET',
			url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/packagestatus/3.0',
			headers: {
				'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
				Authorization: `${this.tokenType} ${this.token}`,
			},
		}).then((response) => {
			expect(response.status).to.eq(200);
			const statuses = response.body; // assuming response body is an array
	
			statuses.forEach((status: any) => {
				const body: any = {};
	
				const assignIfProvided = (fieldName, cellType) => {
					if (data[apiParameters.Keys[cellType]] !== undefined) {
						body[fieldName] = data[apiParameters.Keys[cellType]];
					}
				};
	
				assignIfProvided("IsEstimate", "IS_ESTIMATE");
	
				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/packagestatus/3.0/${status.Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((patchResponse) => {
					expect(patchResponse.status).to.eq(200);
				});
			});
		});
	}
	
	public createCostCodeTypeUnderCustomizing(statusDescription,data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
	  
		return cy.request({
					method: 'GET',
					url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/costcodetype/2.0',
					headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) {
					const body: any = {};

					const assignIfProvided = (fieldName, cellType) => {
						if (data[app.GridCells[cellType]] !== undefined) {
							body[fieldName] = data[app.GridCells[cellType]];
						}
					};

					// Update only if specific fields are provided in data
					assignIfProvided('Description', 'DESCRIPTION');
					assignIfProvided('IsEstimateCostCode', 'IS_ESTIMATE_CC');
					assignIfProvided('IsRevenueCostCode', 'IS_REVENUE');
					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('Icon', 'ICON');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('IsMounting', 'IS_MOUNTING');
					assignIfProvided('IsAllowance', 'IS_ALLOWANCE');
					assignIfProvided('Isrp', 'IS_RP');
					assignIfProvided('Isga', 'IS_GA');
					assignIfProvided('Isam', 'IS_AM');
					assignIfProvided('IsCommissioning', 'IS_COMMISSIONING');
					assignIfProvided('HasOrder', 'HAS_ORDER');
					assignIfProvided('IsInformation', 'IS_INFORMATION');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/costcodetype/2.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public createEstAssemblyTypeUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/estassemblytype/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) {
					const body: any = {};

					const assignIfProvided = (fieldName, cellType) => {
						if (data[app.GridCells[cellType]] !== undefined) {
							body[fieldName] = data[app.GridCells.SORTING[cellType]];
						}
					};

					// Update only if specific fields are provided in data
					assignIfProvided('Description', 'DESCRIPTION');
					assignIfProvided('AssemblytypeLogicId', 'ASSEMBLY_TYPE_LOGIC_FK');
					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('ShortKey', 'SHORT_KEY_INFO');
					assignIfProvided('Sorting', 'SORTING');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/estassemblytype/1.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public assignUserToClerk(userId: string, userDesc: string, clerkCode: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/clerk/4.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.UserDesc === userDesc);
				const clerk = responseData.find((item) => item.Code === clerkCode);
				if (status) {
					const { Id } = status;
					const body: any = { UserId: null };
					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/publicapi/clerk/4.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				} else {
					const { Id } = clerk;

					const body: any = { UserId: userId };

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/publicapi/clerk/4.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public getULoggedInUserId(userName: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + `services/usermanagement/publicapi/user/3.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: {},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Name === userName);
				const { Id } = status;

				Cypress.env('API_LOGGED_IN_USER_ID', Id);
				cy.log(`API_LOGGED_IN_USER_ID: ${Id}`);
			});
	}

	public updatePaymentTerm(paymentTermCode: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/publicapi/paymentterm/5.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const code = responseData.find((item) => item.Code === paymentTermCode);
				if (code) {
					const { Id } = code;
					const body: any = { Id: Id };

					const assignIfProvided = (fieldName, cellType) => {
						if (data[app.GridCells[cellType]] !== undefined) {
							body[fieldName] = data[app.GridCells[cellType]];
						}
					};

					assignIfProvided('NetDays', 'NET_DAYS');
					assignIfProvided('DiscountDays', 'DISCOUNT_DAYS');
					assignIfProvided('DayOfMonth', 'DAY_OF_MONTH');
					assignIfProvided('Month', 'MONTH');
					assignIfProvided('BasCalculationTypeId', 'CALCULATION_TYPE_FK');
					assignIfProvided('IsDefault', 'IS_DEFAULT');

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/publicapi/paymentterm/5.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						cy.log(JSON.stringify(response.body, null, 2));
						cy.log(`ID : ${response.body.Id}`);
					});
				} else {
					cy.log(`No data for the code provided.`);
				}
			});
	}

	public numberRange_validationSalesContractAPI(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let failOnStatusCode;
		const USERDEFINED1 = _common.generateRandomString(2);
		const USERDEFINED2 = _common.generateRandomString(2);
		const USERDEFINED3 = _common.generateRandomString(2);
		const USERDEFINED4 = _common.generateRandomString(2);
		const USERDEFINED5 = _common.generateRandomString(2);

		const body: any = {
			Id: 1000001,
			RubricCategoryId: 35,
			LanguageId: 1,
			CompanyId: 1000005,
			CompanyResponsibleId: 1000005,
			ProjectId: 1000040,
			OrdStatusId: 3,
			Description: 'Rohbauarbeiten',
			CurrencyId: 1,
			Exchangerate: 1,
			ClerkId: 1000002,
			BusinesspartnerId: 1000006,
			SubsidiaryId: 1000014,
			CustomerId: 1000009,
			BillingSchemaId: 1000003,
			PaymentTermFiId: 9,
			PaymentTermPaId: 3,
			TaxCodeId: 1000055,
			ContractTypeId: 2,
			BidHeaderId: 1000001,
			EstHeaderId: 1000003,
			PrcStructureId: 1000003,
			VatgroupId: 1000001,
			RevisionApplicable: 'false',
			Isdays: 'false',
			Iswarrenty: 'false',
			WarrantyAmount: 0,
			ContactId: 1000037,
			PrcIncotermId: 1,
			AmountNet: 50600,
			AmountNetOc: 50600,
			AmountGross: 60214,
			AmountGrossOc: 60214,
			OrdConditionFk: 1,
			PrcConfigurationId: 1,
			SalesTaxMethodId: 1,
			IsnotaccrualPrr: 'false',
			IsDiverseDebitorsAllowed: 'false',
			Iscanceled: 'false',
			Isframework: 'false',
			Isfreeitemsallowed: 'false',
			UserDefined1: USERDEFINED1,
			UserDefined2: USERDEFINED2,
			UserDefined3: USERDEFINED3,
			UserDefined4: USERDEFINED4,
			UserDefined5: USERDEFINED5,
		};

		switch (data[commonLocators.CommonKeys.CASE_TYPE]) {
			case 'EmptyCodeDefined':
				Object.assign(body, { Code: '' });
				failOnStatusCode = false;
				break;

			case 'CodeDefined':
				Object.assign(body, { Code: data[app.GridCells.CODE] });
				failOnStatusCode = true;
				break;

			case 'CodeNotPassed':
				failOnStatusCode = true;
				break;
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/sales/publicapi/order/1.1`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
				failOnStatusCode: failOnStatusCode,
			})
			.then((response) => {
				const caseType = data[commonLocators.CommonKeys.CASE_TYPE];
				switch (caseType) {
					case 'EmptyCodeDefined':
						expect(response.status).to.eq(400);
						expect(response.body.ErrorMessage).to.eq(data[commonLocators.CommonKeys.ERROR_MESSAGE]);
						break;

					case 'CodeDefined':
						expect(response.status).to.eq(200);
						expect(response.body.Code).to.eq(data[app.GridCells.CODE]);
						expect(response.body.UserDefined1).to.eq(USERDEFINED1);
						expect(response.body.UserDefined2).to.eq(USERDEFINED2);
						expect(response.body.UserDefined3).to.eq(USERDEFINED3);
						expect(response.body.UserDefined4).to.eq(USERDEFINED4);
						expect(response.body.UserDefined5).to.eq(USERDEFINED5);
						break;

					case 'CodeNotPassed':
						expect(response.status).to.eq(200);
						expect(response.body.Code).not.to.be.empty;
						expect(response.body.UserDefined1).to.eq(USERDEFINED1);
						expect(response.body.UserDefined2).to.eq(USERDEFINED2);
						expect(response.body.UserDefined3).to.eq(USERDEFINED3);
						expect(response.body.UserDefined4).to.eq(USERDEFINED4);
						expect(response.body.UserDefined5).to.eq(USERDEFINED5);
						break;

					default:
						throw new Error(`Unhandled CASE_TYPE: ${caseType}`);
				}
			});
	}

	public numberRange_validationCreationOfBillHeaderAPI(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let failOnStatusCode;
		const USERDEFINED1 = _common.generateRandomString(2);
		const USERDEFINED2 = _common.generateRandomString(2);
		const USERDEFINED3 = _common.generateRandomString(2);
		const USERDEFINED4 = _common.generateRandomString(2);
		const USERDEFINED5 = _common.generateRandomString(2);

		const body: any = {
			Id: 1000001,
			RubricCategoryFk: 21,
			CompanyFk: 1000005,
			CompanyResponsibleFk: 1000005,
			ProjectFk: 1000040,
			LanguageFk: 2,
			BilStatusFk: 2,
			VoucherTypeFk: 1,
			InvoiceTypeFk: 2,
			Description: 'AR1 Erdarbeiten',
			DescriptionTr: 1006573,
			CurrencyFk: 1,
			ExchangeRate: 1,
			ClerkFk: 1000002,
			BusinesspartnerFk: 1000006,
			SubsidiaryFk: 1000014,
			CustomerFk: 1000009,
			BillingSchemaFk: 1000003,
			PaymentTermFiFk: null,
			PaymentTermPaFk: 3,
			TaxCodeFk: 1000055,
			ContractTypeFk: 2,
			UserDefined1: USERDEFINED1,
			UserDefined2: USERDEFINED2,
			UserDefined3: USERDEFINED3,
			UserDefined4: USERDEFINED4,
			UserDefined5: USERDEFINED5,
			IsCanceled: false,
			BookingText: 'AR1-ImmoInvest',
			OrdHeaderFk: 1000001,
			PrcStructureFk: 1000003,
			ControllingUnitFk: 1000206,
			VatGroupFk: 1000001,
			ContactFk: 1000037,
			AmountNet: 2200,
			AmountNetOc: 2200,
			AmountGross: 2356.2,
			AmountGrossOc: 2356.2,
			TypeFk: 1,
			OrdConditionFk: 1,
			PaymentTermFk: 3,
			BasSalesTaxMethodFk: 1,
			IsNotAccrual: false,
			BoqEntities: [
				{
					Id: 1000001,
					BoqHeaderFk: 1000029,
					BilHeaderFk: 1000001,
					InsertedBy: 1,
					Version: 1,
				},
			],
			Version: 10,
		};

		switch (data[commonLocators.CommonKeys.CASE_TYPE]) {
			case 'EmptyBillNoDefined':
				Object.assign(body, { BillNo: '' });
				failOnStatusCode = false;
				break;

			case 'BillNoDefined':
				Object.assign(body, { BillNo: data[app.GridCells.BILL_NO] });
				failOnStatusCode = true;
				break;

			case 'BillNoNotPassed':
				failOnStatusCode = true;
				break;
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/sales/publicapi/bilheader/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
				failOnStatusCode: failOnStatusCode,
			})
			.then((response) => {
				const caseType = data[commonLocators.CommonKeys.CASE_TYPE];
				switch (caseType) {
					case 'EmptyBillNoDefined':
						expect(response.status).to.eq(400);
						expect(response.body.ErrorMessage).to.eq(data[commonLocators.CommonKeys.ERROR_MESSAGE]);
						break;

					case 'BillNoDefined':
						expect(response.status).to.eq(200);
						expect(response.body.BillNo).to.eq(data[app.GridCells.BILL_NO]);
						expect(response.body.UserDefined1).to.eq(USERDEFINED1);
						expect(response.body.UserDefined2).to.eq(USERDEFINED2);
						expect(response.body.UserDefined3).to.eq(USERDEFINED3);
						expect(response.body.UserDefined4).to.eq(USERDEFINED4);
						expect(response.body.UserDefined5).to.eq(USERDEFINED5);
						break;

					case 'BillNoNotPassed':
						expect(response.status).to.eq(200);
						expect(response.body.BillNo).not.to.be.empty;
						expect(response.body.UserDefined1).to.eq(USERDEFINED1);
						expect(response.body.UserDefined2).to.eq(USERDEFINED2);
						expect(response.body.UserDefined3).to.eq(USERDEFINED3);
						expect(response.body.UserDefined4).to.eq(USERDEFINED4);
						expect(response.body.UserDefined5).to.eq(USERDEFINED5);
						break;

					default:
						throw new Error(`Unhandled CASE_TYPE: ${caseType}`);
				}
			});
	}

	public numberRange_validationCreationOfWIPHeaderAPI(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let failOnStatusCode;
		const USERDEFINED1 = _common.generateRandomString(2);
		const USERDEFINED2 = _common.generateRandomString(2);
		const USERDEFINED3 = _common.generateRandomString(2);
		const USERDEFINED4 = _common.generateRandomString(2);
		const USERDEFINED5 = _common.generateRandomString(2);

		const body: any = {
			Id: 1000001,
			RubricCategoryFk: 97,
			CompanyFk: 1000005,
			CompanyResponsibleFk: 1000005,
			ProjectFk: 1000040,
			LanguageFk: 2,
			WipStatusFk: 3,
			Code: 'LE-V-00100',
			Description: 'LE Periode1',
			DescriptionTr: 1006537,
			CurrencyFk: 1,
			ExchangeRate: 1,
			ClerkFk: 1000002,
			BusinesspartnerFk: 1000006,
			SubsidiaryFk: 1000014,
			CustomerFk: 1000009,
			UserDefined1: USERDEFINED1,
			UserDefined2: USERDEFINED2,
			UserDefined3: USERDEFINED3,
			UserDefined4: USERDEFINED4,
			UserDefined5: USERDEFINED5,
			OrdHeaderFk: 1000001,
			AmountNet: 2200,
			WipVat: 418,
			AmountNetOc: 2200,
			WipVatOc: 418,
			IsBilled: false,
			PrcStructureFk: 1000003,
			VatgroupFk: 1000001,
			PaymentTermFiFk: 9,
			PaymentTermPaFk: 3,
			TaxCodeFk: 1000055,
			BillingSchemaFk: 1000003,
			ContactFk: 1000037,
			IsNotAccrual: false,
			IsMulticontract: false,
			SalesTaxMethodFk: 1,
			IsCanceled: false,
			WipBoqEntities: [
				{
					Id: 1000001,
					BoqHeaderFk: 1000025,
					WipHeaderFk: 1000001,
					Version: 1,
				},
			],
			Version: 10,
			DocumentDate: data[commonLocators.CommonLabels.DATE],
		};

		switch (data[commonLocators.CommonKeys.CASE_TYPE]) {
			case 'CodeDefined':
				Object.assign(body, { Code: data[app.GridCells.CODE] });
				failOnStatusCode = true;
				break;
		}

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/sales/publicapi/wipheader/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
				failOnStatusCode: failOnStatusCode,
			})
			.then((response) => {
				const caseType = data[commonLocators.CommonKeys.CASE_TYPE];
				switch (caseType) {
					case 'CodeDefined':
						expect(response.status).to.eq(200);
						expect(response.body.Code).to.not.eq(data[app.GridCells.CODE]);
						expect(response.body.Code).to.not.empty;
						expect(response.body.UserDefined1).to.eq(USERDEFINED1);
						expect(response.body.UserDefined2).to.eq(USERDEFINED2);
						expect(response.body.UserDefined3).to.eq(USERDEFINED3);
						expect(response.body.UserDefined4).to.eq(USERDEFINED4);
						expect(response.body.UserDefined5).to.eq(USERDEFINED5);
						expect(response.body.DocumentDate).to.contains(data[commonLocators.CommonLabels.DATE]);
						break;

					default:
						throw new Error(`Unhandled CASE_TYPE: ${caseType}`);
				}
			});
	}

	public createRubricCategory_underCustomizing(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let rubricCategoryCounter = Cypress.env('rubricCategoryCounter') || 0; // Start from 0 if not defined
		rubricCategoryCounter += 1;
		Cypress.env('rubricCategoryCounter', rubricCategoryCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('RubricId', 'RUBRIC_FK');
		assignIfProvided('Description', 'DESCRIPTION');
		assignIfProvided('IsLive', 'IS_LIVE');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/rubriccategory/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_RUBRIC_CATEGORY_ID_${rubricCategoryCounter}`, response.body.Id);

				cy.log(`API_RUBRIC_CATEGORY_ID_${rubricCategoryCounter}:  ${response.body.Id}`);
			});
	}

	public createProjectDocumentStatus_underCustomizing(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let projectDocumentStatusCounter = Cypress.env('projectDocumentStatusCounter') || 0; // Start from 0 if not defined
		projectDocumentStatusCounter += 1;
		Cypress.env('projectDocumentStatusCounter', projectDocumentStatusCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Description', 'DESCRIPTION');
		assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_FK');
		assignIfProvided('Code', 'CODE');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('Sorting', 'SORTING');
		assignIfProvided('Icon', 'ICON');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/projectdocumentstatus/4.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_PROJECT_DOCUMENT_STATUS_ID_${projectDocumentStatusCounter}`, response.body.Id);

				cy.log(`API_PROJECT_DOCUMENT_STATUS_ID_${projectDocumentStatusCounter}:  ${response.body.Id}`);
				cy.log(`API_PROJECT_DOCUMENT_STATUS_DESC_${projectDocumentStatusCounter}:  ${response.body.Description}`);
			});
	}

	public createProjectDocumentType_underCustomizing(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let projectDocumentTypeCounter = Cypress.env('projectDocumentTypeCounter') || 0; // Start from 0 if not defined
		projectDocumentTypeCounter += 1;
		Cypress.env('projectDocumentTypeCounter', projectDocumentTypeCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Description', 'DESCRIPTION');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('Sorting', 'SORTING');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/projectdocumenttype/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_PROJECT_DOCUMENT_TYPE_ID_${projectDocumentTypeCounter}`, response.body.Id);

				cy.log(`API_PROJECT_DOCUMENT_TYPE_ID_${projectDocumentTypeCounter}:  ${response.body.Id}`);
				cy.log(`API_PROJECT_DOCUMENT_TYPE_DESC_${projectDocumentTypeCounter}:  ${response.body.Description}`);
			});
	}

	public createProjectDocumentCategory_underCustomizing(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let projectDocumentCategoryCounter = Cypress.env('projectDocumentCategoryCounter') || 0; // Start from 0 if not defined
		projectDocumentCategoryCounter += 1;
		Cypress.env('projectDocumentCategoryCounter', projectDocumentCategoryCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Description', 'DESCRIPTION');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('Sorting', 'SORTING');
		assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_FK');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/projectdocumentcategory/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_PROJECT_DOCUMENT_CATEGORY_ID_${projectDocumentCategoryCounter}`, response.body.Id);

				cy.log(`API_PROJECT_DOCUMENT_CATEGORY_ID_${projectDocumentCategoryCounter}:  ${response.body.Id}`);
				cy.log(`API_PROJECT_DOCUMENT_CAT_DESC_${projectDocumentCategoryCounter}:  ${response.body.Description}`);
			});
	}

	public createProjectDocumentCategoryType2_underCustomizing(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let projectDocumentCategoryType2Counter = Cypress.env('projectDocumentCategoryType2Counter') || 0; // Start from 0 if not defined
		projectDocumentCategoryType2Counter += 1;
		Cypress.env('projectDocumentCategoryType2Counter', projectDocumentCategoryType2Counter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('DocumentcategoryId', 'DOCUMENT_CATEGORY_FK');
		assignIfProvided('DocumenttypeId', 'DOCUMENT_TYPE_FK');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/projectdocumentcategory2type/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_PROJECT_DOCUMENT_CATEGORY_TYPE_2_ID_${projectDocumentCategoryType2Counter}`, response.body.Id);

				cy.log(`API_PROJECT_DOCUMENT_CATEGORY_TYPE_2_ID_${projectDocumentCategoryType2Counter}:  ${response.body.Id}`);
				//cy.log(`API_PROJECT_DOCUMENT_CAT_TYPE2_DESC_${projectDocumentCategoryType2Counter}:  ${response.body.Description}`);
			});
	}

	public createBill(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let billCounter = Cypress.env('billCounter') || 0; // Start from 0 if not defined
		billCounter += 1;
		Cypress.env('billCounter', billCounter); // Update counter in env globally

		const body: any = {
			Description: _common.generateRandomString(5),
			CompanyFk: data[app.GridCells.COMPANY_FK],
			PreviousBillFk: data[app.GridCells.PREVIOUS_BILL_FK],
			RubricCategoryFk: data[app.GridCells.RUBRIC_CATEGORY_FK],
			CompanyResponsibleFk: data[app.GridCells.COMPANY_RESPONSIBLE_FK],
			ProjectFk: data[app.GridCells.PROJECT_FK],
			LanguageFk: data[app.GridCells.BAS_LANGUAGE_FK],
			BilStatusFk: data[app.GridCells.BIL_STATUS_FK],
			ClerkFk: data[app.GridCells.CLERK_FK],
			BusinesspartnerFk: data[app.GridCells.BUSINESS_PARTNER_FK],
			SubsidiaryFk: data[app.GridCells.SUBSIDIARY_FK],
			CustomerFk: data[app.GridCells.CUSTOMER_FK],
			ConfigurationFk: data[app.GridCells.CONFIGURATION_FK],
			TypeFk: data[app.GridCells.TYPE_FK],
			BoqEntities: [
				{
					BoqHeaderFk: data[app.GridCells.BOQ_HEADER_FK],
				},
			],
		};

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/sales/publicapi/bilheader/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_BILL_ID_${billCounter}`, response.body.Id);
				Cypress.env(`API_BILL_DESCRIPTION_${billCounter}`, response.body.Description);

				cy.log(`API_BILL_ID_${billCounter}:  ${response.body.Id}`);
				cy.log(`API_BILL_DESCRIPTION_${billCounter}:  ${response.body.Description}`);
			});
	}

	public createContract(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let contractCounter = Cypress.env('contractCounter') || 0; // Start from 0 if not defined
		contractCounter += 1;
		Cypress.env('contractCounter', contractCounter); // Update counter in env globally

		const body: any = {
			Description: _common.generateRandomString(5),
		};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('CompanyId', 'COMPANY_FK');
		assignIfProvided('Code', 'CODE');
		assignIfProvided('ConStatusId', 'STATUS_FK');
		assignIfProvided('BusinesspartnerId', 'BUSINESS_PARTNER_FK');
		assignIfProvided('SubsidiaryId', 'SUBSIDIARY_FK');
		assignIfProvided('SupplierId', 'SUPPLIER_FK');
		assignIfProvided('ContactId', 'CONTACT_FK');
		assignIfProvided('TaxCodeId', 'TAXCODE_FK');
		assignIfProvided('CurrencyId', 'CURRENCY_FK');
		assignIfProvided('PaymentTermFiId', 'PAYMENT_TERM_FI_FK');
		assignIfProvided('ConTypeId', 'CON_TYPE_FK');
		assignIfProvided('ProjectId', 'PROJECT_FK');
		assignIfProvided('PaymentTermPaId', 'PAYMENT_TERM_PA_FK');
		assignIfProvided('PrcConfigurationId', 'PRC_CONFIGURATION_FK');
		assignIfProvided('ClerkPrcId', 'CLERK_PRC_FK');
		assignIfProvided('ClerkReqId', 'CLERK_REQ_FK');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/procurement/publicapi/conheader/5.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_CONTRACT_ID_${contractCounter}`, response.body.Id);
				Cypress.env(`API_CONTRACT_CODE_${contractCounter}`, response.body.Code);
				Cypress.env(`API_CONTRACT_DESCRIPTION_${contractCounter}`, response.body.Description);

				cy.log(`API_CONTRACT_ID_${contractCounter}:  ${response.body.Id}`);
				cy.log(`API_CONTRACT_CODE_${contractCounter}:  ${response.body.Code}`);
				cy.log(`API_CONTRACT_DESCRIPTION_${contractCounter}:  ${response.body.Description}`);
			});
	}

	public updateDispatchHeaderStatus_underCustomizing(data: DataCells, description: string, rubricCategoryDesc: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/dispatchheaderstatus/5.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const arrayStatus = responseData.filter((item) => item.Description === description && item.RubricCategoryDesc === rubricCategoryDesc);

				const body: any = {};
				const assignIfProvided = (fieldName, cellType) => {
					if (data[app.GridCells[cellType]] !== undefined) {
						body[fieldName] = data[app.GridCells[cellType]];
					}
				};

				assignIfProvided('IsPlanned', 'IS_PLANNED');
				assignIfProvided('IsStarted', 'IS_STARTED');
				assignIfProvided('IsFinished', 'IS_FINISHED');
				assignIfProvided('IsStockPosted', 'IS_STOCK_POSTED');
				assignIfProvided('IsReadOnly', 'IS_READONLY');
				assignIfProvided('IsInvoiced', 'IS_INVOICED');
				assignIfProvided('IsLive', 'IS_LIVE');
				assignIfProvided('IsDefault', 'IS_DEFAULT');
				assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_FK');
				assignIfProvided('IsReadyForSettlement', 'IS_READY_FOR_SETTLEMENT');
				assignIfProvided('Code', 'CODE');

				arrayStatus.forEach((status) => {
					const { Id } = status;
					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/dispatchheaderstatus/5.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				});
			});
	}

	public createPlantListType(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let plantListTypeCounter = Cypress.env('plantListTypeCounter') || 0; // Start from 0 if not defined
		plantListTypeCounter += 1;
		Cypress.env('plantListTypeCounter', plantListTypeCounter); // Update counter in env globally

		const body: any = {
			Description: 'PLT_' + _common.generateRandomString(5),
		};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('IsDefault', 'IS_DEFAULT');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/equipmentpricelisttype/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PLANT_LIST_TYPE_ID_${plantListTypeCounter}`, response.body.Id);
				Cypress.env(`API_PLANT_LIST_TYPE_DESCRIPTION_${plantListTypeCounter}`, response.body.Description);

				cy.log(`API_PLANT_LIST_TYPE_ID_${plantListTypeCounter}:  ${response.body.Id}`);
				cy.log(`API_PLANT_LIST_TYPE_DESCRIPTION_${plantListTypeCounter}:  ${response.body.Description}`);
			});
	}

	public createPlantPriceList(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let plantPriceListCounter = Cypress.env('plantPriceListCounter') || 0; // Start from 0 if not defined
		plantPriceListCounter += 1;
		Cypress.env('plantPriceListCounter', plantPriceListCounter); // Update counter in env globally

		const body: any = {
			Description: 'PLC_' + _common.generateRandomString(5),
		};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};

		assignIfProvided('EtmContextId', 'CONTEXT_FK');
		assignIfProvided('PricelistTypeId', 'PRICE_LIST_TYPE_FK');
		assignIfProvided('CurrencyId', 'CURRENCY');
		assignIfProvided('UomId', 'UOM_FK');
		assignIfProvided('CalculationTypeId', 'CALCULATION_TYPE_FK');
		assignIfProvided('Percent', 'PERCENT');
		assignIfProvided('ReferenceYear', 'REFERENCE_YEAR');
		assignIfProvided('IsManualEditPlantMaster', 'IS_MANUAL_EDIT_PLANT_MASTER');
		assignIfProvided('Description', 'DESCRIPTION_INFO');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/equipmentpricelist/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PLANT_PRICE_LIST_ID_${plantPriceListCounter}`, response.body.Id);
				Cypress.env(`API_PLANT_PRICE_LIST_DESC_${plantPriceListCounter}`, response.body.Description);

				cy.log(`API_PLANT_PRICE_LIST_ID_${plantPriceListCounter}:  ${response.body.Id}`);
				cy.log(`API_PLANT_PRICE_LIST_DESC_${plantPriceListCounter}:  ${response.body.Description}`);
			});
	}

	public createPlantType(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let plantTypeCounter = Cypress.env('plantTypeCounter') || 0; // Start from 0 if not defined
		plantTypeCounter += 1;
		Cypress.env('plantTypeCounter', plantTypeCounter); // Update counter in env globally

		const body: any = {
			Description: 'PT_' + _common.generateRandomString(5),
		};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};
		assignIfProvided('IsBulk', 'IS_BULK');
		assignIfProvided('IsCluster', 'IS_CLUSTER');
		assignIfProvided('IsClustered', 'IS_CLUSTERED');
		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Istimekeeping', 'IS_TIMEKEEPING');
		assignIfProvided('Description', 'DESCRIPTION_INFO');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/planttype/5.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PLANT_TYPE_ID_${plantTypeCounter}`, response.body.Id);
				Cypress.env(`API_PLANT_TYPE_DESC_${plantTypeCounter}`, response.body.Description);

				cy.log(`API_PLANT_TYPE_ID_${plantTypeCounter}:  ${response.body.Id}`);
				cy.log(`API_PLANT_TYPE_DESC_${plantTypeCounter}:  ${response.body.Description}`);
			});
	}

	public createWorkOperationType(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let workOperationTypeCounter = Cypress.env('workOperationTypeCounter') || 0; // Start from 0 if not defined
		workOperationTypeCounter += 1;
		Cypress.env('workOperationTypeCounter', workOperationTypeCounter); // Update counter in env globally

		const body: any = {
			Description: 'WOT_' + _common.generateRandomString(5),
			Code: _common.generateRandomString(5),
			PlantContextId: 1000001,
		};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};
		assignIfProvided('IsHire', 'IS_HIRE');
		assignIfProvided('UomId', 'UOM');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Description', 'DESCRIPTION_INFO');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/resource/publicapi/workoptype/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_WORK_OPERATION_TYPE_ID_${workOperationTypeCounter}`, response.body.Id);
				Cypress.env(`API_WORK_OPERATION_TYPE_DESC_${workOperationTypeCounter}`, response.body.Description);
				Cypress.env(`API_WORK_OPERATION_TYPE_CODE_${workOperationTypeCounter}`, response.body.Code);

				cy.log(`API_WORK_OPERATION_TYPE_ID_${workOperationTypeCounter}:  ${response.body.Id}`);
				cy.log(`API_WORK_OPERATION_TYPE_DESC_${workOperationTypeCounter}:  ${response.body.Description}`);
				cy.log(`API_WORK_OPERATION_TYPE_CODE_${workOperationTypeCounter}:  ${response.body.Code}`);
			});
	}

	public createWorkOperationPlantType(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let workOperationPlantTypeCounter = Cypress.env('workOperationPlantTypeCounter') || 0; // Start from 0 if not defined
		workOperationPlantTypeCounter += 1;
		Cypress.env('workOperationPlantTypeCounter', workOperationPlantTypeCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, cellType) => {
			if (data[app.GridCells[cellType]] !== undefined) {
				body[fieldName] = data[app.GridCells[cellType]];
			}
		};
		assignIfProvided('WorkOperationTypeId', 'WORK_OPERATION_TYPE_FK');
		assignIfProvided('PlantTypeId', 'PLANT_TYPE_FK');
		assignIfProvided('IsTimekeepingDefault', 'IS_TIMEKEEPING_DEFAULT');
		assignIfProvided('IsDefault', 'IS_DEFAULT');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/resource/publicapi/workoptype/planttype/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_WORK_OPERATION_PLANT_TYPE_ID_${workOperationPlantTypeCounter}`, response.body.Id);

				cy.log(`API_WORK_OPERATION_PLANT_TYPE_ID_${workOperationPlantTypeCounter}:  ${response.body.Id}`);
			});
	}

	public updateSystemOptionUnderCustomizing(description, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/systemoption/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === description);

				if (!status) throw new Error('description not found');

				const { Id, Version } = status;

				// Set initial default values from the status data
				const body: any = { Version };

				const assignIfProvided = (fieldName, key) => {
					if (data[apiParameters.Keys[key]] !== undefined) {
						body[fieldName] = data[apiParameters.Keys[key]];
					}
				};

				// Update only if specific fields are provided in data
				assignIfProvided('ParameterValue', 'PARAMETER_VALUE');

				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/systemoption/1.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
				});
			});
	}

	public updateBillStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/billstatus/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) {
					const body: any = {
						Description: statusDescription,
					};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('Isarchived', 'IS_ARCHIVED');
					assignIfProvided('Isbilled', 'IS_BILLED');
					assignIfProvided('Isposted', 'IS_POSTED');
					assignIfProvided('ReadOnly', 'READONLY');
					assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');
					assignIfProvided('Code', 'CODE');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Isbtrequired', 'IS_BT_REQUIRED');
					assignIfProvided('Isonlyfwd', 'IS_ONLY_FWD');
					assignIfProvided('Isstorno', 'IS_STOR_NO');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/billstatus/3.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				} else {
					const { Id } = status;

					const body: any = {};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('Isarchived', 'IS_ARCHIVED');
					assignIfProvided('Isbilled', 'IS_BILLED');
					assignIfProvided('Isposted', 'IS_POSTED');
					assignIfProvided('ReadOnly', 'READONLY');
					assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_FK');
					assignIfProvided('Code', 'CODE');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Isbtrequired', 'IS_BT_REQUIRED');
					assignIfProvided('Isonlyfwd', 'IS_ONLY_FWD');
					assignIfProvided('Isstorno', 'IS_STOR_NO');

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/billstatus/3.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public getPlantDetails(description): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/resource/publicapi/plant/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === description);

				if (!status) throw new Error('description not found');

				const { Id, Version } = status;

				// Set initial default values from the status data
				const body: any = { Version };

				cy.request({
					method: 'GET',
					url: Cypress.env('Base_URL') + `services/resource/publicapi/plant/3.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
					cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
					Cypress.env(`API_GET_PLANT_ID`, response.body.Id);
					Cypress.env(`API_GET_PLANT_DESC`, response.body.Description);
					Cypress.env(`API_GET_PLANT_CODE`, response.body.Code);

					cy.log(`API_GET_PLANT_ID:  ${response.body.Id}`);
					cy.log(`API_GET_PLANT_DESC:  ${response.body.Description}`);
					cy.log(`API_GET_PLANT_CODE:  ${response.body.Code}`);
				});
			});
	}

	public createPlant(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let plantCounter = Cypress.env('plantCounter') || 0; // Start from 0 if not defined
		plantCounter += 1;
		Cypress.env('plantCounter', plantCounter); // Update counter in env globally

		const body: any = {
			Description: 'P_' + _common.generateRandomString(5),
			Code: _common.generateRandomString(5),
			PlantContextId: 1000001,
			PlantDivisionId: 3,
			CompanyId: 1000005,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('PlantStatusId', 'PLANT_STATUS_ID');
		assignIfProvided('PlantGroupId', 'PLANT_GROUP_ID');
		assignIfProvided('IsPlannable', 'IS_PLANNABLE');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('PlantTypeId', 'PLANT_TYPE_ID');
		assignIfProvided('PrcStructureId', 'PRC_STRUCTURE_ID');
		assignIfProvided('UomId', 'UOM_ID');
		assignIfProvided('PlantKindId', 'PLANT_KIND_ID');
		assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');
		assignIfProvided('ClerkOwnerId', 'CLERK_OWNER_ID');
		assignIfProvided('ClerkResponsibleId', 'CLERK_RESPONSIBLE_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/resource/publicapi/plant/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PLANT_ID_${plantCounter}`, response.body.Id);
				Cypress.env(`API_PLANT_DESC_${plantCounter}`, response.body.Description);
				Cypress.env(`API_PLANT_CODE_${plantCounter}`, response.body.Code);

				cy.log(`API_PLANT_ID_${plantCounter}:  ${response.body.Id}`);
				cy.log(`API_PLANT_DESC_${plantCounter}:  ${response.body.Description}`);
				cy.log(`API_PLANT_CODE_${plantCounter}:  ${response.body.Code}`);
			});
	}

	public createControllingUnitUnderPlant(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let plantControllingUnitCounter = Cypress.env('plantControllingUnitCounter') || 0; // Start from 0 if not defined
		plantControllingUnitCounter += 1;
		Cypress.env('plantControllingUnitCounter', plantControllingUnitCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('PlantId', 'PLANT_ID');
		assignIfProvided('ContextId', 'CONTEXT_ID');
		assignIfProvided('ProjectId', 'PROJECT_ID');
		assignIfProvided('ControllingUnitId', 'CONTROLLING_UNIT_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/resource/publicapi/plant/controllingunit/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PLANT_CNT_ID_${plantControllingUnitCounter}`, response.body.Id);

				cy.log(`API_PLANT_CNT_ID_${plantControllingUnitCounter}:  ${response.body.Id}`);
			});
	}

	public createPriceListUnderPlant(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let plantPriceListCounter = Cypress.env('plantPriceListCounter') || 0; // Start from 0 if not defined
		plantPriceListCounter += 1;
		Cypress.env('plantPriceListCounter', plantPriceListCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('PlantId', 'PLANT_ID');
		assignIfProvided('PriceListId', 'PRICE_LIST_ID');
		assignIfProvided('IsManual', 'IS_MANUAL');
		assignIfProvided('UomId', 'UOM_ID');
		assignIfProvided('PricePortion1', 'PRICE_PORTION_1');
		assignIfProvided('PricePortion2', 'PRICE_PORTION_2');
		assignIfProvided('PricePortion3', 'PRICE_PORTION_3');
		assignIfProvided('PricePortion4', 'PRICE_PORTION_4');
		assignIfProvided('PricePortion5', 'PRICE_PORTION_5');
		assignIfProvided('PricePortion6', 'PRICE_PORTION_6');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/resource/publicapi/plant/pricelist/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PLANT_PRICE_LIST_ID_${plantPriceListCounter}`, response.body.Id);

				cy.log(`API_PLANT_PRICE_LIST_ID_${plantPriceListCounter}:  ${response.body.Id}`);
			});
	}

	/**
	 * @deprecated
	 * !Note there is error in GET API for JOBS so do not use this API for now
	 */
	public updateJob(projectId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let jobCounter = Cypress.env('jobCounter') || 0; // Start from 0 if not defined
		jobCounter += 1;
		Cypress.env('jobCounter', jobCounter); // Update counter in env globally

		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/job/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.ProjectId === projectId);

				if (!status) throw new Error('ProjectId not found');

				const { Id } = status;

				const body: any = {
					CompanyId: apiConstantData.ID.COMPANY_999,
				};

				const assignIfProvided = (fieldName, key) => {
					if (data[apiParameters.Keys[key]] !== undefined) {
						body[fieldName] = data[apiParameters.Keys[key]];
					}
				};

				assignIfProvided('ContextId', 'CONTEXT_ID');
				assignIfProvided('DivisionId', 'DIVISION_ID');
				assignIfProvided('JobTypeId', 'JOB_TYPE_ID');
				assignIfProvided('IsMaintenance', 'IS_MAINTENANCE');
				assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');
				assignIfProvided('ProjectId', 'PROJECT_ID');
				assignIfProvided('ControllingUnitId', 'CONTROLLING_UNIT_ID');
				assignIfProvided('Code', 'CODE');
				assignIfProvided('IsLive', 'IS_LIVE');
				assignIfProvided('CalendarId', 'CALENDAR_ID');
				assignIfProvided('JobStatusId', 'JOB_STATUS_ID');
				assignIfProvided('JobGroupId', 'JOB_GROUP_ID');
				assignIfProvided('BusinessPartnerId', 'BUSINESS_PARTNER_ID');
				assignIfProvided('SubsidiaryId', 'SUBSIDIARY_ID');
				assignIfProvided('CustomerId', 'CUSTOMER_ID');
				assignIfProvided('PriceConditionId', 'PRICE_CONDITION_ID');
				assignIfProvided('IsProjectDefault', 'IS_PROJECT_DEFAULT');
				assignIfProvided('ContactId', 'CONTACT_ID');
				assignIfProvided('PriceListId', 'PRICE_LIST_ID');
				assignIfProvided('PlantId', 'PLANT_ID');
				assignIfProvided('PlantgroupId', 'PLANT_GROUP_ID');
				assignIfProvided('SettledByTypeId', 'SETTLED_BY_TYPE_ID');
				assignIfProvided('CurrencyId', 'CURRENCY_ID');
				assignIfProvided('ClerkResponsibleId', 'CLERK_RESPONSIBLE_ID');
				assignIfProvided('PlantEstimatePriceListId', 'PLANT_ESTIMATE_LIST_ID');
				assignIfProvided('SiteId', 'SITE_ID');
				assignIfProvided('JobBillingId', 'JOB_BILLING_ID');
				assignIfProvided('IncotermId', 'INCO_TERM_ID');
				assignIfProvided('PlantComponentId', 'PLANT_COMPONENT_ID');

				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/logistic/publicapi/job/3.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
				});
			});
	}

	/**
	 * @deprecated
	 * !Note there is error in API for JOBS so do not use this API for now
	 */
	public createJob(projectId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let jobCounter = Cypress.env('jobCounter') || 0; // Start from 0 if not defined
		jobCounter += 1;
		Cypress.env('jobCounter', jobCounter); // Update counter in env globally

		const body: any = {
			Code: _common.generateRandomString(5),
			ProjectId: projectId,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('ContextId', 'CONTEXT_ID');
		assignIfProvided('DivisionId', 'DIVISION_ID');
		assignIfProvided('JobTypeId', 'JOB_TYPE_ID');
		assignIfProvided('IsMaintenance', 'IS_MAINTENANCE');
		assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');
		assignIfProvided('ControllingUnitId', 'CONTROLLING_UNIT_ID');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('CalendarId', 'CALENDAR_ID');
		assignIfProvided('JobStatusId', 'JOB_STATUS_ID');
		assignIfProvided('JobGroupId', 'JOB_GROUP_ID');
		assignIfProvided('BusinessPartnerId', 'BUSINESS_PARTNER_ID');
		assignIfProvided('SubsidiaryId', 'SUBSIDIARY_ID');
		assignIfProvided('CustomerId', 'CUSTOMER_ID');
		assignIfProvided('PriceConditionId', 'PRICE_CONDITION_ID');
		assignIfProvided('IsProjectDefault', 'IS_PROJECT_DEFAULT');
		assignIfProvided('ContactId', 'CONTACT_ID');
		assignIfProvided('PriceListId', 'PRICE_LIST_ID');
		assignIfProvided('PlantId', 'PLANT_ID');
		assignIfProvided('PlantgroupId', 'PLANT_GROUP_ID');
		assignIfProvided('SettledByTypeId', 'SETTLED_BY_TYPE_ID');
		assignIfProvided('CurrencyId', 'CURRENCY_ID');
		assignIfProvided('ClerkResponsibleId', 'CLERK_RESPONSIBLE_ID');
		assignIfProvided('PlantEstimatePriceListId', 'PLANT_ESTIMATE_LIST_ID');
		assignIfProvided('SiteId', 'SITE_ID');
		assignIfProvided('JobBillingId', 'JOB_BILLING_ID');
		assignIfProvided('IncotermId', 'INCO_TERM_ID');
		assignIfProvided('PlantComponentId', 'PLANT_COMPONENT_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/logistic/publicapi/job/3.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				Cypress.env(`API_JOB_ID_${jobCounter}`, response.body.Id);
				cy.log(`API_JOB_ID_${jobCounter}:  ${response.body.Id}`);
			});
	}

	public createLogisticPriceCondition(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let logisticPriceConditionCounter = Cypress.env('logisticPriceConditionCounter') || 0; // Start from 0 if not defined
		logisticPriceConditionCounter += 1;
		Cypress.env('logisticPriceConditionCounter', logisticPriceConditionCounter); // Update counter in env globally

		const body: any = {
			ContextId: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO_PROJECT_CONTEXT,
			Code: _common.generateRandomString(4),
			Description: _common.generateRandomString(4),
			CurrencyId: apiConstantData.ID.CURRENCY_EUR,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('IsHandlingCharge', 'IS_HANDLING_CHARGE');
		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('IsLive', 'IS_LIVE');
		assignIfProvided('PriceListId', 'PRICE_LIST_ID');
		assignIfProvided('DepartureRatingPercent', 'DEPARTURE_RATING_PERCENT');
		assignIfProvided('IsMultiple01', 'PRICE_PORTION_2');
		assignIfProvided('IsMultiple02', 'PRICE_PORTION_3');
		assignIfProvided('IsMultiple03', 'PRICE_PORTION_4');
		assignIfProvided('IsMultiple04', 'PRICE_PORTION_5');
		assignIfProvided('HandlingChargefull', 'HANDLING_CHARGE_FULL');
		assignIfProvided('HandlingChargeReduced', 'HANDLING_CHARGE_REDUCED');
		assignIfProvided('HandlingChargeExtern', 'HANDLING_CHARGE_EXTERN');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/pricecondition/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_LOGISTIC_PRICE_CONDITION_ID_${logisticPriceConditionCounter}`, response.body.Id);
				Cypress.env(`API_LOGISTIC_PRICE_CONDITION_DESCRIPTION_${logisticPriceConditionCounter}`, response.body.Description);
				Cypress.env(`API_LOGISTIC_PRICE_CONDITION_CODE_${logisticPriceConditionCounter}`, response.body.Code);

				cy.log(`API_LOGISTIC_PRICE_CONDITION_ID_${logisticPriceConditionCounter}:  ${response.body.Id}`);
				cy.log(`API_LOGISTIC_PRICE_CONDITION_DESCRIPTION_${logisticPriceConditionCounter}:  ${response.body.Description}`);
				cy.log(`API_LOGISTIC_PRICE_CONDITION_CODE_${logisticPriceConditionCounter}:  ${response.body.Code}`);
			});
	}

	/**
	 * @deprecated
	 * !Note there DB error in API so do not use this API for now
	 */

	public createLogisticPlantCatalogPrice(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let logisticPlantCatalogPriceCounter = Cypress.env('logisticPlantCatalogPriceCounter') || 0; // Start from 0 if not defined
		logisticPlantCatalogPriceCounter += 1;
		Cypress.env('logisticPlantCatalogPriceCounter', logisticPlantCatalogPriceCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('PriceConditionId', 'PRICE_CONDITION_ID');
		assignIfProvided('PlantPriceListId', 'PLANT_PRICE_LIST_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/pricecondition/plantcatalogprice/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_LOGISTIC_PLANT_CATALOG_PRICE_ID_${logisticPlantCatalogPriceCounter}`, response.body.Id);

				cy.log(`API_LOGISTIC_PLANT_CATALOG_PRICE_ID_${logisticPlantCatalogPriceCounter}:  ${response.body.Id}`);
			});
	}

	public createLogisticPriceConditionItem(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let logisticPriceConditionItemCounter = Cypress.env('logisticPriceConditionItemCounter') || 0; // Start from 0 if not defined
		logisticPriceConditionItemCounter += 1;
		Cypress.env('logisticPriceConditionItemCounter', logisticPriceConditionItemCounter); // Update counter in env globally

		const body: any = {
			Description: _common.generateRandomString(5),
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('PriceConditionId', 'PRICE_CONDITION_ID');
		assignIfProvided('PlantPriceListId', 'PLANT_PRICE_LIST_ID');
		assignIfProvided('WorkOperationTypeId', 'WORK_OPERATION_TYPE_ID');
		assignIfProvided('PricingGroupId', 'PRICING_GROUP_ID');
		assignIfProvided('Percentage01', 'PRECENTAGE_01');
		assignIfProvided('Percentage02', 'PRECENTAGE_02');
		assignIfProvided('Percentage03', 'PRECENTAGE_03');
		assignIfProvided('Percentage04', 'PRECENTAGE_04');
		assignIfProvided('Percentage05', 'PRECENTAGE_05');
		assignIfProvided('Percentage06', 'PRECENTAGE_06');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/pricecondition/item/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_LOGISTIC_PRICE_CONDITION_ITEM_ID_${logisticPriceConditionItemCounter}`, response.body.Id);

				cy.log(`API_LOGISTIC_PRICE_CONDITION_ITEM_ID_${logisticPriceConditionItemCounter}:  ${response.body.Id}`);
			});
	}

	/**
	 * @deprecated
	 * !Note there is error in GET API for JOBS so do not use this API for now
	 */
	public createDispatchingHeader(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let dispatchingHeaderCounter = Cypress.env('dispatchingHeaderCounter') || 0; // Start from 0 if not defined
		dispatchingHeaderCounter += 1;
		Cypress.env('dispatchingHeaderCounter', dispatchingHeaderCounter); // Update counter in env globally

		const body: any = {
			Description: _common.generateRandomString(5),
			CompanyId: apiConstantData.ID.COMPANY_999,
			ReceivingJobCompanyId: apiConstantData.ID.COMPANY_999,
			ProvidingJobCompanyId: apiConstantData.ID.COMPANY_999,
			DispatchStatusId: apiConstantData.ID.DISPATCHING_STATUS_CREATED,
			DivisionId: apiConstantData.ID.DIVISION_ID_RIB_DEMO_ISC_PLANT_DIVISION,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('ProvidingJobId', 'PROVIDING_JOB_ID');
		assignIfProvided('ReceivingJobId', 'RECEIVING_JOB_ID');
		assignIfProvided('DispatchStatusId', 'DISPATCH_STATUS_ID');
		assignIfProvided('IsPicking', 'IS_PICKING');
		assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');
		assignIfProvided('Code', 'CODE');
		assignIfProvided('EffectiveDate', 'EFFECTIVE_DATE');
		assignIfProvided('ProvidingJobProjectId', 'PROVIDING_JOB_PROJECT_ID');
		assignIfProvided('ReceivingJobProjectId', 'RECEIVING_JOB_PROJECT_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/dispatchheader/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_DISPATCHING_HEADER_ID_${dispatchingHeaderCounter}`, response.body.Id);
				Cypress.env(`API_DISPATCHING_HEADER_DESCRIPTION_${dispatchingHeaderCounter}`, response.body.Description);
				Cypress.env(`API_DISPATCHING_HEADER_CODE_${dispatchingHeaderCounter}`, response.body.Code);

				cy.log(`API_DISPATCHING_HEADER_ID_${dispatchingHeaderCounter}:  ${response.body.Id}`);
				cy.log(`API_DISPATCHING_HEADER_DESCRIPTION_${dispatchingHeaderCounter}:  ${response.body.Description}`);
				cy.log(`API_DISPATCHING_HEADER_CODE_${dispatchingHeaderCounter}:  ${response.body.Code}`);
			});
	}
	/**
	 * @deprecated
	 * !Note there is error in GET API for JOBS so do not use this API for now
	 */
	public createDispatchingRecord(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let dispatchingRecordCounter = Cypress.env('dispatchingRecordCounter') || 0; // Start from 0 if not defined
		dispatchingRecordCounter += 1;
		Cypress.env('dispatchingRecordCounter', dispatchingRecordCounter); // Update counter in env globally

		const body: any = {
			Description: _common.generateRandomString(5),
			CompanyId: apiConstantData.ID.COMPANY_999,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('RecordNumber', 'RECORD_NUMBER');
		assignIfProvided('RecordStatusId', 'RECORD_STATUS_ID');
		assignIfProvided('RecordTypeId', 'RECORD_TYPE_ID');
		assignIfProvided('PricingGroupId', 'PRICING_GROUP_ID');
		assignIfProvided('WorkOperationTypeId', 'WORK_OPERATION_TYPE_ID');
		assignIfProvided('MaterialId', 'MATERIAL_ID');
		assignIfProvided('ResourceId', 'RESOURCE_ID');
		assignIfProvided('CostCodeId', 'COST_CODE_ID');
		assignIfProvided('EmployeeId', 'EMPLOYEE_ID');
		assignIfProvided('ControllingUnitId', 'CONTROLLING_UNIT_ID');
		assignIfProvided('ProcurementStructureId', 'PROCUREMENT_STRUCTURE_ID');
		assignIfProvided('Quantity', 'QUANTITY');
		assignIfProvided('DispatchHeaderId', 'DISPATCH_HEADER_ID');
		assignIfProvided('PlantId', 'PLANT_ID');
		assignIfProvided('ResourceId', 'RESOURCE_ID');
		assignIfProvided('DeliveredQuantity', 'DELIVERED_QUANTITY');
		assignIfProvided('UnitId', 'UNIT_ID');
		assignIfProvided('Price', 'PRICE');
		assignIfProvided('IsSettled', 'IS_SETTLED');
		assignIfProvided('ArticleId', 'ARTICLE_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/dispatchrecord/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_DISPATCHING_RECORD_ID_${dispatchingRecordCounter}`, response.body.Id);
				Cypress.env(`API_DISPATCHING_RECORD_DESCRIPTION_${dispatchingRecordCounter}`, response.body.Description);

				cy.log(`API_DISPATCHING_RECORD_ID_${dispatchingRecordCounter}:  ${response.body.Id}`);
				cy.log(`API_DISPATCHING_RECORD_DESCRIPTION_${dispatchingRecordCounter}:  ${response.body.Description}`);
			});
	}

	/**
	 * @deprecated
	 * !Note there is error in GET API for JOBS so do not use this API for now
	 */
	public getJobDetails(projectId): Cypress.Chainable<Cypress.Response<any>> {
		let jobDetailsCounter = Cypress.env('jobDetailsCounter') || 0; // Start from 0 if not defined
		jobDetailsCounter += 1;
		Cypress.env('jobDetailsCounter', jobDetailsCounter); // Update counter in env globally

		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/logistic/publicapi/job/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);

				const responseData = response.body;
				const status = responseData.find((item) => item.ProjectId === projectId);

				if (!status) throw new Error('Project not found');

				const { Id } = status;

				// Set initial default values from the status data
				const body: any = {};

				cy.request({
					method: 'GET',
					url: Cypress.env('Base_URL') + `services/logistic/publicapi/job/3.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
					cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
					Cypress.env(`API_GET_JOB_ID_${jobDetailsCounter}`, response.body.Id);

					cy.log(`API_GET_JOB_ID_${jobDetailsCounter}:  ${response.body.Id}`);
				});
			});
	}

	public updateOrderStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/orderstatus/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) {
					const body: any = {
						Description: statusDescription,
					};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('Isfinallybilled', 'IS_FINALLY_BILLED');
					assignIfProvided('Iscanceled', 'IS_CANCELED');
					assignIfProvided('Isordered', 'IS_ORDERED');
					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Code', 'CODE');
					assignIfProvided('ReadOnly', 'READONLY');
					assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/orderstatus/3.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				} else {
					const { Id } = status;

					const body: any = {};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('Isfinallybilled', 'IS_FINALLY_BILLED');
					assignIfProvided('Iscanceled', 'IS_CANCELED');
					assignIfProvided('Isordered', 'IS_ORDERED');
					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Code', 'CODE');
					assignIfProvided('ReadOnly', 'READONLY');
					assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/orderstatus/3.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public createCertificates(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let certificateCounter = Cypress.env('certificateCounter') || 0; // Start from 0 if not defined
		certificateCounter += 1;
		Cypress.env('certificateCounter', certificateCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('BusinessPartnerId', 'BUSINESS_PARTNER_ID');
		assignIfProvided('ConHeaderId	', 'CON_HEADER_ID');
		assignIfProvided('CompanyId', 'COMPANY_ID');
		assignIfProvided('CertificateStatusId', 'CERTIFICATE_STATUS_ID');
		assignIfProvided('CertificateTypeId', 'CERTIFICATE_TYPE_ID');
		assignIfProvided('Code', 'CODE');
		assignIfProvided('CertificateDate', 'READONLY');
		assignIfProvided('BpIssuerId', 'BP_ISSUER_ID');
		assignIfProvided('ValidFrom', 'VALID_FROM');
		assignIfProvided('ValidTo', 'VALID_TO');
		assignIfProvided('ProjectId', 'PROJECT_ID');
		assignIfProvided('OrdHeaderId', 'ORD_HEADER_ID');
		assignIfProvided('SubsidiaryId', 'SUBSIDIARY_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/businesspartner/publicapi/Certificate/2.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_CERTIFICATE_ID_${certificateCounter}`, response.body.Id);
				Cypress.env(`API_CERTIFICATE_CODE_${certificateCounter}`, response.body.Code);

				cy.log(`API_CERTIFICATE_ID_${certificateCounter}:  ${response.body.Id}`);
				cy.log(`API_CERTIFICATE_CODE_${certificateCounter}:  ${response.body.Code}`);
			});
	}

	public createCertificateDocument(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let certificateDocumentCounter = Cypress.env('certificateDocumentCounter') || 0; // Start from 0 if not defined
		certificateDocumentCounter += 1;
		Cypress.env('certificateDocumentCounter', certificateDocumentCounter); // Update counter in env globally

		const body: any = {
			BasCompanyId: apiConstantData.ID.COMPANY_999,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('Description', 'DESCRIPTION');
		assignIfProvided('DocumentStatusId', 'DOCUMENT_STATUS_ID');
		assignIfProvided('DocumentCategoryId', 'DOCUMENT_CATEGORY_ID');
		assignIfProvided('BasDocumentTypeId', 'BAS_DOCUMENT_TYPE_ID');
		assignIfProvided('PrjDocumentTypeId', 'PRJ_DOCUMENT_TYPE_ID');
		assignIfProvided('ProjectId', 'PROJECT_ID');
		assignIfProvided('MdcControllingUnitId', 'MDC_CONTROLLING_UNIT_ID');
		assignIfProvided('PrjLocationId', 'PRJ_LOCATION_ID');
		assignIfProvided('BpdBusinessPartnerId', 'BPD_BUSINESS_PARTNER_ID');
		assignIfProvided('BpdCertificateId', 'BPD_CERTIFICATE_ID');
		assignIfProvided('PrcStructureId', 'PRC_STRUCTURE_ID');
		assignIfProvided('BasRubricCategoryId', 'BAS_RUBRIC_CATEGORY_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/documents/publicapi/projectdocument/2.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_CERTIFICATE_DOCUMENT_ID_${certificateDocumentCounter}`, response.body.Id);
				Cypress.env(`API_CERTIFICATE_DOCUMENT_DESC_${certificateDocumentCounter}`, response.body.Description);

				cy.log(`API_CERTIFICATE_DOCUMENT_ID_${certificateDocumentCounter}:  ${response.body.Id}`);
				cy.log(`API_CERTIFICATE_DOCUMENT_DESC_${certificateDocumentCounter}:  ${response.body.Description}`);
			});
	}

	public updateFileTypeUnderCustomizing(fileTypeDescription: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/documenttype/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === fileTypeDescription);

				let fileTypeCounter = Cypress.env('fileTypeCounter') || 0; // Start from 0 if not defined
				fileTypeCounter += 1;
				Cypress.env('fileTypeCounter', fileTypeCounter); // Update counter in env globally

				if (!status) {
					const body: any = {
						Description: fileTypeDescription,
					};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('MaxByte', 'MAX_BYTE');
					assignIfProvided('MaxLength', 'MAX_LENGTH');
					assignIfProvided('MaxWidth', 'MAX_WIDTH');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Is2dModel', 'IS_2_MODEL');
					assignIfProvided('Is3dModel', 'IS_3_MODEL');
					assignIfProvided('AllowUpload', 'ALLOW_UPLOAD');
					assignIfProvided('AllowPreview', 'ALLOW_PREVIEW');
					assignIfProvided('ValidateFileSignature', 'VALIDATE_FILE_SIGNATURE');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/documenttype/2.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
						Cypress.env(`API_FILE_TYPE_ID_${fileTypeCounter}`, response.body.Id);
						Cypress.env(`API_FILE_TYPE_DESC_${fileTypeCounter}`, response.body.Description);

						cy.log(`API_FILE_TYPE_ID_${fileTypeCounter}:  ${response.body.Id}`);
						cy.log(`API_FILE_TYPE_DESC_${fileTypeCounter}:  ${response.body.Description}`);
					});
				} else {
					const { Id } = status;

					const body: any = {};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('MaxByte', 'MAX_BYTE');
					assignIfProvided('MaxLength', 'MAX_LENGTH');
					assignIfProvided('MaxWidth', 'MAX_WIDTH');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Is2dModel', 'IS_2_MODEL');
					assignIfProvided('Is3dModel', 'IS_3_MODEL');
					assignIfProvided('AllowUpload', 'ALLOW_UPLOAD');
					assignIfProvided('AllowPreview', 'ALLOW_PREVIEW');
					assignIfProvided('ValidateFileSignature', 'VALIDATE_FILE_SIGNATURE');

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/documenttype/2.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public projectChangeStatusUnderCustomizing(statusDescription: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/projectchangestatus/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				let projectChangeStatusCounter = Cypress.env('projectChangeStatusCounter') || 0; // Start from 0 if not defined
				projectChangeStatusCounter += 1;
				Cypress.env('projectChangeStatusCounter', projectChangeStatusCounter); // Update counter in env globally

				if (!status) {
					const body: any = {
						Description: statusDescription,
					};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('IsAccepted', 'IS_ACCEPTED');
					assignIfProvided('IsRejected', 'IS_REJECTED');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Icon', 'ICON');
					assignIfProvided('IsIdentified', 'IS_IDENTIFIED');
					assignIfProvided('IsAnnounced', 'IS_ANNOUNCED');
					assignIfProvided('IsSubmitted', 'IS_SUBMITTED');
					assignIfProvided('Code', 'CODE');
					assignIfProvided('IsReadOnly', 'IS_READ_ONLY');
					assignIfProvided('IsWithDrawn', 'IS_WITH_DRAWN');
					assignIfProvided('IsRejectedWithProtest', 'IS_REJECTED_WITH_PROTEST');
					assignIfProvided('IsAcceptedInPrinciple', 'IS_ACCEPTED_IN_PRINCIPLE');
					assignIfProvided('FactorByReason', 'FACTOR_BY_REASON');
					assignIfProvided('FactorByAmount', 'FACTOR_BY_AMOUNT');
					assignIfProvided('IsAllowedQtoForSales', 'IS_ALLOWED_QTO_FOR_SALES');
					assignIfProvided('IsAllowedQtoForProc', 'IS_ALLOWED_QTO_FOR_PROC');
					assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/projectchangestatus/2.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
						Cypress.env(`API_PROJECT_CHANGE_STATUS_ID_${projectChangeStatusCounter}`, response.body.Id);
						Cypress.env(`API_PROJECT_CHANGE_STATUS_DESC_${projectChangeStatusCounter}`, response.body.Description);

						cy.log(`API_PROJECT_CHANGE_STATUS_ID_${projectChangeStatusCounter}:  ${response.body.Id}`);
						cy.log(`API_PROJECT_CHANGE_STATUS_DESC_${projectChangeStatusCounter}:  ${response.body.Description}`);
					});
				} else {
					const { Id } = status;

					const body: any = {};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('IsDefault', 'IS_DEFAULT');
					assignIfProvided('IsAccepted', 'IS_ACCEPTED');
					assignIfProvided('IsRejected', 'IS_REJECTED');
					assignIfProvided('IsLive', 'IS_LIVE');
					assignIfProvided('Icon', 'ICON');
					assignIfProvided('IsIdentified', 'IS_IDENTIFIED');
					assignIfProvided('IsAnnounced', 'IS_ANNOUNCED');
					assignIfProvided('IsSubmitted', 'IS_SUBMITTED');
					assignIfProvided('Code', 'CODE');
					assignIfProvided('IsReadOnly', 'IS_READ_ONLY');
					assignIfProvided('IsWithDrawn', 'IS_WITH_DRAWN');
					assignIfProvided('IsRejectedWithProtest', 'IS_REJECTED_WITH_PROTEST');
					assignIfProvided('IsAcceptedInPrinciple', 'IS_ACCEPTED_IN_PRINCIPLE');
					assignIfProvided('FactorByReason', 'FACTOR_BY_REASON');
					assignIfProvided('FactorByAmount', 'FACTOR_BY_AMOUNT');
					assignIfProvided('IsAllowedQtoForSales', 'IS_ALLOWED_QTO_FOR_SALES');
					assignIfProvided('IsAllowedQtoForProc', 'IS_ALLOWED_QTO_FOR_PROC');
					assignIfProvided('RubricCategoryId', 'RUBRIC_CATEGORY_ID');

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/projectchangestatus/2.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	/**
	 * @deprecated
	 * !Note there is error in API where checkbox is not getting checked and field is getting disabled
	 */
	public createUsers(logonName: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/usermanagement/publicapi/user/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.LogonName === logonName);

				let usersCounter = Cypress.env('usersCounter') || 0; // Start from 0 if not defined
				usersCounter += 1;
				Cypress.env('usersCounter', usersCounter); // Update counter in env globally

				if (!status) {
					const body: any = {
						LogonName: logonName,
					};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('Name', 'NAME');
					assignIfProvided('IsExternal', 'IS_EXTERNAL');
					assignIfProvided('IsPasswordChangeNeeded', 'IS_PASSWORD_CHANGE_NEEDED');
					assignIfProvided('Email', 'EMAIL');
					assignIfProvided('State', 'STATE');
					assignIfProvided('ExplicitAccess', 'EXPLICIT_ACCESS');
					assignIfProvided('IntegratedAccess', 'INTEGRATED_ACCESS');

					cy.request({
						method: 'POST',
						url: Cypress.env('Base_URL') + `services/usermanagement/publicapi/user/3.0`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
						cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
						Cypress.env(`API_USERS_ID_${usersCounter}`, response.body.Id);
						Cypress.env(`API_USERS_LOGON_NAME_${usersCounter}`, response.body.LogonName);
						Cypress.env(`API_USERS_NAME_${usersCounter}`, response.body.Name);

						cy.log(`API_USERS_ID_${usersCounter}:  ${response.body.Id}`);
						cy.log(`API_USERS_LOGON_NAME_${usersCounter}:  ${response.body.LogonName}`);
						cy.log(`API_USERS_NAME_${usersCounter}:  ${response.body.Name}`);
					});
				} else {
					const { Id } = status;

					const body: any = {};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('LogonName', 'LOGON_NAME');
					assignIfProvided('Name', 'NAME');
					assignIfProvided('IsExternal', 'IS_EXTERNAL');
					assignIfProvided('IsPasswordChangeNeeded', 'IS_PASSWORD_CHANGE_NEEDED');
					assignIfProvided('Email', 'EMAIL');
					assignIfProvided('State', 'STATE');
					assignIfProvided('ExplicitAccess', 'EXPLICIT_ACCESS');
					assignIfProvided('IntegratedAccess', 'INTEGRATED_ACCESS');

					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/usermanagement/publicapi/user/3.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				}
			});
	}

	public updateUsersPassword(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('Password', 'PASSWORD');
		assignIfProvided('LogonName', 'LOGON_NAME');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/usermanagement/publicapi/user/3.0/updatepassword`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
			});
	}

	public assignUserToGroup(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('GroupName', 'GROUP_NAME');
		assignIfProvided('LogonName', 'LOGON_NAME');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/usermanagement/publicapi/user2group/3.0/assignusertogroup`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
			});
	}

	public createPaymentGroupList(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let paymentGroupListCounter = Cypress.env('paymentGroupListCounter') || 0; // Start from 0 if not defined
		paymentGroupListCounter += 1;
		Cypress.env('paymentGroupListCounter', paymentGroupListCounter); // Update counter in env globally

		const body: any = {
			Code: _common.generateRandomString(5),
			Description: _common.generateRandomString(5),
			IsLive: 'true',
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('IsDefault', 'LOGON_NAME');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/paymentgroup/2.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PAYMENT_GROUP_LIST_ID_${paymentGroupListCounter}`, response.body.Id);
				Cypress.env(`API_PAYMENT_GROUP_LIST_DESC_${paymentGroupListCounter}`, response.body.Description);

				cy.log(`API_PAYMENT_GROUP_LIST_ID_${paymentGroupListCounter}:  ${response.body.Id}`);
				cy.log(`API_PAYMENT_GROUP_LIST_DESC_${paymentGroupListCounter}:  ${response.body.Description}`);
			});
	}

	public createPaymentGroupRate(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let paymentGroupRateCounter = Cypress.env('paymentGroupRateCounter') || 0; // Start from 0 if not defined
		paymentGroupRateCounter += 1;
		Cypress.env('paymentGroupRateCounter', paymentGroupRateCounter); // Update counter in env globally

		const body: any = {
			CompanyId: apiConstantData.ID.COMPANY_999,
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('PaymentGroupId', 'PAYMENT_GROUP_ID');
		assignIfProvided('ValidFrom', 'VALID_FROM');
		assignIfProvided('ControllingUnitId', 'CONTROLLING_UNIT_ID');
		assignIfProvided('Rate', 'RATE');
		assignIfProvided('SurchargeTypeId', 'SURCHARGE_TYPE_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/paymentgroup/rate/2.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_PAYMENT_GROUP_RATE_ID_${paymentGroupRateCounter}`, response.body.Id);
				cy.log(`API_PAYMENT_GROUP_RATE_ID_${paymentGroupRateCounter}:  ${response.body.Id}`);
			});
	}

	public createTimeSymbol(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let timeSymbolCounter = Cypress.env('timeSymbolCounter') || 0; // Start from 0 if not defined
		timeSymbolCounter += 1;
		Cypress.env('timeSymbolCounter', timeSymbolCounter); // Update counter in env globally

		const body: any = {
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
			Code: _common.generateRandomString(2),
			Description: _common.generateRandomString(2),
			IsLive: 'true',
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Sorting', 'SORTING');
		assignIfProvided('IsProductive', 'IS_PRODUCTIVE');
		assignIfProvided('IsCUMandatory', 'IS_CU_MANDATORY');
		assignIfProvided('IsUplift', 'IS_UPLIFT');
		assignIfProvided('IsExpense', 'IS_EXPENSE');
		assignIfProvided('ValuationPercent', 'VALUATION_PERCENT');
		assignIfProvided('ValuationRate', 'VALUATION_RATE');
		assignIfProvided('TimeSymbolTypeId', 'TIME_SYMBOL_TYPE_ID');
		assignIfProvided('UoMId', 'UO_M_ID');
		assignIfProvided('IsPresence', 'IS_PRESENCE');
		assignIfProvided('IsOvertime', 'IS_OVERTIME');
		assignIfProvided('IsPayroll', 'IS_PAYROLL');
		assignIfProvided('IsTimeAccount', 'IS_TIME_ACCOUNT');
		assignIfProvided('TimeSymbolGroupId', 'TIME_SYMBOL_GROUP_ID');
		assignIfProvided('IsWorkingTimeModelRelevant', 'IS_WORKING_TIME_MODEL_RELEVANT');
		assignIfProvided('IsOffDay', 'IS_OFF_DAY');
		assignIfProvided('IsVacation', 'IS_VACATION');
		assignIfProvided('TaxCodeId', 'TAX_CODE_ID');
		assignIfProvided('IsReporting', 'IS_REPORTING');
		assignIfProvided('TimeSymbolGroupId', 'TIME_SYMBOL_GROUP_ID');
		assignIfProvided('IsTravelTime', 'IS_TRAVEL_TIME');
		assignIfProvided('IsTravelDistance', 'IS_TRAVEL_DISTANCE');
		assignIfProvided('IsTravelAllowance', 'IS_TRAVEL_ALLOWANCE');
		assignIfProvided('IsSurcharges', 'IS_SURCHARGES');
		assignIfProvided('IsAction', 'IS_ACTION');
		assignIfProvided('IsTimeAllocation', 'IS_TIME_ALLOCATION');
		assignIfProvided('IsOvernightTravel', 'IS_OVERNIGHT_TRAVEL');
		assignIfProvided('IsAbsence', 'IS_ABSENCE');
		assignIfProvided('IsDriver', 'IS_DRIVER');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/timesymbol/4.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_TIME_SYMBOL_ID_${timeSymbolCounter}`, response.body.Id);
				Cypress.env(`API_TIME_SYMBOL_DESC_${timeSymbolCounter}`, response.body.Description);

				cy.log(`API_TIME_SYMBOL_ID_${timeSymbolCounter}:  ${response.body.Id}`);
				cy.log(`API_TIME_SYMBOL_DESC_${timeSymbolCounter}:  ${response.body.Description}`);
			});
	}

	public createShiftModels(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let shiftModelsCounter = Cypress.env('shiftModelsCounter') || 0; // Start from 0 if not defined
		shiftModelsCounter += 1;
		Cypress.env('shiftModelsCounter', shiftModelsCounter); // Update counter in env globally

		const body: any = {
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
			Description: _common.generateRandomString(2),
			IsLive: 'true',
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Sorting', 'SORTING');
		assignIfProvided('CalendarId', 'CALENDAR_ID');
		assignIfProvided('DefaultWorkdayId', 'DEFAULT_WORKDAY_ID');
		assignIfProvided('ShiftGroupId', 'SHIFT_GROUP_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/shiftmodel/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_SHIFT_MODEL_ID_${shiftModelsCounter}`, response.body.Id);
				Cypress.env(`API_SHIFT_MODEL_DESC_${shiftModelsCounter}`, response.body.Description);

				cy.log(`API_SHIFT_MODEL_ID_${shiftModelsCounter}:  ${response.body.Id}`);
				cy.log(`API_SHIFT_MODEL_DESC_${shiftModelsCounter}:  ${response.body.Description}`);
			});
	}

	public createShiftWorkingTime(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let shiftWorkingTimeCounter = Cypress.env('shiftWorkingTimeCounter') || 0; // Start from 0 if not defined
		shiftWorkingTimeCounter += 1;
		Cypress.env('shiftWorkingTimeCounter', shiftWorkingTimeCounter); // Update counter in env globally

		const body: any = {
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
			Description: _common.generateRandomString(2),
			IsLive: 'true',
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('WeekdayId', 'WEEKDAY_ID');
		assignIfProvided('ShiftId', 'SHIFT_ID');
		assignIfProvided('TimeSymbolId', 'TIME_SYMBOL_ID');
		assignIfProvided('ExceptiondayId', 'EXCEPTIONDAY_ID');
		assignIfProvided('FromTime', 'FROM_TIME');
		assignIfProvided('ToTime', 'TO_TIME');
		assignIfProvided('BreakFrom', 'BREAK_FROM');
		assignIfProvided('BreakTo', 'BREAK_TO');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/shiftmodel/workingtime/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_SHIFT_WORKING_TIME_ID_${shiftWorkingTimeCounter}`, response.body.Id);
				Cypress.env(`API_SHIFT_WORKING_TIME_DESC_${shiftWorkingTimeCounter}`, response.body.Description);

				cy.log(`API_SHIFT_WORKING_TIME_ID_${shiftWorkingTimeCounter}:  ${response.body.Id}`);
				cy.log(`API_SHIFT_WORKING_TIME_DESC_${shiftWorkingTimeCounter}:  ${response.body.Description}`);
			});
	}

	public createWorkingTimeModel(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let workingTimeModelCounter = Cypress.env('workingTimeModelCounter') || 0; // Start from 0 if not defined
		workingTimeModelCounter += 1;
		Cypress.env('workingTimeModelCounter', workingTimeModelCounter); // Update counter in env globally

		const body: any = {
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
			Description: _common.generateRandomString(2),
			IsLive: 'true',
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('Sorting', 'SORTING');
		assignIfProvided('IsDefault', 'IS_DEFAULT');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/workingtimemodel/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_WORKING_TIME_MODEL_ID_${workingTimeModelCounter}`, response.body.Id);
				Cypress.env(`API_WORKING_TIME_MODEL_DESC_${workingTimeModelCounter}`, response.body.Description);

				cy.log(`API_WORKING_TIME_MODEL_ID_${workingTimeModelCounter}:  ${response.body.Id}`);
				cy.log(`API_WORKING_TIME_MODEL_DESC_${workingTimeModelCounter}:  ${response.body.Description}`);
			});
	}

	public updateRubricCategoryStatus_underCustomizing(data: DataCells, description: string, rubricCategoryDesc: string): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/rubriccategory/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const arrayStatus = responseData.filter((item) => item.Description === description && item.RubricDesc === rubricCategoryDesc);

				const body: any = {};
				const assignIfProvided = (fieldName, cellType) => {
					if (data[apiParameters.Keys[cellType]] !== undefined) {
						body[fieldName] = data[apiParameters.Keys[cellType]];
					}
				};

				assignIfProvided('IsDefault', 'IS_DEFAULT');
				assignIfProvided('IsLive', 'IS_LIVE');

				arrayStatus.forEach((status) => {
					const { Id } = status;
					cy.request({
						method: 'PATCH',
						url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/rubriccategory/1.0/${Id}`,
						headers: {
							'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
							Authorization: `${this.tokenType} ${this.token}`,
						},
						body: body,
					}).then((response) => {
						expect(response.status).to.be.eq(200);
					});
				});
			});
	}

	public createWorkingTimeDay(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let workingTimeDayCounter = Cypress.env('workingTimeDayCounter') || 0; // Start from 0 if not defined
		workingTimeDayCounter += 1;
		Cypress.env('workingTimeDayCounter', workingTimeDayCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('WorkingTimeModelId', 'WORKING_TIME_MODEL_ID');
		assignIfProvided('ValidFrom', 'VALID_FROM');
		assignIfProvided('WeekDayIndex', 'WEEK_DAY_INDEX');
		assignIfProvided('TargetHours', 'TARGET_HOURS');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/workingtimemodel/day/1.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_WORKING_TIME_DAY_ID_${workingTimeDayCounter}`, response.body.Id);

				cy.log(`API_WORKING_TIME_DAY_ID_${workingTimeDayCounter}:  ${response.body.Id}`);
			});
	}

	public createEmployee(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let employeeCounter = Cypress.env('employeeCounter') || 0; // Start from 0 if not defined
		employeeCounter += 1;
		Cypress.env('employeeCounter', employeeCounter); // Update counter in env globally

		const body: any = {
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
			Code: _common.generateRandomString(3),
			Description: _common.generateRandomString(3),
			IsLive: 'true',
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};
		assignIfProvided('CompanyId', 'COMPANY_ID');
		assignIfProvided('CompanyOperatingId', 'COMPANY_OPERATING_ID');
		assignIfProvided('FirstName', 'FIRST_NAME');
		assignIfProvided('FamilyName', 'FAMILY_NAME');
		assignIfProvided('Initials', 'INITIALS');

		assignIfProvided('AddressId', 'ADDRESS_ID');
		assignIfProvided('CountryId', 'COUNTRY_ID');
		assignIfProvided('TelephoneNumberId', 'TELEPHONE_NUMBER_ID');

		assignIfProvided('TelephoneNumberMobId', 'TELEPHONE_NUMBER_MOB_ID');
		assignIfProvided('Email', 'EMAIL');
		assignIfProvided('BirthDate', 'BIRTH_DATE');

		assignIfProvided('StartDate', 'START_DATE');
		assignIfProvided('TerminalDate', 'TERMINAL_DATE');
		assignIfProvided('IsCrewLeader', 'IS_CREW_LEADER');

		assignIfProvided('IsWhiteCollar', 'IS_WHITE_COLLAR');
		assignIfProvided('ShiftId', 'SHIFT_ID');
		assignIfProvided('GroupId', 'GROUP_ID');

		assignIfProvided('ProfessionalCategoryId', 'PROFESSIONAL_CATEGORY_ID');
		assignIfProvided('PaymentGroupId', 'PAYMENT_GROUP_ID');
		assignIfProvided('ClerkId', 'CLERK_ID');

		assignIfProvided('IsHiredLabor', 'IS_HIRED_LABOR');
		assignIfProvided('IsTimekeeper', 'IS_TIMEKEEPER');
		assignIfProvided('TimekeepingGroupId', 'TIMEKEEPING_GROUP_ID');

		assignIfProvided('CalendarId', 'CALENDAR_ID');
		assignIfProvided('EmployeeGroupId', 'EMPLOYEE_GROUP_ID');
		assignIfProvided('UserId', 'USER_ID');

		assignIfProvided('IsPayroll', 'IS_PAYROLL');
		assignIfProvided('EmployeeStatusId', 'EMPLOYEE_STATUS_ID');
		assignIfProvided('WorkingTimeModelId', 'WORKING_TIME_MODEL_ID');

		assignIfProvided('GenerateRecording', 'GENERATE_RECORDING');
		assignIfProvided('IsClocking', 'IS_CLOCKING');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/employee/4.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_EMPLOYEE_ID_${employeeCounter}`, response.body.Id);
				Cypress.env(`API_EMPLOYEE_CODE_${employeeCounter}`, response.body.Code);
				Cypress.env(`API_EMPLOYEE_FIRSTNAME_${employeeCounter}`, response.body.FirstName);
				Cypress.env(`API_EMPLOYEE_FAMILY_NAME_${employeeCounter}`, response.body.FamilyName);

				cy.log(`API_EMPLOYEE_ID_${employeeCounter}:  ${response.body.Id}`);
				cy.log(`API_EMPLOYEE_CODE_${employeeCounter}:  ${response.body.Code}`);
				cy.log(`API_EMPLOYEE_FIRSTNAME_${employeeCounter}:  ${response.body.FirstName}`);
				cy.log(`API_EMPLOYEE_FAMILY_NAME_${employeeCounter}:  ${response.body.FamilyName}`);
			});
	}

	public createEmployeeWTM(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let employeeWTMCounter = Cypress.env('employeeWTMCounter') || 0; // Start from 0 if not defined
		employeeWTMCounter += 1;
		Cypress.env('employeeWTMCounter', employeeWTMCounter); // Update counter in env globally

		const body: any = {};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('EmployeeId', 'EMPLOYEE_ID');
		assignIfProvided('WorkingTimeModelId', 'WORKING_TIME_MODEL_ID');
		assignIfProvided('ValidFrom', 'VALID_FROM');
		assignIfProvided('ValidTo', 'VALID_TO');
		assignIfProvided('HasOptedPayout', 'HAS_OPTED_PAYOUT');
		assignIfProvided('TimeSymbolId', 'TIME_SYMBOL_ID');
		assignIfProvided('IsFallbackWtmActive', 'IS_FALLBACK_WTM_ACTIVE');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/employee/employeewtm/2.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_EMPLOYEE_ID_${employeeWTMCounter}`, response.body.Id);
				Cypress.env(`API_EMPLOYEE_CODE_${employeeWTMCounter}`, response.body.Code);
				Cypress.env(`API_EMPLOYEE_FIRSTNAME_${employeeWTMCounter}`, response.body.FirstName);
				Cypress.env(`API_EMPLOYEE_FAMILY_NAME_${employeeWTMCounter}`, response.body.FamilyName);

				cy.log(`API_EMPLOYEE_ID_${employeeWTMCounter}:  ${response.body.Id}`);
				cy.log(`API_EMPLOYEE_CODE_${employeeWTMCounter}:  ${response.body.Code}`);
				cy.log(`API_EMPLOYEE_FIRSTNAME_${employeeWTMCounter}:  ${response.body.FirstName}`);
				cy.log(`API_EMPLOYEE_FAMILY_NAME_${employeeWTMCounter}:  ${response.body.FamilyName}`);
			});
	}

	public createTimeKeepingGroup(data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		let timekeepingGroupCounter = Cypress.env('timekeepingGroupCounter') || 0; // Start from 0 if not defined
		timekeepingGroupCounter += 1;
		Cypress.env('timekeepingGroupCounter', timekeepingGroupCounter); // Update counter in env globally

		const body: any = {
			TimesheetContextId: apiConstantData.ID.TIME_SHEET_CONTEXT_ID_STANDARD,
			Code: _common.generateRandomString(4),
			Description: _common.generateRandomString(4),
			IsLive: 'true',
		};

		const assignIfProvided = (fieldName, key) => {
			if (data[apiParameters.Keys[key]] !== undefined) {
				body[fieldName] = data[apiParameters.Keys[key]];
			}
		};

		assignIfProvided('CompanyId', 'COMPANY_ID');
		assignIfProvided('IsDefault', 'IS_DEFAULT');
		assignIfProvided('Sorting', 'SORTING');
		assignIfProvided('Icon', 'ICON');
		assignIfProvided('LanguageId', 'LANGUAGE_ID');

		return cy
			.request({
				method: 'POST',
				url: Cypress.env('Base_URL') + `services/basics/publicapi/company/timekeepinggroup/2.0`,
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
				body: body,
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
				Cypress.env(`API_TIMEKEEPING_GROUP_ID_${timekeepingGroupCounter}`, response.body.Id);
				Cypress.env(`API_TIMEKEEPING_GROUP_CODE_${timekeepingGroupCounter}`, response.body.Code);
				Cypress.env(`API_TIMEKEEPING_GROUP_DESC_${timekeepingGroupCounter}`, response.body.Description);

					cy.log(`API_TIMEKEEPING_GROUP_ID_${timekeepingGroupCounter}:  ${response.body.Id}`);
					cy.log(`API_TIMEKEEPING_GROUP_CODE_${timekeepingGroupCounter}:  ${response.body.Code}`);
					cy.log(`API_TIMEKEEPING_GROUP_DESC_${timekeepingGroupCounter}:  ${response.body.Description}`);

				});
	}

	public updateEmployeeWTM(workingTimeModelDesc, employeeId: string, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/usermanagement/publicapi/user/3.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const user = responseData.find((item) => item.EmployeeId === employeeId);

				if (user && user.WorkingTimeModelDesc === workingTimeModelDesc) {
					cy.log(`Found WTM: ${workingTimeModelDesc} for employeeId: ${employeeId}`);
					const { Id } = user;

					const body: any = {};

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

					assignIfProvided('WorkingTimeModelId', 'WORKING_TIME_MODEL_ID');
					assignIfProvided('ValidFrom', 'VALID_FROM');
					assignIfProvided('ValidTo', 'VALID_TO');
					assignIfProvided('HasOptedPayout', 'HAS_OPTED_PAYOUT');
					assignIfProvided('TimeSymbolId', 'TIME_SYMBOL_ID');
					assignIfProvided('IsFallbackWtmActive', 'IS_FALLBACK_WTM_ACTIVE');

					return cy
						.request({
							method: 'PATCH',
							url: Cypress.env('Base_URL') + `services/timekeeping/publicapi/employee/employeewtm/2.0/${Id}`,
							headers: {
								'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
								Authorization: `${this.tokenType} ${this.token}`,
							},
							body: body,
						})
						.then((response) => {
							expect(response.status).to.be.eq(200);
							cy.log(JSON.stringify(response.body, null, 2)); // Pretty-print JSON in log
						});
				} else {
					throw new Error(`No matching WTM: ${workingTimeModelDesc} found for employeeId: ${employeeId}`);
				}
			});
	}

	public updateCertificateStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/certificatestatus/1.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);

				if (!status) throw new Error('Status description not found');

				const { Id, Version } = status;

				// Set initial default values from the status data
				const body: any = { Version };

					const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};

				// Update only if specific fields are provided in data
				assignIfProvided('IsReadOnly', 'IS_READONLY');
				assignIfProvided('"Isescalation"', 'IS_ESCALATION');
				assignIfProvided('Isrequest', 'IS_REQUEST');
				assignIfProvided('IsDefault', 'IS_DEFAULT');
				assignIfProvided('IsoptionalUpwards', 'IS_OPTIONAL_UPWARDS');
				assignIfProvided('IsoptionalDownwards', 'IS_OPTIONAL_DOWNWARDS');
				assignIfProvided('IsLive', 'IS_LIVE');
				assignIfProvided('Sorting', 'SORTING');
				assignIfProvided('Description', 'DESCRIPTION');


				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/certificatestatus/1.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
				});
			});
	}

	public updateCertificateTypeStatusUnderCustomizing(statusDescription, data: DataCells): Cypress.Chainable<Cypress.Response<any>> {
		return cy
			.request({
				method: 'GET',
				url: Cypress.env('Base_URL') + 'services/basics/customizepublicapi/certificatetype/2.0',
				headers: {
					'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
					Authorization: `${this.tokenType} ${this.token}`,
				},
			})
			.then((response) => {
				expect(response.status).to.be.eq(200);
				const responseData = response.body;
				const status = responseData.find((item) => item.Description === statusDescription);
				const { Id, Version } = status;

				const body: any = { Version };

				const assignIfProvided = (fieldName, key) => {
						if (data[apiParameters.Keys[key]] !== undefined) {
							body[fieldName] = data[apiParameters.Keys[key]];
						}
					};


				assignIfProvided('IsDefault', 'IS_DEFAULT');
				assignIfProvided('IsBond', 'IS_BOND');
				assignIfProvided('IsEmitted', 'IS_EMITTED');
				assignIfProvided('HasCompany', 'HAS_COMPANY');
				assignIfProvided('HasCertificateDate', 'HAS_CERTIFICATE_DATE');
				assignIfProvided('HasIssuer', 'HAS_ISSUER');
				assignIfProvided('HasIssuerBp', 'HAS_ISSUER_BP');
				assignIfProvided('HasValidFrom', 'HAS_VALID_FROM');
				assignIfProvided('HasValidTo', 'HAS_VALID_TO');
				assignIfProvided('HasReference', 'HAS_REFERENCE');
				assignIfProvided('HasReferenceDate', 'HAS_REFERENCE_DATE');
				assignIfProvided('HasProject', 'HAS_PROJECT');
				assignIfProvided('HasContract', 'HAS_CONTRACT');
				assignIfProvided('HasAmount', 'HAS_AMOUNT');
				assignIfProvided('HasExpirationDate', 'HAS_EXPIRATION_DATE');
				assignIfProvided('Reference', 'REFERENCE');
				assignIfProvided('IsValued', 'IS_VALUE');
				assignIfProvided('IsLive', 'IS_LIVE');
				assignIfProvided('AccessrightDescriptorId', 'ACCESS_RIGHT_DESCRIPTION_ID');
				assignIfProvided('AccessrightdescriptorDesc', 'ACCESS_RIGHT_DESCRIPTOR_DESC');
				assignIfProvided('HasOrder', 'HAS_ORDER');
				assignIfProvided('IsForAccounting', 'IS_FOR_ACCOUNTING');
				assignIfProvided('LanguageId', 'LANGUAGE_ID');
				assignIfProvided('InsertedBy', 'INSERTED_BY');
				assignIfProvided('InsertedAt', 'INSERTED_AT');
				assignIfProvided('UpdatedBy', 'UPDATED_BY');
				assignIfProvided('UpdatedAt', 'UPDATED_AT');
				assignIfProvided('Version', 'VERSION');
				

				cy.request({
					method: 'PATCH',
					url: Cypress.env('Base_URL') + `services/basics/customizepublicapi/certificatetype/2.0/${Id}`,
					headers: {
						'Client-Context': `{'dataLanguageId':1,'language':'en','culture':'en-gb','secureClientRole':'${Cypress.env('secureClientRole')}'}`,
						Authorization: `${this.tokenType} ${this.token}`,
					},
					body: body,
				}).then((response) => {
					expect(response.status).to.be.eq(200);
				});
			});
	}
}
