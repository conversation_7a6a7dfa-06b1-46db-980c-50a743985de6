/*
 * Copyright(c) RIB Software GmbH
 */
import { CompleteIdentification } from '@libs/platform/common';
import { ISalesBillingQtoHeaderEntity } from '../entities/sales-billing-qto-header-entity.interface';

/**
 * Sales Billing QTO Header Complete Class
 */
export class SalesBillingQtoHeaderComplete extends CompleteIdentification<ISalesBillingQtoHeaderEntity> {
	public constructor(entity: ISalesBillingQtoHeaderEntity) {
		super(entity);
	}

	// Add any billing-specific complete class methods here if needed
}
