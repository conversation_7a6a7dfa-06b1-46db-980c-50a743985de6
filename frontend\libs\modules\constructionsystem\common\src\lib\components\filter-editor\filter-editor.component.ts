/*
 * Copyright(c) RIB Software GmbH
 */

import { AfterViewInit, Component, ElementRef, EventEmitter, forwardRef, inject, Input, Output, ViewChild } from '@angular/core';
import { Compartment, EditorState } from '@codemirror/state';
import { EditorView, basicSetup } from 'codemirror';
import { autocompletion, CompletionContext, CompletionSource } from '@codemirror/autocomplete';
import { lineNumbers, highlightActiveLine, } from '@codemirror/view';
import { linter } from '@codemirror/lint';
import { PlatformHttpService, PlatformTranslateService } from '@libs/platform/common';
import { IFieldKeyDef, IFilterMethond, IFilterScript, IProperty, IPropertyDef } from '../../model/entities/construction-system-common-filter-editor-entity.interface';
import { ConstructionsystemCommonPropertyValueTypeDataService } from '../../service/constructionsystem-common-property-value-type-data.service';
import { CodeMirrorInstance, Token } from '../../service/construction-system-common-filter-editor-hint-data-service';
import { ConstructionSystemCommonFilterEditorLinterDataService } from '../../service/construction-system-common-filter-editor-linter-data.service';
import { syntaxHighlighting, syntaxTree, defaultHighlightStyle } from '@codemirror/language';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
	selector: 'constructionsystem-common-filter-editor',
	templateUrl: './filter-editor.component.html',
	styleUrls: ['./filter-editor.component.scss'],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(()=> FilterEditorComponent),
			multi: true
		}
	]
})
export class FilterEditorComponent implements AfterViewInit, ControlValueAccessor{
	@ViewChild('container')
	private container!: ElementRef;
	public view?: EditorView;
	public state!: EditorState;
	public doccontent: string = '';

	private readonly http = inject(PlatformHttpService);
	public methods: IFilterMethond[] = [];
	public propertyDef: IProperty[] = [];
	public fieldKeyDef: IProperty[] = [];
	private readonly translate = inject(PlatformTranslateService);
	private readonly valueTypeService = inject(ConstructionsystemCommonPropertyValueTypeDataService);
	private readonly linterService = inject(ConstructionSystemCommonFilterEditorLinterDataService);


	@Input()
	public set model(value: string) {
		if (this.doccontent != value) {
			this.doccontent = value;
			if (this.view) {
				this.updateDoc(value);
			}
		}
	}

	private updateDoc(text: string) {
		if (this.view) {
			const transaction = this.view.state.update({changes: {from: 0, to: this.view.state.doc.toString().length, insert: text}});
			this.view.dispatch(transaction);
		}
	}

	@Output()
	public modelChange = new EventEmitter<string>();

	public get value() {
		return this.doccontent;
	}

	//todo:lius fix error
	private initTips() {
		const filterUrl = 'constructionsystem/common/src/lib/components/filter-editor/filterscript.json';//todo:lius error uri
		const getPropertyDefUrl = 'model/administration/propertykey/listwithvaluetype';
		const getFieldKeyDefUrl = 'constructionsystem/master/selectionstatement/getfieldkeylist';
		this.getFilterScript(filterUrl);
		this.getPropertyDef(getPropertyDefUrl);
		this.getFieldKeyDef(getFieldKeyDefUrl);
	}

	private trim(str: string) {
		const reg = new RegExp(/\s+/);
		str = str.replace(reg, '');
		return str;
	}

	private async getFilterScript(filterUrl: string) {
		const resp = await this.http.get<IFilterScript[]>(filterUrl);
		const funcReg = new RegExp(/fn\(([\w\W]*[^)]*)\)(\s*->\s*([\w\W]+))*/);
		for (const m in resp) {
			const method: IFilterMethond = {name: m, paramCount: 0, params: [], resultType: '', text: m, description: ''};
			const dm = resp[m];
			if (funcReg.test(dm['!type'])) {
				const match = funcReg.exec(dm['!type']);
				if (match && match[1]) {
					const p = match[1].split(',');
					method.paramCount = p.length;
					for (let n = 0, plen = p.length; n < plen; n++) {
						const pt = p[n].split(':');
						method.params.push({name: this.trim(pt[0]), type: this.trim(pt[1])});
					}
				}
				if (match && match[3]) {
					method.resultType = match[3];
				}
				method.description = this.translate.instant(dm['!doc'] || '').text;
				this.methods[m] = method;
			}
		}
	}

	private async getPropertyDef(getPropertyDefUrl: string) {
		const resp = await this.http.get<IPropertyDef[]>(getPropertyDefUrl);
		const propertyDef: IProperty[] = [];
		if (resp) {
			for (let j = 0, l = resp.length; j < l; j++) {
				const prop: IProperty = {type: '', name: '', text: '', description: ''};
				const p = resp[j];
				prop.type = p.ValueType.toString();
				prop.name = p.PropertyName;
				prop.text = '[' + p.PropertyName + ']';
				prop.description = this.valueTypeService.getValueTypeDescription(prop.type);
				propertyDef.push(prop);
			}
		}
		this.propertyDef = propertyDef;
	}

	private async getFieldKeyDef(getFieldKeyDefUrl: string) {
		const resp = await this.http.get<IFieldKeyDef[]>(getFieldKeyDefUrl);
		const fieldKeyDef: IProperty[] = [];
		if (resp) {
			for (let j = 0, l = resp.length; j < l; j++) {
				const prop = {type: '', name: '', text: '', description: ''};
				const p = resp[j];
				prop.type = p.valueType.toString();
				prop.name = p.propertyName;
				prop.text = '[' + p.propertyName + ']';
				prop.description = this.valueTypeService.getValueTypeDescription(prop.type);
				fieldKeyDef.push(prop);
			}
		}
		this.fieldKeyDef = fieldKeyDef;
	}

	private customHint: CompletionSource = (context: CompletionContext) => {
		const word: { from: number, to: number, text: string } = context.matchBefore(/\w*/) || {from: 0, to: 0, text: ''};
		if (!word || word.from === word.to) {
			return null;
		}
		const list = this.fieldKeyDef.concat(this.propertyDef);
		return {
			from: word.from,
			options: list.map((p) => ({
				label: p.name,
				type: p.type,
				info: p.description
			}))
		};
	};

	private customLinter = linter((view) => {
		const option: CodeMirrorInstance = {
			lineCount: () => view.state.doc.lines,
			getLine: line => view.state.doc.line(line).text,
			getLineTokens: line => this.getLineTokens(line),
			options: {}
		};
		return this.linterService.validator(view.state.doc.toString(), option, view.state);
	});

	private getLineTokens = (lineNum: number) => {
		const line = this.state.doc.line(lineNum);
		const tokens: Token[] = [];
		const tree = syntaxTree(this.state);

		tree.iterate({
			from: line.from,
			to: line.to,
			enter: node => {
				if (node.node.firstChild) {
					return;
				}

				const from = Math.max(node.from, line.from);
				const to = Math.min(node.to, line.to);
				if (from >= to) {
					return;
				}

				tokens.push({
					type: node.type.name,
					string: this.state.sliceDoc(from, to),
					start: from - line.from,
					end: to - line.from,
					line: line.number - 1
				});
			}
		});

		return tokens;
	};

	public ngAfterViewInit(): void {
		this.view = new EditorView({
			doc: this.doccontent,
			parent: this.container.nativeElement,
			extensions: [
				basicSetup,
				lineNumbers(),
				highlightActiveLine(),
				syntaxHighlighting(defaultHighlightStyle),
				this.readOnlyCompartment.of(EditorState.readOnly.of(this.isDisabled)),
				autocompletion({
					override: [
						this.customHint
					]
				}),
				this.customLinter,
				EditorView.updateListener.of(v => {
					if (v.docChanged && !this.isDisabled) {
						this.doccontent = v.state.doc.toString();
						this.onChange(this.doccontent);
						this.modelChange.emit(this.doccontent);
					}
				})
			]
		});
	}

	private onChange = (value: string) => {};
	private onTouched = () => {};

	public writeValue(value: string): void {
		if (value !== this.doccontent) {
			this.doccontent = value || '';
			if (this.view) {
				this.updateDoc(this.doccontent);
			}
		}
	}

	public registerOnChange(fn: (value: string) => void): void {
		this.onChange = fn;
	}

	public registerOnTouched(fn: () => void): void {
		this.onTouched = fn;
	}

	private isDisabled = false;
	private readOnlyCompartment = new Compartment();
	public setDisabledState(isDisabled: boolean): void {
		this.isDisabled = isDisabled;

		if (this.view) {
			this.view.dispatch({
				effects: this.readOnlyCompartment.reconfigure(
					EditorState.readOnly.of(isDisabled)
				)
			});

			if (isDisabled) {
				this.container.nativeElement.classList.add('disabled');
			} else {
				this.container.nativeElement.classList.remove('disabled');
			}
		}
	}

}
