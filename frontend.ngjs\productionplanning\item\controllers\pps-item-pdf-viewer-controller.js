(angular => {
    'use strict';
    const moduleName = 'productionplanning.item';
    angular.module(moduleName).controller('ppsItemPDFViewerController', ppsItemPDFViewerController);
    ppsItemPDFViewerController.$inject = ['$scope', '$controller', 'ppsCommonDocumentAnnotationExtension', 'productionplanningItemDataService', 'modelWdeViewerSelectionService'];
    function ppsItemPDFViewerController($scope, $controller,  ppsCommonDocumentAnnotationExtension, productionplanningItemDataService, modelWdeViewerSelectionService) {
        const baseController = 'modelWdeViewerIgeController';
        angular.extend(this, $controller(baseController, { $scope: $scope }));

        let isLoaded = false;
        $scope.$on('model.wdeviewer.loading', () => {
            isLoaded = false;
        });
        $scope.$on('model.wdeviewer.loaded', () => {
            isLoaded = true;
        });

        function updateTools() {
            $scope.tools.update();
        }

        function onItemSelectionChanged(){
            updateTools();
            ppsCommonDocumentAnnotationExtension.onItemSelectionChanged();
        }

        function onSelectedPDFChanged() {
            isLoaded = false;
            updateTools();
            ppsCommonDocumentAnnotationExtension.clearRecordedSelections();
        }

        const scopeFn = {
            isLoaded: () => isLoaded,
        };
        ppsCommonDocumentAnnotationExtension.setScopeFn(scopeFn);
        productionplanningItemDataService.registerSelectionChanged(onItemSelectionChanged);
        modelWdeViewerSelectionService.selectionChanged.register(onSelectedPDFChanged);

        $scope.$on('$destroy', () => {
            productionplanningItemDataService.unregisterSelectionChanged(onItemSelectionChanged);
            modelWdeViewerSelectionService.selectionChanged.unregister(onSelectedPDFChanged);
        });
    }

})(angular);