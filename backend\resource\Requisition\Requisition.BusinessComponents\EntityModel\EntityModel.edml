﻿<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2012" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="BAS_DDTEMPIDS" EntityType="BusinessComponents.Store.BAS_DDTEMPIDS" store:Type="Tables" Table="BAS_DDTEMPIDS" />
          <EntitySet Name="RES_REQUISITIONs" EntityType="BusinessComponents.Store.RES_REQUISITION" store:Type="Tables" Table="RES_REQUISITION" />
          <EntitySet Name="RES_REQUISITION2SKILLs" EntityType="BusinessComponents.Store.RES_REQUISITION2SKILL" store:Type="Tables" Table="RES_REQUISITION2SKILL" />
          <EntitySet Name="RES_REQUISITIONDOCUMENTs" EntityType="BusinessComponents.Store.RES_REQUISITIONDOCUMENT" store:Type="Tables" Table="RES_REQUISITIONDOCUMENT" />
          <EntitySet Name="RES_REQUISITIONITEMs" EntityType="BusinessComponents.Store.RES_REQUISITIONITEM" store:Type="Tables" Table="RES_REQUISITIONITEM" />
          <EntitySet Name="PRC_STOCKTOTAL_Vs" EntityType="BusinessComponents.Store.PRC_STOCKTOTAL_V" store:Type="Views" Table="PRC_STOCKTOTAL_V" />
          <EntitySet Name="RES_REQUISITIONINFO_Vs" EntityType="BusinessComponents.Store.RES_REQUISITIONINFO_V" store:Type="Views" Table="RES_REQUISITIONINFO_V" />
          <EntitySet Name="RES_REQUISITION2REQUISITIONs" EntityType="BusinessComponents.Store.RES_REQUISITION2REQUISITION" store:Type="Tables" Table="RES_REQUISITION2REQUISITION" />
          <EntitySet Name="RES_GEN_REQ_FROM_EST_Vs" EntityType="BusinessComponents.Store.RES_GEN_REQ_FROM_EST_V" store:Type="Views" Table="RES_GEN_REQ_FROM_EST_V" />
          <EntitySet Name="RES_GEN_REQ_SKILL_Vs" EntityType="BusinessComponents.Store.RES_GEN_REQ_SKILL_V" store:Type="Views" Table="RES_GEN_REQ_SKILL_V" />
          <EntitySet Name="RES_GEN_REQ_TYP_Vs" EntityType="BusinessComponents.Store.RES_GEN_REQ_TYP_V" store:Type="Views" Table="RES_GEN_REQ_TYP_V" />
          <EntitySet Name="RES_REQUI_2_REQUI_INFO_Vs" EntityType="BusinessComponents.Store.RES_REQUI_2_REQUI_INFO_V" store:Type="Views" Table="RES_REQUI_2_REQUI_INFO_V" />
          <EntitySet Name="RES_REQ_CHANGE_REQDATE_INFO_Vs" EntityType="BusinessComponents.Store.RES_REQ_CHANGE_REQDATE_INFO_V" store:Type="Views" Table="RES_REQ_CHANGE_REQDATE_INFO_V" />
          <AssociationSet Name="RES_REQUISITION_FK03" Association="BusinessComponents.Store.RES_REQUISITION_FK03">
            <End Role="RES_REQUISITION" EntitySet="RES_REQUISITIONs" />
            <End Role="RES_REQUISITION2SKILL" EntitySet="RES_REQUISITION2SKILLs" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK04" Association="BusinessComponents.Store.RES_REQUISITION_FK04">
            <End Role="RES_REQUISITION" EntitySet="RES_REQUISITIONs" />
            <End Role="RES_REQUISITIONDOCUMENT" EntitySet="RES_REQUISITIONDOCUMENTs" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK09" Association="BusinessComponents.Store.RES_REQUISITION_FK09">
            <End Role="RES_REQUISITION" EntitySet="RES_REQUISITIONs" />
            <End Role="RES_REQUISITION1" EntitySet="RES_REQUISITIONs" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK08" Association="BusinessComponents.Store.RES_REQUISITION_FK08">
            <End Role="RES_REQUISITION" EntitySet="RES_REQUISITIONs" />
            <End Role="RES_REQUISITIONITEM" EntitySet="RES_REQUISITIONITEMs" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK25" Association="BusinessComponents.Store.RES_REQUISITION_FK25">
            <End Role="RES_REQUISITION" EntitySet="RES_REQUISITIONs" />
            <End Role="RES_REQUISITION2REQUISITION" EntitySet="RES_REQUISITION2REQUISITIONs" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK26" Association="BusinessComponents.Store.RES_REQUISITION_FK26">
            <End Role="RES_REQUISITION" EntitySet="RES_REQUISITIONs" />
            <End Role="RES_REQUISITION2REQUISITION" EntitySet="RES_REQUISITION2REQUISITIONs" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="BAS_DDTEMPIDS">
          <Key>
            <PropertyRef Name="REQUESTID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="REQUESTID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="KEY1" Type="int" />
          <Property Name="KEY2" Type="int" />
          <Property Name="KEY3" Type="int" />
        </EntityType>
        <EntityType Name="RES_REQUISITION">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="PRJ_PROJECT_FK" Type="int" Nullable="false" />
          <Property Name="RES_RESOURCE_FK" Type="int" />
          <Property Name="RES_REQUISITIONSTATUS_FK" Type="int" Nullable="false" />
          <Property Name="RES_TYPE_FK" Type="int" />
          <Property Name="QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="BAS_UOM_FK" Type="int" Nullable="false" />
          <Property Name="REQUESTED_FROM" Type="datetime" Nullable="false" />
          <Property Name="REQUESTED_TO" Type="datetime" Nullable="false" />
          <Property Name="LGM_JOB_FK" Type="int" Nullable="false" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="PSD_ACTIVITY_FK" Type="int" />
          <Property Name="TRS_REQUISITION_FK" Type="int" />
          <Property Name="PPS_EVENT_FK" Type="int" />
          <Property Name="ISLINKEDFIXTORESERVATION" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="SEARCH_PATTERN" Type="nvarchar" MaxLength="450" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="RESERVED_FROM" Type="datetime" />
          <Property Name="RESERVED_TO" Type="datetime" />
          <Property Name="USERDEFINEDTEXT01" Type="nvarchar" MaxLength="255" />
          <Property Name="USERDEFINEDTEXT02" Type="nvarchar" MaxLength="255" />
          <Property Name="USERDEFINEDTEXT03" Type="nvarchar" MaxLength="255" />
          <Property Name="USERDEFINEDTEXT04" Type="nvarchar" MaxLength="255" />
          <Property Name="USERDEFINEDTEXT05" Type="nvarchar" MaxLength="255" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="MDC_MATERIAL_FK" Type="int" />
          <Property Name="BAS_RESOURCE_CONTEXT_FK" Type="int" />
          <Property Name="REMARK" Type="nvarchar" MaxLength="2000" />
          <Property Name="RESERVATION_ID" Type="int" />
          <Property Name="BAS_CLERKOWNER_FK" Type="int" />
          <Property Name="BAS_CLERKRESPONSIBLE_FK" Type="int" />
          <Property Name="BAS_SITE_FK" Type="int" />
          <Property Name="PRJ_STOCK_FK" Type="int" />
          <Property Name="RES_REQUISITIONGROUP_FK" Type="int" />
          <Property Name="RES_REQUISITIONPRIORITY_FK" Type="int" />
          <Property Name="RES_REQUISITION_TYPE_FK" Type="int" />
          <Property Name="RES_REQUISITION_FK" Type="int" />
          <Property Name="LGM_JOBPREFERRED_FK" Type="int" />
          <Property Name="PRJ_CHANGE_FK" Type="int" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="BAS_RUBRIC_CATEGORY_FK" Type="int" />
          <Property Name="EST_HEADER_FK" Type="int" />
          <Property Name="EST_LINE_ITEM_FK" Type="int" />
          <Property Name="EST_RESOURCE_FK" Type="int" />
          <Property Name="RES_EXECPLANNERITEM_FK" Type="int" />
          <Property Name="RES_PROJECTTIMESLOT_FK" Type="int" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="ETM_WORKOPERATIONTYPE_FK" Type="int" />
          <Property Name="PRJ_DROPPOINT_FK" Type="int" />
          <Property Name="RES_SKILL_FK" Type="int" />
          <Property Name="RES_TYPEALTERNATIVE_FK" Type="int" />
          <Property Name="RES_TYPEFROMESTIMATE_FK" Type="int" />
          <Property Name="ISBOTTLENECK" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="QUANTITYFROMESTIMATE" Type="numeric" Precision="19" Scale="6" />
          <Property Name="ETM_WORKOPERATIONTYPEEST_FK" Type="int" />
          <Property Name="ISDELETED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISARCHIVED" Type="bit" Nullable="false" devart:DefaultValue="0" />
        </EntityType>
        <EntityType Name="RES_REQUISITION2SKILL">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="RES_SKILL_FK" Type="int" Nullable="false" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="USERDEFTEXT01" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFTEXT02" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFTEXT03" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFTEXT04" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFTEXT05" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFNUMBER01" Type="numeric" Precision="19" Scale="7" />
          <Property Name="USERDEFNUMBER02" Type="numeric" Precision="19" Scale="7" />
          <Property Name="USERDEFNUMBER03" Type="numeric" Precision="19" Scale="7" />
          <Property Name="USERDEFNUMBER04" Type="numeric" Precision="19" Scale="7" />
          <Property Name="USERDEFNUMBER05" Type="numeric" Precision="19" Scale="7" />
          <Property Name="USERDEFDATE01" Type="datetime" />
          <Property Name="USERDEFDATE02" Type="datetime" />
          <Property Name="USERDEFDATE03" Type="datetime" />
          <Property Name="USERDEFDATE04" Type="datetime" />
          <Property Name="USERDEFDATE05" Type="datetime" />
        </EntityType>
        <EntityType Name="RES_REQUISITIONDOCUMENT">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="BAS_DOCUMENT_TYPE_FK" Type="int" Nullable="false" />
          <Property Name="DATE" Type="date" Nullable="false" />
          <Property Name="BARCODE" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_FILEARCHIVEDOC_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="URL" Type="nvarchar" MaxLength="2000" />
        </EntityType>
        <EntityType Name="RES_REQUISITIONITEM">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="MDC_MATERIAL_FK" Type="int" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="RESERVATION_ID" Type="int" />
          <Property Name="PRJ_STOCK_FK" Type="int" />
          <Property Name="QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="BAS_UOM_FK" Type="int" Nullable="false" />
          <Property Name="USERDEFINEDTEXT01" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINEDTEXT02" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINEDTEXT03" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINEDTEXT04" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINEDTEXT05" Type="nvarchar" MaxLength="252" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="PRC_STOCKTOTAL_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="STOCK2MAT_ID" Type="int" />
          <Property Name="PRJ_STOCK_FK" Type="int" Nullable="false" />
          <Property Name="CATALOG_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="CATALOG_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="MATERIAL_CODE" Type="nvarchar" MaxLength="20" />
          <Property Name="CATALOG_ID" Type="int" />
          <Property Name="PRC_STRUCTURE_FK" Type="int" />
          <Property Name="MATERIAL_GROUP_ID" Type="int" />
          <Property Name="MDC_MATERIAL_FK" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION1" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_BLOBS_FK" Type="int" />
          <Property Name="DESCRIPTION2" Type="nvarchar" MaxLength="252" />
          <Property Name="MODELNAME" Type="nvarchar" MaxLength="252" />
          <Property Name="BRAND_ID" Type="int" />
          <Property Name="BRAND_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="TOTAL" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="PROVISION_TOTAL" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="PROVISION_PERCENT" Type="numeric" Precision="10" Scale="2" />
          <Property Name="PROVISION_PERUOM" Type="numeric" Precision="19" Scale="7" />
          <Property Name="ISLOTMANAGEMENT" Type="bit" />
          <Property Name="MIN_QUANTITY" Type="numeric" Precision="19" Scale="6" />
          <Property Name="MAX_QUANTITY" Type="numeric" Precision="19" Scale="6" />
          <Property Name="UOM" Type="nvarchar" MaxLength="16" />
          <Property Name="QUANTITY_RECEIPT" Type="numeric" Precision="38" Scale="6" />
          <Property Name="QUANTITY_CONSUMED" Type="numeric" Precision="38" Scale="6" />
          <Property Name="TOTAL_RECEIPT" Type="numeric" Precision="38" Scale="7" />
          <Property Name="TOTAL_CONSUMED" Type="numeric" Precision="38" Scale="7" />
          <Property Name="QUANTITY_RESERVED" Type="numeric" Precision="38" Scale="6" />
          <Property Name="QUANTITY_AVAILABLE" Type="numeric" Precision="38" Scale="6" />
          <Property Name="PROVISION_RECEIPT" Type="numeric" Precision="38" Scale="7" />
          <Property Name="PROVISION_CONSUMED" Type="numeric" Precision="38" Scale="7" />
          <Property Name="EXPENSE_TOTAL" Type="numeric" Precision="38" Scale="7" />
          <Property Name="PPS_PRODUCT_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="RES_REQUISITIONINFO_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="ISREADONLY" Type="bit" Nullable="false" />
          <Property Name="PRJ_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="PROJECTNO" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="PROJECT_NAME" Type="nvarchar" MaxLength="252" />
          <Property Name="PROJECT_NAME2" Type="nvarchar" MaxLength="252" />
          <Property Name="PRJ_CURRENCY_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_BUSINESSPARTNER_FK" Type="int" />
          <Property Name="PRJ_ISLIVE" Type="bit" Nullable="false" />
          <Property Name="PRJ_USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="PRJ_USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="PRJ_ADDRESS_FK" Type="int" />
          <Property Name="PRJ_STOCKPROJECT_FK" Type="int" />
          <Property Name="PSD_SCHEDULE_FK" Type="int" />
          <Property Name="JOB_CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="JOB_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="LGM_JOBGROUP_FK" Type="int" />
          <Property Name="JOB_SITE_FK" Type="int" />
          <Property Name="JOB_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="JOB_USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="JOB_USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="JOB_VALIDFROM" Type="date" />
          <Property Name="JOB_VALIDTO" Type="date" />
          <Property Name="JOB_BUSINESSPARTNER_FK" Type="int" />
          <Property Name="JOB_DELIVERYADDR_REMARK" Type="nvarchar" MaxLength="2000" />
          <Property Name="JOB_BLOBSDELIVERYADDR_FK" Type="int" />
          <Property Name="JOB_ADDRESS_FK" Type="int" />
          <Property Name="JOB_PLANT_FK" Type="int" />
          <Property Name="JOB_ISPROJECTDEFAULT" Type="bit" Nullable="false" />
          <Property Name="JOB_ISLIVE" Type="bit" Nullable="false" />
          <Property Name="JOB_TYPE_FK" Type="int" Nullable="false" />
          <Property Name="JOB_ISMAINTENANCE" Type="bit" Nullable="false" />
          <Property Name="JOB_CALENDAR_FK" Type="int" Nullable="false" />
          <Property Name="JOB_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="JOB_RUBRICCATEGORY_FK" Type="int" Nullable="false" />
          <Property Name="JOB_CONTEXT_FK" Type="int" Nullable="false" />
          <Property Name="JOB_COSTCODEPRICELIST_FK" Type="int" />
          <Property Name="JOB_COSTCODEPRICEVER_FK" Type="int" />
          <Property Name="RES_TYPEISBULK" Type="bit" />
          <Property Name="RES_TYPEISSMALLTOOLS" Type="bit" />
          <Property Name="LGM_DISPATCHER_GROUP_FK" Type="int" />
          <Property Name="RES_SITE_FK" Type="int" />
          <Property Name="RESERVEDFROM" Type="datetime" />
          <Property Name="ONSITEFROM" Type="datetime" />
          <Property Name="RESERVEDTO" Type="datetime" />
          <Property Name="ONSITETO" Type="datetime" />
          <Property Name="PRJ_CHANGE_FK" Type="int" />
          <Property Name="PRJ_CHANGESTATUS_FK" Type="int" />
          <Property Name="ISTIMEENHANCEMENT" Type="bit" />
          <Property Name="RES_REQUISITION2REQUISITION_FK" Type="int" />
          <Property Name="CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="ISREQUESTBIZPARTNER" Type="bit" />
          <Property Name="ISREQUESTPRJDOC" Type="bit" />
          <Property Name="PLANNEDSTART" Type="date" />
          <Property Name="PLANNEDEND" Type="date" />
          <Property Name="ESTIMATEQUANTITY" Type="numeric" Precision="19" Scale="6" />
          <Property Name="EST_WORKOPERATIONTYPE_FK" Type="int" />
          <Property Name="LGM_DISPATCHHEADERMAP_FK" Type="int" />
          <Property Name="RES_REQUISITION_FORMDATA_FK" Type="int" />
          <Property Name="RES_REQUISITION_MAPPER_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="RES_REQUISITION2REQUISITION">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="RES_REQUISITIONLINKED_FK" Type="int" Nullable="false" />
          <Property Name="ISREFERENCE" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISTIMEENHANCEMENT" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="RES_GEN_REQ_FROM_EST_V">
          <Key>
            <PropertyRef Name="PRJ_PROJECT_FK" />
            <PropertyRef Name="BAS_COMPANY_FK" />
            <PropertyRef Name="PRJ_ESTIMATE_FK" />
            <PropertyRef Name="EST_HEADER_FK" />
            <PropertyRef Name="EST_LINE_ITEM_FK" />
            <PropertyRef Name="EST_RESOURCE_FK" />
            <PropertyRef Name="EST_RESOURCE_TYPE" />
            <PropertyRef Name="RES_BAS_UOM_FK" />
          </Key>
          <Property Name="PRJ_PROJECT_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_PROJECT_NAME" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_ESTIMATE_FK" Type="int" Nullable="false" />
          <Property Name="EST_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="EST_HEADER_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="EST_LINE_ITEM_FK" Type="int" Nullable="false" />
          <Property Name="EST_LINE_ITEM_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="EST_ASSEMBLY_LINE_ITEM_FK" Type="int" />
          <Property Name="EST_ASSEMBLY_LINE_ITEM_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="EST_RESOURCE_FK" Type="int" Nullable="false" />
          <Property Name="EST_RESOURCE_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="EST_RESOURCE_TYPE" Type="int" Nullable="false" />
          <Property Name="EST_ASSEMBLY_FK" Type="int" />
          <Property Name="RES_QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="RES_BAS_UOM_FK" Type="int" Nullable="false" />
          <Property Name="LGM_JOB_FK" Type="int" />
          <Property Name="LGM_JOB_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="CAL_CALENDAR_FK" Type="int" />
          <Property Name="BAS_UOMHOUR_FK" Type="int" />
          <Property Name="BAS_UOMDAY_FK" Type="int" />
          <Property Name="BAS_UOMMONTH_FK" Type="int" />
          <Property Name="WORKHOURSPERDAY" Type="numeric" Precision="15" Scale="6" />
          <Property Name="WORKHOURSPERMONTH" Type="numeric" Precision="15" Scale="6" />
          <Property Name="ETM_PLANT_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="ETM_PLANTGROUP_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="ETM_PLANT_FK" Type="int" />
          <Property Name="ETM_PLANT_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="ETM_PLANT_BAS_UOM_FK" Type="int" />
          <Property Name="ETM_PLANTGROUP_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="MDC_CONTROLLINGUNIT_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_CONTROLLINGUNIT_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="MDC_CONTROLLINGUNIT_PLANNED_START" Type="date" />
          <Property Name="RES_TYPE_FK" Type="int" />
          <Property Name="RES_TYPE_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="LGM_DISPATCHER_GROUP_FK" Type="int" />
          <Property Name="RES_WOT_BAS_UOM_FK" Type="int" />
          <Property Name="ETM_WORKOPERATIONTYPE_FK" Type="int" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
        </EntityType>
        <EntityType Name="RES_GEN_REQ_SKILL_V">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="RES_TYPE_FK" />
            <PropertyRef Name="RES_SKILL_FK" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="RES_TYPE_FK" Type="int" Nullable="false" />
          <Property Name="RES_SKILL_FK" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="RES_GEN_REQ_TYP_V">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="RES_TYPE_FK" />
            <PropertyRef Name="RES_SKILL_FK" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="RES_TYPE_FK" Type="int" Nullable="false" />
          <Property Name="DURATION" Type="int" />
          <Property Name="BAS_UOMDAY_FK" Type="int" />
          <Property Name="RES_TYPEREQUESTED_FK" Type="int" />
          <Property Name="LGM_DISPATCHER_GROUP_FK" Type="int" />
          <Property Name="ISPLANTREQUESTED" Type="bit" Nullable="false" />
          <Property Name="ISDRIVERREQUESTED" Type="bit" Nullable="false" />
          <Property Name="ISCRANEREQUESTED" Type="bit" Nullable="false" />
          <Property Name="ISTRUCKREQUESTED" Type="bit" Nullable="false" />
          <Property Name="RES_SKILL_OFREQUESTED_FK" Type="int" />
          <Property Name="ISREQUESTEDENTIREPERIOD" Type="bit" Nullable="false" />
          <Property Name="NECESSARYOPERATORS" Type="int" Nullable="false" />
          <Property Name="CAL_CALENDAR_FK" Type="int" Nullable="false" />
          <Property Name="RES_SKILL_RES_TYPE_FK" Type="int" />
          <Property Name="RES_SKILL_FK" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="RES_REQUI_2_REQUI_INFO_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="PARENT_RES_REQUISITION_FK" Type="int" />
          <Property Name="PRIMARY_RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="CHILD_RES_REQUISITION_FK" Type="int" />
        </EntityType>
        <EntityType Name="RES_REQ_CHANGE_REQDATE_INFO_V">
          <Key>
            <PropertyRef Name="PRIMARY_RES_REQUISITION_FK" />
          </Key>
          <Property Name="PARENT_RES_REQUISITION_FK" Type="int" />
          <Property Name="PRIMARY_RES_REQUISITION_FK" Type="int" Nullable="false" />
          <Property Name="RES_TYPE_FK" Type="int" />
          <Property Name="DURATION" Type="numeric" Precision="19" Scale="6" />
          <Property Name="PRIMARY_BAS_UOMDAY_FK" Type="int" />
          <Property Name="RES_TYPEREQUESTED_FK" Type="int" />
          <Property Name="LGM_DISPATCHER_GROUP_FK" Type="int" />
          <Property Name="ISREQUESTEDENTIREPERIOD" Type="bit" Nullable="false" />
          <Property Name="NECESSARYOPERATORS" Type="int" Nullable="false" />
          <Property Name="PARENT_DURATION" Type="int" />
          <Property Name="PARENT_BAS_UOMDAY_FK" Type="int" />
          <Property Name="PARENT_RES_TYPEREQUESTED_FK" Type="int" />
          <Property Name="PARENT_ISREQUESTEDENTIREPERIOD" Type="bit" Nullable="false" />
          <Property Name="PARENT_NECESSARYOPERATORS" Type="int" Nullable="false" />
          <Property Name="LGM_JOB_FK" Type="int" />
          <Property Name="LGM_JOB_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="CAL_CALENDAR_FK" Type="int" />
          <Property Name="BAS_UOMHOUR_FK" Type="int" />
          <Property Name="BAS_UOMDAY_FK" Type="int" />
          <Property Name="BAS_UOMMONTH_FK" Type="int" />
          <Property Name="WORKHOURSPERDAY" Type="numeric" Precision="15" Scale="6" />
          <Property Name="WORKHOURSPERMONTH" Type="numeric" Precision="15" Scale="6" />
          <Property Name="RES_QUANTITY" Type="numeric" Precision="19" Scale="6" />
          <Property Name="RES_WOT_BAS_UOM_FK" Type="int" />
          <Property Name="RES_SKILL_FK" Type="int" />
          <Property Name="IS_SKILL_DEMAND" Type="bit" />
          <Property Name="IS_TERTIARY_DEMAND" Type="bit" />
        </EntityType>
        <Association Name="RES_REQUISITION_FK03">
          <End Role="RES_REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="1" />
          <End Role="RES_REQUISITION2SKILL" Type="BusinessComponents.Store.RES_REQUISITION2SKILL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RES_REQUISITION">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="RES_REQUISITION2SKILL">
              <PropertyRef Name="RES_REQUISITION_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK04">
          <End Role="RES_REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="1" />
          <End Role="RES_REQUISITIONDOCUMENT" Type="BusinessComponents.Store.RES_REQUISITIONDOCUMENT" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RES_REQUISITION">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="RES_REQUISITIONDOCUMENT">
              <PropertyRef Name="RES_REQUISITION_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK09">
          <End Role="RES_REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="0..1" />
          <End Role="RES_REQUISITION1" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RES_REQUISITION">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="RES_REQUISITION1">
              <PropertyRef Name="RES_REQUISITION_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK08">
          <End Role="RES_REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="1" />
          <End Role="RES_REQUISITIONITEM" Type="BusinessComponents.Store.RES_REQUISITIONITEM" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RES_REQUISITION">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="RES_REQUISITIONITEM">
              <PropertyRef Name="RES_REQUISITION_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK25">
          <End Role="RES_REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="1" />
          <End Role="RES_REQUISITION2REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION2REQUISITION" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RES_REQUISITION">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="RES_REQUISITION2REQUISITION">
              <PropertyRef Name="RES_REQUISITION_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK26">
          <End Role="RES_REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION" Multiplicity="1" />
          <End Role="RES_REQUISITION2REQUISITION" Type="BusinessComponents.Store.RES_REQUISITION2REQUISITION" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RES_REQUISITION">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="RES_REQUISITION2REQUISITION">
              <PropertyRef Name="RES_REQUISITIONLINKED_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Resource.Requisition.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Resource.Requisition.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="DdTempIdsEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.DdTempIdsEntity" />
          <EntitySet Name="RequisitionEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" />
          <EntitySet Name="RequisitionRequiredSkillEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionRequiredSkillEntity" />
          <EntitySet Name="RequisitionDocumentEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionDocumentEntity" />
          <EntitySet Name="RequisitionitemEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionitemEntity" />
          <EntitySet Name="StockTotalVEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.StockTotalVEntity" />
          <EntitySet Name="RequisitionInformationEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionInformationEntity" />
          <EntitySet Name="Requisition2RequisitionEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.Requisition2RequisitionEntity" />
          <EntitySet Name="RequisitionGenReqFromEstVEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqFromEstVEntity" />
          <EntitySet Name="RequisitionGenReqSkillVEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqSkillVEntity" />
          <EntitySet Name="RequisitionGenReqTypVEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqTypVEntity" />
          <EntitySet Name="Requisition2RequisitionInfoVEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.Requisition2RequisitionInfoVEntity" />
          <EntitySet Name="RequisitionChangeReqdateInfoVEntities" EntityType="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionChangeReqdateInfoVEntity" />
          <AssociationSet Name="RES_REQUISITION_FK03Set" Association="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK03">
            <End Role="RequisitionEntity" EntitySet="RequisitionEntities" />
            <End Role="ResRequisition2skillEntities" EntitySet="RequisitionRequiredSkillEntities" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK04Set" Association="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK04">
            <End Role="RequisitionEntity" EntitySet="RequisitionEntities" />
            <End Role="ResRequisitiondocumentEntities" EntitySet="RequisitionDocumentEntities" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK09Set" Association="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK09">
            <End Role="RequisitionEntity_ResRequisitionFk" EntitySet="RequisitionEntities" />
            <End Role="RequisitionEntities_ResRequisitionFk" EntitySet="RequisitionEntities" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK08Set" Association="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK08">
            <End Role="RequisitionEntity" EntitySet="RequisitionEntities" />
            <End Role="ResRequisitionitemEntities" EntitySet="RequisitionitemEntities" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK25Set" Association="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK25">
            <End Role="RequisitionEntity_ResRequisitionFk" EntitySet="RequisitionEntities" />
            <End Role="Requisition2RequisitionEntities_ResRequisitionFk" EntitySet="Requisition2RequisitionEntities" />
          </AssociationSet>
          <AssociationSet Name="RES_REQUISITION_FK26Set" Association="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK26">
            <End Role="RequisitionEntity_ResRequisitionlinkedFk" EntitySet="RequisitionEntities" />
            <End Role="Requisition2RequisitionEntities_ResRequisitionlinkedFk" EntitySet="Requisition2RequisitionEntities" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="DdTempIdsEntity" ed:Guid="6902c783-fe1a-4395-9200-d7ba04c3ee9a" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="RequestId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="true" ed:Guid="fadd9d49-dad6-4f9d-aaea-b56f8bdf6269" />
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="06c83e50-aa8d-4e66-b740-2bac6cd8ce19" />
          <Property Name="Key1" Type="Int32" ed:ValidateRequired="false" ed:Guid="8a0be058-2e8f-496f-8f21-468d8e9915a5" />
          <Property Name="Key2" Type="Int32" ed:ValidateRequired="false" ed:Guid="daff8572-af11-451d-8ff8-681ab3420c98" />
          <Property Name="Key3" Type="Int32" ed:ValidateRequired="false" ed:Guid="fd5b57d3-3032-4da8-8aec-56524a89e0f2" />
        </EntityType>
        <EntityType Name="RequisitionEntity" ed:Guid="2fb85cfe-20fb-43c6-bd35-d3b2807017c4" ed:SupportsGrouping="True">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a900078d-ab46-4190-8d7c-ad7e8054d88c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ResourceContextFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="61ef332b-9142-47c7-8aae-413d267d8913">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="6a2e4b81-7854-42ba-b43e-ab06fcd44578">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9f03e3d8-3c11-40e4-89a2-dfa8911500ae">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProjectFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="51511f20-332e-44eb-a13b-aff4a84c1145">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ResourceFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="44e4a7a4-991c-4fd6-84be-64d46d2de4d5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionStatusFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="69e13cc0-cedb-4bde-877f-f6fb9e428058">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="1c4e95ff-896f-42a5-82ea-828001a87cba">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Quantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="f168599e-535c-4773-b311-a033cddc8ead">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UomFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="15b7ffdf-2e1b-4841-b595-8a9b8e46c60c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequestedFrom" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="3ae0698d-657c-4cf4-b444-8bd288ddca64">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">datetimeutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequestedTo" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="e8a75342-e415-4332-acf0-c9caffa7cfb9">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">datetimeutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ReservedFrom" Type="DateTime" ed:ValidateRequired="false" ed:Guid="87e2f0fd-2ce7-4d66-aa78-355f8ea95680">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ReservedTo" Type="DateTime" ed:ValidateRequired="false" ed:Guid="5419e791-c103-4fca-83b5-35f4801f120a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="JobFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ac7580b3-547c-44e3-8fc6-74f71e706bf2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CommentText" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="a1659bbb-2b70-45ae-805b-6c95074d8a84">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">comment</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ActivityFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="ff6a92fe-4be2-4dff-8332-555e6c74ede2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TrsRequisitionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f58f9c88-9d27-4296-8e54-5d30a950cb32">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PpsEventFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="028eee73-8848-4c73-96c0-b78ef6d1da6e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsLinkedFixToReservation" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="22d71049-9538-4d0d-958b-5667dc8d2fcf">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ReservationId" Type="Int32" ed:ValidateRequired="false" ed:Guid="4d18cb7f-9588-4ae2-8d88-5b49cb07dde3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText01" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="f6de20ab-9c04-41ea-a75d-2e3f83c55cd6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText02" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="effa1639-e144-434a-a6fe-2e04ce312cc3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText03" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="2414690d-cbcf-4883-bba4-38237bdf05e2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText04" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="6db84313-36be-4184-aef2-4c11ea6fb75a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText05" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="b2887306-956c-4c1c-8eae-de2a426ea8e8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaterialFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f6d7fce9-5abc-4af1-9709-3b748ef289e2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Remark" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="eba39ec1-7a0a-47bd-bd79-260cd3ec2be3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">remark</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SearchPattern" Type="String" MaxLength="450" Unicode="true" ed:ValidateMaxLength="450" ed:ValidateRequired="false" ed:Guid="843e25e0-790c-4c33-8f29-156bc467eaca">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">text</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="22142152-54c0-454d-befd-894a84e760c1" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b51b7fec-1b28-4d15-8fbd-1bc10570dcb0" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="abdf917d-b93f-4e2f-8dd3-9910eb4720bd" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="9b2059a6-1e3c-4d11-94ab-ef5cc01e3cd3" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ef709a41-058b-4706-8618-be3355f76cf1" />
          <Property Name="ClerkOwnerFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="8ef51145-4c0d-42a7-80e4-143771335fa6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ClerkResponsibleFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="465ddfcc-8b9e-492d-9ba5-4189b2fa877d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SiteFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7c604d0a-f4c1-4531-82a0-f56826ae7b5f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="StockFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="a70de821-4964-4b94-9873-9c566982edeb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0a68e61b-ed5a-4b96-ad02-dd0753c91faa">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionPriorityFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f8bb57a1-2761-429d-a094-2306d9536321">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="052203f5-75fc-4357-a4eb-4e0220a68568">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4a1af2b5-0c66-49d7-a055-07742293877f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="JobPreferredFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c8b1c986-585d-411a-a55f-b4e4a5c862e6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProjectChangeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="18ea2c9a-0e0e-44e8-bc20-b8f12ec4baca">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="c49facd5-5857-484a-ad27-3c23d050bfb9">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RubricCategoryFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7d3d6b27-cf13-4863-bc8a-533ead04fdeb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="EstHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7d786a81-c8b9-431f-99c2-6bf45113e82b" />
          <Property Name="EstLineItemFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f0891b10-d410-49ba-bbd9-60760aef2c2e" />
          <Property Name="EstResourceFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="16622ec8-40d0-47d8-aa3c-249209fe59bb" />
          <Property Name="ExecPlannerItemFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="987fe140-6bab-4660-b9a1-03a9f499f2f4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProjectTimeSlotFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e2d67952-d974-4de7-8b91-7c831cb2c690">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MdcControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c01afea4-aa87-491a-bbf5-461f8235665d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="WorkOperationTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6b2ccbf1-7997-48dc-930c-14316b727c37">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DropPointFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f4b308a9-cbc9-4a8f-8cb0-42684161d162">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SkillFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6c20e57c-a4c4-4776-9b47-53ee8982d65e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TypeAlternativeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3116e107-471d-487a-b5c6-91dc5d68ec17">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TypeFromEstimateFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3128e107-471t-467a-b4c6-91da5d68ec17">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsBottleNeck" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="dba1f6d2-5241-4aab-9cf9-c53d8a7b248e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="QuantityFromEstimate" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="3e467ea6-4c4a-4b68-9720-dbceef4d9489">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="WorkOperationTypeFromEstimateFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="656bf3b1-b3b0-4474-bfa7-666e88c8719d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsDeleted" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="29ff6c1c-9fd7-4905-a1ac-e394d4f29c7f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsArchived" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="7f61b67e-a378-4ecf-8584-1962dff393e4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <NavigationProperty Name="ResRequisition2skillEntities" Relationship="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK03" FromRole="RequisitionEntity" ToRole="ResRequisition2skillEntities" ed:Guid="000cb14b-12e0-4d64-8bc2-80f3bfe14b58" />
          <NavigationProperty Name="Requisition2RequisitionEntities_ResRequisitionFk" Relationship="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK25" FromRole="RequisitionEntity_ResRequisitionFk" ToRole="Requisition2RequisitionEntities_ResRequisitionFk" ed:Guid="2d850d66-3094-48b7-a8a1-337c44b4491d" />
          <NavigationProperty Name="Requisition2RequisitionEntities_ResRequisitionlinkedFk" Relationship="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK26" FromRole="RequisitionEntity_ResRequisitionlinkedFk" ToRole="Requisition2RequisitionEntities_ResRequisitionlinkedFk" ed:Guid="1a1a0bb0-37ea-4c13-90ad-6b844734a901" />
        </EntityType>
        <EntityType Name="RequisitionRequiredSkillEntity" ed:Guid="3cac84f5-c049-4e8e-a568-ab8b4a43d69e">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="32b6619a-cc7f-49a1-ab54-8f1c1fa971fe">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="33b33eec-6b32-45d2-bd04-2bf493ef0587">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SkillFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b33e704e-ae25-47e5-94d6-52b853cfa332">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CommentText" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="85882f74-2d36-4759-a3da-8b99bc0f0b75">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">comment</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText01" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="42" ed:ValidateRequired="false" ed:Guid="32c4fa40-873c-45ee-ab0a-0d20f8ac9c2f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText02" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="42" ed:ValidateRequired="false" ed:Guid="c6a3c269-034c-4e17-afce-3d47bea77a28">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText03" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="42" ed:ValidateRequired="false" ed:Guid="f9980d10-1fab-4c34-a05d-c30863d5491b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText04" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="42" ed:ValidateRequired="false" ed:Guid="80cf0cfb-b1a4-4e54-b404-051ebb5f3d5d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText05" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="42" ed:ValidateRequired="false" ed:Guid="6e065e99-6169-4d75-bd4e-c1c81ebbab02">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedNumber01" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="8c630053-6281-4e5b-855f-4ca3605e789b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedNumber02" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="84a7dcde-f2a9-485f-9880-0b68bbf2ccd3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedNumber03" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="f1b506f2-3511-4f82-bf39-d9b76069acb7">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedNumber04" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="dbeebb8a-ae40-4186-9197-bab6341551cd">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedNumber05" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="bb04aeb1-879e-404a-b97b-ded362fbcaa4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedDate01" Type="DateTime" ed:ValidateRequired="false" ed:Guid="b48dcbf8-8ede-4f07-86be-a7447ebddd2d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedDate02" Type="DateTime" ed:ValidateRequired="false" ed:Guid="501b6c6a-054a-4706-b284-4badef5c6255">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedDate03" Type="DateTime" ed:ValidateRequired="false" ed:Guid="32ce347e-0fce-459c-b1e5-a09b69d5f911">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedDate04" Type="DateTime" ed:ValidateRequired="false" ed:Guid="56d925a6-d9ab-4b7c-9f3e-1da1e8cb3195">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedDate05" Type="DateTime" ed:ValidateRequired="false" ed:Guid="80962127-0ca9-4993-9f83-c4d5fda598f8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="5f5a8e7e-7813-46c7-b354-11e956a3838f" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e8005064-d993-40b5-898b-a1fccca3a00f" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="0564f7d8-e267-4ead-8a11-e7dd29234298" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="ee367a6f-99eb-4e88-aec6-e26457a64707" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="abd36b36-bc19-4088-8b53-d8d4712979f7" />
        </EntityType>
        <EntityType Name="RequisitionDocumentEntity" ed:Guid="c80ebe91-bb6f-4c38-81b8-7a3d7c6d4fe4">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5e322c90-4f6d-4124-b8b6-c3deac4707d3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f81bd260-5ec0-4565-a3b9-e7b39039b5cf">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DescriptionInfo" Type="RIB.Visual.Resource.Requisition.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="60f1e0d4-4d6a-4dd6-a5c8-9eb24bae29b2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">translation</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DocumentTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8ee4b81a-949f-45a6-80e4-1a9334b53538">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Date" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="1f59e49d-0aff-426e-85b6-cf5613f43f95">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Barcode" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="423c587b-32a8-46bc-85fe-10ce85443a3e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="FileArchiveDocFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d8df9424-184a-44a2-a04d-2663c581e335">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="3420292f-0196-4144-88b7-fbef6cdf8d3c" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8c90aaf7-c2f0-469c-b5d6-e41fa01d7495" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="8babe6b7-1750-40c2-9281-6885bceefe79" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="1ce7ede0-c23a-43f1-8a01-5702801ad5bb" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="74917a2d-2572-432e-8171-070d8da414e9" />
          <Property Name="Url" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="3cd5bdfa-9958-4c36-a681-d4d78f80a3a2" />
        </EntityType>
        <EntityType Name="RequisitionitemEntity" ed:Guid="5a91351b-ec20-4974-a994-617b62ae4c4a" ed:SupportsGrouping="True">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="950ea4da-88d0-4873-a165-4d5664a5b38d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="RequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="81f43370-59ad-4e3a-8617-19c40ab9f874">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaterialFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="69dfcced-ac24-4439-9fed-7d0ea19d2aaa">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="b26282ac-aace-4a4e-9184-3c7fa38b5fbe">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ReservationId" Type="Int32" ed:ValidateRequired="false" ed:Guid="353b4867-8f0c-48bf-95c9-5f7299e10fb7">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="StockFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="83d0114f-1908-4249-921c-e9258dcb393c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Quantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="a86827ee-d954-4e67-92cc-45f5f8454d22">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UomFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="279c454f-65ad-45ec-84ff-557edb1ba734">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText01" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="8a392c03-9562-4a77-b5a8-171df14a7503">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText02" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="686af2d7-84f5-4cfd-92fb-b8685c8de0fa">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText03" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="214fd543-665c-4432-8dbd-6a35ba419ebc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText04" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="25b0960f-9417-428e-adfc-2c2d9295d59d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefinedText05" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="c7a55206-75a2-4a0e-a8a4-4dd34f7ff120">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="83ad532a-4bdf-406a-992c-8e64a72fa96c" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="6d59ebe7-da5e-446b-b41b-a2fbb85d6516" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="ee1f3e9e-55bb-4545-9827-b68071bb9952" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="ffa5ff66-f4a2-4628-bc92-f0bcf7415b46" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="cd2112ea-b0cb-4c6d-84fe-0f2ccd02273d" />
        </EntityType>
        <EntityType Name="StockTotalVEntity" ed:Guid="bbfcda47-f012-4706-ad01-e97989a28263">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0dbe9e23-0d7b-4298-9f4c-1e997e94afc2" />
          <Property Name="Stock2matId" Type="Int32" ed:ValidateRequired="false" ed:Guid="7e5a9718-baa6-4c83-8000-d4d4605a72b3" />
          <Property Name="StockFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a29a743a-abe9-463c-8dea-b645980840f3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CatalogCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="abba6f0d-aede-4d4d-bc2a-dac7391b1a9d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CatalogDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="39aaf3a8-f9b3-4218-ba2c-2a5c646f37bd">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaterialCode" Type="String" MaxLength="20" Unicode="true" ed:ValidateMaxLength="20" ed:ValidateRequired="false" ed:Guid="2b30105b-5b22-407c-9740-381198f005d0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">code</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CatalogId" Type="Int32" ed:ValidateRequired="false" ed:Guid="b6768ee1-9875-450e-a6e5-b4acc7da2970" />
          <Property Name="StructureFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="df1a67e8-92ef-453e-8d1f-9372fcb62e49">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaterialGroupId" Type="Int32" ed:ValidateRequired="false" ed:Guid="7f6b4491-0ee6-4c4c-9c0b-596e1a3d236f" />
          <Property Name="MaterialFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="dfd77535-98ca-411f-a989-5eaac705697e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="f9b35bb6-6e75-4be0-b57b-24468a87cbc1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasBlobsFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7e51d83b-0256-4268-947c-9a2cfc296d02" />
          <Property Name="Description2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="98548618-ea4b-448b-b7da-5c8bc6d9dd6e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Modelname" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="1fe79bdd-1f5b-4ff1-9a60-ebcf8323d830">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BrandId" Type="Int32" ed:ValidateRequired="false" ed:Guid="ca2e03fb-f0dd-4bb0-99db-58edd475c111">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BrandDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5210e13e-fb1b-42b7-b595-574052958ebb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Quantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="6b7c86c2-6fb5-4b38-a8f4-887d9d213a20">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Total" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="5190e183-5a08-42d9-9c26-e3fe6ec93db7">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvisionTotal" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="2a581b90-4eb5-4e07-bc3f-2833d2186f27">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvisionPercent" Type="Decimal" Precision="10" Scale="2" ed:ValidateRequired="false" ed:Guid="0c063710-2180-42e0-ac1f-559c2d5d4e38">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvisionPeruom" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="e252d2aa-542d-4e02-9627-b427b5d55594">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Islotmanagement" Type="Boolean" ed:ValidateRequired="false" ed:Guid="cb694c4e-7f00-4f24-a287-0a83af3d1dd8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MinQuantity" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="fe23a4b0-e3fd-455c-a6be-6d519ba49f2d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaxQuantity" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="d52d1f66-25f8-4923-ab56-ca9389c7e9ff">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Uom" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="614d4723-f412-40c2-82a9-5a0bb5704376">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="QuantityReceipt" Type="Decimal" Precision="38" Scale="6" ed:ValidateRequired="false" ed:Guid="e04e18ab-03d2-498e-b553-3e797976cfdb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="QuantityConsumed" Type="Decimal" Precision="38" Scale="6" ed:ValidateRequired="false" ed:Guid="42d8a1b3-1a9b-4284-b34d-61d4de984852">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TotalReceipt" Type="Decimal" Precision="38" Scale="7" ed:ValidateRequired="false" ed:Guid="9e933f80-a117-43ea-87bd-a74befb46e96">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TotalConsumed" Type="Decimal" Precision="38" Scale="7" ed:ValidateRequired="false" ed:Guid="31b6ccb7-7216-4f0e-b060-b6577ed42b86">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="QuantityReserved" Type="Decimal" Precision="38" Scale="6" ed:ValidateRequired="false" ed:Guid="9a2cdb08-dcc1-4875-91b5-b9de42edfaee">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="QuantityAvailable" Type="Decimal" Precision="38" Scale="6" ed:ValidateRequired="false" ed:Guid="219ea7cc-5986-40c5-8488-fa52c46e9666">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvisionReceipt" Type="Decimal" Precision="38" Scale="7" ed:ValidateRequired="false" ed:Guid="e4fd4ea8-3883-4394-b4ac-321bf963c8e3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvisionConsumed" Type="Decimal" Precision="38" Scale="7" ed:ValidateRequired="false" ed:Guid="1efc53ac-8db6-4e6e-985b-74c5612e9779">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ExpenseTotal" Type="Decimal" Precision="38" Scale="7" ed:ValidateRequired="false" ed:Guid="36b6d4d4-2ecb-4f04-b0a8-d3acfafd0a26">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">decimal</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProductFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="34964a01-f774-4dd4-b575-68985f20fdd6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                  <ed:PropertyValue Name="MaxLength" Type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <int xmlns="">0</int>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="a24cd1ed-0df2-42d5-8b22-1e7e2dc64778" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8a0f143e-bde9-4379-83fd-488588e8b182" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="c5728a70-7575-4250-84fb-38ea3cb56f57" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="683aa8a4-9fd5-47c8-94af-995195dcb4d7" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0a492f8e-8191-4db5-9f7e-e8aa8f92b52c" />
        </EntityType>
        <EntityType Name="RequisitionInformationEntity" ed:Guid="902b903b-bb59-4277-abe5-5bbd9350caa4" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="09ac4269-d559-489f-a0d4-6c85095a2dab" />
          <Property Name="IsReadOnly" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="07478f86-a8a5-4476-b022-2e26e5ef4178" />
          <Property Name="ProjectCompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bb2cb681-fede-4487-9fdd-3f7e1af7f0f4" />
          <Property Name="ProjectNo" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="d750cf85-88a0-45ae-9801-884f97165bef" />
          <Property Name="ProjectName" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="b9907f02-1635-4fcb-b52e-3ec7d9b1731d" />
          <Property Name="ProjectName2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="9776d22d-5733-411a-a9be-4c4c46d9a3eb" />
          <Property Name="ProjectCurrencyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="fdbda6a0-1937-4fa1-88ba-4695421c43a0" />
          <Property Name="ProjectBusinessPartnerFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e1aad0f2-cbf7-4b98-a40e-a6208bdbeec9" />
          <Property Name="ProjectIsLive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="84f2e9ea-9939-4024-8e1d-1ab17dfb8397" />
          <Property Name="ProjectUserDefined01" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="144320c6-a2fa-4e30-9a13-3a43335a8008" />
          <Property Name="ProjectUserDefined02" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="35fda937-0391-4ea1-a9ea-2f4a12c64d09" />
          <Property Name="ProjectAddressFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4d021228-0831-449b-8962-1a8381fde672" />
          <Property Name="StockProjectFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="8072c8db-3675-46a5-851e-95731ea9b42a" />
          <Property Name="ScheduleFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4d021228-0831-449b-8962-1a8381fde672" />
          <Property Name="JobCode" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="a85ba999-0214-463b-9448-77aae632faa0" />
          <Property Name="JobDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="26b90410-feab-4feb-8e96-b7649d7ae3ba" />
          <Property Name="JobGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="71f6dd78-2119-4fbf-8eac-d481e464c196" />
          <Property Name="JobSiteFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0226e94f-1855-4706-8172-301102a2ee68" />
          <Property Name="JobControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="49c6a23d-de2f-4fa2-80d1-9af30a673588" />
          <Property Name="JobUserDefined01" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="0c0df175-5e0e-45d4-846d-a09defb09619" />
          <Property Name="JobUserDefined02" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="9b28e705-883d-4dca-9ac4-d2c5fd221456" />
          <Property Name="JobValidFrom" Type="DateTime" ed:ValidateRequired="false" ed:Guid="1ed2597d-ffe0-46e9-94fe-7d2ed5bdef09" />
          <Property Name="JobValidTo" Type="DateTime" ed:ValidateRequired="false" ed:Guid="19adebaa-cf5e-438d-8e4d-cb9224d5988d" />
          <Property Name="JobBusinessPartnerFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2bd188c5-cfd9-4633-b1a0-7ddfc56d1fc1" />
          <Property Name="JobDeliveryAddressRemark" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="1d5554f2-ff19-4b10-a9b7-2004893b3035" />
          <Property Name="JobDeliveryAddressBlobFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="179b329f-f785-48da-af1f-180103dabf7a" />
          <Property Name="JobAddressFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="30d5f198-a0d4-4d8b-8abe-92c8fc6329b6" />
          <Property Name="JobPlantFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="67820fc3-9fe0-492d-b96b-bfdc5ea2234a" />
          <Property Name="JobIsProjectDefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="a0081786-0ce6-447c-8bc1-4ccbbf3d3bb5" />
          <Property Name="JobIsLive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="a44f18c4-2edf-4b5e-b15c-0e06ea87eb9b" />
          <Property Name="JobTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="494b9ec6-cf62-44b0-bb12-8a2e70ebd0e6" />
          <Property Name="JobIsMaintenance" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="45b9a7ef-6f92-4cc9-9d5a-8d42e6dcdd40" />
          <Property Name="JobCalendarFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="132a0996-4cb5-403d-96bb-8fd47f25b24f" />
          <Property Name="JobCompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a5d901af-0cd0-40e6-9503-b548b3e32ede" />
          <Property Name="JobRubricCategoryFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c10f8876-dd3d-414b-9f0f-3a1419aa188e" />
          <Property Name="JobContextFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0228c354-9549-4b90-9b61-cc8e89934439" />
          <Property Name="JobCostCodePriceListFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4fe151fe-cff9-4b28-944c-cd2c5f38f638" />
          <Property Name="JobCostCodePriceVersionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6e9bfde6-2740-4718-b04c-4d81b29c41c0" />
          <Property Name="ResourceTypeIsBulk" Type="Boolean" ed:ValidateRequired="false" ed:Guid="bbf8dfa7-d516-4af9-a738-53275015b9e1" />
          <Property Name="ResourceTypeIsSmallTools" Type="Boolean" ed:ValidateRequired="false" ed:Guid="e75c67e9-d6c2-449b-9a9a-911a138514f7" />
          <Property Name="DispatcherGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2930221d-21f2-4aaf-a67f-13e6dbe146fe" />
          <Property Name="ResourceSiteFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b3a93559-11bd-4050-8d56-684bafd0573e" />
          <Property Name="ReservedFrom" Type="DateTime" ed:ValidateRequired="false" ed:Guid="e2be5b1e-f811-4d3c-951e-dac83115fd39" />
          <Property Name="OnSiteFrom" Type="DateTime" ed:ValidateRequired="false" ed:Guid="f825bd7a-10f8-4ce9-88ee-1200d622eaa8" />
          <Property Name="ReservedTo" Type="DateTime" ed:ValidateRequired="false" ed:Guid="7696daa4-28f9-49c9-be35-5e43697051b5" />
          <Property Name="OnSiteTo" Type="DateTime" ed:ValidateRequired="false" ed:Guid="ebdf3825-43b0-4bf6-bb7d-a3eac938751d" />
          <Property Name="ProjectChangeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="59b05968-0a26-47a3-b090-366f7835ccb0" />
          <Property Name="ProjectChangeStatusFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="ec58b348-0e4c-4ae8-8af3-e72edf5be2e4" />
          <Property Name="IsTimeEnhancement" Type="Boolean" ed:ValidateRequired="false" ed:Guid="cc0239b0-c7a3-4422-b7b7-d2d6559e1bdb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Requisition2RequisitionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="98351dc0-a416-47ad-a540-bbf26aa15f25">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Code" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="933df20a-6f68-4632-8b94-a9d94222d5cd">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="472a42e5-32c6-4538-920a-570feabc1570">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsRequestBizPartner" Type="Boolean" ed:ValidateRequired="false" ed:Guid="a5fe17e8-18cb-4916-9ea6-0a84b52d7974">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsRequestProjectDoc" Type="Boolean" ed:ValidateRequired="false" ed:Guid="e6ee1f33-a41b-4325-854b-05af7bb918ea">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PlannedStart" Type="DateTime" ed:ValidateRequired="false" ed:Guid="1217f3ae-e70c-4756-9c01-9f19e72d4995">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">datetimeutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PlannedEnd" Type="DateTime" ed:ValidateRequired="false" ed:Guid="18cf42b6-f620-47a8-9671-60d4d9d22256">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">datetimeutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="EstimateQuantity" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="05034ed2-8893-485a-ba0f-f062ea39d161">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="EstWorkOperationTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="abdb7d02-33b6-43ee-9be9-335df218faec">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*******, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">lookup</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="LgmDispatchHeaderMapFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="03bd54d0-7234-4b0d-96fe-43b4b7c35db5" />
          <Property Name="ResRequisitionFormDataFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="5444dc60-0987-4d6f-90b5-9c475e0d05e9" />
          <Property Name="ResRequisitionMapperFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="90f49c36-50e1-46c1-9d91-bbab5d128f9d" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="df1e7f56-9a7e-42e8-849d-97632b61c509" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4f71755e-0d40-4ff2-a870-b203a294431b" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="a2f8fd81-6ebb-44aa-a5e6-db36146cc604" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="038860a7-78e1-4d56-92cc-0bb2e203e215" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="353a7e03-8b23-4fe9-a93b-e67c7412c783" />
        </EntityType>
        <EntityType Name="Requisition2RequisitionEntity" ed:Guid="986875eb-0d87-4d01-a12a-ac46fa47bccd">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="26f2c472-7140-4d5a-8270-c3986d91d605" />
          <Property Name="RequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="cf4982bf-a3db-4b63-b718-8e763eeb9a59" />
          <Property Name="RequisitionLinkedFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1cd471d1-c3b5-42cb-84bf-6f9a5774d55c" />
          <Property Name="IsReference" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="1bd59d87-0497-42f4-b75f-bf64bad96c87" />
          <Property Name="IsTimeEnhancement" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="31db5924-61d5-455e-8234-56466f7314e4" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="09ba22ee-af59-4a4c-85de-37329b441558" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9c31c70e-c497-4456-a999-fbaf25097b18" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="4e6f3dec-f55a-4163-9b3e-d0b8a7cf9319" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="10d3a641-41d4-461c-884d-ba93161dd0aa" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1bcde720-fef0-4ff4-93fe-326b10770000" />
          <NavigationProperty Name="RequisitionEntity_ResRequisitionFk" Relationship="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK25" FromRole="Requisition2RequisitionEntities_ResRequisitionFk" ToRole="RequisitionEntity_ResRequisitionFk" ed:Guid="de3c4307-3be1-4649-b741-cfcbe8c3f5fb" />
          <NavigationProperty Name="RequisitionEntity_ResRequisitionlinkedFk" Relationship="RIB.Visual.Resource.Requisition.BusinessComponents.RES_REQUISITION_FK26" FromRole="Requisition2RequisitionEntities_ResRequisitionlinkedFk" ToRole="RequisitionEntity_ResRequisitionlinkedFk" ed:Guid="f138beaa-2847-4f32-89d6-7eeb76ee177d" />
        </EntityType>
        <EntityType Name="RequisitionGenReqFromEstVEntity" ed:Guid="aa976c95-db22-4f0b-8687-47108b964e35">
          <Key>
            <PropertyRef Name="PrjProjectFk" />
            <PropertyRef Name="BasCompanyFk" />
            <PropertyRef Name="PrjEstimateFk" />
            <PropertyRef Name="EstHeaderFk" />
            <PropertyRef Name="EstLineItemFk" />
            <PropertyRef Name="EstResourceFk" />
            <PropertyRef Name="EstResourceType" />
            <PropertyRef Name="ResBasUomFk" />
          </Key>
          <Property Name="PrjProjectFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9b1bfe5d-80fb-478b-9792-1f4801e236db" />
          <Property Name="PrjProjectName" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="8fa75525-a867-486d-a97d-87d4250a88a3" />
          <Property Name="BasCompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="3ce0431d-c568-4c99-b9d9-31e791127343" />
          <Property Name="PrjEstimateFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b570f803-5775-48f4-a85f-bea5b8fb5bda" />
          <Property Name="EstHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="85b42d88-0808-481c-b470-7c6b608bf28a" />
          <Property Name="EstHeaderDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="7b5f3bf3-f43f-4e1d-906c-161caa9d8d84" />
          <Property Name="EstLineItemFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="adcadfbf-4977-429b-92c2-f7fb47048748" />
          <Property Name="EstLineItemDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="c0589650-c41c-4ee4-868e-7a50ddbecb96" />
          <Property Name="EstAssemblyLineItemFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d23d60b4-ef30-47c3-a829-b6ef33796475" />
          <Property Name="EstAssemblyLineItemDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="f842ecc7-b2b1-4cdc-b9e2-78d16a64b93e" />
          <Property Name="EstResourceFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="804ba7c0-d337-4a6a-bbb6-************" />
          <Property Name="EstResourceDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="92245d43-3809-4b8f-bc5d-091913326b3b" />
          <Property Name="EstResourceType" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bdd9f487-d2db-49ee-9083-ab5156886374" />
          <Property Name="EstAssemblyFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="ad1fffcd-**************-b748f89d0e47" />
          <Property Name="ResQuantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="5dc773d4-7e2a-456f-9e1f-e5d3f2043629" />
          <Property Name="ResBasUomFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="2621c520-bc06-4d74-9714-3cbba7d0a539" />
          <Property Name="LgmJobFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e4f52fcb-0324-4317-b48d-ad9808714b71" />
          <Property Name="LgmJobDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="9cacea95-cf4b-43a0-a51a-23e9016caef9" />
          <Property Name="CalCalendarFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c9ea51a6-ad95-4f93-8e98-a742b8bcbd89" />
          <Property Name="BasUomhourFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="89d39d04-cd3a-4b2f-8979-e6508832032d" />
          <Property Name="BasUomdayFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="432e4205-bb15-46b0-86c6-1b69ace138de" />
          <Property Name="BasUommonthFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="300e2e3e-fb87-4425-8758-16405e958cd8" />
          <Property Name="Workhoursperday" Type="Decimal" Precision="15" Scale="6" ed:ValidateRequired="false" ed:Guid="d2757b6c-0e66-4c7b-a1d1-764c2d998434" />
          <Property Name="Workhourspermonth" Type="Decimal" Precision="15" Scale="6" ed:ValidateRequired="false" ed:Guid="92ffc998-1f4c-46b2-a760-bfe62547eed8" />
          <Property Name="EtmPlantDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="fb02cfbf-c3fb-40cd-8201-fb6f4f46729e" />
          <Property Name="EtmPlantgroupDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5d09544c-12d2-436c-8901-7e0d3d5fd207" />
          <Property Name="PlantFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0295b1a6-0a1e-4286-ac2f-61fed55d1e65" />
          <Property Name="EtmPlantCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="fff8418c-743c-4b36-a212-a852a8692f0a" />
          <Property Name="EtmPlantBasUomFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d2dad01e-48ed-410f-b43c-aac4800bba8e" />
          <Property Name="EtmPlantgroupCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="926b268e-8a1f-4d87-8d24-dc262fdb4c1f" />
          <Property Name="MdcControllingunitCode" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="ce4bbded-43da-450c-8c8e-64a44d32d75b" />
          <Property Name="MdcControllingunitDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="218ef798-a248-4c41-9b60-4a3d773d5d2e" />
          <Property Name="MdcControllingunitPlannedStart" Type="DateTime" ed:ValidateRequired="false" ed:Guid="43ddf874-06a3-4083-a6e3-fd3c6ee51d30" />
          <Property Name="ResTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="66b07e83-5044-4cdd-a9d0-74ad532600d5" />
          <Property Name="ResTypeDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5f9da12f-2dd8-4bde-bbc9-3be78a7314d6" />
          <Property Name="LgmDispatcherGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="18da7e9e-73ff-45a1-9665-26977ee8a308" />
          <Property Name="ResWotBasUomFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e29ff7cc-1dbb-473f-b8de-6f533719358f" />
          <Property Name="WorkOperationTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e19fv7cd-1dcb-483f-b8de-6f533519358c" />
          <Property Name="ControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e23ff5cv-1dcb-433f-b8de-6f533819358a" />
        </EntityType>
        <EntityType Name="RequisitionGenReqSkillVEntity" ed:Guid="42986ab5-759f-48cc-b18c-0a4d58b10a2a">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="ResTypeFk" />
            <PropertyRef Name="ResSkillFk" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9301b3ec-54a1-40c9-9937-3d5c87383f05" />
          <Property Name="ResTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5b32a34d-ad88-4751-8d30-fb94e1b46811" />
          <Property Name="ResSkillFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b3d6352c-a500-4879-946b-7d665124a97c" />
        </EntityType>
        <EntityType Name="RequisitionGenReqTypVEntity" ed:Guid="3b9552fc-1f39-4222-b43f-2f0ef3c94194">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="ResTypeFk" />
            <PropertyRef Name="ResSkillFk" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0978470d-4f80-4b37-a580-995cc8b5ed74" />
          <Property Name="ResRequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1ef5f9bb-6433-456c-8323-7938f952b902" />
          <Property Name="ResTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="045a93bb-9cf9-4f7c-aa16-266484cb6e11" />
          <Property Name="Duration" Type="Int32" ed:ValidateRequired="false" ed:Guid="4a33a871-f3c3-479b-8f20-39f65adb869b" />
          <Property Name="BasUomdayFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="5a69dc8e-5f64-4507-9dc2-1523426f10bc" />
          <Property Name="ResTyperequestedFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="095ec2ab-4477-41fd-beef-20e8191e6652" />
          <Property Name="LgmDispatcherGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="392c6889-d43a-461f-a30d-3ab8c976a5e6" />
          <Property Name="IsPlantRequested" Type="Boolean" ed:ValidateRequired="false" ed:Guid="fb229e72-4eff-4934-ae55-4422c2a1ae3e" />
          <Property Name="IsDriverRequested" Type="Boolean" ed:ValidateRequired="false" ed:Guid="d43b70b0-e874-4e3f-a973-0ff65896dea3" />
          <Property Name="IsCraneRequested" Type="Boolean" ed:ValidateRequired="false" ed:Guid="d8135705-ab98-4884-860d-70859e444f95" />
          <Property Name="IsTruckRequested" Type="Boolean" ed:ValidateRequired="false" ed:Guid="bb0d8ed0-3dff-49c7-8a29-da26756db0da" />
          <Property Name="SkillOfRequestedTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6b512542-c166-4735-8d4e-6233cfacb888" />
          <Property Name="Isrequestedentireperiod" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="548b82ea-c014-40ce-970b-47e5246df137" />
          <Property Name="Necessaryoperators" Type="Int32" Nullable="false" ed:ValidateRequired="false" ed:Guid="cf57ed9c-f6fd-43e3-bf69-5ee4ab2467a6" />
          <Property Name="CalCalendarFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e3be3478-9d2b-4a5e-9471-d5c02e559259" />
          <Property Name="ResSkillResTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="19959431-2210-45b2-9503-8280a0bb9c77" />
          <Property Name="ResSkillFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bc1f4ef4-d914-4eb5-9db0-88821b46eb93" />
        </EntityType>
        <EntityType Name="Requisition2RequisitionInfoVEntity" ed:Guid="6ee926f5-c427-4943-83ab-433cc7905044">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c1d993fe-a882-460b-9d10-a7ce8d72c7e2" />
          <Property Name="ParentResRequisitionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="8b4e4d5f-676d-4741-a753-b7f86b71cf21" />
          <Property Name="PrimaryResRequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="980cb560-263b-49e0-b053-b8acdfd31587" />
          <Property Name="ChildResRequisitionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7269cce3-95e8-40f7-a151-941623c42c5c" />
        </EntityType>
        <EntityType Name="RequisitionChangeReqdateInfoVEntity" ed:Guid="59b259a7-1b33-4962-ab89-b040ecdf81cd">
          <Key>
            <PropertyRef Name="PrimaryResRequisitionFk" />
          </Key>
          <Property Name="ParentResRequisitionFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b6121622-ef94-4c20-86a2-85ff2b48afd1" />
          <Property Name="PrimaryResRequisitionFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c25825e4-07a3-4517-a42b-fe9d71fd6bdd" />
          <Property Name="ResTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="1f5b0f53-4b8c-4119-a645-21684418aa20" />
          <Property Name="Duration" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="a16437c5-0153-4119-8ffe-1f419978be08" />
          <Property Name="PrimaryBasUomdayFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4195510d-771f-4823-9acb-ce4f0378be23" />
          <Property Name="ResTyperequestedFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="409acb49-efd9-4ba9-8cff-4e8255c632cc" />
          <Property Name="LgmDispatcherGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b54ad142-70de-4748-a0a6-527a776a0d78" />
          <Property Name="Isrequestedentireperiod" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="f1b49c64-e925-4d3c-b8e4-2aa92ec0b8e6" />
          <Property Name="Necessaryoperators" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8c4d8730-af87-491e-b49f-044def16ba48" />
          <Property Name="ParentDuration" Type="Int32" ed:ValidateRequired="false" ed:Guid="2c7b2203-a801-4a7c-b1f6-c888b80ebcd0" />
          <Property Name="ParentBasUomdayFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3eeb2573-d186-42db-93c5-fc2db76c56cc" />
          <Property Name="ParentResTyperequestedFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="f310c64c-c31c-4828-827e-f249dfb4da23" />
          <Property Name="ParentIsrequestedentireperiod" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="2e529625-f245-4fcd-af4b-f0b83cbf3aaf" />
          <Property Name="ParentNecessaryoperators" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9c1acac5-c4ed-46d0-a085-0a0c81c6f5c2" />
          <Property Name="LgmJobFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="942708a7-0f6f-4676-b55d-fbc9ca4409ec" />
          <Property Name="LgmJobDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="1c627ab6-4a53-455c-9641-74c908b224f3" />
          <Property Name="CalCalendarFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2b35d270-7ca5-401b-8e3e-7ac8e9fbcba2" />
          <Property Name="BasUomhourFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="25db4f18-a5b0-4c08-ac31-e21f245ac583" />
          <Property Name="BasUomdayFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2cd365d7-3a03-4444-8eba-800b5eb494f2" />
          <Property Name="BasUommonthFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3e75a291-495f-4a3b-a8d6-009f0585079f" />
          <Property Name="Workhoursperday" Type="Decimal" Precision="15" Scale="6" ed:ValidateRequired="false" ed:Guid="9bc55109-90e4-4fe7-acb5-6ae2ceef56a9" />
          <Property Name="Workhourspermonth" Type="Decimal" Precision="15" Scale="6" ed:ValidateRequired="false" ed:Guid="35432013-322f-4efa-8428-51fb08f05112" />
          <Property Name="ResQuantity" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="24a421ee-1da9-46bc-a30f-29a10cdb28fa" />
          <Property Name="ResWotBasUomFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="48429aa4-8eae-40af-91f1-df5bf97e9068" />
          <Property Name="ResSkillFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="08e59bef-be1f-457e-b0bb-5080bd2405ca" />
          <Property Name="IsSkillDemand" Type="Boolean" ed:ValidateRequired="false" ed:Guid="fa535f07-dcd7-43ef-80c5-d863d6574a3e" />
          <Property Name="IsTertiaryDemand" Type="Boolean" ed:ValidateRequired="false" ed:Guid="441d6810-7b0d-41a5-af43-cff2b7d5d53c" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="d503cfc6-f9ef-411c-af64-0e66c76468fc" ed:GenerateOnlyMapping="True">
          <Property Name="Description" Type="String" ed:ValidateRequired="false" ed:Guid="69283371-dcfd-487a-8900-4a9e28ba8105" />
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="false" ed:Guid="7019d3f9-9cd2-4cd7-bd4a-5e52a2d17a1c" />
        </ComplexType>
        <Association Name="RES_REQUISITION_FK03" ed:Guid="2bd5f5d2-b73d-4622-8b61-837228ff200c">
          <End Role="RequisitionEntity" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="1" />
          <End Role="ResRequisition2skillEntities" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionRequiredSkillEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RequisitionEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ResRequisition2skillEntities">
              <PropertyRef Name="RequisitionFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK04" ed:Guid="f8e2e01e-3636-438f-b4fd-a89b99a52539">
          <End Role="RequisitionEntity" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="1" />
          <End Role="ResRequisitiondocumentEntities" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionDocumentEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RequisitionEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ResRequisitiondocumentEntities">
              <PropertyRef Name="RequisitionFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK09" ed:Guid="fcc8a714-5c2a-43c6-8814-532cf1b2e439">
          <End Role="RequisitionEntity_ResRequisitionFk" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="0..1" />
          <End Role="RequisitionEntities_ResRequisitionFk" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RequisitionEntity_ResRequisitionFk">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="RequisitionEntities_ResRequisitionFk">
              <PropertyRef Name="RequisitionFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK08" ed:Guid="43554fe4-1a8d-4ad6-a1b5-adbeac6a30c6">
          <End Role="RequisitionEntity" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="1" />
          <End Role="ResRequisitionitemEntities" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionitemEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RequisitionEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ResRequisitionitemEntities">
              <PropertyRef Name="RequisitionFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK25" ed:Guid="e3de66a9-db64-41fa-bb7d-f6757b42f0c3">
          <End Role="RequisitionEntity_ResRequisitionFk" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="1" />
          <End Role="Requisition2RequisitionEntities_ResRequisitionFk" Type="RIB.Visual.Resource.Requisition.BusinessComponents.Requisition2RequisitionEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RequisitionEntity_ResRequisitionFk">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="Requisition2RequisitionEntities_ResRequisitionFk">
              <PropertyRef Name="RequisitionFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="RES_REQUISITION_FK26" ed:Guid="15879303-de5a-4397-906d-47f3a08f7d1c">
          <End Role="RequisitionEntity_ResRequisitionlinkedFk" Type="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity" Multiplicity="1" />
          <End Role="Requisition2RequisitionEntities_ResRequisitionlinkedFk" Type="RIB.Visual.Resource.Requisition.BusinessComponents.Requisition2RequisitionEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="RequisitionEntity_ResRequisitionlinkedFk">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="Requisition2RequisitionEntities_ResRequisitionlinkedFk">
              <PropertyRef Name="RequisitionLinkedFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="DdTempIdsEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.DdTempIdsEntity">
              <MappingFragment StoreEntitySet="BAS_DDTEMPIDS">
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Key1" ColumnName="KEY1" />
                <ScalarProperty Name="Key2" ColumnName="KEY2" />
                <ScalarProperty Name="Key3" ColumnName="KEY3" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionEntity">
              <MappingFragment StoreEntitySet="RES_REQUISITIONs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="ProjectFk" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="ResourceFk" ColumnName="RES_RESOURCE_FK" />
                <ScalarProperty Name="RequisitionStatusFk" ColumnName="RES_REQUISITIONSTATUS_FK" />
                <ScalarProperty Name="TypeFk" ColumnName="RES_TYPE_FK" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="UomFk" ColumnName="BAS_UOM_FK" />
                <ScalarProperty Name="RequestedFrom" ColumnName="REQUESTED_FROM" />
                <ScalarProperty Name="RequestedTo" ColumnName="REQUESTED_TO" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="ActivityFk" ColumnName="PSD_ACTIVITY_FK" />
                <ScalarProperty Name="TrsRequisitionFk" ColumnName="TRS_REQUISITION_FK" />
                <ScalarProperty Name="PpsEventFk" ColumnName="PPS_EVENT_FK" />
                <ScalarProperty Name="SearchPattern" ColumnName="SEARCH_PATTERN" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="JobFk" ColumnName="LGM_JOB_FK" />
                <ScalarProperty Name="IsLinkedFixToReservation" ColumnName="ISLINKEDFIXTORESERVATION" />
                <ScalarProperty Name="ReservedFrom" ColumnName="RESERVED_FROM" />
                <ScalarProperty Name="ReservedTo" ColumnName="RESERVED_TO" />
                <ScalarProperty Name="UserDefinedText01" ColumnName="USERDEFINEDTEXT01" />
                <ScalarProperty Name="UserDefinedText02" ColumnName="USERDEFINEDTEXT02" />
                <ScalarProperty Name="UserDefinedText03" ColumnName="USERDEFINEDTEXT03" />
                <ScalarProperty Name="UserDefinedText04" ColumnName="USERDEFINEDTEXT04" />
                <ScalarProperty Name="UserDefinedText05" ColumnName="USERDEFINEDTEXT05" />
                <ScalarProperty Name="CompanyFk" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="ResourceContextFk" ColumnName="BAS_RESOURCE_CONTEXT_FK" />
                <ScalarProperty Name="MaterialFk" ColumnName="MDC_MATERIAL_FK" />
                <ScalarProperty Name="Remark" ColumnName="REMARK" />
                <ScalarProperty Name="ReservationId" ColumnName="RESERVATION_ID" />
                <ScalarProperty Name="ClerkOwnerFk" ColumnName="BAS_CLERKOWNER_FK" />
                <ScalarProperty Name="ClerkResponsibleFk" ColumnName="BAS_CLERKRESPONSIBLE_FK" />
                <ScalarProperty Name="SiteFk" ColumnName="BAS_SITE_FK" />
                <ScalarProperty Name="StockFk" ColumnName="PRJ_STOCK_FK" />
                <ScalarProperty Name="RequisitionGroupFk" ColumnName="RES_REQUISITIONGROUP_FK" />
                <ScalarProperty Name="RequisitionPriorityFk" ColumnName="RES_REQUISITIONPRIORITY_FK" />
                <ScalarProperty Name="RequisitionTypeFk" ColumnName="RES_REQUISITION_TYPE_FK" />
                <ScalarProperty Name="RequisitionFk" ColumnName="RES_REQUISITION_FK" />
                <ScalarProperty Name="JobPreferredFk" ColumnName="LGM_JOBPREFERRED_FK" />
                <ScalarProperty Name="ProjectChangeFk" ColumnName="PRJ_CHANGE_FK" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="RubricCategoryFk" ColumnName="BAS_RUBRIC_CATEGORY_FK" />
                <ScalarProperty Name="EstHeaderFk" ColumnName="EST_HEADER_FK" />
                <ScalarProperty Name="EstLineItemFk" ColumnName="EST_LINE_ITEM_FK" />
                <ScalarProperty Name="EstResourceFk" ColumnName="EST_RESOURCE_FK" />
                <ScalarProperty Name="ExecPlannerItemFk" ColumnName="RES_EXECPLANNERITEM_FK" />
                <ScalarProperty Name="ProjectTimeSlotFk" ColumnName="RES_PROJECTTIMESLOT_FK" />
                <ScalarProperty Name="MdcControllingUnitFk" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="WorkOperationTypeFk" ColumnName="ETM_WORKOPERATIONTYPE_FK" />
                <ScalarProperty Name="DropPointFk" ColumnName="PRJ_DROPPOINT_FK" />
                <ScalarProperty Name="SkillFk" ColumnName="RES_SKILL_FK" />
                <ScalarProperty Name="TypeAlternativeFk" ColumnName="RES_TYPEALTERNATIVE_FK" />
                <ScalarProperty Name="TypeFromEstimateFk" ColumnName="RES_TYPEFROMESTIMATE_FK" />
                <ScalarProperty Name="IsBottleNeck" ColumnName="ISBOTTLENECK" />
                <ScalarProperty Name="QuantityFromEstimate" ColumnName="QUANTITYFROMESTIMATE" />
                <ScalarProperty Name="WorkOperationTypeFromEstimateFk" ColumnName="ETM_WORKOPERATIONTYPEEST_FK" />
                <ScalarProperty Name="IsDeleted" ColumnName="ISDELETED" />
                <ScalarProperty Name="IsArchived" ColumnName="ISARCHIVED" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionRequiredSkillEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionRequiredSkillEntity">
              <MappingFragment StoreEntitySet="RES_REQUISITION2SKILLs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="RequisitionFk" ColumnName="RES_REQUISITION_FK" />
                <ScalarProperty Name="SkillFk" ColumnName="RES_SKILL_FK" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="UserDefinedText01" ColumnName="USERDEFTEXT01" />
                <ScalarProperty Name="UserDefinedText02" ColumnName="USERDEFTEXT02" />
                <ScalarProperty Name="UserDefinedText03" ColumnName="USERDEFTEXT03" />
                <ScalarProperty Name="UserDefinedText04" ColumnName="USERDEFTEXT04" />
                <ScalarProperty Name="UserDefinedText05" ColumnName="USERDEFTEXT05" />
                <ScalarProperty Name="UserDefinedNumber01" ColumnName="USERDEFNUMBER01" />
                <ScalarProperty Name="UserDefinedNumber02" ColumnName="USERDEFNUMBER02" />
                <ScalarProperty Name="UserDefinedNumber03" ColumnName="USERDEFNUMBER03" />
                <ScalarProperty Name="UserDefinedNumber04" ColumnName="USERDEFNUMBER04" />
                <ScalarProperty Name="UserDefinedNumber05" ColumnName="USERDEFNUMBER05" />
                <ScalarProperty Name="UserDefinedDate01" ColumnName="USERDEFDATE01" />
                <ScalarProperty Name="UserDefinedDate02" ColumnName="USERDEFDATE02" />
                <ScalarProperty Name="UserDefinedDate03" ColumnName="USERDEFDATE03" />
                <ScalarProperty Name="UserDefinedDate04" ColumnName="USERDEFDATE04" />
                <ScalarProperty Name="UserDefinedDate05" ColumnName="USERDEFDATE05" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionDocumentEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionDocumentEntity">
              <MappingFragment StoreEntitySet="RES_REQUISITIONDOCUMENTs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="RequisitionFk" ColumnName="RES_REQUISITION_FK" />
                <ScalarProperty Name="DocumentTypeFk" ColumnName="BAS_DOCUMENT_TYPE_FK" />
                <ScalarProperty Name="Date" ColumnName="DATE" />
                <ScalarProperty Name="Barcode" ColumnName="BARCODE" />
                <ScalarProperty Name="FileArchiveDocFk" ColumnName="BAS_FILEARCHIVEDOC_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="Url" ColumnName="URL" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionitemEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionitemEntity">
              <MappingFragment StoreEntitySet="RES_REQUISITIONITEMs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="RequisitionFk" ColumnName="RES_REQUISITION_FK" />
                <ScalarProperty Name="MaterialFk" ColumnName="MDC_MATERIAL_FK" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="ReservationId" ColumnName="RESERVATION_ID" />
                <ScalarProperty Name="StockFk" ColumnName="PRJ_STOCK_FK" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="UomFk" ColumnName="BAS_UOM_FK" />
                <ScalarProperty Name="UserDefinedText01" ColumnName="USERDEFINEDTEXT01" />
                <ScalarProperty Name="UserDefinedText02" ColumnName="USERDEFINEDTEXT02" />
                <ScalarProperty Name="UserDefinedText03" ColumnName="USERDEFINEDTEXT03" />
                <ScalarProperty Name="UserDefinedText04" ColumnName="USERDEFINEDTEXT04" />
                <ScalarProperty Name="UserDefinedText05" ColumnName="USERDEFINEDTEXT05" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="StockTotalVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.StockTotalVEntity">
              <MappingFragment StoreEntitySet="PRC_STOCKTOTAL_Vs">
                <ScalarProperty Name="StockFk" ColumnName="PRJ_STOCK_FK" />
                <ScalarProperty Name="Description1" ColumnName="DESCRIPTION1" />
                <ScalarProperty Name="Description2" ColumnName="DESCRIPTION2" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="Total" ColumnName="TOTAL" />
                <ScalarProperty Name="ProvisionTotal" ColumnName="PROVISION_TOTAL" />
                <ScalarProperty Name="ProvisionPercent" ColumnName="PROVISION_PERCENT" />
                <ScalarProperty Name="ProvisionPeruom" ColumnName="PROVISION_PERUOM" />
                <ScalarProperty Name="Islotmanagement" ColumnName="ISLOTMANAGEMENT" />
                <ScalarProperty Name="MinQuantity" ColumnName="MIN_QUANTITY" />
                <ScalarProperty Name="MaxQuantity" ColumnName="MAX_QUANTITY" />
                <ScalarProperty Name="Uom" ColumnName="UOM" />
                <ScalarProperty Name="MaterialFk" ColumnName="MDC_MATERIAL_FK" />
                <ScalarProperty Name="CatalogCode" ColumnName="CATALOG_CODE" />
                <ScalarProperty Name="CatalogDescription" ColumnName="CATALOG_DESCRIPTION" />
                <ScalarProperty Name="MaterialCode" ColumnName="MATERIAL_CODE" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Stock2matId" ColumnName="STOCK2MAT_ID" />
                <ScalarProperty Name="QuantityReceipt" ColumnName="QUANTITY_RECEIPT" />
                <ScalarProperty Name="QuantityConsumed" ColumnName="QUANTITY_CONSUMED" />
                <ScalarProperty Name="TotalReceipt" ColumnName="TOTAL_RECEIPT" />
                <ScalarProperty Name="TotalConsumed" ColumnName="TOTAL_CONSUMED" />
                <ScalarProperty Name="ProvisionReceipt" ColumnName="PROVISION_RECEIPT" />
                <ScalarProperty Name="ProvisionConsumed" ColumnName="PROVISION_CONSUMED" />
                <ScalarProperty Name="ExpenseTotal" ColumnName="EXPENSE_TOTAL" />
                <ScalarProperty Name="StructureFk" ColumnName="PRC_STRUCTURE_FK" />
                <ScalarProperty Name="QuantityReserved" ColumnName="QUANTITY_RESERVED" />
                <ScalarProperty Name="QuantityAvailable" ColumnName="QUANTITY_AVAILABLE" />
                <ScalarProperty Name="BasBlobsFk" ColumnName="BAS_BLOBS_FK" />
                <ScalarProperty Name="CatalogId" ColumnName="CATALOG_ID" />
                <ScalarProperty Name="Modelname" ColumnName="MODELNAME" />
                <ScalarProperty Name="BrandId" ColumnName="BRAND_ID" />
                <ScalarProperty Name="BrandDescription" ColumnName="BRAND_DESCRIPTION" />
                <ScalarProperty Name="ProductFk" ColumnName="PPS_PRODUCT_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="MaterialGroupId" ColumnName="MATERIAL_GROUP_ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionInformationEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionInformationEntity">
              <MappingFragment StoreEntitySet="RES_REQUISITIONINFO_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="IsReadOnly" ColumnName="ISREADONLY" />
                <ScalarProperty Name="ProjectCompanyFk" ColumnName="PRJ_COMPANY_FK" />
                <ScalarProperty Name="ProjectNo" ColumnName="PROJECTNO" />
                <ScalarProperty Name="ProjectName" ColumnName="PROJECT_NAME" />
                <ScalarProperty Name="ProjectName2" ColumnName="PROJECT_NAME2" />
                <ScalarProperty Name="ProjectCurrencyFk" ColumnName="PRJ_CURRENCY_FK" />
                <ScalarProperty Name="ProjectBusinessPartnerFk" ColumnName="PRJ_BUSINESSPARTNER_FK" />
                <ScalarProperty Name="ProjectIsLive" ColumnName="PRJ_ISLIVE" />
                <ScalarProperty Name="ProjectUserDefined01" ColumnName="PRJ_USERDEFINED1" />
                <ScalarProperty Name="ProjectUserDefined02" ColumnName="PRJ_USERDEFINED2" />
                <ScalarProperty Name="ProjectAddressFk" ColumnName="PRJ_ADDRESS_FK" />
                <ScalarProperty Name="ScheduleFk" ColumnName="PSD_SCHEDULE_FK" />
                <ScalarProperty Name="JobCode" ColumnName="JOB_CODE" />
                <ScalarProperty Name="JobDescription" ColumnName="JOB_DESCRIPTION" />
                <ScalarProperty Name="JobGroupFk" ColumnName="LGM_JOBGROUP_FK" />
                <ScalarProperty Name="JobSiteFk" ColumnName="JOB_SITE_FK" />
                <ScalarProperty Name="JobControllingUnitFk" ColumnName="JOB_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="JobUserDefined01" ColumnName="JOB_USERDEFINED1" />
                <ScalarProperty Name="JobUserDefined02" ColumnName="JOB_USERDEFINED2" />
                <ScalarProperty Name="JobValidFrom" ColumnName="JOB_VALIDFROM" />
                <ScalarProperty Name="JobValidTo" ColumnName="JOB_VALIDTO" />
                <ScalarProperty Name="JobBusinessPartnerFk" ColumnName="JOB_BUSINESSPARTNER_FK" />
                <ScalarProperty Name="JobDeliveryAddressRemark" ColumnName="JOB_DELIVERYADDR_REMARK" />
                <ScalarProperty Name="JobDeliveryAddressBlobFk" ColumnName="JOB_BLOBSDELIVERYADDR_FK" />
                <ScalarProperty Name="JobAddressFk" ColumnName="JOB_ADDRESS_FK" />
                <ScalarProperty Name="JobPlantFk" ColumnName="JOB_PLANT_FK" />
                <ScalarProperty Name="JobIsProjectDefault" ColumnName="JOB_ISPROJECTDEFAULT" />
                <ScalarProperty Name="JobIsLive" ColumnName="JOB_ISLIVE" />
                <ScalarProperty Name="JobTypeFk" ColumnName="JOB_TYPE_FK" />
                <ScalarProperty Name="JobIsMaintenance" ColumnName="JOB_ISMAINTENANCE" />
                <ScalarProperty Name="JobCalendarFk" ColumnName="JOB_CALENDAR_FK" />
                <ScalarProperty Name="JobCompanyFk" ColumnName="JOB_COMPANY_FK" />
                <ScalarProperty Name="JobRubricCategoryFk" ColumnName="JOB_RUBRICCATEGORY_FK" />
                <ScalarProperty Name="JobContextFk" ColumnName="JOB_CONTEXT_FK" />
                <ScalarProperty Name="JobCostCodePriceListFk" ColumnName="JOB_COSTCODEPRICELIST_FK" />
                <ScalarProperty Name="JobCostCodePriceVersionFk" ColumnName="JOB_COSTCODEPRICEVER_FK" />
                <ScalarProperty Name="ResourceTypeIsBulk" ColumnName="RES_TYPEISBULK" />
                <ScalarProperty Name="ResourceTypeIsSmallTools" ColumnName="RES_TYPEISSMALLTOOLS" />
                <ScalarProperty Name="DispatcherGroupFk" ColumnName="LGM_DISPATCHER_GROUP_FK" />
                <ScalarProperty Name="ResourceSiteFk" ColumnName="RES_SITE_FK" />
                <ScalarProperty Name="ReservedFrom" ColumnName="RESERVEDFROM" />
                <ScalarProperty Name="OnSiteFrom" ColumnName="ONSITEFROM" />
                <ScalarProperty Name="ReservedTo" ColumnName="RESERVEDTO" />
                <ScalarProperty Name="OnSiteTo" ColumnName="ONSITETO" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="StockProjectFk" ColumnName="PRJ_STOCKPROJECT_FK" />
                <ScalarProperty Name="ProjectChangeFk" ColumnName="PRJ_CHANGE_FK" />
                <ScalarProperty Name="ProjectChangeStatusFk" ColumnName="PRJ_CHANGESTATUS_FK" />
                <ScalarProperty Name="IsTimeEnhancement" ColumnName="ISTIMEENHANCEMENT" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="IsRequestBizPartner" ColumnName="ISREQUESTBIZPARTNER" />
                <ScalarProperty Name="IsRequestProjectDoc" ColumnName="ISREQUESTPRJDOC" />
                <ScalarProperty Name="PlannedStart" ColumnName="PLANNEDSTART" />
                <ScalarProperty Name="PlannedEnd" ColumnName="PLANNEDEND" />
                <ScalarProperty Name="EstimateQuantity" ColumnName="ESTIMATEQUANTITY" />
                <ScalarProperty Name="EstWorkOperationTypeFk" ColumnName="EST_WORKOPERATIONTYPE_FK" />
                <ScalarProperty Name="LgmDispatchHeaderMapFk" ColumnName="LGM_DISPATCHHEADERMAP_FK" />
                <ScalarProperty Name="ResRequisitionFormDataFk" ColumnName="RES_REQUISITION_FORMDATA_FK" />
                <ScalarProperty Name="ResRequisitionMapperFk" ColumnName="RES_REQUISITION_MAPPER_FK" />
                <ScalarProperty Name="Requisition2RequisitionFk" ColumnName="RES_REQUISITION2REQUISITION_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Requisition2RequisitionEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.Requisition2RequisitionEntity">
              <MappingFragment StoreEntitySet="RES_REQUISITION2REQUISITIONs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="IsReference" ColumnName="ISREFERENCE" />
                <ScalarProperty Name="IsTimeEnhancement" ColumnName="ISTIMEENHANCEMENT" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="RequisitionFk" ColumnName="RES_REQUISITION_FK" />
                <ScalarProperty Name="RequisitionLinkedFk" ColumnName="RES_REQUISITIONLINKED_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionGenReqFromEstVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqFromEstVEntity">
              <MappingFragment StoreEntitySet="RES_GEN_REQ_FROM_EST_Vs">
                <ScalarProperty Name="PrjProjectFk" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="PrjProjectName" ColumnName="PRJ_PROJECT_NAME" />
                <ScalarProperty Name="BasCompanyFk" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="PrjEstimateFk" ColumnName="PRJ_ESTIMATE_FK" />
                <ScalarProperty Name="EstHeaderFk" ColumnName="EST_HEADER_FK" />
                <ScalarProperty Name="EstHeaderDescription" ColumnName="EST_HEADER_DESCRIPTION" />
                <ScalarProperty Name="EstLineItemFk" ColumnName="EST_LINE_ITEM_FK" />
                <ScalarProperty Name="EstLineItemDescription" ColumnName="EST_LINE_ITEM_DESCRIPTION" />
                <ScalarProperty Name="EstAssemblyLineItemFk" ColumnName="EST_ASSEMBLY_LINE_ITEM_FK" />
                <ScalarProperty Name="EstAssemblyLineItemDescription" ColumnName="EST_ASSEMBLY_LINE_ITEM_DESCRIPTION" />
                <ScalarProperty Name="EstResourceFk" ColumnName="EST_RESOURCE_FK" />
                <ScalarProperty Name="EstResourceDescription" ColumnName="EST_RESOURCE_DESCRIPTION" />
                <ScalarProperty Name="EstResourceType" ColumnName="EST_RESOURCE_TYPE" />
                <ScalarProperty Name="EstAssemblyFk" ColumnName="EST_ASSEMBLY_FK" />
                <ScalarProperty Name="LgmJobFk" ColumnName="LGM_JOB_FK" />
                <ScalarProperty Name="LgmJobDescription" ColumnName="LGM_JOB_DESCRIPTION" />
                <ScalarProperty Name="CalCalendarFk" ColumnName="CAL_CALENDAR_FK" />
                <ScalarProperty Name="EtmPlantDescription" ColumnName="ETM_PLANT_DESCRIPTION" />
                <ScalarProperty Name="EtmPlantgroupDescription" ColumnName="ETM_PLANTGROUP_DESCRIPTION" />
                <ScalarProperty Name="PlantFk" ColumnName="ETM_PLANT_FK" />
                <ScalarProperty Name="EtmPlantCode" ColumnName="ETM_PLANT_CODE" />
                <ScalarProperty Name="EtmPlantgroupCode" ColumnName="ETM_PLANTGROUP_CODE" />
                <ScalarProperty Name="MdcControllingunitCode" ColumnName="MDC_CONTROLLINGUNIT_CODE" />
                <ScalarProperty Name="MdcControllingunitDescription" ColumnName="MDC_CONTROLLINGUNIT_DESCRIPTION" />
                <ScalarProperty Name="MdcControllingunitPlannedStart" ColumnName="MDC_CONTROLLINGUNIT_PLANNED_START" />
                <ScalarProperty Name="ResTypeFk" ColumnName="RES_TYPE_FK" />
                <ScalarProperty Name="ResTypeDescription" ColumnName="RES_TYPE_DESCRIPTION" />
                <ScalarProperty Name="LgmDispatcherGroupFk" ColumnName="LGM_DISPATCHER_GROUP_FK" />
                <ScalarProperty Name="EtmPlantBasUomFk" ColumnName="ETM_PLANT_BAS_UOM_FK" />
                <ScalarProperty Name="ResQuantity" ColumnName="RES_QUANTITY" />
                <ScalarProperty Name="ResBasUomFk" ColumnName="RES_BAS_UOM_FK" />
                <ScalarProperty Name="BasUomhourFk" ColumnName="BAS_UOMHOUR_FK" />
                <ScalarProperty Name="BasUomdayFk" ColumnName="BAS_UOMDAY_FK" />
                <ScalarProperty Name="BasUommonthFk" ColumnName="BAS_UOMMONTH_FK" />
                <ScalarProperty Name="ResWotBasUomFk" ColumnName="RES_WOT_BAS_UOM_FK" />
                <ScalarProperty Name="WorkOperationTypeFk" ColumnName="ETM_WORKOPERATIONTYPE_FK" />
                <ScalarProperty Name="ControllingUnitFk" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="Workhoursperday" ColumnName="WORKHOURSPERDAY" />
                <ScalarProperty Name="Workhourspermonth" ColumnName="WORKHOURSPERMONTH" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionGenReqSkillVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqSkillVEntity">
              <MappingFragment StoreEntitySet="RES_GEN_REQ_SKILL_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ResTypeFk" ColumnName="RES_TYPE_FK" />
                <ScalarProperty Name="ResSkillFk" ColumnName="RES_SKILL_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionGenReqTypVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqTypVEntity">
              <MappingFragment StoreEntitySet="RES_GEN_REQ_TYP_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ResTypeFk" ColumnName="RES_TYPE_FK" />
                <ScalarProperty Name="Duration" ColumnName="DURATION" />
                <ScalarProperty Name="BasUomdayFk" ColumnName="BAS_UOMDAY_FK" />
                <ScalarProperty Name="IsPlantRequested" ColumnName="ISPLANTREQUESTED" />
                <ScalarProperty Name="IsDriverRequested" ColumnName="ISDRIVERREQUESTED" />
                <ScalarProperty Name="IsCraneRequested" ColumnName="ISCRANEREQUESTED" />
                <ScalarProperty Name="IsTruckRequested" ColumnName="ISTRUCKREQUESTED" />
                <ScalarProperty Name="SkillOfRequestedTypeFk" ColumnName="RES_SKILL_OFREQUESTED_FK" />
                <ScalarProperty Name="Isrequestedentireperiod" ColumnName="ISREQUESTEDENTIREPERIOD" />
                <ScalarProperty Name="Necessaryoperators" ColumnName="NECESSARYOPERATORS" />
                <ScalarProperty Name="ResSkillFk" ColumnName="RES_SKILL_FK" />
                <ScalarProperty Name="ResTyperequestedFk" ColumnName="RES_TYPEREQUESTED_FK" />
                <ScalarProperty Name="LgmDispatcherGroupFk" ColumnName="LGM_DISPATCHER_GROUP_FK" />
                <ScalarProperty Name="CalCalendarFk" ColumnName="CAL_CALENDAR_FK" />
                <ScalarProperty Name="ResRequisitionFk" ColumnName="RES_REQUISITION_FK" />
                <ScalarProperty Name="ResSkillResTypeFk" ColumnName="RES_SKILL_RES_TYPE_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Requisition2RequisitionInfoVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.Requisition2RequisitionInfoVEntity">
              <MappingFragment StoreEntitySet="RES_REQUI_2_REQUI_INFO_Vs">
                <ScalarProperty Name="ParentResRequisitionFk" ColumnName="PARENT_RES_REQUISITION_FK" />
                <ScalarProperty Name="PrimaryResRequisitionFk" ColumnName="PRIMARY_RES_REQUISITION_FK" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ChildResRequisitionFk" ColumnName="CHILD_RES_REQUISITION_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="RequisitionChangeReqdateInfoVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionChangeReqdateInfoVEntity">
              <MappingFragment StoreEntitySet="RES_REQ_CHANGE_REQDATE_INFO_Vs">
                <ScalarProperty Name="ParentResRequisitionFk" ColumnName="PARENT_RES_REQUISITION_FK" />
                <ScalarProperty Name="PrimaryResRequisitionFk" ColumnName="PRIMARY_RES_REQUISITION_FK" />
                <ScalarProperty Name="ResTypeFk" ColumnName="RES_TYPE_FK" />
                <ScalarProperty Name="Duration" ColumnName="DURATION" />
                <ScalarProperty Name="PrimaryBasUomdayFk" ColumnName="PRIMARY_BAS_UOMDAY_FK" />
                <ScalarProperty Name="ResTyperequestedFk" ColumnName="RES_TYPEREQUESTED_FK" />
                <ScalarProperty Name="LgmDispatcherGroupFk" ColumnName="LGM_DISPATCHER_GROUP_FK" />
                <ScalarProperty Name="Isrequestedentireperiod" ColumnName="ISREQUESTEDENTIREPERIOD" />
                <ScalarProperty Name="Necessaryoperators" ColumnName="NECESSARYOPERATORS" />
                <ScalarProperty Name="ParentDuration" ColumnName="PARENT_DURATION" />
                <ScalarProperty Name="ParentBasUomdayFk" ColumnName="PARENT_BAS_UOMDAY_FK" />
                <ScalarProperty Name="ParentResTyperequestedFk" ColumnName="PARENT_RES_TYPEREQUESTED_FK" />
                <ScalarProperty Name="ParentIsrequestedentireperiod" ColumnName="PARENT_ISREQUESTEDENTIREPERIOD" />
                <ScalarProperty Name="ParentNecessaryoperators" ColumnName="PARENT_NECESSARYOPERATORS" />
                <ScalarProperty Name="LgmJobFk" ColumnName="LGM_JOB_FK" />
                <ScalarProperty Name="LgmJobDescription" ColumnName="LGM_JOB_DESCRIPTION" />
                <ScalarProperty Name="CalCalendarFk" ColumnName="CAL_CALENDAR_FK" />
                <ScalarProperty Name="BasUomhourFk" ColumnName="BAS_UOMHOUR_FK" />
                <ScalarProperty Name="BasUomdayFk" ColumnName="BAS_UOMDAY_FK" />
                <ScalarProperty Name="BasUommonthFk" ColumnName="BAS_UOMMONTH_FK" />
                <ScalarProperty Name="Workhoursperday" ColumnName="WORKHOURSPERDAY" />
                <ScalarProperty Name="Workhourspermonth" ColumnName="WORKHOURSPERMONTH" />
                <ScalarProperty Name="ResQuantity" ColumnName="RES_QUANTITY" />
                <ScalarProperty Name="ResWotBasUomFk" ColumnName="RES_WOT_BAS_UOM_FK" />
                <ScalarProperty Name="ResSkillFk" ColumnName="RES_SKILL_FK" />
                <ScalarProperty Name="IsSkillDemand" ColumnName="IS_SKILL_DEMAND" />
                <ScalarProperty Name="IsTertiaryDemand" ColumnName="IS_TERTIARY_DEMAND" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>