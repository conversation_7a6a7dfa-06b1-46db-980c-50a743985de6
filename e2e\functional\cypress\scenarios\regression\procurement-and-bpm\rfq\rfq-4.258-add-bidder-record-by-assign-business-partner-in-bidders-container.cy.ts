import { commonLocators, sidebar, app, cnt, btn } from "cypress/locators";
import { _common, _businessPartnerPage } from "cypress/pages";

const RFQ_HEADER = _common.generateRandomString(4)

let CONTAINER_COLUMNS_RFQ, CONTAINER_COLUMNS_BIDDERS;

let MODAL_BUSINESS_PARTNER;

let BUSINESSPARTNER_PARAMETERS_1;

describe("PCM- 4.258 | Add Bidder record by assign business partner  in Bidders container", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/rfq-4.258-add-bidder-record-by-assign-business-partner-in-bidders-container.json")
            .then((data) => {
                this.data = data;
                CONTAINER_COLUMNS_RFQ = this.data.CONTAINER_COLUMNS.RFQ
                CONTAINER_COLUMNS_BIDDERS = this.data.CONTAINER_COLUMNS.BIDDERS
                MODAL_BUSINESS_PARTNER = this.data.MODAL.BUSINESS_PARTNER
                BUSINESSPARTNER_PARAMETERS_1 = {
                    [commonLocators.CommonLabels.BUSINESS_PARTNER]: [MODAL_BUSINESS_PARTNER.BUSINESS_PARTNER_NAME[0]],
                };
            })
            .then(() => {
                cy.preLoading(Cypress.env("adminUserName"), Cypress.env("adminPassword"), Cypress.env("parentCompanyName"), Cypress.env("childCompanyName"));
            })
    });

    after(() => {
        cy.LOGOUT();
    })

    it("TC - Create a rfQ header record in rfq module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.RFQ);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.RFQ).then(() => {
            _common.setDefaultView(app.TabBar.RFQ)
            _common.select_tabFromFooter(cnt.uuid.REQUEST_FOR_QUOTE, app.FooterTab.RFQ, 0);
            _common.setup_gridLayout(cnt.uuid.REQUEST_FOR_QUOTE, CONTAINER_COLUMNS_RFQ)
        })
        _common.maximizeContainer(cnt.uuid.REQUEST_FOR_QUOTE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.REQUEST_FOR_QUOTE)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.REQUEST_FOR_QUOTE, 0)
        _common.enterRecord_inNewRow(cnt.uuid.REQUEST_FOR_QUOTE, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, RFQ_HEADER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.REQUEST_FOR_QUOTE)
    })

    it("TC - Create a bidder record in rfq module using lookup and verify the record in bidders container", function () {
        _common.openTab(app.TabBar.RFQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BIDDERS, app.FooterTab.BIDDERS, 1);
            _common.setup_gridLayout(cnt.uuid.BIDDERS, CONTAINER_COLUMNS_BIDDERS)
        })
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.BIDDERS)
        _common.clear_subContainerFilter(cnt.uuid.BIDDERS)
        _common.create_newRecord(cnt.uuid.BIDDERS, 0)
        _common.lookUpButtonInCell(cnt.uuid.BIDDERS, app.GridCells.BUSINESS_PARTNER_FK, btn.IconButtons.ICO_INPUT_LOOKUP, 0)
        _common.waitForLoaderToDisappear()
        _businessPartnerPage.enterRecord_toAddBusinessPartnerUsingLookUp(BUSINESSPARTNER_PARAMETERS_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BIDDERS)
        _common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.BIDDERS, app.GridCells.BUSINESS_PARTNER_FK, MODAL_BUSINESS_PARTNER.BUSINESS_PARTNER_NAME[0])
        _common.minimizeContainer(cnt.uuid.BIDDERS)    
    })

})