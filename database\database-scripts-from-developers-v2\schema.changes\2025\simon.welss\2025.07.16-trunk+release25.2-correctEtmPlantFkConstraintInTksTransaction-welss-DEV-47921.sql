-------------------------------------------------------
-- Ignore Errors (OPTIONAL): Off
-- JIRA Number(REQUIRED): DEV-47921
-- Script Type (REQUIRED): Required schema change script
-- Reason(REQUIRED):- [Timekeeping Period]: Error message popup appears in Timekeeping period module 
-- InstallOn(OPTIONAL): Release 25.2 + Trunk
-------------------------------------------------------
if not exists (SELECT * 
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_NAME='ETM_PLANT_FK53')
BEGIN
ALTER TABLE TKS_TRANSACTION ADD ETM_PLANT_FK int null
ALTER TABLE TKS_TRANSACTION 
    ADD CONSTRAINT ETM_PLANT_FK53
FOREIGN KEY (ETM_PLANT_FK)
REFERENCES ETM_PLANT (ID)
END
GO