{"CONTAINERS": {"ESTIMATE": {"RUBRIC_CATEGORY": "Planning", "ESTIMATE_TYPE": "Bid Estimate"}, "LINE_ITEM": {"QUANTITY": "100", "UOM": "%"}, "RESOURCE": {"SHORT_KEY": "M", "CODE": ["S452", "S451"], "QUANTITY": "50"}, "CONFIDENCE_CHECK": {"DESCRIPTION": "Package Created but Price not updated from Prc"}, "PACKAGE": {"CRITERIA_SELECTION": "Material & Cost Code"}, "PACKAGE_ITEMS": {"UPDATED_QUANTITY": "900"}}, "MODAL": {"UPDATE_ESTIMATE_WIZARD": [{"LABEL_NAME": "Linked Material Item", "INDEX": "0", "VALUE": "check"}, {"LABEL_NAME": "Create new Line Item for new Material Item", "INDEX": "0", "VALUE": "uncheck"}]}, "CONTAINER_COLUMNS": {"ESTIMATE": {"estheader.code": "Code", "estheader.descriptioninfo": "Description", "estheader.esttypefk": "Estimate Type", "estheader.rubriccategoryfk": "Category"}, "LINE_ITEM": {"descriptioninfo": "Description", "quantity": "Quantity", "basuomfk": "UoM", "costtotal": "Cost Total"}, "RESOURCE": {"estresourcetypeshortkey": "Short Key", "code": "Code", "quantity": "Quantity", "descriptioninfo": "Description", "basuomfk": "UoM", "costtotal": "Cost Total"}, "CONFIDENCE_CHECK": {"count": "Count"}, "PACKAGE_ITEMS": {"price": "Price"}, "ESTIMATE_LINE_ITEM": {"projectname": "Project Name", "projectno": "Project Number", "estimationcode": "Estimate Code", "estimationdescription": "Estimate Description"}}}