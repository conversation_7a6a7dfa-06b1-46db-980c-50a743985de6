{"basics": {"customize": {"mainTitle": "Masterdata", "entityTypeListTitle": "Data Types", "entityInstanceListTitle": "Data Records", "entityDataType": "Data Type", "entityModule": "<PERSON><PERSON><PERSON>", "threedvisualizationtype": "3D Visualization Type", "abcclassification": "ABC Classification", "addressformat": "Address Format", "addresstype": "Address Type", "characteristictype": "Characteristic Type", "chartprint": "Chart Print", "clerkgroup": "Clerk Group", "clerkrole2entityprop": "Clerk Role X Entity Property", "clerkrole": "Clerk Role", "companytype": "Company Type", "companyurltype": "Company URL Type", "costcodeportion": "Cost Code Portion", "costcodetype": "Cost Code Type", "costgroupportion": "Cost Group Portion", "currencyratetype": "Currency Rate Type", "dbactiontype": "DB Action Type", "displaydomain": "Display Domain", "documenttype": "Document Type", "itemstatus": "Item Status", "itemtype": "Item Type", "itemtype2": "Item Type 2", "itwobaselineserver": "iTwo-Baseline Server", "language": "Language", "papersize": "Paper Size", "paymentterm": "Payment Term", "periodstate": "Period Status", "quantitytype": "Quantity Type", "resourcekind": "Resource Kind", "resourcetype": "Resource Type", "resrequisitionstatus": "Resource Requisition Status", "resrequisitionstatusrole": "Resource Requisition Status Role", "resrequisitionstatusrule": "Resource Requisition Status Rule", "resrequisitionstatusworkflow": "Resource Requisition Status Workflow", "resreservationstatus": "Resource Reservation Status", "resreservationstatusrole": "Resource Reservation Status Role", "resreservationstatusrule": "Resource Reservation Status Rule", "resreservationstatusworkflow": "Resource Reservation Status Workflow", "rubric": "<PERSON><PERSON><PERSON>", "rubriccategory": "Rubric Category", "rubricindex": "Rubric Index", "schedulecontext": "Schedule Context", "sitetype": "Site Type", "scurve": "S-Curve", "scurvedetail": "S-Curve <PERSON>", "systemoption": "System Option", "textmoduletype": "Text Module Type", "title": "Title", "catalogassigntype": "Catalog Assignmet Type", "boqcatalog": "Boq-Catalog", "divisiontype": "Division Type", "boqitemflag": "BoQ Item Flag", "linetype": "Line Type", "boqstandard": "BoQ Standard", "boqtype": "BoQ Type", "bpdactivitytype": "BP Activity Type", "bpstatus": "BP Status", "bpstatusworkflow": "BP Status Workflow", "vatgroup": "VAT Group", "banktype": "Bank Type", "businessunit": "BusinessUnit", "certificatestatus": "Certificate Status", "certificatestatusrule": "Certificate Status Rule", "certificatestatusworkflow": "Certificate Status Workflow", "certificatetype": "Certificate Type", "contactabc": "Contact ABC", "contactorigin": "Contact Origin", "contactrole": "Contact Role", "contacttimelineness": "Contact Timelineness", "customerabc": "Customer ABC", "customerledgergroup": "Customer Ledger Group", "customergroup": "Customer Group", "customersector": "Customer Sector", "customerstate": "Customer State", "realestatetype": "Realestate Type", "bprole": "BP Role", "subledgercontext": "Subledger Context", "supplierledgergroup": "Supplier Ledgergroup", "calendartype": "Calendar Type", "contype": "Contract Type", "constatus": "Contract Status", "constatusworkflow": "Contract Status Workflow", "costrisk": "Cost Risk", "estcosttype": "Cost Type", "estmaterialtype": "Material Type", "ruleexecutiontype": "Rule Execution Type", "estassemblytype": "Est. Assembly Type", "estassemblytypelogic": "Est. Assembly Type Logic", "estactqtyrel": "Line-Item Quantity Relation Activity", "estboqqtyrel": "Line-Item Quantity Relation BoQ", "estctuqtyrel": "Line-Item Quantity Relation Controlling Unit", "estlocqtyrel": "Line-Item Quantity Relation Location", "estresourcetype": "Estimation Resource Type", "eststatus": "Estimation Status", "eststatusworkflow": "Estimation Status Workflow", "invrejectionreason": "Invoice Rejection Reason", "invoicestatus": "Invoice Status", "invoicestatusworkflow": "Invoice Status Workflow", "invoicetype": "Invoice Type", "billinglinetype": "Billing Line Type", "masterdatacontext": "Masterdata Context", "controllingunitassignment": "Controlling Unit Assignment", "ledgercontext": "Ledger Context", "lineitemcontext": "Lineitem Context", "materialcatalogtype": "Materialcatalog Type", "materialabc": "Material ABC", "pricelist": "Price List", "taxcode": "Tax Code", "wictype": "WIC Type", "accounttype": "Account Type", "awardmethod": "Award Method", "configuration2strategy": "Configuration X Strategy", "configuration2texttype": "Configuration2 X Texttype", "communiationchannel": "Communication Channel", "configurationheader": "Configuration Header", "procurementconfiguration": "Configuration", "procurementcontracttype": "Contract Type", "procurementcontractcopymode": "Contract Copy Mode", "generaltype": "General Type", "incoterm": "!Inco Term", "prcitemstatus": "Proc. Item Status", "prcitemstatusrole": "Proc. Item Status Role", "prcitemstatusrule": "Proc. Item Status Rule", "prcitemstatusworkflow": "Proc. Item Status Workflow", "uomtype": "Unit of Measurement Type", "milestonetype": "Milestone Type", "priceconditiontype": "Price Condition Type", "radius": "<PERSON><PERSON>", "strategy": "Strategy", "structuretype": "Structure Type", "projectcostgroup": "Project CostGroup", "enterprisecostgroup": "Enterprise CostGroup", "texttype": "Text Type", "orderstatus": "Order Status", "orderstatusrole": "Order Status Role", "orderstatusrule": "Order Status Rule", "orderstatusworkflow": "Order Status Workflow", "packagestatus": "Package Status", "packagestatusworkflow": "Package Status Workflow", "pesstatus": "PES Status", "pesstatusworkflow": "PES Status Workflow", "reqstatus": "Requisition Status", "reqstatusworkflow": "Requisition Status Workflow", "rfqstatus": "Rfq. Status", "rfqstatusworkflow": "Rfq. Status Workflow", "projectchance": "Project Chance", "changereason": "Change Reason", "changetype": "Change Type", "projectcontracttype": "Project Contract Type", "currencytype": "Currency Type", "projectdecision": "Project Decision", "projectgroup": "Project Group", "projectoutcome": "Project Outcome", "projecttype": "Project Type", "projectregion": "Project Region", "projectstadium": "Project Stadium", "activitytype": "Activity Type", "activitypresentation": "Activity Presentation", "baselinespec": "Baseline Specification", "constrainttype": "Constraint Types", "eventtype": "Scheduling Event Types", "performancesheet": "Performancesheet", "progressreportmethod": "Progressreport Method", "relationkind": "Relation Kind", "schedulemethod": "Schedule Method", "scheduletype": "Schedule Type", "tasktype": "Task Type", "quotationtype": "Quotation Type", "formulatype": "Formula Type", "qtolinetype": "QtO-Line Type", "qtotype": "QtO Type", "quotationstatus": "Quotation Status", "quotationstatusworkflow": "Quotation Status Workflow", "reqtype": "Requisition Type", "rfqrejectionreason": "Rfq. Rejection Reason", "rfqtype": "Rfq. Type", "wfeworkflowkind": "Workflow Kind", "wfeworkflowtype": "Workflow Type", "keyfigure": "Project Key Figure", "template": "Template", "linetemplate": "Linetemplate", "codelength": "Code Length", "isestimatecc": "IsestimateCc", "isrevenuecc": "IsrevenueCc", "icon": "Icon", "domainname": "Domain Name", "extention": "Extension", "byte": "Byte", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "filearchiveurlfk": "FilearchiveurlFk", "filearchivedocfk": "FilearchivedocFk", "estimate": "Estimate", "price": "Price", "location": "Location", "structure": "Structure", "classification": "Classification", "workcategory": "WorkCategory", "assetmaster": "AssetMaster", "budgetcode": "BudgetCode", "automatical": "Automatical", "islive": "Is Live", "color": "Color", "culture": "Culture", "days": "Days", "discountdays": "DiscountDays", "discountpercent": "DiscountPercent", "calculationtype": "CalculationType", "ofmonth": "OfMonth", "printdescription": "PrintDescription", "printdescriptiontr": "PrintDescriptionTr", "printtext": "Print Text", "descriptionshort": "Short Description", "parametervalue": "Parameter Value", "salutation": "Salutation", "addresstitle": "AddressTitle", "ratingschemafk": "Rating <PERSON><PERSON>", "isvalued": "Is Valued", "accountreceivables": "AccountReceivables", "accountinstallment": "AccountInstallment", "isfixed": "Is Fixed", "accountpayables": "Account<PERSON><PERSON><PERSON>", "shortkey": "Short Key", "rubriccategoryfk": "Rubric Category", "isprogress": "Is Progress", "currencyfk": "<PERSON><PERSON><PERSON><PERSON>", "contextfk": "Context", "issupplier": "Issupplier", "vatpercent": "Vatpercent", "icon1": "Icon1", "icon2": "Icon2", "configurationfk": "Configuration", "strategyfk": "Strategy", "texttypefk": "Texttype", "awardmethodfk": "Awardmethod", "contracttypefk": "Contracttype", "paymenttermfifk": "PaymentTermFiFk", "paymenttermpafk": "PaymentTermPaFk", "iscost": "Is Cost", "ispercent": "Is Percent", "isdelivered": "Is Delivered", "ispartdelivered": "Is Partial Delivered", "iscanceled": "Is Canceled", "iscritical": "Critical", "isinformationonly": "Is Information Only", "value": "Value", "hasvalue": "Has Value", "hastotal": "Has Total", "ispricecomponent": "Is Price Component", "isprinted": "Is Printed", "isbold": "Is Bold", "formula": "Formula", "radiusinmeter": "<PERSON><PERSON><PERSON><PERSON>", "forheader": "Forheader", "foritem": "Foritem", "isitalic": "Isitalic", "chance": "Chance", "project": "Project", "currencyconversion": "Currency Conversion", "currencyhome": "Home Currency", "currencyforeign": "Foreign Currency", "ratedate": "RateDate", "rate": "Rate", "commenttext": "Comment Text", "groupparentfk": "Group Parent", "uncpath": "<PERSON><PERSON><PERSON>", "stadiumfk": "Stadium", "businesspartnerfk": "Businesspartner", "rank": "Rank", "quotation": "Quotation", "discount": "Discount", "globalpercentage": "Globalpercentage", "otherdiscount": "OtherDiscount", "finalquotation": "FinalQuotation", "numberproposals": "NumberProposals", "remark": "Remark", "companyfk": "Company", "isfixeddate": "Isfixeddate", "schedulingcontextfk": "Scheduling Context", "allowmodify": "Allowmodify", "goniometer": "Goniometer", "textmodulefk": "Text Module", "isbond": "<PERSON>", "isemitted": "Is Emitted", "hascompany": "Has Company", "hascertificateDate": "Has Certificate Date", "hasissuer": "Has Issuer", "hasissuerbp": "Has Issuer Business Partner", "hasvalidfrom": "<PERSON>", "hasvalidto": "<PERSON>", "hasreference": "Has Reference", "hasreferenceDate": "Has Reference Date", "hasproject": "Has Project", "hascontract": "Has Contract", "hasvmount": "Has VMount", "isautomatic": "Is Automatic", "isstarted": "Is Started", "isdelayed": "Is Delayed", "isahead": "Is Ahead", "isfinished": "Is Finished", "isfinisheddelayed": "Is Finished Delayed", "isexecution": "Is Execution", "isprocurement": "Is Procurement", "headerFk": "Header", "hasexpirationDate": "Has Expiration Date", "reference": "Code Finance", "activitystate": "Activity State", "activitystateworkflow": "Activity Status Workflow", "customerbranch": "Customer Branch", "uomfk": "Unit", "iscurrency": "Is Currency", "isvisible": "Is Visible", "sequencename": "Sequence Name", "startvalue": "Start Value", "lastvalue": "Last Value", "incrementvalue": "Increment Value", "numbersequence": "Number Sequence", "customerstatus": "Customer Status", "customerstatusrule": "Customer Status Rule", "customerstatusworkflow": "Customer Status Workflow", "bpstatus2": "BP Status 2", "bpstatus2rule": "BP Status 2 Rule", "bpstatus2workflow": "BP Status 2 Workflow", "bpsupplierstatus": "Supplier Status", "bpsupplierstatusrule": "Supplier Status Rule", "bpsupplierstatusworkflow": "Supplier Status Workflow", "projectdocumentcategory": "Project Document Category", "projectdocumentstatus": "Project Document Status", "projectdocumentstatusrole": "Project Document Status Role", "projectdocumentstatusrule": "Project Document Status Rule", "projectdocumentstatusworkflow": "Project Document Status Workflow", "textmodulecontext": "Textmodule Context", "projectdocumenttype": "Project Document Type", "bidstatus": "Bid Status", "bidstatusrole": "Bid Status Role", "bidstatusrule": "Bid Status Rule", "bidstatusworkflow": "Bid Status Workflow", "qtostatus": "QTO Status", "qtostatusrole": "QTO Status Role", "qtostatusrule": "QTO Status Rule", "qtostatusworkflow": "QTO Status Workflow", "config": "Configuration", "columnconfig": "Column Configuration", "columnconfigtype": "Column Configuration Type", "evaluationmotive": "Evaluation Motive", "guarantortype": "Guarantor Type", "legalform": "Legal Form", "prcpackagetemplate": "Procurement Package Template", "prcpackagetemplateitem": "Procurement Package Template Item", "estconfigtype": "Estimation Config Type", "estquantityrel": "Estimation Quantity Relation", "eststructure": "Estimation Structure", "eststructuretype": "Estimation Structure Type", "estuppconfigtype": "Estimation URP Type", "costBudgetType": "Cost Budget Type", "estimationtype": "Estimation Type", "equipmentdivision": "Plant Division", "equipmentfixedasset": "Plant Fixed Asset", "plantkind": "Plant Kind", "plantstatus": "Plant Status", "plantstatusrole": "Plant Status Role", "plantstatusrule": "Plant Status Rule", "planttatusworkflow": "Plant Status Workflow", "planttype": "Plant Type", "billcategorydefault": "Bill <PERSON> Default", "billinvoicetype": "Bill Invoice Type", "billstatus": "Bill Status", "billstatusrole": "Bill Status Role", "billstatusrule": "Bill Status Rule", "billstatusworkflow": "Bill Status Workflow", "vouchertype": "Voucher Type", "workinprogressaccrualmode": "Work In Progress Accrual Mode", "workinprogressstatus": "Work In Progress Status", "workinprogressstatusrole": "Work In Progress Status Role", "workinprogressstatusrule": "Work In Progress Status Rule", "workinprogressstatusworkflow": "Work In Progress Status Workflow", "workinprogresstype": "Work In Progress Type", "chartpresentation": "Chart Presentation", "genericwizardsteptype": "Generic Wizard Step Type", "genericwizardscripttype": "Generic Wizard Script Type", "bpagreementtype": "Agreement Type", "tlssourcetype": "Translation Source Type", "cosinstheaderstatus": "COS Instance Header Status", "cosinstheaderstatusrole": "COS Instance Header Status Role", "cosinstheaderstatusrule": "COS Instance Header Status Rule", "cosinstheaderstatusworkflow": "COS Instance Header Status Workflow", "codeformat": "Code Format", "codeformattype": "Code Format Type", "objectinterest": "Object Interest", "objectleveltype": "Object Level Type", "objecttype": "Object Type", "objectpricelist": "Price List", "pricelistdetail": "Price List Detail", "objectprospectstatus": "Prospect Status", "objectprospectstatusrole": "Prospect Status Role", "objectprospectstatusrule": "Prospect Status Rule", "objectunitprospectworkflow": "Prospect Status Workflow", "objectunitareatype": "Object Unit Area Type", "objectunitactivitytype": "Object Unit Activity Type", "objectunitcategory": "Object Unit Category", "objectunitdocumenttype": "Object Unit Document Type", "objectunitkind": "Object Unit Kind", "objectunitpricetype": "Object Unit Price Type", "objectunitsubtype": "Object Unit Subtype", "objectunittype": "Object Unit Type", "objectunittypespec": "Object Unit Type Specification", "objectunitstatus": "Object Unit Status", "objectunitstatusrole": "Object Unit Status Role", "objectunitstatusrule": "Object Unit Status Rule", "objectunitstatusworkflow": "Object Unit Status Workflow", "resourcegroup": "Resource Group", "resourcedocumenttype": "Resource Document Type", "defectcontext": "Defect Context", "defectdocumenttype": "Defect Document Type", "equipmentcontext": "Plant Context", "defectgroup": "Defect Group", "defectpriority": "Defect Priority", "defectraisedby": "Defect Raised By", "defectseverity": "Defect Severity", "defectstatus": "Defect Status", "defectstatusrole": "Defect Status Role", "defectstatusrule": "Defect Status Rule", "defectstatusworkflow": "Defect Status Workflow", "defecttype": "Defect Type", "desktoppage": "Desktop Page", "desktopgroup": "Desktop Group", "resourcecontext": "Resource Context", "timesheetcontext": "Timesheet Context", "modelmarkertype": "Model Marker Type", "modelobjectsetstatus": "Object Set Status", "modelobjectsetstatusrole": "Object Set Status Role", "modelobjectsetstatusrule": "Object Set Status Rule", "modelobjectsetstatusworkflow": "Object Set Status Workflow", "modelobjectsettype": "Object Set Type", "tlssubject": "Translation Subject", "modulecontext": "Module Context", "warrantystatus": "Warranty Status", "ppseventtype": "PPS Event Type", "ppsheadergroup": "PPS Header Group", "ppsheaderstatus": "PPS Header Status", "ppsheaderstatusrole": "PPS Header Status Role", "ppsheaderstatusrule": "PPS Header Status Rule", "ppsheaderstatusworkflow": "PPS Header Status Workflow", "ppsitemstatus": "PPS Item Status", "ppsitemstatusrole": "PPS Item Status Role", "ppsitemstatusrule": "PPS Item Status Rule", "ppsitemstatusworkflow": "PPS Item Status Workflow", "ppsproductstatus": "PPS Product Status", "ppsproductstatusrole": "PPS Product Status Role", "ppsproductstatusrule": "PPS Product Status Rule", "ppsproductstatusworkflow": "PPS Product Status Workflow", "creditstanding": "Credit Standing", "reference01": "Reference 01", "reference02": "Reference 02", "reference03": "Reference 03", "reference04": "Reference 04", "reference05": "Reference 05", "postinggroup": "Posting Group", "evaluationstatus": "Evaluation Status", "evaluationstatusrole": "Evaluation Status Role", "evaluationstatusrule": "Evaluation Status Rule", "evaluationstatusworkflow": "Evaluation Status Workflow", "externalconfiguration": "External Configuration", "externalsource": "External Source", "externalsource2user": "External Source User", "externalsourcetype": "External Source Type", "authtype": "Authorization Type", "accountlookup": "Account <PERSON><PERSON>", "communicationchannel": "Communication Channel", "rythm": "<PERSON><PERSON><PERSON>", "installment": "Installment", "installmentagreement": "Installment Agreement", "metertype": "Meter Type", "projectstockaccountingtype": "Project Stock Accounting Type", "projectstockvaluationrule": "Project Stock Valuation Rule", "trspackagetatus": "Transport Package Status", "trspackagetatusrole": "Transport Package Status Role", "trspackagetatusrule": "Transport Package Status Rule", "trspackagetatusworkflow": "Transport Package Status Workflow", "trstatus": "Transport Status", "trstatusrole": "Transport Status Role", "trstatusrule": "Transport Status Rule", "trstatusworkflow": "Transport Status Workflow", "wfeworkflowversionstatus": "Workflow Version Status", "Invwarningtype": "Warning Type", "mdccontrollinggroup": "Controlling Group", "mdccontrollinggroupdetail": "Controlling Group Detail", "prceventtype": "Procurement Event Types", "prcitemevaluation": "Proc. Item Evaluation", "totalkind": "Total Kind", "packagetype": "Package Type", "prcsystemeventtype": "System Event Type", "prjcontrollingunittemplate": "Controlling Unit Template", "codegensequencetype": "Code Generation Sequence Type", "esttotalsconfigtype": "Est. Totals Configuration Type", "rficontributiontype": "RfI Contribution Type", "rfigroup": "RfI Group", "rfistatus": "RfI Status", "rfistatusrole": "RfI Status Role", "rfistatusrule": "RfI Status Rule", "rfistatusworkflow": "RfI Status Workflow", "rfitype": "RfI Type", "timekeepinggroup": "Timekeeping Group", "timekeepingpaymentgroup": "Timekeeping Payment Group", "timekeepingprofessionalcategory": "Timekeeping Professional Category", "Isshowstopper": "Is Show Stopper", "controllinggroup": "Controlling Group", "Ismainevent": "Ist das Hauptereignis", "EventtypeFk": "Scheduling Event Type", "code": "Code", "Hasstartdate": "Has Start Date", "SystemeventtypeStartFk": "Start System Event Type", "systemeventtypeEndFk": "End System Event Type", "assignment01": "Assignment 1", "assignment02": "Assignment 2", "assignment03": "Assignment 3", "assignment04": "Assignment 4", "assignment05": "Assignment 5", "assignment06": "Assignment 6", "assignment07": "Assignment 7", "assignment08": "Assignment 8", "assignment09": "Assignment 9", "assignment10": "Assignment 10", "message": "Message", "messageinfo": "MessageInfo", "issapcreated": "Created by SAP", "editname": "Edit Name", "workflowtemplatefk": "Workflow Template", "isbeforestatus": "Before", "isreadonly": "<PERSON><PERSON><PERSON>", "isreported": "Reported", "isvirtual": "Virtual", "isordered": "Ordered", "isinvoiced": "Invoiced", "statusfk": "Status", "ismandatory": "Mandatory", "clerkfk": "Clerk", "isactive": "Active", "isaccepted": "Accepted", "ispublished": "Published", "isadvertised": "Advertised", "isquoted": "Quoted", "isposted": "Posted", "ischained": "Chained", "businessgroup": "Business Group", "debtorgroup": "Debtor Group", "creditorgroup": "Creditor Group", "action": "Action", "activitystatusrole": "Activity Status Role", "activitystatusrule": "Activity Status Rule", "statusrule": "Status-Rule", "hasrolevalidation": "Validated", "workflow": "Workflow", "combinations": "Combinations", "rules": "Rules", "roles": "Roles", "isoptionalupwards": "Upwards", "isoptionaldownwards": "Downwards", "boqitemstatus": "Boq Item Status", "boqitemstatusrole": "Boq Item Status Role", "boqitemstatusworkflow": "Boq Item Status Workflow", "constatusrole": "Contract Status Role", "constatusrule": "Contract Status Rule", "eststatusrole": "Estimation Status Role", "eststatusrule": "Estimation Status Rule", "quotationstatusrole": "Quotation Status Role", "quotationstatusrule": "Quotation Status Rule", "pesstatusrole": "PES Status Role", "pesstatusrule": "PES Status Rule", "packagestatusrole": "Package Status Role", "packagestatusrule": "Package Status Rule", "reqstatusrole": "Requisition Status Role", "reqstatusrule": "Requisition Status Rule", "rfqstatusrole": "RfQ Status Role", "rfqstatusrule": "RfQ Status Rule", "invoicestatusrole": "Invoice Status Role", "invoicestatusrule": "Invoice Status Rule", "itemstatusrole": "Item Status Role", "itemstatusrule": "Item Status Rule", "itemstatusworkflow": "Item Status Workflow", "periodstatusrole": "Period Status Role", "periodstatusrule": "Period Status Rule", "projectchangestatus": "Project Change Status", "projectchangestatusrole": "Project Change Status Role", "projectchangestatusrule": "Project Change Status Rule", "projectchangestatustarget": "Project Change Status Target", "changestatusrulefk": "Project Change Status Rule", "projectstatus": "Project Status", "projectstatusrole": "Project Status Role", "projectstatusrule": "Project Status Rule", "projectchangestatusworkflow": "Project Change Status Workflow", "projectstatusworkflow": "Project Status Workflow", "accessrightdescriptor": "Access Right", "targetstatus": "Target Status", "bpstatusrule": "BP Status Rule", "periodstatusworkflow": "Period Status Workflow", "cosysdefaulttype": "Construction System Default", "cosysparametertype": "Construction System Parameter Type", "cosystype": "Construction System Type", "estevaluationsequence": "Evaluation Sequence", "estparameter": "Estimation Parameter", "estparametergroup": "Estimation Parametergroup", "modellevelofdevelopment": "Level of Development", "modelstatus": "Model Status", "modelstatusrole": "Model Status Role", "modelstatusrule": "Model Status Rule", "modelstatusworkflow": "Model Status Workflow", "modeltype": "Model Type", "modelvaluetype": "Model Value Type", "default": "<PERSON><PERSON><PERSON>", "conditiontype": "Condition Type", "matrixdatasource": "Matrix Data Source", "matrixsummarizetype": "Summarize Type", "operator": "Operator", "verticaldata": "Vertical Data", "horizontaldata": "Horizontal Data", "celldata": "Cell Data", "domain": "Domain", "entityDocumentation": "Documentation", "entityTypeDocumentation": "Type Documentation", "contrunitstatus": "Controlling Unit Status", "contrunitstatusrole": "Controlling Unit Status Role", "contrunitstatusrule": "Controlling Unit Status Rule", "contrunitstatusworkflow": "Controlling Unit Status Workflow", "creditorlinetype": "Creditor Line Type", "debitorlinetype": "Debitor Line Type", "type01": "Clock", "type02": "Play", "type03": "Flag", "type04": "Star", "type05": "Key", "type06": "Two Circles", "type07": "Envelope", "type08": "Envelope Open", "type09": "Person", "type10": "<PERSON>n", "type11": "Bell", "type12": "Glass", "type13": "<PERSON>g", "type14": "Balloon", "type15": "Padlock", "type16": "Calendar", "type17": "Gift", "type18": "Asterix", "type19": "Plus", "type20": "Minus", "type21": "Arrow1 Down", "type22": "Arrow1 Up", "type23": "Arrow1 Left", "type24": "Arrow1 Right", "type25": "Arrow2 Down", "type26": "Arrow2 Up", "type27": "Arrow2 Left", "type28": "Arrow2 Right", "type29": "Arrow3 Down", "type30": "Arrow3 Up", "type31": "Arrow3 Left", "type32": "Arrow3 Right", "type33": "Cross", "type34": "Circle", "type35": "Rhombus", "type36": "Hook", "type37": "Pause", "type38": "Equal", "type39": "Square", "type40": "Star", "ccode01": "Circle", "ccode02": "Equal", "ccode03": "Arrow Up", "ccode04": "Arrow Down", "construction01": "Construction Work", "construction02": "Earthworks", "construction03": "Drilling Works", "construction04": "Borehole Sinking Operations", "construction05": "Timbering to Trenchwork", "construction06": "Piling", "construction07": "Groundwater Lowering", "construction08": "Underground Drainage", "construction09": "Pressure Pipework", "construction10": "Land Drainage", "construction11": "Ground Treatment by Grouting", "construction12": "Dredging Work", "construction13": "Underground Construction Work", "construction14": "Diaphragm Walling", "construction15": "Sprayed <PERSON><PERSON><PERSON>", "construction16": "Road Construction1", "construction17": "Road Construction2", "construction18": "Road Construction3", "construction19": "Road Construction4", "construction20": "Trenchless <PERSON>", "construction21": "Landscape Works", "construction22": "Jet Grouting Work", "construction23": "Underground Cable Laying Work", "construction24": "Clearing of Unexploded Ordnance", "construction25": "Horizontal Directional Drilling Works", "construction26": "Track Construction", "construction27": "Drainage Channels", "construction28": "Masonry Work", "construction29": "Concrete Work", "construction30": "Natural Stone Works", "construction31": "Cast Stone Works", "construction32": "Timber Construction Works", "construction33": "Structural Steelwork", "construction34": "Waterproofing", "construction35": "Roofing Work", "construction36": "Covering work", "construction37": "Dry Lining and Partitioning Work", "construction38": "Thermal Insulation Composite Systems", "construction39": "Repair Work on Concrete Structures", "construction40": "Plastering and Rendering", "construction41": "Curtain walling", "construction42": "Wall and Floor Tiling", "construction43": "Screed Work", "construction44": "Asphalt Flooring Work", "construction45": "Joinery", "construction46": "Parquet Work", "construction47": "Mounting of Door and Window Hardware", "construction48": "Rolling Shutters Works", "construction49": "Metal Work", "construction50": "Glazing works", "construction51": "Painting and Coating Work", "construction52": "Corrosion Protection", "construction53": "Flooring Work", "construction54": "Wallpapering", "construction55": "Wood Block Flooring", "construction56": "Air Conditioning Systems", "construction57": "Heating Systems", "construction58": "<PERSON><PERSON>", "construction59": "Electrical Supply Systems", "construction60": "Lightning Protection", "construction61": "Escalators", "construction62": "Building Automation", "construction63": "Scaffolding Work", "construction64": "Demolition Work", "construction65": "Sanitary Works", "facility01": "Organization", "facility02": "Branch", "facility03": "Production facility", "facility04": "Storage", "facility05": "Stock yard", "facility06": "Office building", "facility07": "Workshop", "facility08": "Production Area", "relationtype": "Businesspartner Relation Type", "OppositeDesc": "Opposite Description", "Accountingvalue": "Accounting Value", "Isinternalonly": "Internal Only", "isrejected": "Is Rejected", "isupdatedimport": "Import Is Updated", "isupdateimport": "Updated Import", "autoupdate": "Autmatic Update", "isonlyfwd": "Only Forwarded", "valuedetail": "Value Detail", "selectCell": " No cell is selected from the matrix or this action is not allowed!", "actionNotAllowed": "This action is not allowed!", "dbtablename": "DB Table Name", "isbid": "Is Bid", "iscontract": "Is Contract", "iswarranty": "Is Warranty", "font": "Font", "fontcolor": "Font Color", "fontsize": "Font Size", "isbilled": "Is Billed", "isstorno": "Is Storno", "isarchived": "Is Archived", "isbtrequired": "Is BT-Required", "increment": "Increment", "minlength": "Minimal Length", "isrequest": "Is Request", "isescalation": "Is Escalation", "certificate": "Certificate", "increaseafterdays": "Increase Date", "reminder": "Reminder", "esticoup": "To Structure", "esticodown": "From Structure", "esticominus": "No Relation", "type": "Type", "acronym": "Acronym", "totalsconfig": "Est. Totals Configuration", "isjob": "Is Job", "specification": "Specification", "unitprice": "Unit Price", "gridguid": "Grid GUID", "detailguid": "Detail GUID", "resgridguid": "Resource Grid Guid", "resdetailguid": "Resource Details Guid", "abbreviation": "Abbreviation", "isadvised": "Is Advised", "ispartiallyaccepted": "Is Partially Accepted", "paymentdelay": "Payment Delay", "totaltype": "Total Type", "percentoftime": "Percent of Time", "percentofcost": "Percent of Cost", "FromTo": "From \\ To", "vertdatatype": "Vertical Datatype", "hordatatype": "Horizontal Datatype", "celldatatype": "Cell Datatype", "texawardmethod": "Tender Exchange Award Method", "texauthoritycode": "Tender Exchange Authority Code", "texlot": "Tender Exchange Lot", "url": "Url", "user": "User", "password": "Password", "isrevitopen": "Revit Open", "ispoolresource": "Pool Resource", "editable": "Editable", "isusercontrolled": "User Controlled", "ismaintable": "Maintable", "isgroupa": "Group A", "isgroupb": "Group B", "isgroupc": "Group C", "isopen": "Is Open", "isaccountpayablesopen": "Is Account Payables Open", "isaccountreceivablesopen": "Is Account Receivable Open", "retentionpayables": "Retention Payables", "retentioninstallment": "Retention Installment", "retentionreceivables": "Retention Receivables", "parametercount": "No. of Parameter", "nameinfo": "Name Info", "isorientationlandscape": "Landscape", "report": "Report", "module": "<PERSON><PERSON><PERSON>", "entity": "Entity", "attribute": "Attribute", "isheader": "Header", "isfooter": "Footer", "height": "Height", "isrevised": "Revised", "estAssemblyTypeShortKeyError": "The length of short key should be equal or less than {{length}} chars", "iscovered": "Covered", "islimitedcovered": "Limited Covered", "isexpired": "Expired", "ischangsent": "Change Is Sent", "ischangeaccepted": "Change Is Accepted", "ischangerejected": "Change Is Rejected", "ischangeable": "Ischangeable", "assignment01name": "Assignment-01-Name", "assignment01transfer": "Assignment-01-Transfer", "assignment02name": "Assignment-02-Name", "assignment02transfer": "Assignment-02-Transfer", "assignment03name": "Assignment-03-Name", "assignment03transfer": "Assignment-03-Transfer", "assignment04name": "Assignment-04-Name", "assignment04transfer": "Assignment-04-Transfer", "assignment05name": "Assignment-05-Name", "assignment05transfer": "Assignment-05-Transfer", "assignment06name": "Assignment-06-Name", "assignment06transfer": "Assignment-06-Transfer", "assignment07name": "Assignment-07-Name", "assignment07transfer": "Assignment-07-Transfer", "assignment08name": "Assignment-08-Name", "assignment08transfer": "Assignment-08-Transfer", "assignment09name": "Assignment-09-Name", "assignment09transfer": "Assignment-09-Transfer", "assignment10name": "Assignment-10-Name", "assignment10transfer": "Assignment-10-Transfer", "resourcefolder01": "Folder People", "resourcefolder02": "Folder Facilities", "resourcefolder03": "Folder Tools", "resourcefolder04": "Folder Transport", "resourcefolder05": "Folder Overlay 1", "resourcefolder06": "Folder Overlay 2", "resourcefolder07": "Folder Overlay 3", "resourcefolder08": "Folder Overlay 4", "resourcefolder09": "Folder Overlay 5", "resourcefolder10": "Folder Overlay 6", "resourcefolder11": "Folder Overlay 7", "resourcefolder12": "Folder Overlay 8", "resourcefolder13": "Folder Overlay 9", "resourcefolder14": "Folder Overlay People", "resourcefolder15": "Folder Overlay Facilities", "resourcefolder16": "Folder Overlay Tools", "resourcefolder17": "Folder Overlay Transport", "resourcetype01": "Worker 1", "resourcetype02": "Worker 2", "resourcetype03": "Worker 3", "resourcetype04": "Employee 1", "resourcetype05": "Robot 1", "resourcetype06": "Robot 2", "resourcetype07": "Brush", "resourcetype08": "Open-end wrench", "resourcetype09": "Hammer", "resourcetype10": "Spa<PERSON>la", "resourcetype11": "<PERSON><PERSON><PERSON>", "resourcetype12": "Spade", "resourcetype13": "Drill", "resourcetype14": "Trowel", "resourcetype15": "Pliers", "resourcetype16": "Tape measure", "resourcetype17": "Truck 1", "resourcetype18": "Truck 2", "resourcetype19": "Truck 3", "resourcetype20": "Mobile Crane", "resourcetype21": "Bulldozer", "resourcetype22": "Forklift", "resourcetype23": "Crane 1", "resourcetype24": "Crane 2", "resourcetype25": "Factory 1", "resourcetype26": "Factory 2", "resourcetype27": "Factory 3", "resourcetype28": "Factory 4", "resourcetype29": "Factory 5", "resourcetype30": "Factory 6", "boqItemFlagCodeError": "Code should be unique!", "catalogassignment": "Catalog Assignment", "valuetype": "Value Type", "islookup": "Is Lookup", "ruleparamvalue": "Rule Parameter Value", "isunique": "Is Unique", "oldvalue": "Old Value", "newvalue": "New Value", "issystemevent": "Is System", "isactivity": "Is Activity", "issystemtype": "Is System Type", "backgroundcolor": "Background Color", "isinproduction": "In Production", "isdeletable": "Deletable", "configwarning": "Warning", "nocontexterrormsg": "Can not be saved if there is no Context! Please select a context first.", "Readonly": "<PERSON><PERSON><PERSON>", "controllinggroup01": "Controlling Group 01", "controllinggroup02": "Controlling Group 02", "controllinggroup03": "Controlling Group 03", "controllinggroup04": "Controlling Group 04", "controllinggroup05": "Controlling Group 05", "controllinggroup06": "Controlling Group 06", "controllinggroup07": "Controlling Group 07", "controllinggroup08": "Controlling Group 08", "controllinggroup09": "Controlling Group 09", "controllinggroup10": "Controlling Group 10", "isfullycovered": "Fully Covered", "isconfirmed": "Fully Confirmed", "usessl": "Use SSL", "requiretimestamp": "Require Timestamp", "enableencryption": "Enable Encryption", "enablesignatures": "Enable Signatures", "encryptresponse": "Enable Response", "isstock": "Stock", "installmentpercent": "Installment Percent", "description1": "Description 1", "description2": "Description 2", "ismountingactivity": "Is Mounting Activity", "asset": "<PERSON><PERSON>", "isplannable": "Is Plannable", "isasset": "<PERSON>", "isbulk": "Is Bulk", "iscluster": "Is Cluster", "isclustered": "Is Clustered", "isinpackaging": "Is In Packaging", "istransportable": "Is Transportable", "isintransport": "Is In Transport", "isinloading": "Is In Loading", "resourcegroupparent": "Parent Group", "relationcolor": "Relation Color", "oppositerelationcolor": "Opposite Relation Color", "formdatastatus": "Form Data Status", "formdatastatusrole": "Form Data Status Role", "formdatastatusrule": "Form Data Status Rule", "formdatastatusworkflow": "Form Data Status Workflow", "jobgroup": "Job Group", "jobtype": "Job Type", "isexternal": "Is External", "report2": "Report 2", "reqstatusfk": "Requisition Status", "reqstatusrulefk": "Requisition Status Rule", "stgstatusfk": "Stage Status", "stgstatusrulefk": "Stage Status Rule", "actstatusrulefk": "Act Status Rule", "actstatusfk": "Act Status", "isapproved": "Is Approved", "repstatusfk": "Report Status", "repstatusrulefk": "Report Status Rule", "typicalcode": "Typical Code", "accessorytypefk": "Accessory Type", "Isallowedmanual": "Is Allowed Manual", "entityfk": "Entity", "resourcefk": "Resource", "accessgroupfk": "Access Group", "accessrolefk": "Access Role", "jobstatus": "Job Status", "jobstatusrule": "Job Status Rule", "jobstatusrole": "Job Status Role", "jobstatusworkflow": "Job Status Workflow", "jobstatustarget": "Job Status Target", "showinpricecomparison": "Show In Price Comparison", "isreceipt": "Is Receipt", "Isconsumed": "Is Consumed", "isprovision": "Is Provision", "isreservation": "Is Reservation", "denydelete": "<PERSON><PERSON>", "assignedcol": "Assigned Col", "projectref": "Project Ref", "remarkinfo": "Remark Info", "isplanned": "Is Planned", "isactual": "Is Actual", "maintstatus": "Maint Status", "maintstaturulefk": "Maint Status Rule", "maintstatusrolefk": "Maint Status Role", "plantgroupfk": "Plant Group", "ispriced": "Is Priced", "percent": "Percent", "ismanualeditplantmaster": "Is Manual Edit Plant Master", "ismanualeditjob": "Is Manual Edit Job", "ismanualeditdispatching": "Is Manual Edit Dispatching", "priceportion1name": "Price Portion 1 Name", "priceportion2name": "Price Portion 2 Name", "priceportion3name": "Price Portion 3 Name", "priceportion4name": "Price Portion 4 Name", "priceportion5name": "Price Portion 5 Name", "priceportion6name": "Price Portion 6 Name", "validfrom": "<PERSON><PERSON>", "validto": "<PERSON><PERSON>", "ishire": "<PERSON>", "rtestatusrulefk": "rtestatusrulefk", "rtestatusfk": "rtestatusfk", "isprotected": "Is Protected", "externalsourcefk": "External Source", "prcitemstatus2external": "Proc. Item Status to External", "pesstatus2external": "PES Status to External", "constatus2external": "Contract Status to External", "invoicestatus2external": "Invoice Status to External", "transportrtestatus": "Transport Route Status", "transportrtestatusrole": "Transport Route Status Role", "transportrtestatusrule": "Transport Route Status Rule", "transportrtestatusworkflow": "Transport Route Status Workflow", "structuretype01": "Structure Type Icon 01", "structuretype02": "Structure Type Icon 02", "structuretype03": "Structure Type Icon 03", "structuretype04": "Structure Type Icon 04", "structuretype05": "Structure Type Icon 05", "structuretype06": "Structure Type Icon 06", "structuretype07": "Structure Type Icon 07", "structuretype08": "Structure Type Icon 08", "structuretype09": "Structure Type Icon 09", "structuretype10": "Structure Type Icon 10", "structuretype11": "Structure Type Icon 11", "structuretype12": "Structure Type Icon 12", "structuretype13": "Structure Type Icon 13", "structuretype14": "Structure Type Icon 14", "structuretype15": "Structure Type Icon 15", "structuretype16": "Structure Type Icon 16", "structuretype17": "Structure Type Icon 17", "structuretype18": "Structure Type Icon 18", "structuretype19": "Structure Type Icon 19", "structuretype20": "Structure Type Icon 20", "structuretype21": "Structure Type Icon 21", "structuretype22": "Structure Type Icon 22", "structuretype23": "Structure Type Icon 23", "structuretype24": "Structure Type Icon 24", "structuretype25": "Structure Type Icon 25", "structuretype26": "Structure Type Icon 26", "structuretype27": "Structure Type Icon 27", "structuretype28": "Structure Type Icon 28", "structuretype29": "Structure Type Icon 29", "structuretype30": "Structure Type Icon 30", "structurefolder01": "Structure Icon 01", "structurefolder02": "Structure Icon 02", "structurefolder03": "Structure Icon 03", "structurefolder04": "Structure Icon 04", "structurefolder05": "Structure Icon 05", "structurefolder06": "Structure Icon 06", "structurefolder07": "Structure Icon 07", "structurefolder08": "Structure Icon 08", "structurefolder09": "Structure Icon 09", "structurefolder10": "Structure Icon 10", "structurefolder11": "Structure Icon 11", "structurefolder12": "Structure Icon 12", "structurefolder13": "Structure Icon 13", "structurefolder14": "Structure Icon 14", "structurefolder15": "Structure Icon 15", "structurefolder16": "Structure Icon 16", "structurefolder17": "Structure Icon 17", "structurefolder18": "Structure Icon 18", "structurefolder19": "Structure Icon 19", "structurefolder20": "Structure Icon 20", "maintenancestatus": "Plant Maintenance Status", "maintenancestatusrole": "Plant Maintenance Status Role", "maintenancestatusrule": "Plant Maintenance Status Rule", "maintenancestatusworkflow": "Plant Maintenance Status Workflow", "assembly2wicflag": "Assembly 2 WIC Flag", "transportmaterialrequisitionstatus": "Transport Material Requisition Status", "transportmaterialrequisitionstatusrole": "Transport Material Requisition Status Role", "transportmaterialrequisitionstatusrule": "Transport Material Requisition Status Rule", "transportmaterialrequisitionstatusworkflow": "Transport Material Requisition Status Workflow", "notshownforpacking": "Not Shown For Packing", "mrqstatusrulefk": "Material Requisition Status Rule", "mrqstatusfk": "Material Requisition Status", "mrqstatustargetfk": "Material Requisition Status Target", "isticketsystem": "Is Ticket System", "isproduced": "Is Produced", "accessrightdescriptor1fk": "<PERSON>", "accessrightdescriptor2fk": "Container PES", "accessrightdescriptor3fk": "Container Contract", "accessrightdescriptor4fk": "Container Other", "accessrightdescriptor5fk": "Container Chained Invocies", "rubriccategorycfk": "Rubric Category C", "rubriccategorysfk": "Rubric Category S", "noAssignment": "No Assignment", "boqHeaderFk": "BoQ", "mdcControllingUnitFk": "Controlling Units", "prjLocationFk": "Location", "psdActivityFk": "Activity", "licCostGroup1Fk": "Cost Group 1", "licCostGroup2Fk": "Cost Group 2", "licCostGroup3Fk": "Cost Group 3", "licCostGroup4Fk": "Cost Group 4", "licCostGroup5Fk": "Cost Group 5", "prjCostGroup1Fk": "Project Cost Group 1", "prjCostGroup2Fk": "Project Cost Group 2", "prjCostGroup3Fk": "Project Cost Group 3", "prjCostGroup4Fk": "Project Cost Group 4", "prjCostGroup5Fk": "Project Cost Group 5", "costTypes": "Cost Types", "resourceFlags": "Resource Flags", "activeUnitRateStrQty": "Active Unit Rate/Str.Qty", "getQuantityTotalToStructure": "Get Line Item Quantity Total To Structure", "toStructureConfig": "To Structure Config", "dispatchergroupfk": "Dispatcher Group", "isfixeddays": "Fixed Days", "daysafter": "Days After", "isperformancebased": "Performance Based", "duration": "Duration", "quantity": "Quantity", "userdefined1": "Userdefined 1", "userdefined2": "Userdefined 2", "userdefined3": "Userdefined 3", "userdefined4": "Userdefined 4", "userdefined5": "Userdefined 5", "abbreviation2": "Abbreviation 2", "dispatchstatus": "Dispatch Status", "projectgroupstatus": "Group Status", "dispatchstatusrule": "Dispatch Status Rule", "dispatchstatusrole": "Dispatch Status Role", "isrequested": "Is Requested", "messageseverityfk": "Message Severity", "ispesaccrual": "<PERSON>", "checkConfig": "Check Configuration", "ConfigurationCheckSuccessful": "Configuration Check was successful", "ConfigurationCheckError": "Error in Configuration Check", "CheckingConfig": "Checking Config", "showintasklist": "Show In Task List", "taskstatusrulefk": "Task Status Rule", "taskstatusfk": "Task Status", "taskstatustargetfk": "Task Status Target", "userflag1": "User Flag 1", "userflag2": "User Flag 2", "isdeleteable": "Is Deleteable", "isshipped": "Is Shipped", "logisticscontext": "Logistic Context", "hasamount": "Has Amount", "isstockposted": "Is Stock Posted", "settlementstatusrulefk": "Settlement Status Rule", "settlementstatusfk": "Settlement Status", "settlementstatustargetfk": "Settlement Status Target", "goniometertypefk": "Goniometer Type", "baseyear": "Base Year", "indexyear": "Index Year", "priceindex": "Price Index", "authenticationtype": "Authentication Type", "goniometertype": "Goniometer Type", "boqitemstatusrule": "Boq Item Status Rule", "messageseverity": "Message Severity", "transactiontype": "Transaction Type", "billtype": "<PERSON>", "engineeringtype": "Engineering Type", "engineeringstatus": "Engineering Status", "engineeringstatusrole": "Engineering Status Role", "engineeringstatusrule": "Engineering Status Rule", "engineeringstatusworkflow": "Engineering Status Workflow", "engineeringtaskstatus": "Engineering Task Status", "engineeringtaskstatusrole": "Engineering Task Status Role", "engineeringtaskstatusrule": "Engineering Task Status Rule", "engineeringtaskstatusworkflow": "Engineering Task Status Workflow", "estcolumnconfigtype": "Est. <PERSON><PERSON>n Config Type", "estresourceflag": "Resource Flag", "equipmentaccessorytype": "Plant Accessory Type", "equipmentcatalogpriceindex": "Plant Catalog Price Index", "equipmentmaintenancerecord": "Palnt Maintenance Record", "equipmentpartnertype": "Plant Partner Type", "plantdocumenttype": "Plant Document Type", "equipmentplantgroupwotype": "Plant Group Wo Type", "equipmentpricinggroup": "Plant Pricing Group", "equipmentpricelist": "Plant Price List", "equipmentpricelisttype": "Plant List Type", "frmidentityprovider": "Framework Identity Provider", "frmportalusergroup": "Framework Portal User Group", "invoicegroup": "Invoice Group", "logisticssettlementstatus": "Logistics Settlement Status", "logisticssettlementstatusrole": "Logistics Settlement Status Role", "logisticssettlementstatusrule": "Logistics Settlement Status Rule", "logisticssettlementstatusworkflow": "Logistics Settlement Status Workflow", "logisticsdispatchergroup": "Logistics Dispatcher Group", "dispatchstatusworkflow": "Dispatch Status Workflow", "jobdocumenttype": "Job Document Type", "logisticrecordtype": "Logistic Record Type", "mdlchangetype": "Mdl Change Type", "modelfilterstate": "Model Filter State", "modelobjectvisibility": "Model Object Visibility", "mountingrequisitionstatus": "Mounting Requisition Status", "mountingrequisitionstatusrole": "Mounting Requisition Status Role", "mountingrequisitionrule": "Mounting Requisition Rule", "mountingrequisitionstatusworkflow": "Mounting Requisition Status Workflow", "mountingstagestatus": "Mounting Stage Status", "mountingstagestatusrole": "Mounting Stage Status Role", "mountingstagestatusrule": "Mounting Stage Status Rule", "mountingstagestatusworkflow": "Mounting Stage Status Workflow", "mountingactivitystatus": "Mounting Activity Status", "mountingactivitystatusrole": "Mounting Activity Status Role", "mountingactivitystatusrule": "Mounting Activity Status Rule", "mountingactivitystatusworkflow": "Mounting Activity Status Workflow", "mountingreportstatus": "Mounting Report Status", "mountingreportstatusrole": "Mounting Report Status Role", "mountingreportstatusrule": "Mounting Report Status Rule", "mountingreportstatusworkflow": "Mounting Report Status Workflow", "Tpricelistdetail": "Price List Detail", "objectpricetype": "Object Price Type", "accrualmode": "Pes Accrual Mode", "prcaccountlookup": "Procurement Account Lookup", "procurementdocumenttype": "Procurement Document Type", "prcstocktransactiontype": "Procurement Stock Transaction Type", "rfqbusinesspartnerstatus": "Rfq. Business Partner Status", "ppsdefaultcategory": "PPS Default Category", "ppsentity": "PPS Entity", "eventtype2restype": "PPS Event Type 2 Resource Type", "Tperformancesheet": "Performancesheet", "qtotstatus": "QtO Status", "resrequisitiontype": "Resource Requisition Type", "resourcerequisitiongroup": "Resource Requisition Group", "resrequisitionpriorty": "Resource Requisition Priority", "Tresourcereservationtype": "Resource Reservation Type", "transportrequisitionstatus": "Transport Requisition Status", "transportrequisitionstatusrole": "Transport Requisition Status Role", "transportrequisitionstatusrule": "Transport Requisition Status Rule", "transportrequisitionstatusworkflow": "Transport Requisition Status Workflow", "reservationtype01": "Transport", "reservationtype02": "Construction", "reservationtype03": "Maintenance", "reservationtype04": "Production", "reservationtype05": "Mounting", "reservationtype06": "Vacation", "reservationtype07": "Illness", "reservationtype08": "Suspension", "pricelisttypefk": "Pricelist Type", "ismounting": "Is Mounting", "itemtype85": "Item Type 85", "projectcontext": "Project Context", "constructionSystemMasterCodeLength": "Value can only be number between 1 to 37!", "maxvalue": "Max Value", "prefix": "Prefix", "suffix": "Suffix ", "jobfk": "Job", "islinkedfixtoreservation": "Is Linked Fixtoreservation", "isvalidatereference": "Validate Reference", "isdeferable": "Deferable", "codefinance": "Code Finance", "itwo40user": "iTWO 4.0 User", "ispicked": "Is Picked", "dispatchrecordstatus": "Dispatch Record Status", "dispatchrecordstatusrule": "Dispatch Record Status Rule", "activitystate2external": "Activity Status 2 External", "dispatchergroupincludein": "Include In", "logisticsdispatchergroup2group": "Logistics Dispatcher Group 2 Group", "foreventtype": "For Event Type", "isplanningfinished": "Is Planning Finished", "costbudgettype": "Cost Budget Type", "costbudgetconfig": "Cost Budget Config", "is3d": "Is 3D", "iscumulativetransaction": "Iscumulativetransaction", "transportpackagetype": "Transport Package Type", "modelobjecttexture": "Model Object Texture", "2dqtotexcrosshatch": "Crosshatch", "2dqtotexdiamonds": "Diamonds", "2dqtotexhorizontal": "Horizontal Bars", "2dqtotexslantleft": "Slant Left", "2dqtotexslantright": "Slant Right", "2dqtotexsolid": "Solid", "2dqtotexvertical": "Vertical Bars", "bpdStatusPropAccessRight1": "AR core ID", "bpdStatusPropAccessRight2": "AR Supplier", "bpdStatusPropAccessRight3": "AR Debtor", "bpdStatusPropAccessRight4": "AR show BP", "invStatusPropAccessRight1": "Edit Invoice Header", "invStatusPropAccessRight2": "Edit PES", "invStatusPropAccessRight3": "Edit Contract", "invStatusPropAccessRight4": "Edit Other Service", "invStatusPropAccessRight5": "Edit Chained Invoices", "bpdCertificatePropAccessRight1": "Certificate", "bpdEvalStatusPropAccessRight1": "Evaluation", "createAccessRightDescriptor": "Create Access Right", "basExternalDesktopTilesLoadIconFile": "Load Icon File", "table": "Table", "class": "Class", "column": "Column", "property": "Property", "maxLength": "Maximal Length", "columnSize": "Column  <PERSON>", "projectContent": "Project Content", "stocktypein": "Stock In", "stocktypeout": "Stock Out", "perioddate": "Period date", "isdatedriven": "Date Driven", "headerid": "Header Id", "entityid": "Entity Id", "isnumeric": "Is Numeric", "charstartvalue": "Char Start Value", "chartendvalue": "Chart End Value", "chartlastvalue": "Chart Last Value", "isficorrection": "Is Final Invoice Correction", "shortdesc": "Short Description", "regiontype": "Region Type", "isautogenerated": "Is Autogenerated", "isintrastat": "Is Intra State", "dispatchheaderdocumenttype": "Dispatch Header Document Type", "statusTransition": "Status Transition", "editStatusTransitions": "Edit Status Transitions", "uppConfigFk": "Upp Config", "isdispatching": "Is Dispatching", "link": "Link", "plus": "Add", "minus": "Reduce", "edit2": "Edit", "refresh": "Exchange", "status": "Correct", "nominaldimension1name": "Nominal Dimension 1 Name", "nominaldimension2name": "Nominal Dimension 2 Name", "nominaldimension3name": "Nominal Dimension 3 Name", "rejectionreasonfk": "Rejection Reason", "taxcodefk": "Tax Code", "account": "Account", "calendarfk": "Calendar", "resourceskillgroup": "Skill Group", "resourceskilltype": "Skill Type", "isawaitingcreditnote": "Is A Waiting Credit Note", "isreserved": "Is Reserved", "delaylessthan": "Delay Less Than", "istruck": "Is Truck", "isdriver": "<PERSON>", "dispatchHeaderLinkageReference": "Reference", "dispatchHeaderLinkageLink": "Link", "dispatchHeaderLinkageAdd": "Add", "dispatchHeaderLinkageReduce": "Reduce", "dispatchHeaderLinkageEdit": "Edit", "dispatchHeaderLinkageExchange": "Exchange", "dispatchHeaderLinkageCorrect": "Correct", "resourceresourceparttype": "Resource Resource Part Type", "valuetext": "Value Text", "parametertext": "Parameter Text", "paramvaluetypefk": "Param Value Type", "isexcludeprcboqprice": "Is Exclude PRC BoQ Price", "equipment": "Equipment", "employee": "Employee", "arassignment": "AR Assignment", "labelplacement": "Label Placement", "isinexecution": "Is Inexecution", "schedulestatusrulefk": "Schedule Status Rule", "isexecuted": "Is Executed", "schedulestatustargetfk": "Schedule Status Target", "schedulestatusfk": "Schedule Status", "vatpercentdominant": "VAT Percent Dominant", "scheduleimportconf": "Schedule Import Configuration", "userdefnumber": "User Defined Number", "isforpackage": "Is For Package", "isforpackageaccess": "Is For Package Access", "numbersequencefk": "Number Sequence", "dunninggroup": "Dunning Group", "ismaintenance": "Is Maintenance", "codegensequencetypefk": "Code Gen Sequence Type", "plantcatalogtype": "Plant Catalog Type", "plantcalculationtype": "Plant Calculation Type", "logisticssettledbytype": "Logistics Settled By Type", "calculationtypefk": "Calculation Type", "transportbundlestatus": "Transport Bundle Status", "transportbundlestatusrole": "Transport Bundle Status Role", "transportbundlestatusrule": "Transport Bundle Status Rule", "transportbundlestatusworkflow": "Transport Bundle Status Workflow", "transportbundletype": "Transport Bundle Type", "dispatchable": "Dispatchable", "ischarged": "Is Charged", "isstockrelevant": "Is Stock Relevant", "jobcardstatusrulefk": "Job Card Status Rule", "jobcardstatusfk": "Job Card Status", "jobcardstatustargetfk": "Job card status Target", "isboq": "Is <PERSON>", "ismaterial": "Is Material", "dataformat": "Data Format", "clerkdocumenttype": "Clerk Document Type", "jobcardstatus": "Job Card Status", "jobcardstatusrole": "Job Card Status Role", "jobcardstatusrule": "Job Card Status Rule", "jobcardstatusworkflow": "Job Card Status Workflow", "jobcardrecordtype": "Job Card Record Type", "jobcardfk": "Job Card", "description1info": "Description 1 Info", "description2info": "Description 2 Info", "accountingtype": "General Accounting Type", "isbalancesheet": "Is Balance Sheet", "isprofitandloss": "Is Profit And Loss", "iscostcode": "Is Cost Code", "isrevenuecode": "Is Revenue Code", "jobcardtemplatefk": "Jobcard Template", "prjDocStatusAccessRight": "Access Right", "quantityfactor1": "Quantity Factor 1", "precision1": "Precision 1", "inputsource1fk": "Input Source 1", "separator": "Separator", "quantityfactor2": "Quantity Factor 2", "precision2": "Precision 2", "inputsource2Fk": "Inputsource 2", "charactervalueopfk": "Character Value Operator", "formattemplate": "Format Template", "vatgroupfk": "VAT Group", "vatclausefk": "VAT Clause", "taxcategory": "Tax Category", "commenttextinfo": "Comment Text Info", "vatcalculationtype": "VAT Calculation Type", "vatclause": "VAT Clause", "taxcodematrix": "Tax Code Matrix", "equipmentcatalogcodecontent": "Plant Catalog Code Content", "equipmentcharactervalueop": "Plant Character Value Operator", "equipmentcharactervaluetype": "Plant Character Value Type", "equipmentinputsource": "Plant Input Source", "logisticjobtasktype": "Logistic Job Task Type", "engineeringdrawingtype": "Engineering Drawing Type", "engineeringdrawingstatus": "Engineering Drawing Status", "engineeringdrawingstatusrole": "Engineering Drawing Status Role", "engineeringdrawingstatusrule": "Engineering Drawing Status Rule", "engineeringdrawingstatusworkflow": "Engineering Drawing Status Workflow", "engineeringdrawingcomponenttype": "Engineering Drawing Component Type", "engineeringdrawingcomponentstatus": "Engineering Drawing Component Status", "engineeringdrawingcomponentstatusrole": "Engineering Drawing Component Status Role", "engineeringdrawingcomponentstatusrule": "Engineering Drawing Component Status Rule", "engineeringdrawingcomponentstatusworkflow": "Engineering Drawing Component Status Workflow", "engineeringaccountingruletype": "Engineering Accounting Rule Type", "engineeringaccountingruleimportformat": "Engineering Accounting Rule Import Format", "engineeringaccountingrulematchfield": "Engineering Accounting Rule Match Field", "drwstatusrulefk": "Drawing Status Rule", "drawingstatustargetfk": "Drawing Status Target", "compstatusrulefk": "Drawing Component Status", "drwcompstatusfk": "Drawing Component Status", "drwcompstatustargetfk": "Drawing Component Status Target", "accimportformatfk": "Accounting  Import Format", "typeSystem": "System", "typeConfiguration": "Configuration", "typeCustomizing": "Customizing", "moduleBasics": "Basics", "moduleBoQ": "BoQ", "moduleBusinessPartner": "BusinessPartner", "moduleScheduling": "Scheduling", "moduleContract": "Contract", "moduleConstructionSystem": "ConstructionSystem", "moduleDefectManagement": "DefectManagement", "moduleEngineering": "Engineering", "moduleEstimation": "Estimation", "moduleResource": "Resource", "moduleFramework": "Framework", "moduleInvoice": "Invoice", "moduleLogistics": "Logistics", "moduleMasterdata": "Masterdata", "moduleModel": "Model", "moduleMounting": "Mounting", "moduleObject": "Object", "modulePps": "Pps", "moduleProject": "Project", "moduleProcurement": "Procurement", "moduleQtO": "QtO", "moduleSales": "Sales", "moduleTimekeeping": "Timekeeping", "moduleTransport": "Transport", "calendartype01": "Project", "calendartype02": "Enterprise", "isTestRun": "Is Test Run", "isRevision": "Is Revision", "postingArea": "Posting Area", "readyForSettlement": "Ready For Settlement", "readyForDispatching": "Ready For Dispatching", "isDispatched": "Is Dispatched", "logisticssettlementitemtype": "Logistics Settlement Item Type", "logisticssettlementtype": "Logistics Settlement Type", "ispublicopen": "Is Public Open", "iscenter": "Is Center", "settlementtype": "Settlement Type", "billingschemafk": "Billing <PERSON><PERSON>", "isinternal": "Is Internal", "transactiontypefk": "Transaction Type", "transactiontypecredfk": "Transaction Type Payable", "isestimate": "Is Estimate", "iscontracted": "Is Contracted", "recordingstatrulefk": "Recording Status Rule", "recordingstatusfk": "Recording Status", "recordingstatustargetfk": "Recording Status Target", "reportstatusrule": "Report Status Rule", "reportstatusfk": "Report Status", "reportstatustargetfk": "Report Status Target", "sheetstatusrulefk": "Sheet Status Rule", "SheetStatusFk": "Sheet Status", "sheetstatustargetfk": "Sheet Status Target", "timekeepingrecordingstatus": "Timekeeping Recording Status", "timekeepingrecordingstatusrole": "Timekeeping Recording Status Role", "timekeepingrecordingstatusrule": "Timekeeping Recording Status Rule", "timekeepingrecordingstatusworkflow": "Timekeeping Recording Status Workflow", "timekeepingreportstatus": "Timekeeping Report Status", "timekeepingreportstatusrole": "Timekeeping Report Status Role", "timekeepingreportstatusrule": "Timekeeping Report Status Rule", "timekeepingreportstatusworkflow": "Timekeeping Report Status Workflow", "timekeepingperiodstatus": "Timekeeping Period Status", "timekeepingperiodstatusrole": "Timekeeping Period Status Role", "timekeepingperiodstatusrule": "Timekeeping Period Status Rule", "timekeepingperiodstatusworkflow": "Timekeeping Period Status Workflow", "timekeepingsheetstatus": "Timekeeping Sheet Status", "timekeepingsheetstatusrole": "Timekeeping Sheet Status Role", "timekeepingsheetstatusrule": "Timekeeping Sheet Status Rule", "timekeepingsheetstatusworkflow": "Timekeeping Sheet Status Workflow", "timekeepingsheetsymbol": "Timekeeping Sheet Symbol", "isdue": "Is Due", "isproject": "Is Project", "isliccos": "Is Construction System", "islicmaterial": "Is Material", "islicactivitycrit": "Is Activity Criteria", "islicemployee": "Is Employee", "catConfig": "Catalog Configuration", "projectCatConfig": "Project Catalog Configuration", "costGroupCatalog": "Cost Group Catalog", "isProjectCatalog": "Project Catalog", "isBoQ": "Is <PERSON>", "isProcurement": "Is Procurement", "isEngineering": "Is Engineering", "isProductionSystem": "Is Production System", "isModel": "Is Model", "isQuantityTakeOff": "Is Quantity Take Off", "sourceCostGroupCatalog": "Enterprise Cost Group Catalog", "projectcostgroupcatalogassignment": "Project Cost Group Catalog Assignment", "projectcatalogconfiguration": "Project Catalog Configuration", "projectcatalogconfigurationtype": "Enterprise Catalog Configuration", "plantcertificatedoctype": "Plant Certificate Document Type", "plantcertificatestatus": "Plant Certificate Status", "plantcertificatestatusrole": "Plant Certificate Status Role", "plantcertificatestatusrule": "Plant Certificate Status Rule", "plantcertificatestatusworkflow": "Plant Certificate Status Workflow", "plantcertificatetype": "Plant Certificate Type", "isdraft": "Is Draft", "certificatestatusrulefk": "Certificate Status Rule", "certificatestatustargetfk": "Certificate Status Target", "contrcostcodefk": "Contr Cost Code", "factor": "Factor", "accountfk": "Account", "accountfk2": "Account 2", "equipmentcontextfk": "Plant Context", "lgmcontextfk": "Logistic Context", "accounting2mdccontrcost": "Accounting to Controlling Cost Code Mapping", "procurementadvancetype": "Procurement Advance Type", "jobcarddocumenttype": "Job Card Document Type", "boqstatus": "Boq Status", "boqstatusrole": "Boq Status Role", "boqstatusrule": "Boq Status Rule", "boqstatusworkflow": "Boq Status Workflow", "projectcontractroletype": "Project Contract Role Type", "stocktransactiontypefk": "Stock Transaction Type", "workoperationtypefk": "Work Operation Type", "reservationtypefk": "Reservation Type", "logisticsdispatcherheadertype": "Logistics Dispatcher Header Type", "stocktransaction2rubriccategory": "Stocktransaction 2 Rubric Category", "reservationtype2workoperationtype": "Reservation Type 2 Work Operation Type", "isallowance": "Is Allowance", "isrp": "Isrp", "isga": "Isga", "isam": "Isam", "istotalwq": "Is Total Wq", "estlineitemstatus": "Est Line Item Status", "orderwarrentytype": "Order Warrenty Type", "selfbillingstatus": "Selfbilling Status", "selfbillingstatusrole": "Selfbilling Status Role", "selfbillingstatusrule": "Selfbilling Status Rule", "selfbillingstatusworkflow": "Selfbilling Status Workflow", "isdefaultext": "Is Default Ext", "iscoredata": "Is Core Data", "iscoredataext": "Is Core Data Ext.", "isdisable": "Is Disable", "isok": "Is Ok", "qtodetailstatus": "QtO Detail Status", "qtodetailstatusrole": "QtO Detail Status Role", "qtodetailstatusrule": "QtO Detail Status Rule", "qtodetailstatusworkflow": "QtO Detail Status Workflow", "detailstatusfk": "Detail Status", "isreproduction": "Is Reproduction", "referenceyear": "Reference Year", "isdisabled": "Is Disabled", "bankstatustargetfk": "Bank Status Target", "bankstatusrulefk": "Bank Status Rule", "bpbankstatus": "BP Bank Status", "bpbankstatusrole": "BP Bank Status Role", "bpbankstatusrule": "BP Bank Status Rule", "bpbankstatusworkflow": "BP Bank Status Workflow", "materialgroupfk": "Material Group", "isFactory": "Is Factory", "isStockYard": "Is Stock Yard", "conStatusAccessRight": "Contract Status Access Right", "invStatusAccessRight": "Invoice Status Access Right", "pesStatusAccessRight": "PES Status Access Right", "prcPackageStatusAccessRight": "Package Status Access Right", "qtnStatusAccessRight": "Quotation Status Access Right", "reqStatusAccessRight": "Requisition Status Access Right", "rfqStatusAccessRight": "RfQ Status Access Right", "rfqBusinessPartnerStatusAccessRight": "RfQ Business Partner Status Access Right", "isAssembly": "Is Assembly", "isWorkItemCatalog": "Is Work Item Catalog", "isverifedbl": "Is Verifed Baseline", "tobeverifiedbl": "To Be Verified Baseline", "isaccounted": "Is Accounted", "uilanguage": "User Interface Language", "islumpsum": "Is Lump Sum", "logreasongroupfk": "Log Reason Group", "isscrap": "<PERSON>", "isreadyforstockyard": "Is Ready For Stock Yard", "iswithengineering": "Is With Engineering", "genericeventstatusrulefk": "Generic Event Status Rule", "genericeventstatusfk": "Generic Event Status", "genericeventstatustargetfk": "Generic Event Status Target", "HasOrder": "Has Order", "planningboardfilter": "Planningboard Filter", "resourceType": "Resource Type", "isforcontractaccess": "Is For Contract Access", "isforcontract": "Is For Contract", "hascertificatedate": "Has Certificate Date", "hasreferencedate": "Has Reference Date", "hasexpirationdate": "Has Expiration Date", "iscommissioning": "Is Commissioning", "inactive": "Inactive", "blocked": "Blocked", "startdate": "Start Date", "date": "Date", "enddate": "End Date", "bin": "Bin", "rubriccatacttmpgrpfk": "Rubric Category Activity Group", "weight": "Weight", "isforaccounting": "Is For Accounting", "blockingreason": "Blocking Reason", "isfullyreserved": "Is Fully Reserved", "isCalculationBasedOnThirtyDays": "Is Calculation Based On Thirty Days A Month", "trsPackageTypeAccessRight": "Right to Handle", "transportgoodstype": "Goods Type", "boqunitratebreakdown": "Boq Unitrate Breakdown", "ppsproductplacetype": "PPS Product Place Type", "canHaveChildren": "Can Have Children", "isFixed": "Is Fixed", "isManual": "Is Manual", "isDelta": "Is Delta", "isAllowedManual": "Is Allowed Manual", "isReceipt": "Is Receipt", "isConsumed": "Is Consumed", "isDispatching": "Is Dispatching", "needDeletePermission": "Need Delete Permission", "factorByReason": "Factor By Reason", "factorByAmount": "Factor By Amount", "isActualValue": "Is Actual Value", "isControlling": "Is Controlling", "division": "DivisionFk", "nominalDimension0101Name": "Nominal Dimension 01 Name 01", "nominalDimension0102Name": "Nominal Dimension 01 Name 02", "nominalDimension0103Name": "Nominal Dimension 01 Name 03", "nominalDimension0201Name": "Nominal Dimension 02 Name 01", "nominalDimension0202Name": "Nominal Dimension 02 Name 02", "nominalDimension0203Name": "Nominal Dimension 02 Name 03", "nominalDimension0301Name": "Nominal Dimension 03 Name 01", "nominalDimension0302Name": "Nominal Dimension 03 Name 02", "nominalDimension0303Name": "Nominal Dimension 03 Name 03", "nominalDimension0401Name": "Nominal Dimension 04 Name 01", "nominalDimension0402Name": "Nominal Dimension 04 Name 02", "nominalDimension0403Name": "Nominal Dimension 04 Name 03", "nominalDimension0501Name": "Nominal Dimension 05 Name 01", "nominalDimension0502Name": "Nominal Dimension 05 Name 02", "nominalDimension0503Name": "Nominal Dimension 05 Name 03", "nominalDimension0601Name": "Nominal Dimension 06 Name 01", "nominalDimension0602Name": "Nominal Dimension 06 Name 02", "nominalDimension0603Name": "Nominal Dimension 06 Name 03", "controllingGroup0101Fk": "Controlling Group 0101", "controllingGroup0102Fk": "Controlling Group 0102", "controllingGroup0103Fk": "Controlling Group 0103", "controllingGroup0201Fk": "Controlling Group 0201", "controllingGroup0202Fk": "Controlling Group 0202", "controllingGroup0203Fk": "Controlling Group 0203", "controllingGroup0301Fk": "Controlling Group 0301", "controllingGroup0302Fk": "Controlling Group 0302", "controllingGroup0303Fk": "Controlling Group 0303", "controllingGroup0401Fk": "Controlling Group 0401", "controllingGroup0402Fk": "Controlling Group 0402", "controllingGroup0403Fk": "Controlling Group 0403", "controllingGroup0501Fk": "Controlling Group 0501", "controllingGroup0502Fk": "Controlling Group 0502", "controllingGroup0503Fk": "Controlling Group 0503", "controllingGroup0601Fk": "Controlling Group 0601", "controllingGroup0602Fk": "Controlling Group 0602", "controllingGroup0603Fk": "Controlling Group 0603", "isInTransport": "Is In Transport", "isInPlanning": "Is In Planning", "isDone": "Is Done", "isTransportDone": "Is Transport Done", "jobcardgroup": "Job Card Group", "jobcardarea": "Job Card Area", "jobcardpriority": "Job Card Priority", "crbPriceConditionTypeFk": "CRB Price Condition Type", "isGeneralstype": "Is Generalstype", "hasSerial": "Has Serial", "wageRateTypeFk": "Wage Rate Type", "group": "Group", "markupRate": "Markup Rate", "operation": "Operation", "sortOrder": "Sort Order", "isCostTotal": "Is Cost Total", "hasRubric": "<PERSON>", "hasRole": "Has Role", "hasClerk": "<PERSON>", "hasPortalGrp": "Has Portalgroup", "errMaxLength": "Column size must not be larger than maximal length", "dangerCategory": "Danger Category", "riskFactor": "Risk Factor", "userDefText01": "User Def Text 01", "userDefText02": "User Def Text 02", "userDefText03": "User Def Text 03", "userDefText04": "User Def Text 04", "userDefText05": "User Def Text 05", "userDefInt01": "User Def Int 01", "userDefInt02": "User Def Int 02", "userDefInt03": "User Def Int 03", "userDefInt04": "User Def Int 04", "userDefInt05": "User Def Int 05", "uomCapacityFk": "Uom Capacity", "regulationName": "Regulation", "hazardLabel": "Hazard Label", "packageGroup": "Package Group", "tunnelRestrictionCode": "Tunnel Restriction Code", "dangerName": "Danger Name", "packageTypeFk": "Package Type", "isFreeCapacity": "Is Free Capacity", "defaultCapacity": "Default Capacity", "IsMandatoryCapacity": "Is Mandatory Capacity", "isAgreed": "Is Agreed", "isIssued": "Is Issued", "psStatusRuleFk": "Payment Schedules Status Rule", "accessRightDescriptorFk": "Access Right Descriptor", "psStatusFk": "Payment Schedules Status", "psStatusTargetFk": "Payment Schedules Status Target", "isPlanningAllowed": "Is Planning Allowed", "isConsolidateChange": "Is Consolidate Change", "transactionItemInc": "Transaction Item Inc", "isMergeAllowed": "<PERSON>owed", "isBglRounding": "Is  Bgl Rounding", "isEquipment": "Is Equipment", "isService": "Is Service", "isDeactivated": "Is Deactivated", "isTemplate": "Is Template", "matchCode": "Match Code", "charges": "Charges", "priceCondition": "Price Condition", "priceExtra": "Price Extra", "estimatePrice": "Estimate Price", "priceUnit": "Price Unit", "uomPriceUnit": "Uom Price Unit", "factorPriceUnit": "Factor Price Unit", "sellUnit": "SellUnit", "materialDiscountGrp": "Material Discount Group", "weightType": "Weight Type", "weightNumber": "Weight Number", "externalCode": "External Code", "blobs": "Blobs", "blobsSpecification": "Blobs Specification", "material": "Material", "agreement": "Agreement", "leadTime": "Lead Time", "minQuantity": "Min Quantity", "costType": "Cost Type", "leadTimeExtra": "Lead Time Extra", "factorHour": "Factor Hour", "isProduct": "Is Product", "brand": "Brand", "modelName": "Model Name", "dangerClass": "Danger Class", "packageType": "Package Type", "UomVolume": "Uom Volume", "volume": "Volume", "updIsLive": "Upd Is Live", "inheritCode": "Inherit Code", "listPrice": "List Price", "cost": "Cost", "statusMatrixOnlyShowActiveStates": "Only show active states", "isForProject": "Is For Project", "isReceiptLedger": "Is Receipt Ledger", "isCreateInvaccount": "Is Create Invaccount", "sapMandant": "SAP Mandant", "imageFileName": "Icon File Name", "blobImageFk": "Blob Image", "basExternalDesktopTilesAccessRight1": "External Desktop Tiles", "basExternalDesktopTilesUploadIcon": "Upload Icon File", "basExternalDesktopTilesDeleteIcon": "Delete Icon File", "basExternamDesktopTilesInfoHeader": "Information", "basExternalDesktopTilesImageDeletePromptHeader": "Are you sure?", "basExternalDesktopTilesImageDeletePrompt": "Delete the icon permanently?", "basExternalDesktopTilesImageUploadedMsg": "Icon image uploaded successfully", "basExternalDesktopTilesImageDeletedMsg": "Icon image deleted successfully", "basExternalDesktopTilesFileSizeMsg": "Upload not allowed. Please select icon with size less than 50kb", "basExternalDesktopTilesFileSvgMsg": "Upload of files of type .svg is not allowed", "runspaceIframe": "Show in IFrame", "ssoJwtTemplate": "SSO JWT Template", "ssoJwtParametername": "SSO JWT Parameter", "externalConfigFk": "ExternalConfig (Key)", "is2Fields": "Is Two Fields", "isGlobal": "Is Global", "IsCalcBasedOnWorkdays": "Is Calculation Based On Workdays Days", "visibility": "Visibility", "basDashboardGroupAccessRight1": "Dashboard Group Access Right", "enhancement": "Enhancement", "crbUrls": "URL's", "crbLicenses": "Licenses (can be listed comma (,) separated)", "crbLicense": "CRB License", "crbLicenseCustomer": "Customer", "crbLicenseFull": "Full license", "crbLicenseReadonly": "Read-only license", "crbLicenseEBkp": "eBKP license", "crbServiceLicense": "License Service", "crbServicePartner": "Partner Service", "crbServicePrdProductCatalog": "PRD Product Catalog", "crbServicePrdPartner": "PRD Partner Service", "crbServiceContextData": "Context Data Service", "crbServiceSiaTest": "SIA Test Service", "crbServices": "CRB Services", "crbLicenseUser": "User", "crbValidLicenses": "Valid Licenses", "crbLicenseTest": "Test", "crbLicenseLogoff": "Log off CRB Licenses", "isFullySpecified": "Is Fully Specified", "updateUomWeight": "Update Uom Weight", "ppsmaterialsitegroup": "PPS Material Site Group", "upstreamItemStatus": "Upstream Item Status", "upstreamItemStatusRule": "Upstream Item Status Rule", "upstreamItemStatusTarget": "Upstream Item Status Target", "isInternalOnly": "Is Internal Only", "baseValueType": "Base Value Type", "invStatusPropAccessRight6": "Edit Payment", "isReopened": "Is Reopened", "resultStatus": "Result Status", "resultStatusRule": "Result Status Rule", "isForEstimate": "Is For Estimate", "isForProcurement": "Is For Procurement", "isForRessourceManagement": "Is For Ressource Management", "isForLogistic": "Is For Logistic", "isForModel": "Is For Model", "isForSales": "Is For Sales", "timekeepingresultstatus": "Result Status", "isAccountingFailed": "Is Accounting Failed", "evaluationLevel": "Evaluation Level", "isStandardRate": "Is Standard Rate", "isSales": "Is Sales", "businessPartnerStatusFk": "Business Partner Status", "isQuoted": "Is Quoted", "isDenied": "Is Denied", "isIdealQuote": "Is Ideal Quote", "reservationStatusEnd": "Reservation Status End", "ddTable": "DdTable", "rfi2projectchangetype": "Project Change Creation From Request for Information", "rfi2defecttype": "Defect Creation From Request for Information", "profileContext": "Profile Context", "isLiveProject": "Is Live Project", "isLiveProcurement": "Is Live Procurement", "isLiveSales": "Is Live Sales", "ppsdocumenttype": "PPS Document Type", "is2DModel": "Is 2D-Model", "is3DModel": "Is 3D-Model", "isImportLock": "Is Import-Lock", "isArchive": "Is Archive", "formulaDate": "Formula Date", "accountassignmentaccounttype": "Account Assignment Account Type", "accountassignmentaccount": "Account Assignment Account", "accountassignmentbusiness": "Account Assignment Business", "accountassignmentcontracttype": "Account Assignment Contract Type", "accountassignmentcontrol": "Account Assignment Control", "accountassignmentitemtype": "Account Assignment Item Type", "accountassignmentfactory": "Account Assignment Factory", "accountassignmentmatgroup": "Account Assignment Mat Group", "accountassignmentprocurementgroup": "Account Assignment Procurement Group", "accountassignmentprocurementorgan": "Account Assignment Procurement Organ", "isWqReadOnly": "Is Wq Read Only", "isTotalAqBudget": "Is Total Aq Budget", "annoStatusRuleFk": "Annotation Status Rule", "annoStatusFk": "Annotation Status", "colour": "Colour", "valueType": "Value Type", "isPreliminaryActual": "Is Preliminary Actual", "isAccrual": "Is Accrual", "isAdditional": "Is Additional", "accessrightdescriptor1": "Access Right Descriptor 1", "isDirected": "Is Directed", "projectquantitycontrol": "Project Quantity Control", "isReadyForAccounting": "Is Ready For Accounting", "companyTransheaderStatusRule": "Company Transheader Status Rule", "companyTransheaderStatus": "Company Transheader Status", "companyTransheaderStatusTarget": "Company Transheader Status Target", "chlStatus": "Chl Status", "chlStatusTarget": "Chl Status Target", "isDefect": "Is Defect", "chlStatusRule": "Chl Status Rule", "isCumulativeTransaction": "Is Cumulative Transaction", "nominalDimension01": "Nominal Dimension 1", "nominalDimension02": "Nominal Dimension 2", "nominalDimension03": "Nominal Dimension 3", "isInformation": "Is Information", "userdefinedcolumn": "User Defined Price Columns", "isRevenueRecognition": "Is Revenue Recognition", "isHired": "<PERSON>", "subsidiaryStatusRule": "Subsidiary Status Rule", "subsidiaryStatus": "Subsidiary Status", "subsidiaryStatusTarget": "Subsidiary Status Target", "countryFk": "Country", "isProforma": "<PERSON>", "prjTypeRunProject": "Run Project", "prjTypeRunPartProject": "Run Part Project", "prjTypeRunSubProject": "Run Sub Project", "prjType5dProject": "5D Project", "prjTypeCloudProject": "Cloud Project", "prjTypeProjectAlternative": "Project Alternative", "isApplied": "Is Applied", "plannedAbsenceStatusRuleFk": "Planned Absence Status Rule", "plannedAbsenceStatusFk": "Planned Absence Status", "plannedAbsenceStatusTargetFk": "Planned Absence Status Target", "accrualType": "Accrual Type", "structrueAccountFk": "<PERSON><PERSON><PERSON><PERSON> Account", "offsetAccount": "Offset Account", "structrueOffsetAccountFk": "<PERSON><PERSON><PERSON><PERSON> Offset Account", "nominalDimension1": "Nominal Dimension 1", "nominalDimension2": "Nominal Dimension 2", "nominalDimension3": "Nominal Dimension 3", "serverUrl": "Server Url", "useAuthentication": "Use Authentication", "encryptionTypeFk": "Encryption Type", "securityType": "Security Type", "senderEmail": "Sender <PERSON><PERSON>", "port": "Port", "isTemporaryAddress": "Is Temporary Address", "isTimekeeping": "Is Timekeeping", "isCreditMemo": "Is Credit Memo", "serverAssignmentConfiguration": "Server Assignment Configuration", "isActualForRevenueRecognition": "Is Actual For Revenue Recognition", "emailServer": {"dialogTitle": {"dlgAssignment": "Company- / E-Mail Server Assignment", "dlgCreate": "New E-Mail Server", "dlgSettings": "E-Mail Server Settings", "changePresent": "Changes found!!", "saveSuccess": "Success!!", "failed": "Failed!!", "createSuccess": "Success!!", "defaultRequired": "Not allowed!!", "testConnection": "Test Connection"}, "gridColumn": {"company": "Company", "status": "E-Mail Server Name, Sender Email"}, "viewHeader": {"configuration": "Configuration", "emailServer": "E-Mail Server", "companyStructure": "Company Structure"}, "groupHeader": {"general": "General", "serverSettings": "E-Mail Server Settings", "securitySettings": "Security Settings", "senderEmail": "Sender Default E-Mail", "others": "Others", "checkConnection": "Check Connection"}, "rowLabel": {"name": "Email Server Name", "namePlaceholder": "Enter Server Name", "isDefault": "<PERSON>", "remark": "Remark", "remarkPlaceholder": "Enter Remark", "serverUrl": "E-Mail Server URL", "serverUrlPlaceholder": "Enter E-Mail Server URL", "port": "Port", "portPlaceholder": "Enter Port", "connectionSecurity": "Connection Security", "secureAuthenticate": "Use Secure Authenticate", "userName": "User Name", "userNamePlaceholder": "Enter User Name", "password": "Password", "passwordPlaceholder": "Enter Password", "senderEmail": "Sender E-Mail", "senderEmailPlaceholder": "Enter Sender E-Mail", "receiverEmail": "Receiver E-Mail", "testConnection": "Test E-Mail Connection", "testConnectionPlaceholder": "Enter Receiver E-Mail", "isLive": "Is Live", "sorting": "Sorting", "insertedAt": "Inserted At", "insertedBy": "Inserted By", "updatedAt": "Updated At", "updatedBy": "Updated By"}, "button": {"btnClose": "Close", "btnSaveCaption": "Save Assignment", "btnSaveTooltip": "Save Assignment", "btnDiscardCaption": "Inherit", "btnDiscardTooltip": "Discard Override and <PERSON><PERSON><PERSON>s", "btnCreate": "Create"}, "messages": {"changePresent": "All of the unsaved changes will be lost. Do you want to proceed?", "saveSuccess": "Changes were successfully saved", "createSuccess": "E-Mail Server settings created successfully", "createFailed": "E-Mail Server settings creation failed. Please check username and password.", "defaultRequired": "Exactly one server must be marked as default. Changing this value here is not permitted. You can mark another server as default, then this value will be automatically reset.", "serverUrlRequired": "Server Url required", "portRequired": "Port required", "usernameRequired": "Username required", "passwordRequired": "Password required", "senderEmailRequired": "Sender email required", "receiverEmailRequired": "Receiver Email required", "testConnectionSuccess": "Connection successful", "testConnectionFailed": "Connection Failed!!", "saveOngoing": "Saving", "createOngoing": "Creating E-Mail Server", "testOngoing": "Testing server configuration"}}, "isChargingNegativQuantity": "Is Charging Negativ Quantity", "timeallocationstatus": "Timeallocation Status", "timeallocationstatusrule": "Timeallocation Status Rule", "timeallocationstatustarget": "Timeallocation Target Status", "revenuerecognitionconfiguration": "Revenue Recognition Configuration", "revenuerecognitionstatus": "Revenue Recognition Status", "revenuerecognitionstatusrole": "Revenue Recognition Status Role", "revenuerecognitionstatusrule": "Revenue Recognition Status Rule", "revenuerecognitionstatusworkflow": "Revenue Recognition Status Workflow", "isPoolJob": "Is Pool Job", "salestaxmethod": "Sales Tax Method", "plantestimatepricelist": "Plant Estimate Pricelist", "headerPlant": "Estimate Plant Header", "costCodeCost": "Cost", "costCodeBudget": "Budget", "costCodeMutation": "Mutation", "costCodeAdditionalExpense": "Additional Expense", "moduleControlling": "Controlling", "isgc": "Is General Contract", "costCodeBudgetShift": "Budget Shift", "overtimeRelevant": "Overtime Relevant", "dateShiftMode": "Date Shift Mode", "isPlaceHolder": "Is Place Holder", "isForCertificate": "Is For Certificate", "isJointVenture": "Is Joint Venture", "invoiceaccrualmode": "Invoice Accrual Mode", "invoicegroupaccount": "Invoice Group Account", "offsetNominalDimension01": "Offset Nominal Dimension 1", "offsetNominalDimension02": "Offset Nominal Dimension 2", "offsetNominalDimension03": "Offset Nominal Dimension 3", "moduleMeeting": "Meeting", "meetingtype": "Meeting Type", "meetingattendeestatus": "Meeting Attendee Status", "meetingattendeestatusrole": "Meeting Attendee Status Role", "meetingattendeestatusrule": "Meeting Attendee Status Rule", "meetingattendeestatusworkflow": "Meeting Attendee Status Workflow", "meetingstatus": "Meeting Status", "meetingstatusrole": "Meeting Status Role", "meetingstatusrule": "Meeting Status Rule", "meetingstatusworkflow": "Meeting Status Workflow", "meetingAttendeeStatusTarget": "Target Attendee Status", "isPublished": "Is Published", "isYearly": "Is Yearly", "isMonthly": "Is Monthly", "isWeekly": "Is Weekly", "isDaily": "Is Daily", "remindercycle": "Reminder Cycle", "includeInTotal": "Include In Total", "billaccrualmode": "Billing Accrual Mode", "objectinstallmentagreementstatus": "Installment Agreement Status", "objectinstallmentagreementstatusrole": "Installment Agreement Status Role", "objectinstallmentagreementstatusrule": "Installment Agreement Status Rule", "objectunitinstallmentagreementworkflow": "Installment Agreement Status Workflow", "indirectCostBalancingConfigurationDetail": "Indirect Cost Balancing Configuration Detail", "billindirectcostbalancingconfiguration": "Indirect Cost Balancing Configuration", "editIndirectCostDetails": "Edit Indirect Cost Details", "editIndirectCostDetailsSavePrompt": "Save configuration first before edit indirect cost details.", "isForScrap": "Is For Scrap", "ppsheadertype": "PPS Header Type", "isFullyImported": "Is Fully Imported", "isEngineered": "Is Engineered", "isNesting": "Is Nesting", "isNested": "Is Nested", "isForStock": "Is For Stock", "isPaymentScheduleBalancing": "Is Payment Schedule Balancing", "typeDetailer": "Type Detailer", "isFramework": "Is Framework", "itemFilterOptions": "<PERSON>em Filter Options", "employeeStatusRule": "Employee Status Rule", "employeeStatus": "Employee Status", "employeeStatusTarget": "Employee Status Target", "jobFilter": "<PERSON> Filter", "mainHierarchy": "Main Hierarchy", "capitalizeTheFirstLetter": "Capitalize the first letter", "priority": "Priority", "allowance": "Estimate Allowance", "allowancetype": "Allowance Type", "markupcalculationtype": "Markup Calculation Type", "isOneStep": "Is One Step", "isBalanceFP": "Is BalanceF P", "dCMQuantity": "DCM Quantity", "markupGa": "<PERSON><PERSON>", "markupRp": "Markup Rp", "markupAm": "<PERSON><PERSON> Am", "markupGaSc": "<PERSON><PERSON>", "markupRpSc": "Markup Rp Sc", "markupAmSc": "<PERSON><PERSON>", "roundingConfigFk": "Rounding Configuration", "enterpriseRoundingConfigFk": "Enterprise Rounding Configuration", "estimateroundingconfigurationtype": "Estimate Rounding Configuration Type", "estimateRoundingConfigFk": "Estimate Rounding Configuration", "roundto": "Round To", "roundingmethod": "Rounding Method", "boqroundingconfigurationtype": "Boq Rounding Configuration Type", "boqRoundingConfigFk": "Boq Rounding Configuration", "timesymboltrafficlight": "Time Symbol Trafficlight", "isActual": "Is Actual", "timekeepingemployeeskillstatus": "Timekeeping Employee Skill Status", "timekeepingemployeeskillstatusrole": "Timekeeping Employee Skill Status Role", "timekeepingemployeeskillstatusrule": "Timekeeping Employee Skill Status Rule", "timekeepingemployeeskillstatusworkflow": "Timekeeping Employee Skill Status Workflow", "boqrevenuetype": "Boq Revenue Type", "isLocked": "<PERSON>", "isAssigned": "Is Assigned", "loadingcost": "Loading Cost", "isFullLoadingCosts": "Is Full Loading Costs", "isReducedLoadingCosts": "Is Reduced Loading Costs", "isFullLoadingCostsVolume": "Is Full Loading Costs Volume", "isReducedLoadingCostsVolume": "Is Reduced Loading Costs Volume", "isForPlant": "Is For Plant", "isForMaterial": "Is For Material", "isEnterprise": "Is Enterprise", "isIgnoreWotByLocation": "Is Ignore Wot By Location", "uomWeight": "<PERSON>om Weight", "uomVolume": "Uom Volume", "estallowanceconfig": "Est. Allowance Config", "estallowanceconfigtype": "Est. Allowance Assignment Type", "estallowanceassignment": "Est. Allowance Assignment", "isPercentage": "Is Percentage", "isLogisticDataRequired": "Is Logistic Data Required", "isExpired": "Is Expired", "plantwarrantystatusrule": "Warranty Status Rule", "plantwarrantystatus": "Warranty Status", "plantwarrantytype": "Warranty Type", "allowUpload": "Allow Upload", "allowPreview": "Allow Preview", "validateFileSignature": "Validate File Signature", "engineeringdrawingstatusext": "Engineering Drawing Status External", "engineeringtaskstatusext": "Engineering Task Status External", "mountingactivitystatusext": "Mounting Activity Status External", "mountingrequisitionstatusext": "Mounting Requisition Status External", "ppsitemstatusext": "PPS Item Status External", "ppsproductionsetstatusext": "PPS Production Set Status External", "ppsproductionsetstatus": "PPS Production Set Status", "ppsproductionsetrole": "PPS Production Set Status Role", "ppsproductionsetrule": "PPS Production Set Status Rule", "ppsproductionsetworkflow": "PPS Production Set Status Workflow", "notToCount": "Not To Count", "resourcerequisitionpriority": "Resource Requisition Priority", "isSettled": "Is Settled", "plantSupplyItemStatusRule": "Plant Supply Item Status Rule", "isBaselineUpdatedAward": "Is Baseline Updated Award", "isBaselineUpdateInvalid": "Is Baseline Update Invalid", "documentStatusRule": "Document Status Rule", "documentStatus": "Document Status", "isBudgetEditable": "Is Budget Editable", "isPartialFinalInvoice": "Is Partial Final Invoice", "costPerUnit": "Cost Per Unit", "isEstimatePrice": "Is Estimate Price", "isDayworkRate": "Is Daywork Rate", "costCode": "Cost Code", "materialportiontype": "Material Portion Type", "projectmode": "Project Mode", "isPesCO": "Is PES Change Order", "documentStatusTarget": "Document Status Target", "isSingle": "Is Single", "groupStatusRuleFk": "Group Status Rule", "isJournal": "Is Journal", "isForPlantSupply": "Is For Plant Supply", "fbLanguageFk": "FB Language", "isRecalcDates": "Is Recalculate Dates", "typeCode": "Type Code", "isAggregatedByUom": "Is Aggregated By Uom", "externalrole": "External Role", "userlabel": "User Label", "keyWords": "Key Words", "procurementpaymentschedulestatus": "Procurement Payment Schedule Status", "procurementpaymentschedulestatusrole": "Procurement Payment Schedule Status Role", "procurementpaymentschedulestatusrule": "Procurement Payment Schedule Status Rule", "procurementpaymentschedulestatusworkflow": "Procurement Payment Schedule Status Workflow", "paymentschedulestatusrule": "Payment Schedule Status Rule", "paymentschedulestatus": "Payment Schedule Status", "paymentschedulestatustarget": "Payment Schedule Status Target", "projectdocumentcategory2type": "Project Document Category 2 Document Type", "isForAutoAssignProduct": "Is For Auto Assign Product", "warningActionFk": "Warning Action", "itemNumberingConfiguration": "Item Numbering Configuration", "editItemNoConfig": "Edit Item Numbering Configuration", "editItemNoConfigSavePrompt": "Save configuration header first before edit item numbering configuration.", "ppsphaserequirementstatus": "PPS Phase Requirement Status", "ppsphaserequirementstatusrole": "PPS Phase Requirement Status Role", "ppsphaserequirementstatusrule": "PPS Phase Requirement Status Rule", "ppsphaserequirementstatusworkflow": "PPS Phase Requirement Status Workflow", "modelmarkershape": "Model <PERSON><PERSON>", "geometry3d": "Geometry 3D", "geometry3dformat": "Geometry 3D Format", "filterHierarchy": "Filter Hierarchy", "isFinalInvoiceCorrection": "Is Final Invoice Correction", "employeeCertificateStatusRule": "Employee Certificate Status Rule", "employeeCertificateStatus": "Employee Certificate Status", "employeeCertificateStatusTarget": "Employee Certificate Status Target", "isLockedDate": "Is Locked Date", "isLockedQty": "<PERSON>", "isLockedDateAndQty": "Is Locked Date And Qty", "isChangeOrder": "Is Change Order", "isVoid": "Is Void", "modelviewpointtype": "Model View Point Type", "isMain": "Is Main", "isChange": "Is Change", "isSide": "Is Side", "titleReferencedWorkflow": "Referenced Workflow", "removedReferencedWorkflow": "When removing the rule, the referenced workflow must also be removed. Would you like to continue?", "lockForCad": "Lock For CAD", "defaultUoM": "Default <PERSON>", "hasLoadingCost": "Has Loading Cost", "isPerformance": "Is Performance", "isEmmission": "Is Emmission", "specificvaluetype": "Specific Value Type", "materialstatus": "Material Status", "isPercentMaterialTotal": "Is Percentage Material Total", "isRecalcPerformance": "Is Recalculate Performance", "textArea": "Text Area", "isFrameworkCallOff": "Is Framework Call Off", "isDefaultNewRevision": "Is Default New Revision", "isDefaultDeleteRevision": "Is Default Delete Revision", "materialStatusRule": "Material Status Rule", "materialStatusTarget": "Material Status Target", "materialStatus": "Material Status", "uomDay": "Day", "uomHour": "Hour", "uomMonth": "Month", "uomIdle": "Idle", "isFinallyBilled": "Is Finally Billed", "isDay": "Is Day", "isHour": "Is Hour", "isMonth": "Is Month", "isIdle": "Is Idle", "instruction": "Instruction", "wizardDescription": "Wizard Description", "dateRequested": "Date Requested", "quantityRequested": "Quantity Requested", "wotRequested": "Wot Requested", "uomRequested": "Uom Requested", "claimStatusRule": "Claim Status Rule", "claimStatus": "Claim Status", "claimStatusTarget": "Claim Status Target", "allAreaGroupType": "All Area Group Type", "materialRoundingConfig": "Material Rounding Config", "isWithoutRounding": "Is Without Rounding", "uiDisplayTo": "Ui Display To", "roundingMethod": "Rounding Method", "ColumnId": "Column Id", "isClient": "Is Client", "isCatalogEstimate": "Is Catalog Estimate", "isFixedCost": "Is Fixed Cost", "isEstimatePlant": "Is Estimate Plant", "isValidatingPerformingJob": "Is Validating Performing Job", "qtnStatusSheetAccessRight": "QTO Status Sheet Access Right", "isPaymentSchedule": "Is Payment Schedule", "isSurcharge": "Is Surcharge", "isProjectChange": "Is Project Change", "WizardDescriptionInfo": "Wizard Description", "allowProcurement": "Allow Procurement", "allowDispatching": "Allow Dispatching", "allowWorkspace": "Allow Workspace", "allowResourceRequisition": "Allow Resource Requisition", "allowApprovedMaterial": "Only Allow Approved Material", "projectstocktype": "Project Stock Type", "isComponentRemoved": "Is Component Removed", "projectstock2materialstatus": "Project Stock 2 Material Status", "projectstock2materialstatusrole": "Project Stock 2 Material Status Role", "projectstock2materialstatusrule": "Project Stock 2 Material Status Rule", "projectstock2materialstatusworkflow": "Project Stock 2 Material Status Workflow", "isDeclined": "Is Declined", "stock2MaterialStatusRule": "Stock 2 Material Status Rule", "stock2MaterialStatus": "Stock 2 Material Status", "stock2MaterialStatusTarget": "Stock 2 Material Status Target", "isNegative": "Is Negative", "isIFCGroupType": "Is IFC Group Type", "isIFCZoneType": "Is IFC Zone Type", "errColumnAlreadyCreated": "Column has already been created", "isForPreliminary": "Is For Preliminary", "plantassemblytype": "Plant Assembly Type", "isVariable": "Is Variable", "isFix": "Is Fix", "rubricCategoryRequisitionFk": "Rubric Category Requisition", "isPicking": "Is Picking", "estimationparametervalue": "Estimation Parameter Value", "parameter": "Parameter", "timekeepingreportverificationtype": "Timekeeping Report Verification Type", "isLoadingGoods": "Is Loading Goods", "hasIssueDetected": "Has Issue Detected", "isEstimateActive": "Is Estimate Active", "accounting": "Accounting", "clerkroledefaultvaluetype": "Clerk Role Default Value Type", "co2source": "CO2 Source", "companytransheaderstatus": "Company Transheader Status", "companytransheaderstatusrole": "Company Transheader Status Role", "companytransheaderstatusrule": "Company Transheader Status Rule", "companytransheaderstatusworkflow": "Company Transheader Status Workflow", "configurationtype": "Configuration Type", "controllingcat": "Controlling Cat", "controllingcolumntype": "Controlling Column Type", "dangerclass": "Dangerous Goods Classes", "dashboardgroup": "Dashboard Group", "dashboardtype": "Dashboard Type", "efbtype": "EFB Type", "emailserver": "EMail Server", "emailserver2company": "EMail Server 2 Company", "excelprofile": "Excel Profile", "externaldesktoptiles": "External Desktop Tiles", "inheritcode": "Inherit Code", "packagingtypes": "Packaging Types", "paymentmethod": "Payment Method", "qtocommenttype": "QTO Comment Type", "salesdatekind": "Sales Date Kind", "salesdatetype": "Sales Date Type", "scopeofsupplytype": "Scope Of Supply Type", "stringcolumnconfig": "Length Limitation", "textarea": "Text Area", "textformat": "Text Format", "textmodulevariable": "Text Module Variable", "wageratetype": "Wage Rate Type", "warrantysecurity": "Warranty Security", "warrantyobligation": "Warranty Obligation", "bidtype": "Bid Type", "billitemnumberconfigurationheader": "Item Numbering Configuration", "boqroundingconfig": "Boq Rounding Config", "boqwarningconfig": "BoQ Warning Config", "boqwarningaction": "BoQ Warning Action", "postinggroupwithholdingtax": "Posting Group Withholding Tax", "subsidiarystatus": "Subsidiary Status", "subsidiarystatusrole": "Subsidiary Status Role", "subsidiarystatusrule": "Subsidiary Status Rule", "subsidiarystatusworkflow": "Subsidiary Status Workflow", "crbpriceconditiontype": "CRB Price Condition Type", "defect2projectchangetype": "Project Change Creation From Defect", "defectstatus2external": "Defect Status 2 External", "calculateorder": "Calculate Order", "estparametervaluetype": "Est. Parameter Value Type", "estrootassignmenttype": "Est. Root Assignment Type", "estrootassignmentlevel": "Est. Root Assignment Level", "estimateroundingconfig": "Estimate Rounding Config", "plantnominaldimensionassignment": "Plant Nominal Dimension Assignment", "plantwarrantystatusrole": "Plant Warranty Status Role", "plantwarrantystatusworkflow": "Plant Warranty Status Workflow", "frmaccessrolecategory": "Framework Access Role Category", "gcccostcodeassign": "General Contractor Controlling Cost Code Assign", "hsqecontext": "HSQE Context", "hsqecheckliststatus": "HSQE Checklist Status", "hsqecheckliststatusrole": "HSQE Checklist Status Role", "hsqecheckliststatusrule": "HSQE Checklist Status Rule", "hsqecheckliststatusworkflow": "HSQE Checklist Status Workflow", "hsqechecklisttype": "HSQE Checklist Type", "invoicerejectionreasonacc": "Invoice Rejection Reason Acc", "logisticsclaimmethod": "Logistics Claim Method", "logisticsclaimreason": "Logistics Claim Reason", "logisticsclaimstatus": "Logistics Claim Status", "logisticsclaimstatusrole": "Logistics Claim Status Role", "logisticsclaimstatusrule": "Logistics Claim Status Rule", "logisticsclaimstatusworkflow": "Logistics Claim Status Workflow", "dispatchaction": "Dispatch Action", "dispheaderlinkreason": "Dispatch Header Link Reason", "dispheaderlinktype": "Dispatch Header Link Type", "dispatchrecordstatusrole": "Dispatch Record Status Role", "dispatchrecordstatusworkflow": "Dispatch Record Status Workflow", "sundrynominaldimensionassignment": "Sundry Nominal Dimension Assignment", "plantsupplyitemstatus": "Plant Supply Item Status", "plantsupplyitemstatusrole": "Plant Supply Item Status Role", "plantsupplyitemstatusrule": "Plant Supply Item Status Rule", "plantsupplyitemstatusworkflow": "Plant Supply Item Status Workflow", "logisticssettlementledgercontexttype": "Logistics Settlement Ledger Context Type", "allareagrouptype": "All Area Group Type", "costlinetype": "Cost Line Type", "materialroundingconfig": "Material Rounding Config", "materialroundingconfigtype": "Material Rounding Config Type", "materialtemplatetype": "Material Template Type", "materialstatusrole": "Material Status Role", "materialstatusrule": "Material Status Rule", "materialstatusworkflow": "Material Status Workflow", "materialtype": "Material Type", "salestaxgroup": "Sales Tax Group", "wagegroup": "Wage Group", "modelannotationcategories": "Annotation Categories", "modelannotationdocumenttype": "Annotation Document Type", "modelannotationpriority": "Model Annotation Priority", "modelannotationreferencetype": "Model Annotation Reference Type", "modelannotationstatus": "Model Annotation Status", "modelannotationstatusrole": "Model Annotation Status Role", "modelannotationstatusrule": "Model Annotation Status Rule", "modelannotationstatusworkflow": "Model Annotation Status Workflow", "modelbasevaluetype": "Model Base Value Type", "modelchangesetstatus": "Model Change Set Status", "modeldimensiontype": "Model Dimension Type", "modelimportpatterntype": "Model Import Pattern Type", "modelstakeholderrole": "Model Stakeholder Role", "modeluommapping": "Model Uom Mapping", "ordercondition": "Order Condition", "orderpaymentschedulesstatus": "Order Payment Schedules Status", "orderpaymentschedulesstatusrole": "Order Payment Schedules Status Role", "orderpaymentschedulesstatusrule": "Order Payment Schedules Status Rule", "orderpaymentschedulesstatusworkflow": "Order Payment Schedules Status Workflow", "ordertype": "Sales Contract Type", "rfqbusinesspartnerstatusrole": "Rfq. Business Partner Status Role", "rfqbusinesspartnerstatusrule": "Rfq. Business Partner Status Rule", "rfqbusinesspartnerstatusworkflow": "Rfq. Business Partner Status Workflow", "ppsfilterhierarchy": "PPS Filter Hierarchy", "ppsgenericeventstatus": "PPS Generic Event Status", "ppsgenericeventstatusrole": "PPS Generic Event Status Role", "ppsgenericeventstatusrule": "PPS Generic Event Status Rule", "ppsgenericeventstatusworkflow": "PPS Generic Event Status Workflow", "ppsitemfilteroptions": "PPS Item Filter Options", "ppsitemtype": "PPS Item Type", "ppslogreason": "PPS Log Reason", "ppslogreasongroup": "PPS Log Reason Group", "ppsphasetype": "PPS Phase Type", "ppsplannedquantitytype": "Pps Planned Quantity Type", "ppsprocesstype": "PPS Process Type", "PPSProductionMaterialGroup": "PPS Production Material Group", "ppsproductstatus2external": "PPS Product Status To External", "ppsreproductionreason": "PPS Reproduction Reason", "ppsupstreamgoodstype": "PPS Upstream Goods Type", "ppsupstreamitemstatus": "PPS Upstream Item Status", "ppsupstreamitemstatusrole": "PPS Upstream Item Status Role", "ppsupstreamitemstatusrule": "PPS Upstream Item Status Rule", "ppsupstreamitemstatusworkflow": "PPS Upstream Item Status Workflow", "ppsupstreamtype": "PPS Upstream Type", "procurementconfigheadertype": "Procurement Configuration Header Type", "procurementdocumentstatus": "Procurement Document Status", "procurementdocumentstatusrole": "Procurement Document Status Role", "procurementdocumentstatusrule": "Procurement Document Status Rule", "procurementdocumentstatusworkflow": "Procurement Document Status Workflow", "projectactiontype": "Action Type", "projectcategory": "Project Category", "projectchangecontributiontype": "Project Change Contribution Type", "projectclassification": "Project Classification", "projectcontenttype": "Project Content Type", "projectdocumentoperation": "Project Document Operation", "projectgroupstatusrole": "Project Group Status Role", "projectgroupstatusrule": "Project Group Status Rule", "projectgroupstatusworkflow": "Project Group Status Workflow", "projectkind": "Project Kind", "revenuerecognitionmethod": "Revenue Recognition Method", "schedulechartinterval": "Schedule Chart Interval", "scheduleimportprop": "Schedule Import Property", "schedulestatus": "Schedule Status", "schedulestatusrole": "Schedule Status Role", "schedulestatusrule": "Schedule Status Rule", "schedulestatusworkflow": "Schedule Status Workflow", "qtodetaildocumenttype": "QtO Detail Document Type", "qtopurposetype": "QTO Purpose Type", "qtosheetstatus": "QTO Sheet Status", "qtosheetstatusrole": "QtO Sheet Status Role", "qtosheetstatusrule": "QtO Sheet Status Rule", "qtosheetstatusworkflow": "QtO Sheet Status Workflow", "resrequisitionresdate": "Resource Requisition Reservation Date", "resourcereservationtype": "Resource Reservation Type", "resourceparttype": "Resource Part Type", "resourceprovidedskilldocumenttype": "Provided Skill Document Type", "resourcestrafficlight": "Resources Traffic Light", "salesadvancetype": "Sales Advance Type", "salesdocumenttype": "Sales Document Type", "timekeepingcostgroup": "Timekeeping Cost Group", "timekeepingdurationmode": "Timekeeping Duration Mode", "timekeepingemployeearea": "Timekeeping Employee Human Resources Area", "timekeepingemployeecertificatetype": "Timekeeping Employee Certificate Type", "timekeepingemployeecertificatestatus": "Timekeeping Employee Certificate Status", "timekeepingemployeecertificatestatusrole": "Timekeeping Employee Certificate Status Role", "timekeepingemployeecertificatestatusrule": "Timekeeping Employee Certificate Status Rule", "timekeepingemployeecertificatestatusworkflow": "Timekeeping Employee Certificate Status Workflow", "timekeepingemployeecertificatedocumenttype": "Timekeeping Employee Certificate Document Type", "timekeepingemployeedocumenttype": "Timekeeping Employee Document Type", "timekeepingemployeestatus": "Timekeeping Employee Status", "timekeepingemployeestatusrole": "Timekeeping Employee Status Role", "timekeepingemployeestatusrule": "Timekeeping Employee Status Rule", "timekeepingemployeestatusworkflow": "Timekeeping Employee Status Workflow", "timekeepingemployeesubarea": "Timekeeping Employee Human Resources Sub Area", "timekeepingemployeegroup": "Timekeeping Employee Group", "timekeepingemployeeskilldocumenttype": "Employee Skill Document Type", "inputphasechainmode": "Input Phase Chain Mode", "plannedabsencestatus": "Planned Absence Status", "plannedabsencestatusrole": "Planned Absence Status Role", "plannedabsencestatusrule": "Planned Absence Status Rule", "plannedabsencestatusworkflow": "Planned Absence Status Workflow", "timekeepingresultstatusrole": "Timekeeping Result Status Role", "timekeepingresultstatusrule": "Timekeeping Result Status Rule", "timekeepingresultstatusworkflow": "Timekeeping Result Status Workflow", "timekeepingroundingconfig": "Timekeeping Rounding Config", "timekeepingroundingconfigtype": "Timekeeping Rounding Config Type", "timekeepingsettlementstatus": "Timekeeping Settlement Status", "timekeepingsettlementstatusrole": "Timekeeping Settlement Status Role", "timekeepingsettlementstatusrule": "Timekeeping Settlement Status Rule", "timekeepingsettlementstatusworkflow": "Timekeeping Settlement Status Workflow", "timekeepingshiftgroup": "Timekeeping Shift Group", "timekeepingsurchargetype": "Timekeeping Surcharge Type", "timeallocationstatusrole": "Timeallocation Status Role", "timeallocationstatusworkflow": "Timeallocation Status Workflow", "timesymbolgroup": "Time Symbol Group", "timesymboltype": "Time Symbol Type", "timesymbolpresentation": "Time Symbol Presentation", "transportgoodsontime": "Transport Goods On Time", "transportgoodsstate": "Transport Goods State", "isDelivery": "Is Delivery", "isAllocation": "Is Allocation", "isHandover": "Is Handover", "isBackDelivery": "Is Back Delivery", "isYardAllocation": "Is Yard Allocation", "isWotChange": "Is Wot Change", "isCorrection": "Is Correction", "isPermanentRental": "Is Permanent Rental", "dispatchActivity": "Dispatch Activity", "dispatchHeaderType": "Dispatch Header Type", "uncheckIsLooKupTitle": "Uncheck IsLooKup Confirmation", "uncheckIsLooKupTip": "Uncheck IsLooKup will clear the parameter values, continue?", "changeParameterTypeTitle": "Parameter Type Changing Confirmation", "changeParameterTypeTip": "Are you sure you want to change the parameter value Type? if so, will clear the parameter values, continue?", "frmuserdirectory": "User Directory", "dispatchActivityRubricCategory": "Dispatch Activity Rubric Category", "groupPath": "Group Path", "userPath": "User Path", "ldapUsername": "LDAP Username", "ldapPassword": "LDAP Password", "ldapsMode": "LDAPS Mode", "vhbsheetdjctype": "VHB Sheet DJC Type", "vhbsheetgctype": "VHB Sheet GC Type", "ldapAuthentication": "LDAP Authentication", "isLdapPasswordChanged": "Is LDAP Password Changed", "orderadvancestatus": "Order Advance Status", "orderadvancestatusrule": "Order Advance Status Role", "orderadvancestatusworkflow": "Order Advance Status Workflow", "isPaymentReceived": "Is Payment Received", "advanceStatusRuleFk": "Advance Status Rule", "advanceStatusFk": "Advance Status", "isCreateComponent": "Is Create Component", "formsheetindextype": "Formsheet Index Type", "isStatusChange": "Is Status Change", "isIssue": "Is Issue", "actionItemStatusRule": "Action Item Status Rule", "actionItemStatus": "Action Item Status", "actionItemStatusTarget": "Action Item Status Target", "logisticsactionitemstatus": "Logistics Action Item Status", "logisticsactionitemstatusrole": "Logistics Action Item Status Role", "logisticsactionitemstatusrule": "Logistics Action Item Status Rule", "logisticsactionitemstatusworkflow": "Logistics Action Item Status Workflow", "dbFormIndexType": "DB Form Index Type", "actionTarget": "Action Target", "isAdministrativeProject": "Is Administrative Projectn", "isConstructiononSite": "Is Constructionon Site", "isGeofence": "Is Geofence", "isDeliveryAddress": "Is Delivery Address", "isEvacuationPoint": "Is Evacuation Point", "isWayPoint": "Is Way Point", "isSpecificationOn": "Is Specification On", "accessFunctionalRole": "Functional Role", "salesbillingmethod": "Sales Billing Method", "resourcetimeslotupdatereason": "Update Reason", "generalContractorBreakdownType": "General Contractor Breakdown Type", "logisticsActionItemType": "Logistics Action Item Type", "logisticsActionTarget": "Logistics Action Target", "orderAdvanceStatusRole": "Order Advance Status Role", "projectDropPointType": "Project Drop Point Type", "projectactivitytype": "Project Activity Type", "areaTypeTotalArea": "Total Area", "areaTypeDeliveryZone": "Delivery Zone", "areaTypeCraneDropPoint": "Crane Drop Point", "areaTypeFirstAid": "First Aid", "areaTypeRestrooms": "Restrooms", "areaTypeOffice": "Office", "areaTypeLoadUnload": "Load-Unload", "areaTypeEvacuationPoint": "Evacuation Point", "areaTypeItemDropPoint": "Item Drop Point", "areaTypeWayPoint": "Way Point", "areaTypeEscapeRoute": "Escape Route", "areaTypeWasteManagement": "Waste Management", "shapeOricon": "Shape <PERSON>", "isPerforming": "Is Performing", "isAval": "Is Aval", "isReceive": "Is Receive", "isReceived": "Is Received", "billpaymentstatus": "Bill Payment Status", "billpaymentstatusrole": "Bill Payment Status Role", "billpaymentstatusrule": "Bill Payment Status Rule", "billpaymentstatusworkflow": "Bill Payment Status Workflow", "isPlant": "Is Plant", "isTransport": "Is Transport", "isSharepoint": "Is Sharepoint", "markupDefaultMGcPercent": "Default Markup Gc%", "markupDefaultMOp": "<PERSON><PERSON><PERSON>", "isAdditionalCost": "Is <PERSON> Cost", "isNonWageCost": "Is Non Wage Cost", "isReadOnlyProjectBoQOrdered": "Is Read Only Project BoQ Ordered", "isDesign": "Is Design", "markupRateWageGroup": "Markup Rate [%] / Rate", "planteventstatus": "Plant Event Status", "planteventstatusrole": "Plant Event Status Role", "planteventstatusrule": "Plant Event Status Rule", "planteventstatusworkflow": "Plant Event Status Workflow", "isCreated": "Is Created", "isRequisitionUpdated": "Is Requisition Updated", "plantEventStatusRule": "Plant Event Status Rule", "plantEventStatusTarget": "Plant Event Status Target", "logisticsbillingsheetstatus": "Logistics Billing Sheet Status", "logisticsbillingsheetstatusrole": "Logistics Billing Sheet Status Role", "logisticsbillingsheetstatusrule": "Logistics Billing Sheet Status Rule", "logisticsbillingsheetstatusworkflow": "Logistics Billing Sheet Status Workflow", "billingSheetStatusRule": "Billing Sheet Status Rule", "planteventtype": "Plant Event Type", "isRepair": "Is Repair", "isRelease": "Is Release", "scheduleTypeWarningMsgIsExe": " 'Is Design' and 'Is Execution' they cannot be with the same value for the same record.", "scheduleTypeWarningMsgIsProc": " 'Is Design' and 'Is Procurement' they cannot be with the same value for the same record.", "isCompleted": "Is Completed", "manufacturer": "Manufacturer", "isDefaultAdHocPrice": "Is Default Ad Hoc Price", "certificatestatusrole": "Certificate Status Role", "customerstatusrole": "Customer Status Role", "bpstatusrole": "BP Status Role", "bpstatus2role": "BP Status 2 Role", "bpsupplierstatusrole": "BP Supplier Status Role", "billrejectionreason": "<PERSON> Reason", "field": "Field", "userLabel": "User Label", "logisticsbillingsheettype": "Billing Sheet Type", "markupRatePercent": "Markup Rate [%] / Rate", "expiryTime": "Expiry Time", "logisticpricecalculationtype": "Logistic Price Calculation Type", "userLabelAssignment": {"dialogTitle": {"create": "New User-defined Label", "createSuccess": "Success!!", "createError": "Error!!"}, "viewHeader": {"addLabel": "Add User-defined Label"}, "groupHeader": {"main": "New Label"}, "button": {"btnCreate": "Create", "btnCancel": "Cancel"}, "messages": {"createSuccess": "A new label was successfully created.", "createOngoing": "Creating a new label.", "createError": "An error occurred while creating a new label.", "lookupRefreshError": "Could not refresh UserLabel list", "keyMismatchError": "'LabelKey must match Entity.Field format. Expected: {{expected}}, but got: {{provided}}"}, "rowLabel": {"labelKey": "Label Key", "labelKeyPlaceholder": "Enter Label Key", "label": "Label", "labelPlaceholder": "Enter Label", "isLive": "Is Live", "sorting": "Sorting", "insertedAt": "Inserted At", "insertedBy": "Inserted By", "updatedAt": "Updated At", "updatedBy": "Updated By"}}}}}