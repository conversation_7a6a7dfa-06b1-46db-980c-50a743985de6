-------------------------------------------------------
-- Ignore Errors: OFF
-- <PERSON>ra NUMBER:   DEV-47889
-- Script Type:   Required Schema change
-- Reason:        Package Language not null Error
-- Install On:    25.2
-------------------------------------------------------
ALTER TABLE PRC_PACKAGE ALTER COLUMN BAS_LANGUAGE_FK INT NULL;

ALTER TABLE REQ_HEADER ALTER COLUMN BAS_LANGUAGE_FK INT NULL;

ALTER TABLE RFQ_HEADER ALTER COLUMN BAS_LANGUAGE_FK INT NULL;

ALTER TABLE QTN_HEADER ALTER COLUMN BAS_LANGUAGE_FK INT NULL;

ALTER TABLE CON_HEADER ALTER COLUMN BAS_LANGUAGE_FK INT NULL;

ALTER TABLE PES_HEADER ALTER COLUMN BAS_LANGUAGE_FK INT NULL;

ALTER TABLE INV_HEADER ALTER COLUMN BAS_LANGUAGE_FK INT NULL;
