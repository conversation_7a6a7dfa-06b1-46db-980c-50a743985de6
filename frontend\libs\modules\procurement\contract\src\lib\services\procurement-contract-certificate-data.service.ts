/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { ProcurementCommonCertificateDataService } from '@libs/procurement/common';
import { IPrcCertificateEntity } from '@libs/procurement/interfaces';
import { IConHeaderEntity } from '../model/entities';
import { ContractComplete } from '../model/contract-complete.class';
import { ProcurementContractHeaderDataService } from './procurement-contract-header-data.service';
import { ProcurementContractTotalDataService } from './procurement-contract-total-data.service';

@Injectable({
	providedIn: 'root',
})

/**
 * Certificate data service
 */
export class ProcurementContractCertificateDataService extends ProcurementCommonCertificateDataService<IPrcCertificateEntity, IConHeaderEntity, ContractComplete> {
	public constructor(protected parentDataService: ProcurementContractHeaderDataService) {
		const contractDataService = inject(ProcurementContractHeaderDataService);
		const totalDataService = inject(ProcurementContractTotalDataService);
		super(contractDataService, {}, totalDataService);
	}

	public override getHeaderContext() {
		return this.parentDataService.getHeaderContext();
	}

	public override isParentFn(parentKey: IConHeaderEntity, entity: IPrcCertificateEntity): boolean {
		return entity.PrcHeaderFk === parentKey.PrcHeaderFk;
	}

	public override getSavedEntitiesFromUpdate(complete: ContractComplete): IPrcCertificateEntity[] {
		if (complete && complete.PrcCertificateToSave) {
			return complete.PrcCertificateToSave;
		}
		return [];
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override registerModificationsToParentUpdate(parentUpdate: ContractComplete, modified: IPrcCertificateEntity[], deleted: IPrcCertificateEntity[]): void {
		if (modified && modified.some(() => true)) {
			parentUpdate.PrcCertificateToSave = modified;
		}
		if (deleted && deleted.some(() => true)) {
			parentUpdate.PrcCertificateToDelete = deleted;
		}
	}
}