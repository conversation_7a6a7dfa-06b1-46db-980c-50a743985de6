﻿{
  "businesspartner": {
    "main": {
      "crefodlg": {
        "gridcoladdress": "Address",
        "gridcolbedirectno": "beDirect No.",
        "gridcolcrefono": "CreFo No.",
        "gridcollocation": "Location",
        "gridcolname": "Name",
        "gridcolphone": "Phone",
        "gridcolstatus": "State",
        "gridcolupdate": "Update",
        "gridcolzipcode": "Zip Code",
        "searchareacode": "Area Code",
        "searchcriteria": "Search Criteria",
        "searchlocation": "Location",
        "searchname": "Name",
        "searchphone": "Phone Number",
        "searchstartbtn": "Search",
        "searchstreet": "Street",
        "searchzipcode": "Zip Code",
        "gridcolfax": "Fax",
			"gridcolbranchcode": "Customer Branch",
			"gridcolemail": "Email",
			"gridcolinterneturl": "Internet",
			"gridcolvatno": "Vat No.",
			"gridcoltaxnoused": "Tax No.",
			"gridcoltraderegisterused": "Trade Register",
			"gridcoltraderegisternoused": "Trade Register No.",
        "bindbpddialogtitle": "Bind beserve Online Address with Business Partner?",
        "buydialognotbindwithexistingbpd": "<%=address %><br><br>Do you wanna bind this address to existing Business Partner (Prevent Duplete)?",
        "buydialogalnavigatebody": "You have selected an assigned beserve address.<br><br><%=address%><br><br>Do you want to navigate to the Business Partner?",
        "buydialogalnavigatetitle": "Navigate to Business Partner",
        "buydialogalreadyassignedwithnavigate": "You have selected a historical beserver address which cannot be purchased.<br><br><%=address%><br><br>However, there is an assigned Business Partner with a fallback address available.<br><br><%=newaddress%><br><br>Do you want to navigate to this Business Partner?",
        "buydialognofallbackaddress": "You have selected a historical beserve address which cannot be retrieved.<br><br><%=address%><br><br>There is no fallback address available",
        "buydialogtakeaddress": "<%=address%><br><br>Do you want to buy the above address from beserve Online Services with charge?",
        "buydialogtakealternateaddress": "You have selected a historical address which cannot be retrieved.<br><br><%=address%><br><br>There is an assigned fallback address available.<br><br><%=newaddress%><br><br>Do you want to buy the following address from beserve Online Services with charge?",
        "buydialogtitle": "Buy address from beserve Online Service with Charge?",
        "formattedAddressTemplate": "<%=name%><br><%=zipcode%> <%=address%><br>beDirect No. <%=bedirectno%>",
        "gridcolscore": "Score",
        "gridcolupdateinfo": "Update Info",
        "keepfilter": "Keep Filter for this Session",
        "searchclearbtn": "Clear",
        "searcherrormsg": "beserve Online Services request failed. <br>Code=<%=code%> <br>Message=<%=msg%>",
        "searchidle": "Please Enter Search Criteria and Start Search",
        "searchloading": "Search in progress. Please wait",
        "searchresult": "Search Result",
        "takeoverloading": "Take over in progress. Please wait",
        "title": "beserve Online Services Search",
        "updateloading": "Update in progress. Please wait",
        "searchquery": "Enter search pattern"
      },
      "crefoupdatedlg": {
        "syncbutton": "Synchronize",
        "confirmupdatebodytext": "Do you want to acquire the selected addresses by charge?",
        "idle": "Nothing selected. Synchronization is not possible",
        "nobpsfound": "There are no Business Partner(s) with valid beDirect number.<br><br>First search and select valid Business Partner(s).<br><br>Update not possible",
        "paymenthint": "Hint: This synchronization with beserve Online Services produces cost",
        "syncrunning": "Synchronization in process. Please wait",
        "title": "Synchronize Business Partner with beserve Online Services",
        "updatetitle": "Update Business Partner(s) address(es) via beserve Online Services"
      },
      "crefowiz": {
        "businesspartnernew": "Create new Business Partner",
        "businesspartnerupdate": "Update selected Business Partner",
        "businesspartnerupdateall": "Update all Business Partners",
        "groupTitle": "beserve Online Services"
      },
      "activitiesContainerTitle": "Activities",
		"currency": "Currency",
      "activityDate": "Date",
      "bankContainerDetailTitle": "Bank Detail",
      "branch": "Branch",
      "businessUnit": "Business Unit",
      "certificateStatusTitle": "Change Certificate Status",
      "code": "Code",
      "contactFormContainerTitle": "Contact Detail",
      "contactGridContainerTitle": "Contacts",
      "customerGridContainerTitle": "Customer",
      "customerGroup": "Group",
      "customerStatusTitle": "Change Customer Status",
      "description": "Description",
      "documentDate": "Document Date",
      "documentName": "Document Name",
      "email": "E-Mail",
      "entityClerkReq": "Requisition Owner",
      "entityClerkReqDescription": "Requisition Owner Description",
      "entityContract": "Contract",
      "entityContractDescription": "Contract Description",
      "entityEvaluation": "Evaluation",
      "entityEvaluationDescription": "Evaluation Schema",
      "entityEvaluationItemDescription": "Item Description",
      "entityEvaluationSchemaFk": "Evaluation Schema",
      "entityInvoice": "Invoice",
      "entityInvoiceDescription": "Invoice Description",
      "entityLedgerContext": "Ledger Context",
      "entityPoints": "Result",
      "entityQuotation": "Quotation",
      "entitySubledgerContext": "Subledger Context",
      "evaluatoinContainerTitle": "Evaluation",
      "evaluationUserFormBtnText":  "Open User Form",

      "groupAddresses": "Address",
      "groupComments": "Comments",
      "groupITwoPortal": "iTWO Portal",
      "groupMarketing": "Marketing",
      "groupOther": "Other",
      "groupSystem": "System",
      "groupUserDefined": "User-Defined Fields",
      "info": {
        "ReferenceImages": "Reference Images"
      },
      "internet": "Internet",
      "isLive": "Live",
      "isMainAddress": "Is Main Address",
      "marketingContainerTitle": "Marketing",
      "matchCode": "Match Code",
      "mobileNumber": "Mobile",
      "name1": "Business Partner Name",
      "name2": "Name Affix",
      "name3": "Name 3",
      "name4": "Name 4",
      "procurementStructureContainerTitle": "Procurement Structure",
      "remark2ContainerTitle": "Remark 2",
      "role": "Role",
      "screenEvaluatoinDailogTitle": "Business Partner Evaluation Chart",
      "screenEvaluatoinGroupDataContainerTitle": "Evaluation Schema",
      "screenEvaluatoinReferencesGroupTitle": "References",
      "selectedBusinessPartnerAddress": "Address",
      "selectedBusinessPartnerBusinessUnit": "Business Unit",
      "subsidiaryAddress": "Branch",
      "supplierCode": "Supplier No.",
      "supplierContainerDetailTitle": "Supplier",
      "supplierNo": "Supplier No.",
      "supplierStatusTitle": "Change Supplier Status",
      "tabContacts": "Contacts",
      "tabDetails": "Detail",
      "tabHeader": "Header",
      "tabProject": "Project",
      "tabRemark": "Remark",
      "telephoneFax": "Telefax",
      "telephoneNumber": "Telephone",
      "title": "Addressee",
      "titleName": "Opening",
      "toolbarColumnChart": "Column Chart",
      "toolbarLineChart": "Line Chart",
      "toolbarNewEvaluationScreen": "Create Evaluation",
      "toolbarRadarChart": "Radar Chart",
      "toolbarSvgRefresh": "Refresh",
      "toolbarSvgZoomIn": "Zoom In",
      "toolbarSvgZoomOut": "Zoom Out",
      "viewDetail": "Detail View",
      "viewHeader": "Header View",
      "prcStructureFk": "Procurement Structure",
      "importContact": {
        "mustSelectBP": "Please select a record first",
        "title": "Import Business Partner Contact",
        "orgNotMatch": "The organization is not matched with the selected; do you want to save it?",
        "mustSelectBp": "Select a Business Partner First"
      },
		"firstName": "First Name",
		"familyName": "Last Name",
		"contactTelephone": "Contact Telephone",
		"contactEmail": "Contact Email",
		"createBusinessPartner": "Create Business Partner",
		"chartLegend": {
        "average": "Average",
        "total": "Total"
      },
      "entityIsOptional": "Isoptional",
      "opposite": "Business Partner",
      "creditstanding": {
        "Code": "Code",
        "Description": "Description"
      },
      "creditstandingTitle": "Credit Standing",
      "entityCreditLine": "Credit Line",
      "entityGuaranteeFee": "Guarantee Fee",
      "entityGuaranteePercent": "Guarantee Percent",
      "entityRhythm": "Rhythm",
      "dialog": {
        "btn": {
          "disable": "Disable",
          "displayAndDiscard": "Discard & Display",
          "ignore": "Ignore"
        },
        "discardDuplicateTaxTitle": "Duplicate Telefax",
        "discardDuplicateTelephoneTitle": "Duplicate Telephone",
        "discardDuplicateBusinessPartnerDescription": "Business Partner with same \"{{invalidModel}}\" already exists",
        "discardDuplicateBusinessPartnerTitle": "Discard Duplicate Business Partner"
      },
      "synContact": {
        "avaid": "AVAID",
        "cancel": "Cancel",
        "responsible": "Responsible",
        "selected": "Selected",
        "businessPartners": "Business Partner",
        "checkContacts": "Check Contacts",
        "contacts": "Contact",
        "is2ExchangeGlobal": "To Exchange Global",
        "isToExchangeUser": "To Exchange User",
        "synCon2ExSer": "Synchronise Contacts to Exchange Server",
        "synContacts": "Synchronize Contacts",
        "title": "Title",
        "toGlobalContact": "To Exchange Server Global Contact",
        "toUserContact": "To Exchange Server User Contact",
        "uncheckContacts": "UnCheck Contacts"
      },
      "CreditstandingFk": "Credit Standing",
      "customerTitle": "Customer Search Dialog",
      "import": {
        "importFileTypePlaceHolder": "Please select zip or xlsx file.",
        "Contact_Email": "Contact Email",
        "Contact_FamilyName": "Contact Family Name",
        "Contact_Id": "Contact ID",
        "Contact_Telephone": "Contact Telephone",
        "entityUserDefined1": "User Defined 1",
        "entityUserDefined2": "User Defined 2",
        "entityUserDefined3": "User Defined 3",
        "entityUserDefined4": "User Defined 4",
        "entityUserDefined5": "User Defined 5",
        "Subsidiary_AddressLine": "Branch AddressLine",
        "Subsidiary_City": "Branch City",
        "Subsidiary_CountryFk": "Branch Country",
        "Subsidiary_Id": "Branch ID",
        "Subsidiary_Mobile": "Branch Mobile",
        "Subsidiary_Street": "Branch Street",
        "Subsidiary_Telefax": "Branch Telefax",
        "Subsidiary_TelephoneNumber": "Branch Phone Number",
        "Subsidiary_TelephoneNumber2": "Branch Phone Number2",
        "Subsidiary_ZipCode": "Branch ZipCode",
        "Photo": "Photo",
        "addressCity": "City",
        "addressCountryFk": "Country",
        "addressStreet": "Street",
        "addressZipCode": "Zip Code",
        "avaid": "AVAID",
        "bedirektNo": "beDirect No.",
        "businessPartnerImage": "Business Partner Image",
        "businessPartnerName1": "Business Partner",
        "businessPartnerName2": "Name2",
        "businessPartnerName4": "Name4",
        "communicationChannel": "Communication Channel",
        "companyName": "Company",
        "contactEmail": "Contact Email",
        "contactFamilyName": "Last Name",
        "contactFirstName": "First Name",
        "contactInitial": "Initial",
        "contactMobile": "Contact Mobile",
        "contactTelephone": "Contact Phone",
        "craftCooperative": "Craft Cooperative",
        "craftCooperativeDate": "Craft Cooperative Date",
        "craftCooperativeType": "Craft Cooperative Type",
        "creditStanding": "Credit Standing",
        "crefoNo": "CreFo No.",
        "customerBillingSchema": "Customer Billing Schema",
        "customerBranch": "Customer Branch",
        "customerBusinessPostingGroup": "Customer Posting Group",
        "customerBusinessUnit": "Customer Business Unit",
        "customerCode": "Customer Code",
        "customerLedgerGroup": "Customer Ledger Group",
        "customerPaymentTermFI": "Customer Payment Term (FI)",
        "customerPaymentTermPA": "Customer Payment Term (PA)",
        "customerStatus": "Customer Status",
        "customerSubsidiary": "Customer Branch",
        "customerSupplierNo": "Supplier No.",
        "customerVatGroup": "Customer VAT Group",
        "dunsNo": "DUNS No.",
        "email": "Email",
        "headerCustomerBranch": "Header Customer Branch",
        "isNationwide": "Nation Wide",
        "language": "Language",
        "legalForm": "Legal Form",
        "remark1": "Remark1",
        "remarkMarketing": "Marketing",
        "salutation": "Salutation",
        "subsidiaryDescription": "Branch Description",
        "subsidiaryMobile": "Mobile",
        "subsidiaryTelefax": "Telefax",
        "subsidiaryTelephoneNumber": "Telephone",
        "subsidiaryTelephoneNumber2": "Telephone2",
        "supplierBank": "Bank",
        "supplierBillingSchema": "Supplier Billing Schema",
        "supplierBusinessPostingGroup": "Supplier Posting Group",
        "supplierCode": "Creditor Code",
        "supplierCustomerNo": "Customer No.",
        "supplierDescription": "Supplier Description",
        "supplierDescription2": "Supplier Description 2",
        "supplierLegerGroup": "Supplier Ledger Group",
        "supplierPaymentTermFI": "Supplier Payment Term (FI)",
        "supplierPaymentTermPA": "Supplier Payment Term (PA)",
        "supplierSubsidiary": "Supplier Branch",
        "supplierVatGroup": "Supplier VAT Group",
        "tradeRegister": "Trade Register",
        "tradeRegisterDate": "Trade Resister Date",
        "tradeRegisterNo": "Trade Resister No.",
        "vatNo": "VAT No.",
        "addressCounty": "County",
        "importSuccessed": "Import business partner succeeded!",
        "businessPartnerBankAccount": "Account",
        "businessPartnerBankIBAN": "IBAN",
        "businessPartnerBankName": "Bank for Business Partner",
        "bankGroupName": "Bank",
        "bpGroupName": "Business Partner",
        "contactGroupName": "Contact",
        "customerGroupName": "Customer",
        "subsidiaryGroupName": "Subsidiary",
        "supplierGroupName": "Supplier",
        "contactFax": "Contact Fax",
        "contactTimeliness": "Contact Timeliness",
        "contactTitle": "Contact Title",
        "isRequired": "{{DisplayName}} is Required",
        "subsidiaryEmail": "Subsidiary Email",
        "DoubletFindMethod0": "Creditreform or D&B D-U-N-S number",
        "DoubletFindMethod1": "TAX number",
        "DoubletFindMethod2": "Telephone pattern",
        "DoubletFindMethod3": "Name, Street, City, and Zip Code",
        "Contact_FirstName": "Contact First Name",
        "Contact_Mobile": "Contact Mobile",
        "emptyErrorMessage": "{{DisplayName}} should map to source field",
        "remark2": "Remark2",
        "addressState": "State",
        "prcIncotermFk": "Incoterm",
        "subsidiaryStatus": "Branch Status",
        "businessPartnerName3": "Name3",
        "addressType": "Address Type"
      },
      "errorMessage": {
        "codeUniqueInASublegerContext": "The field Code should be unique in a Subledger Context",
        "fieldLengthLessThenSpecificNumber": "The length of field {{p0}} should less then {{p1}}",
        "fieldLengthRestrict": "The length of field {{p0}} should between {{p1}} and {{p2}}",
        "failedCopyContact": "The contact cannot be copied again because all possible combinations of contact and role have already been created.",
        "duplicateValue": "The value of field {{p_0}} is duplicated",
        "placeholder": {
          "codeAndLedgerGroup": "Code and Ledger Group"
        }
      },
      "referenceValue1": "Reference Value1",
      "referenceValue2": "Reference Value2",
      "screenEvaluatoinDocumentDataContainerTitle": "Evaluation Document",
      "businessPartnerStatus2": "Business Partner Status 2",
      "state": "State",
      "crefostatusicon": {
        "alreadytherematchbycrefo": "Business partner is matching the Crefo Identifier but not the Bedirect Number",
        "alreadythere": "Business Partner Already Exists",
        "changes": "Business Partner updated with latest data",
        "historical": "Business Partner address is historical. New address available",
        "historicalinactive": "Business Partner exists but is historical",
        "importok": "Business Partner can be accepted",
        "nochanges": "Business Partner no change found",
        "notfound": "Business Partner not found in beserve Online Services Database",
			"otherduplicatedatafound": "Other duplicate data is found"
      },
      "entityExternal": "Business Partner to External",
      "entityExternalDetail": "Business Partner to External Detail",
      "HasFrameworkAgreement": "Has Material Catalog",
		 "isframework": "Has Framework Agreement",
		 "activeframeworkcontract": "Active Framework Contract",
      "portalAccessGroup": "Portal Access Group",
      "portal": {
        "wizard": {
          "errAccessGroupTitle": "Reading UserAccessGroup failed!",
          "errClerkInvalidBody": "The current login user does not having a valid clerk attached.<br>The clerk need a valid E-Mail address and a family name<br><br><b>Please add a new clerk or check its values!</b>",
          "errClerkInvalidTitle": "Checking Current User Clerk failed!",
          "errSomeThingFailedTitle": "Error occured while preparing Dialog!",
          "invitationDialogBody": "Do you want to invite the following Contact as a Bidder for the iTWO4.0 Portal?<br><br>",
          "invitationDialogTitle": "Bidder Invitation Wizard for iTWO4.0 Portal",
          "loading": "Loading dialog data. Please Wait ...",
          "errUsernotFoundBody": "Selected <b>User</> not found or there is no <b>Contact</b> found for this user!<br>We can use user with contacts only.<br><br>Please select another user and try again!<br>",
          "errUsernotFoundTitle": "User not Found!"
        },
        "wfaction": {
          "companyName": "Company",
          "createContact2Bizpartner": "Create Contact to Businesspartner",
          "linkSelectedContact": "Assign to Selected Contact",
          "providerName": "Name",
          "startBizPartnerModule": "Start Businesspartner Module",
          "startContactModule": "Start Contact Module",
          "portalAccessGroup": "Portal Access Group"
        }
      },
      "geographicLocation": {
        "convertComplete": "Convert Complete.",
        "convertOnlyEmpty": "Only convert empty coordinate",
        "status": {
          "error": "Error",
          "notConvert": "NotConvert",
          "waiting": "Waiting",
          "success": "Success"
        },
        "title": "Convert Address To Geographic Coordinates"
      },
      "invalidTaxNoOrVatNo": "{{field}} does not fulfill the given specification!",
      "screenQuoteEvaluationnDailogTitle": "Quote Evaluation",
      "configUniqueMessage": "{{fieldName}} should be unique.",
      "advancedCriteria": "Advanced Criteria",
      "businesspartner": {
        "entityClerkAlt": "Clerk (Authorization)",
        "entityClerkAltDetail": "Clerk Detail (Authorization)",
        "entityClerk": "Business Partner Clerk",
        "entityClerkForm": "Business Partner Clerk Detail"
      },
      "controllingGroupAssignments": {
        "Detail": "Controlling Group Assignments Details",
        "grid": "Controlling Group Assignments"
      },
      "ControllinggroupFk": "Controlling Group",
      "ControllinggrpdetailFk": "Controlling Group Detail",
      "general": {
        "grid": "Generals",
        "Detail": "General Details"
      },
      "commentText": "Comment Text",
      "isAccepted": "Is Accepted",
      "newValue": "New Value",
      "newValueDescription": "New Value Description",
      "objectFk": "Object Id",
      "objectFkDescription": "Object Id Description",
      "objectFkNew": "Object Id New",
      "oldValue": "Old Value",
      "screenEvaluationClerkDataContainerTitle": "Evaluation Clerk",
      "updateColumn": "Update Column",
      "updateRequestTitle": "Update Requests",
      "updateSource": "Update Source",
      "updateTable": "Update Table",
      "businessPartnerDialog": {
        "city": "City",
        "column": "Column:",
        "endsWith": "Ends With",
        "mode": "Mode:",
        "name": "Name",
        "startsWith": "Starts With",
        "street": "Street",
        "userDefined1": "User Defined1",
        "searchString": "Search String",
        "advancedCriteria": "Advanced Criteria",
        "zip": "ZIP",
        "includes": "includes"
      },
      "changeBankStatusTitle": "Change Business Partner Bank Status",
      "screenEvaluationClerkDataContainerCommonTitle": "Clerk(Authorizations)",
      "screenEvaluationClerkDataOnOffButtonText": "Is Evaluation Group/Sub Group Clerk On",
      "scrrenEvaluationClerkContainerSubTitle": {
        "evalGroupData": "Evaluation Group Data",
        "evalSubGroupData": "Evaluation Sub Group Data"
      },
      "failedCopyContactTitle": "Failed Copy Contact",
      "certificateCharacteristicContainerTitle": "Certificate Characteristics",
      "remark2Field": "Remark2",
      "tradeName": "Trade Name",
      "subsidiaryDuplicateTitle": "Subsidiary Duplicate",
      "subsidiaryDuplicateWarning": "There is a duplicate entry for this branch. We recommend to adapt the description.",
      "powerBIElements": "Power BI Workspaces,Dashboards and Reports",
      "powerbiView": "PowerBI Viewer",
      "importBusinessPartner": {
        "title": "Import Business Partner",
        "leaveSame": "Do you want to save the existing business partner as a new record?",
        "bpNameIsEmpty": "The Business Partner Name cannot be Null"
      },
      "creforesultdlg": {
        "navigatetobpstitle": "Navigate to Business Partner(s)",
        "navigatetobpstitlebody": "Do you want to refresh the Business Partner(s) resultset?",
        "title": "Synchronize Business Partner Result"
      },
      "entityCustomerCompany": "Customer Company",
      "activityFormContainerTitle": "Activity Details",
      "addressType": "Address Type",
      "agreementDetailContainerTitle": "Agreement Detail",
      "agreementsContainerTitle": "Agreements",
      "alreadyDisabled": "Business Partner Main {{code}} is already disabled",
      "alreadyEnabled": "Business Partner Main {{code}} is already enabled",
      "bankContainerTitle": "Banks",
      "beDirectNo": "beDirect No.",
      "birthDate": "Date of Birth",
      "bpRelation": "Business Partner Relation",
      "bpRelationChart": "Business Partner Relation Chart",
      "bpRelationDetail": "Business Partner Relation Detail",
      "branchDescription": "Branch Description",
      "businessPartner2CompanyContainerTitle": "Registered for Company",
      "businessPartner2CompanyFormContainerTitle": "Registered for Company Detail",
      "children": "Children",
      "codeAndLedgerGroupDuplicationError": "Duplicate value in fields Code and Ledger Group",
      "companyResponsibleCompany": "Responsible Profit Centre",
      "companyResponsibleCompanyCode": "Responsible Profit Centre (Code)",
      "companyResponsibleCompanyName": "Responsible Profit Centre (Description)",
      "contactAddress": "Private Address",
      "contactCharacteristicContainerTitle": "Contact Characteristics",
      "contactFormData": "Contact Form Data",
      "contactPhotoContainerTitle": "Contact Photo",
      "contactTelephoneNumber": "Private Telephone",
      "craftCooperative": "Craft Cooperative",
      "craftCooperativeDate": "Craft Cooperative Date",
      "craftCooperativeType": "Craft Cooperative Type",
      "creFoNo": "CreFo No.",
      "customerAbc": "ABC Classification",
      "customerBranch": "Branch",
      "customerBranchCode": "Branch Code",
      "customerCode": "Code (Debtor No.)",
      "customerFormContainerTitle": "Customer Detail",
      "customerNo": "Customer No.",
      "customerSector": "Sector",
      "customerStatus": "Customer Status",
      "dialogTitleAgreement": "Assign Agreement",
      "disableDone": "Business Partner Main {{code}} disabled successfully. Please save the record to make this changes permanent.",
      "documentType": "File Type",
      "dunsNo": "DUNS No.",
      "enableDone": "Business Partner Main {{code}} enabled successfully. Please save the record to make this changes permanent.",
      "entityBpOpposite": "Business Partner Opposite",
      "entityBpOwner": "Business Partner Owner",
      "entityClerkPrc": "Procurement Clerk",
      "entityClerkPrcDescription": "Procurement Clerk Description",
      "entityContact1": "Contact 1",
      "entityContact2": "Contact 2",
      "entityCreditorCode": "Creditor Code",
      "entityDebtorCode": "Debtor Code",
      "entityDescription": "Supplier Description",
      "entityEvaluationDate": "Evaluation Date",
      "entityEvaluationGroupFk": "Group/Subgroup",
      "entityEvaluationMotiveFk": "Evaluation Motive",
      "entityEvaluationWeightingGroup": "Weighting Group",
      "entityPointsPossible": "Possible",
      "entityQuotationDescription": "Quotation Description",
      "entityReferenceDate": "Reference Date",
      "entityStatus2": "Status Sales",
      "entityWeighting": "Weighting",
      "formData": "Business Partner Form Data",
      "further": "Further",
      "groupCommunication": "Communication",
      "groupExternalIdentities": "External Identities",
      "headerFormContainerTitle": "Business Partner Detail",
      "headerGridContainerTitle": "Business Partners",
      "initials": "Initials",
      "InnNo": "INN No.",
      "inquiry": {
        "noaddress": "Address Missing",
        "noname": "Name Missing",
        "nosubsidiarydesc": "Description Missing"
      },
      "isNationwide": "Is Nationwide",
      "lastAction": "Last Action",
      "lastLogin": "Last Logon",
      "ledgerGroup": "Ledger Group",
      "legalForm": "Legal Form",
      "nickname": "Nickname",
      "objectName": "Object Name",
      "origin": "Origin",
      "partnerName": "Partner Name",
      "photoContainerTitle": "Reference Images",
      "pinboard": {
        "contactPrivateCommentTitle": "Contact Private Comment",
        "certificateCommentTitle": "Certificate Pin Board"
      },
      "potential": "Potential",
      "pronunciation": "Pronunciation",
      "ratingViewContainerTitle": "Customer Satisfaction",
      "realEstateDetailContainerTitle": "Object of Customer Detail",
      "realEstateGridContainerTitle": "Objects of Customer",
      "remark1ContainerTitle": "Remark 1",
      "screenEvaluatoinContainerDetailTitle": "Screen Evaluation Detail",
      "screenEvaluatoinContainerTitle": "Screen Evaluation",
      "screenEvaluatoinItemDataContainerTitle": "Evaluation Items",
      "selectedBusinessPartnerContainerTitle": "Selected Business Partner",
      "selectedBusinessPartnerName1": "Name 1",
      "status2Title": "Change Business Partner Status 2",
      "statusTitle": "Change Business Partner Status",
      "subsidiaryDetailContainerTitle": "Branch Detail",
      "subsidiaryGridContainerTitle": "Branches",
      "supplierContainerDetail": "Supplier Detail",
      "supplierContainerTitle": "Suppliers",
      "supplierStatus": "Supplier Status",
      "tabEvaluatoin": "Evaluation",
      "tabPhoto": "Photo",
      "taxNo": "Tax No.",
      "telephoneNumber2": "Other Telephone",
      "timeliness": "Contact Timeliness",
      "toolbar3DColumns": "3D Columns",
      "toolbarSvgCentral": "Zoom to Central",
      "toolbarSvgWhole": "Zoom All",
      "tradeRegister": "Trade Register",
      "tradeRegisterDate": "Trade Register Date",
      "tradeRegisterNo": "Trade Register No.",
      "userName": "User Name",
      "avgEvaluationA": "Avg. Evaluation Group A",
      "avgEvaluationB": "Avg. Evaluation Group B",
      "avgEvaluationC": "Avg. Evaluation Group C",
      "countEvaluationA": "No. of Eval. A",
      "vatGroup": "VAT Group",
      "vatNo": "VAT No.",
      "viewContact": "Contact View",
      "viewEvaluatoin": "Evaluation View",
      "viewPhoto": "Photo View",
      "viewProject": "Project View",
      "viewRemark": "Remark View",
      "wizardChangeStatus": "Status Change",
      "wizardTitle": "Group Name - Business Partner",
      "wrongAttempts": "No Wrong Attempts",
      "supplierTitle": "Supplier Search Dialog",
      "countEvaluationB": "No. of Eval. B",
      "countEvaluationC": "No. of Eval. C",
      "screenCustomerCompanyDailogTitle": "Define Supplier No. Used in Company",
      "screenSupplierCompanyDailogTitle": "Define Customer No. used in Company",
      "toolbarDefineCompanyRestriction": "Define Company Restriction",
      "actualCertificateListContainerTitle": "Current Certificates",
      "bankIBANFormatError": "It's a wrong IBAN account number format.",
      "contact": {
        "entityClerk": "Contact Clerk",
        "entityClerkForm": "Contact Clerk Details",
        "deleteError": "Business partner contact cannot be deleted as it is assigned to other business partners"
      },
      "OppositeStatus": "Opposite Status",
      "OwnerStatus": "Owner Status",
      "contact2exchange": {
        "IsToExchange": "Synchronise to Exchange Server User Contact",
        "IsToExchangeGlobal": "Synchronise to Exchange Server Global Contact"
      },
      "businessPartnerMustSelect": "Please select a Business Partner.",
      "identityProviderName": "Identity Provider Name",
      "portalUserGroupName": "Portal User Group",
      "provider": "Provider",
      "providerAddress": "Provider Address",
      "providerEmail": "Provider E-mail",
      "providerFamilyName": "Provider Family Name",
      "providerId": "Provider Id",
      "setInactiveDate": "Set Inactive Date",
      "wizardErrorNoContactSelected": "Please select at least one Contact to execute.",
      "wizardReactivateOrInactivatePortalUserTitle": "Re/In-Activate Portal User",
      "wizardRemoveOrUnlinkPortalUserTitle": "Remove/Unlink Portal User",
      "evaluationStatusTitle": "Change Evaluation Status",
      "amongValueErrorMessage": "The result ({{value}}) has to between {{min}} and {{max}}",
      "entityAmountMaximum": "Amount Maximum",
      "entityGuaranteeFeeMinimum": "Guarantee Fee Minimum",
      "entityGuaranteeType": "Guarantee Type {{p_0}}",
      "entityGuarantor": "Guarantors",
      "entityGuarantorDetail": "Guarantor Detail",
      "entityGuarantorType": "Guarantor Type",
      "businessPostingGroup": "Business Posting Group",
      "activityType": "BP Activity Type",
		 "entityGuaranteeUsed": "Guarantee Used",
		 "finished": "Finished",
		 "ReminderFrequency": "Reminder Frequency",
		 "ReminderCycle": "Reminder Cycle",
		 "ReminderStartDate": "Start Date",
		 "ReminderEndDate": "End Date",
      "bpActivityDate": "Activity Date",
      "messageText": "Message Text",
      "remark1Field": "Remark1",
      "blockingReason": "Blocking Reason",
      "taxOfficeCode": "Tax Office No.",
      "entityProfitCenter": "Profit Center",
      "entityProfitCenterName": "Profit Center Name",
      "businessPartnerAssignment": {
        "detail": "Business Partner Assignment Detail",
        "grid": "Business Partner Assignments"
      },
      "failToExecuteFormula": "The point formula '{{formulaParsed}}' for '{{formula}}' can't be executed, please correct and try it again.",
      "failToRecalculate": "Fail to recalculate: ",
      "failToExecuteSql": "The sql of formula '{{formula}}' can't be executed. Please correct and try it again. Message: {{message}}",
      "selectedCompanyInvalid": "Company invalid by distinct contexts",
      "isChecked": "Checked",
      "checkVatWizard": {
        "title": "Check Business Partner VAT No. Result",
        "unknownIssue": "Unknown issue blocks the validation."
      },
      "dunsUrl": {
        "addressCity": "The City of Address",
        "addressStreet": "The Street of Address",
        "basicsPart": "Basics Part",
        "countryISO": "The ISO of Country",
        "placeholders": "Placeholders",
        "urlConfigHelp": "Url Configuration Help",
        "variablePart": "Variable Part"
      },
      "invalidTaxNoOrVatNoWithExample": "{{field}} does not fulfill the given specification. A valid input is {{example}}!",
      "vatNoEu": "Vat No.EU",
      "updatePrcStructureWizard": {
        "btnUpdate": "Update",
        "error": {
          "emptyQuoteOrContractStatus": "{{quoteStatus}} or {{contractStatus}} can not be empty.",
          "selectOneBp": "Please select at least one branch.",
          "validFromToDate": "{{from}} can't be later than {{to}}."
        },
        "label": {
          "contractStatus": "Contract Status",
          "from": "From",
          "quoteStatus": "Quote Status",
          "scope": "Scope of Quote and Contract",
          "to": "To"
        },
        "notification": "Selected branch's procurement structures will be updated from respective Quotes and Contracts.",
        "result": "{{count}} branch(s)'s procurement structure updated successfully!",
        "title": "Update Procurement Structure from Quote and Contract"
      },
      "community": {
        "detailTitle": "Joint Business Partner Detail",
        "error": {
          "bpSelfForbidden": "Selecting single business partners is prohibited."
        },
        "title": "Joint Business Partners"
      },
      "communityBidderFk": "Business Partner",
      "communitySubsidiaryFk": "Branch Description",
      "subsidiaryStatusTitle": "Change Branch Status",
      "evaluationDeleteMessage": "The selected evaluation will be deleted totally,are you sure?",
      "evaluationDeleteTitle": "Delete Business Partner Evaluation",
      "bpIsLive": "Active",
      "toolbarEvaluationDetail": "Evaluation Detail",
      "bp2SubRelationTypeBpDesc": "Business Partner",
      "bp2SubRelationTypeBpOppositeDesc": "Branch",
      "bpBranch": "BP Branch",
      "bpBranchAddress": "BP Branch Address",
      "bpOppositeBranch": "BP Opposite Branch",
      "bpOppositeBranchAddress": "BP Opposite Branch Address",
      "relationInfoAddressLabel": "Address",
      "relationInfoTelephoneLabel": "Telephone",
      "toolbarShowBranch": "With / Without branch dimension",
      "sub2BpRelationTypeDesc": "Branch",
      "sub2BpRelationTypeOppositeDesc": "Business Partner",
      "legalFormDialogTitle": "Legal Form Input",
      "entityBuyerReference": "Buyer Reference",
      "contactIsLive": "Is Live",
      "subsidiaryStatusErrMsg": "Warning: An address flagged as main address cannot be deactivated.",
      "activitiesSpecificationContainerTitle": "Specification",
      "branchAddress": "Branch Address",
      "vatCountryFk": "Vat Country",
		"vatCountryDesc": "Vat Country-Description",
      "changeBpCode": {
        "noRubricCategory": "No select rubric category",
        "successChange": "Change code successfully",
        "title": "Change Business Partner Code",
        "unknownIssue": "Can not change business partner code,Please check the number range setting in the company module",
        "zeroVersion": "Can not use in new data"
      },
      "RubricCategoryFk": "Rubric Category",
      "entityValidFrom": "Valid From",
      "entityValidTo": "Valid To",
      "entityGuaranteeTypeFk": "Guarantee Type",
      "entityBpdIssuerbusinesspartnerFk": "Issuer BusinessPartner",
      "entityDate": "Date",
      "entityDischargedDate": "Discharged Date",
      "entityExpirationDate": "Expiration Date",
      "entityIssuer": "Issuer",
      "entityRequiredDate": "Required Date",
      "entityValidatedDate": "Validated Date",
      "entityValidfrom": "Validfrom",
      "entityValidto": "Validto",
		"entityAmountMaximumText": "Amount Maximum Text",
		"entityAmountRemaining": "Amount Remaining",
		"entityAmountCalledOff": "Amount Called Off",
		"entityGuaranteeStartDate": "Guarantee Start Date",
		"entityGuaranteeEndDate": "Guarantee End Date",
		"entityGuarantorActive": "Guarantor Active",
		"entityFrameNumber": "Frame Number",
		"entityTotalAval": "Total Aval Frame",
		"error": {
			"valueNotLessThanZero": "Value cannot be less than 0.",
			"totalAvalNotLessThanAmountCalledOff": "Total Aval Frame cannot be less than Amount Called Off.",
			"totalAvalNotLessThanAmountMax": "Total Aval Frame cannot be less than the Amount Maximum.",
			"guarantorTotalAvalNotLessThanConditionTotalAval": "Guarantor Total Aval Frame cannot be less than the sum of all Condition Total Aval Frame.",
			"guarantorAmountMaxNotLessThanConditionAmountMax": "The Amount Maximum of each Guarantor Condition must not exceed the Guarantor Amount Maximum."
		},
      "certificateToSubsidiaryDailogTitle": "Certificate relate to Subsidiary",
      "toolbarCertificate2Subsidiary": "Certificate To Branch",
      "toolbarCertificate2Branch": "Certificate To Branch",
		 "entityGuaranteeStartDateLargeEndDate": "{{enddate}} must be greater than {{startdate}}",
		 "entityGuaranteeValidFromLargeValidTo": "{{validto}} must be greater than {{validfrom}}",
		 "distance": "Distance",
      "entityEinvoice": "E-Invoice",
		 "creditLimit": "Credit Limit",
		 "oppositeAddress": "Opposite Address",
		 "isReadOnlyMessage": "Selected business partner status is read only, wizard cannot be used!",
		 "changeCode": {
			 "statusIsReadonly": "The record is readonly.",
			 "noGenerateCodeSettingIsSet": "No Code generation setting is set.",
			 "supplierTitle": "Change Supplier Code",
			 "customerTitle": "Change Customer Code",
			 "selectAtLeastOneCustomer": "Please select one Customer first.",
			 "selectAtLeastOneSupplier": "Please select one Supplier first.",
			 "customerDiffSubLedgerContext": "The Customer Code can not be edited, as the selected Customer does not belong to the current company's ledger context",
			 "supplierDiffSubLedgerContext": "The Supplier Code can not be edited, as the selected Supplier does not belong to the current company's ledger context"
		 },
		 "mainBranchDesc": "Main Branch Description",
		 "customerOpenItemGridContainerTitle": "Customer Open Items",
		 "supplierOpenItemGridContainerTitle": "Supplier Open Items",
		 "openItem": {
			 "customerNo": "Customer No.",
			 "customerName": "Customer Name",
			 "vendorNo": "Vendor No.",
			 "vendorName": "Vendor Name",
			 "amount": "Amount",
			 "amountLCY": "Amount LCY",
			 "auxiliaryIndex1": "Auxiliary Index1",
			 "creditAmount": "Credit Amount",
			 "creditAmountLCY": "Credit Amount LCY",
			 "currencyCode": "Currency Code",
			 "debitAmount": "Debit Amount",
			 "debitAmountLCY": "Debit Amount LCY",
			 "dimensionSetID": "Dimension Set ID",
			 "documentDate": "Document Date",
			 "documentNo": "Document No.",
			 "documentType": "Document Type",
			 "dueDate": "Due Date",
			 "ICPartnerCode": "IC Partner Code",
			 "open": "Open",
			 "originalAmtLCY": "Original Amount LCY",
			 "pmtDiscountDate": "Pmt Discount Date",
			 "postingDate": "Posting Date",
			 "reasonCode" : "Reason Code",
			 "remainingAmount": "Remaining Amount",
			 "remainingAmountLcy": "Remaining Amount LCY",
			 "salespersonCode": "Sales Person Code",
			 "purchaserCode": "Purchaser Code",
			 "transactionNO": "Transaction No."
		 },
		 "ExternalRoleFk": "External Role",
		 "extRoleGridContainerTitle": "External Roles",
		 "extRoleDetailContainerTitle": "External Role Detail",
		 "isDefaultBaseline": "Is Default Baseline",
		 "bankIsDefaultCustomer": "Is Default Customer",
		 "bankIsDefaultSupplier": "Is Default Supplier",
		 "ledgerGroupIcRecharging": "Ledger Group IC Recharging",
		 "externalid": "External Id",
		 "externaldescription": "External Description",
         "oppositeDescriptionRelationLookup": "Opposite Description",
         "descriptionRelationLookup": "Description",
         "entityPercentage": "Percentage",
         "PercentageCountError": "The distribution is incomplete, it is not reached 100%.",
         "EntityId": "Id",
		 "regionGridContainerTitle": "Region",
		 "regionDetailContainerTitle": "Region Detail",
		 "isActive" : "Is Active",
		 "filterByBranch": "Filter By Branch",
         "biIdnr": "BI-IDNr",
         "supplierStatusWarning": "The selected supplier status is locked",
		 "entityConditionNumber": "Condition Number",
		 "guarantorConditionListTitle": "Guarantor Conditions",
		 "guarantorConditionDetailTitle": "Guarantor Condition Detail",
		 "guarantorConditionError": {
			 "validFromBeforePreviousValidTo": "The Valid From date must be greater than the previous Valid To date.",
			 "validFromBeforePreviousValidFrom": "The Valid From date must be greater than the previous Valid From date.",
			 "guaranteeTypeCannotBeModified": "The guarantee type cannot be modified while this condition is in use.",
			 "divisionNumberNotLessAssignedQuantity": "The division number must not be less than the assigned quantity under this condition."
		 },
		 "creationSuccess": "Created successfully.",
		 "creationError": "Creation failed.",
		 "updateByGuarantorCondition": {
			 "title": "Update Guarantee Used by Guarantor Condition",
			 "selectGuarantorCondition": "Please select a saved Guarantor Condition record first.",
			 "conditionNotMeetCriteria": "The data defined in the selected guarantee condition are invalid. Please verify the settings for Guarantee Type, Company, Project, Valid From/To, Amount Maximum and Division Number.",
			 "success": "Guarantee Used updated successfully based on the selected guarantor condition."
		 },
        "differentAccountHolder": "Different Account Holder",
        "isEmailNotification": "Notification Sent By Email",
        "notificationEmail": "Notification Email Address",
        "postAvis": "Avis From Post",
        "isBpSettlement": "Business Partner Settlement",
        "itemPerTransfer": "Items Per Transfer"
    }
  }
}
