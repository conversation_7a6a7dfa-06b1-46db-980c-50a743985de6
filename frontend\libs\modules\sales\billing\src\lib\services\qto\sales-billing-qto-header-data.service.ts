/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable, inject } from '@angular/core';
import { DataServiceFlatRoot, IDataServiceOptions, ServiceRole } from '@libs/platform/data-access';
import { ISalesBillingQtoHeaderEntity } from '../../model/entities/sales-billing-qto-header-entity.interface';
import { SalesBillingQtoHeaderComplete } from '../../model/complete-class/sales-billing-qto-header-complete.class';
import { SalesBillingBillsDataService } from '../sales-billing-bills-data.service';

/**
 * Sales Billing QTO Header Data Service
 * Manages QTO headers for billing
 */
@Injectable({
	providedIn: 'root',
})
export class SalesBillingQtoHeaderDataService extends DataServiceFlatRoot<
	ISalesBillingQtoHeaderEntity,
	SalesBillingQtoHeaderComplete
> {
	public childDetailService?: any; // Will be set by the detail service

	public constructor() {
		const parentService = inject(SalesBillingBillsDataService);
		
		const options: IDataServiceOptions<ISalesBillingQtoHeaderEntity> = {
			apiUrl: 'qto/main/header', // TODO: Update with correct API URL for billing QTO
			readInfo: {
				endPoint: 'list',
				usePost: true,
			},
			createInfo: {
				endPoint: 'create',
				usePost: true,
			},
			roleInfo: {
				role: ServiceRole.Root,
				itemName: 'QtoHeader',
			},
		};

		super(options);
	}

	protected override provideLoadPayload(): object {
		// TODO: Implement proper load payload for billing QTO headers
		return {};
	}

	/**
	 * Get the currently selected QTO header
	 */
	public getCurrentHeader(): ISalesBillingQtoHeaderEntity | null {
		return this.getSelectedEntity();
	}
}
