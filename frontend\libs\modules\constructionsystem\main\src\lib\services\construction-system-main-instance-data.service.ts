/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, InjectionToken } from '@angular/core';
import { DataServiceFlatRoot, IDataServiceEndPointOptions, IDataServiceOptions, IDataServiceRoleOptions, ServiceRole } from '@libs/platform/data-access';
import { ConstructionSystemSharedTemplateLookupService, ICosInstanceEntity } from '@libs/constructionsystem/shared';
import { CosMainComplete } from '../model/entities/cos-main-complete.class';
import { IDescriptionInfo, IPinningContext, ISearchPayload, ISearchResult, PlatformHttpService, PlatformModuleNavigationService, PlatformPinningContextService, ServiceLocator } from '@libs/platform/common';
import { CostGroupCompleteEntity, IBasicMainItem2CostGroup, PinningContextToken } from '@libs/basics/shared';
import { CosInstanceDto } from '../model/cos-instance-dto.class';
import { ConstructionSystemMainCommonLookupService } from './construction-system-main-common-lookup.service';
import { InstanceHeaderProjectInfo } from '../model/entities/Instance-header-project-info.interface';
import { ConstructionSystemMainInitFilterService } from './filter-structure/construction-system-main-init-filter.service';
import { IInstanceFilterRequest } from '../model/entities/filter-request/instance-filter-request.interface';
import { ConstructionSystemMainFilterService } from './filter-structure/construction-system-main-filter.service';
import { ReplaySubject } from 'rxjs';
import { ICosTemplateChangeData } from '../model/entities/cos-template-change-data.interface';
import { IEstimateCompositeEntity } from '@libs/estimate/shared';
import { CosInstanceStatus } from '../model/enums/cos-instance-status.enum';
import { IClosingDialogButtonEventInfo, ICustomDialog, StandardDialogButtonId, UiCommonDialogService, UiCommonMessageBoxService } from '@libs/ui/common';
import { ITokenValueFilter } from '../model/entities/filter-request/instance-token-value-filter.interface';
import { ConstructionSystemMainInstanceHeaderParameterDataService } from './construction-system-main-instance-header-parameter-data.service';
import { ConstructionSystemCommonPropertyNameLookupService } from '@libs/constructionsystem/common';
import { ConstructionSystemMainHeaderListDataService } from './construction-system-main-header-list-data.service';
import { Instance2dObjectDialogComponent } from '../components/instance-2d-object-dialog/instance-2d-object-dialog.component';
import { ConstructionSystemMainObjectTemplateDataService } from './construction-system-main-object-template-data.service';

export const CONSTRUCTION_SYSTEM_MAIN_INSTANCE_DATA_TOKEN = new InjectionToken<ConstructionSystemMainInstanceDataService>('constructionSystemMainInstanceDataToken');

interface IsyncCostGroup {
	dtos: ICosInstanceEntity[];
	CostGroupCats?: CostGroupCompleteEntity;
	Header2CostGroups: IBasicMainItem2CostGroup[];
}

@Injectable({
	providedIn: 'root',
})
export class ConstructionSystemMainInstanceDataService extends DataServiceFlatRoot<ICosInstanceEntity, CosMainComplete> {
	private readonly constructionSystemMainCommonLookupService = inject(ConstructionSystemMainCommonLookupService);
	private readonly constructionSystemMainInitFilterService = inject(ConstructionSystemMainInitFilterService);
	private readonly constructionSystemMainFilterService = inject(ConstructionSystemMainFilterService);
	private readonly platformModuleNavigationService = inject(PlatformModuleNavigationService);
	private readonly msgDialogService = ServiceLocator.injector.get(UiCommonMessageBoxService);
	private readonly instanceHeaderParameterService = ServiceLocator.injector.get(ConstructionSystemMainInstanceHeaderParameterDataService);
	private readonly http = inject(PlatformHttpService);
	private readonly constructionSystemCommonPropertyNameLookupService = ServiceLocator.injector.get(ConstructionSystemCommonPropertyNameLookupService);
	private readonly constructionSystemSharedTemplateLookupService = ServiceLocator.injector.get(ConstructionSystemSharedTemplateLookupService);
	private readonly platformPinningContextService = inject(PlatformPinningContextService);
	private readonly modalDialogService = ServiceLocator.injector.get(UiCommonDialogService);
	protected templateChanged$ = new ReplaySubject<ICosTemplateChangeData>(1);
	public onContextUpdated$ = new ReplaySubject<ICosInstanceEntity | undefined>(1);
	private selectedProjectInfo?: InstanceHeaderProjectInfo;
	private currentSelectedProjectId?: number;
	private currentSelectedModelId?: number;
	private currentInstanceHeaderId?: number;
	private currentSelectedEstimateHeaderId?: number;
	private currentBoqHeaderId?: number;
	private currentProjectEntity: { ProjectNo?: number; ProjectName?: string } = {};
	private currentInsHeaderEntity: { Code?: string; DescriptionInfo?: IDescriptionInfo } = {};
	private isDeepCopyAllowed: boolean = true;
	private customFurtherFilter: ITokenValueFilter[] = [];
	private instanceHeaderDto?: unknown;
	private requestCache = new Map<string, boolean>();
	public is2DModel: boolean = true;

	public constructor() {
		const options: IDataServiceOptions<ICosInstanceEntity> = {
			apiUrl: 'constructionsystem/main/instance',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'list',
				usePost: true,
			},
			roleInfo: <IDataServiceRoleOptions<ICosInstanceEntity>>{
				role: ServiceRole.Root,
				itemName: 'Instances',
			},
			entityActions: { createSupported: false, deleteSupported: true },
		};
		super(options);
		this.processor.addProcessor([this.assignProjectProcessor()]);
		this.initContext();
		this.subscriptToTemplateChange();

		this.onContextUpdated$.subscribe(this.updateModuleHeaderInfo.bind(this));
	}

	private assignProjectProcessor() {
		return {
			process: (item: ICosInstanceEntity) => {
				item.ProjectFk = this.getCurrentSelectedProjectId(); /// use in projectLocation lookup,
			},
			revertProcess() {},
		};
	}
	//todo  'constructionSystemMainInstanceParameterHelpService' //  don't remove for it should be initialized when the controller initializes
	///todo waiting for cloudDesktopPinningContextService.getContext() ready
	private initContext() {
		this.instanceHeaderParameterService.setInstanceHeaderId(this.currentInstanceHeaderId);
		this.constructionSystemCommonPropertyNameLookupService.setCurrentModelId(this.currentSelectedModelId);
	}

	public override createUpdateEntity(modified: ICosInstanceEntity | null): CosMainComplete {
		const complete = new CosMainComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.Instance = modified;
		}
		return complete;
		///todo InstanceHeaderParameter
		// function onUpdateRequested(d) {
		// 	if (d.InstanceHeaderParameter && d.InstanceHeaderParameter.length !== 0) {
		// 		instanceHeaderParameterService.markEntitiesAsModified(d.InstanceHeaderParameter);
		// 		delete d.InstanceHeaderParameter;
		// 	}
		// }
	}

	public override getModificationsFromUpdate(complete: CosMainComplete): ICosInstanceEntity[] {
		if (complete.Instance) {
			complete.Instances = [complete.Instance];
		}
		return complete.Instances ?? [];
	}

	/**
	 * If not project is pinned, then stop the procedure
	 * @param payload
	 */
	public override filter(payload: ISearchPayload): Promise<ISearchResult<ICosInstanceEntity>> {
		const projectContext = payload.pinningContext.find((e) => e.Token === PinningContextToken.Project);
		const headerContext = payload.pinningContext.find((e) => e.Token === PinningContextToken.Cos);
		const estimateContext = payload.pinningContext.find((e) => e.Token === PinningContextToken.Estimate);
		const modelContext = payload.pinningContext.find((e) => e.Token === PinningContextToken.Model);
		const boqContext = payload.pinningContext.find((e) => e.Token === PinningContextToken.Boq);

		if (!projectContext) {
			this.msgDialogService.showErrorDialog('constructionsystem.main.entryError').then();
			throw new Error('No project is pinned');
		}

		payload.projectContextId = projectContext?.Id;

		// navigation from cos instance header container
		if (headerContext) {
			// we need to clear pKeys which stores instance header ids because backend logic recognize it as instance ids.
			payload.pKeys = undefined;
		}

		this.currentSelectedProjectId = projectContext?.Id;
		this.currentInstanceHeaderId = headerContext?.Id;
		this.currentSelectedEstimateHeaderId = estimateContext?.Id;
		this.currentSelectedModelId = modelContext?.Id;
		this.currentBoqHeaderId = boqContext?.Id;

		return super.filter(payload);
	}

	public override provideLoadByFilterPayload(payload: ISearchPayload): object {
		///todo Jump from favorites case
		///if (data.searchFilter && data.searchFilter.furtherFilters && angular.isArray(data.searchFilter.furtherFilters) && data.searchFilter.furtherFilters.length === 1 && data.searchFilter.furtherFilters[0].Token === 'COS_INS_HEADER') {
		const filterRequest: IInstanceFilterRequest = {};
		filterRequest.StructuresFilters = this.constructionSystemMainFilterService.getFilters();
		filterRequest.CustomFurtherFilter = this.customFurtherFilter;
		return { ...filterRequest, ...payload };
	}

	protected override onLoadByFilterSucceeded(loaded: object): ISearchResult<ICosInstanceEntity> {
		//	InstanceHeaderProjectInfoDto
		const dto = new CosInstanceDto(loaded);
		if (dto.SystemOptions) {
			this.constructionSystemMainCommonLookupService.setSysOpts(dto.SystemOptions);
		}
		this.setModelType().then();
		//	const cosTemplates = dto.getValueAs<ICosTemplateEntity[]>('CosTemplate');
		// if (cosTemplates && cosTemplates.length > 0) {
		// 	this.constructionSystemSharedTemplateLookupService.cache.setList(cosTemplates);
		// }
		if (dto.InstanceHeaderProjectInfoDto) {
			this.setSelectedProjectInfo(dto.InstanceHeaderProjectInfoDto);
			//this.setTitleShowData(dto.InstanceHeaderProjectInfoDto);
			if (dto.IsFavoritesJump) {
				this.setCurrentInstanceHeader(dto.InstanceHeaderProjectInfoDto, true);
			}
			//	platformContextService.setPermissionObjectInfo(readData.InstanceHeaderProjectInfoDto.PermissionObjectInfo || null); //todo
		}
		const fr = dto.FilterResult!;
		return {
			FilterResult: {
				ExecutionInfo: fr.ExecutionInfo,
				RecordsFound: fr.RecordsFound,
				RecordsRetrieved: fr.RecordsRetrieved,
				ResultIds: fr.ResultIds,
			},
			dtos: dto.dtos!, //dto.dtos!,
		};
	}

	private setSelectedProjectInfo(projectEntity: InstanceHeaderProjectInfo) {
		this.selectedProjectInfo = projectEntity;
	}

	private getSelectedProjectInfo() {
		return this.selectedProjectInfo;
	}

	public getCurrentSelectedProjectId() {
		return this.currentSelectedProjectId;
	}

	public getCurrentBoqHeaderId() {
		return this.currentBoqHeaderId;
	}

	public getCurrentSelectedModelId() {
		return this.currentSelectedModelId;
	}

	public getCurrentInstanceHeaderId() {
		return this.currentInstanceHeaderId;
	}

	private setCurrentInstanceHeader(projectHeaderInfo: InstanceHeaderProjectInfo, isFavoritesJump: boolean) {
		this.currentSelectedProjectId = projectHeaderInfo.ProjectId || projectHeaderInfo.ProjectFK;
		this.currentSelectedModelId = projectHeaderInfo.ModelId || projectHeaderInfo.ModelFk;
		this.currentInstanceHeaderId = projectHeaderInfo.Id || projectHeaderInfo.HeaderId;
		this.currentSelectedEstimateHeaderId = projectHeaderInfo.EstimateHeaderId || projectHeaderInfo.EstimateHeaderFk;
		this.currentBoqHeaderId = projectHeaderInfo.BoqHeaderId || projectHeaderInfo.BoqHeaderFk;
		this.updateHeaderId();
		if (isFavoritesJump) {
			this.setPinningContext(projectHeaderInfo);
		} else if (this.currentInstanceHeaderId) {
			this.setCurrentPinningContext(this.currentInstanceHeaderId, true).then();
		}
	}

	private updateHeaderId() {
		this.constructionSystemMainInitFilterService.setEstHeaderId(this.currentSelectedEstimateHeaderId, this.currentSelectedProjectId);
		//setLeadingStructuresFilters(currentSelectedProjectId, currentBoqHeaderId); //todo structure is not ready

		this.instanceHeaderParameterService.setInstanceHeaderId(this.currentInstanceHeaderId);
		this.constructionSystemCommonPropertyNameLookupService.setCurrentModelId(this.currentSelectedModelId);
	}

	private async setCurrentPinningContext(instanceHeaderId: number, isSetCurrentContextData: boolean) {
		const instanceHeaderInfo = await this.getInstanceHeaderInfo(instanceHeaderId);
		if (isSetCurrentContextData) {
			this.currentSelectedProjectId = instanceHeaderInfo.ProjectId;
			this.currentSelectedModelId = instanceHeaderInfo.ModelId;
			this.currentInstanceHeaderId = instanceHeaderInfo.HeaderId;
			this.currentSelectedEstimateHeaderId = instanceHeaderInfo.EstimateHeaderId;
			this.currentBoqHeaderId = instanceHeaderInfo.BoqHeaderId;
			this.updateHeaderId();
		}
		this.setPinningContext(instanceHeaderInfo);
		// cloudDesktopSidebarService.filterStartSearch(true);
	}

	private concate2StringsWithDelimiter(string1: string | undefined, string2: string | undefined, delimiter: string = ' - ') {
		/// todo this should be common method
		string1 = string1 ?? '';
		string2 = string2 ?? '';
		let result = '' + string1 ? string1 : '';
		result += string1 && string2 ? delimiter : '';
		result += string2 ? string2 : '';
		return result;
	}

	private setPinningContext(instanceHeaderInfo: InstanceHeaderProjectInfo) {
		const pinningContext: IPinningContext[] = [
			{
				Id: instanceHeaderInfo.ProjectId ?? 0,
				Token: PinningContextToken.Project,
				Info: this.concate2StringsWithDelimiter(instanceHeaderInfo.ProjectNo, instanceHeaderInfo.ProjectName),
			},
			{
				Id: instanceHeaderInfo.ModelId ?? 0,
				Token: PinningContextToken.Model,
				Info: this.concate2StringsWithDelimiter(instanceHeaderInfo.EstimateHeaderCode, instanceHeaderInfo.EstimateHeaderDescription),
			},
			{
				Id: instanceHeaderInfo.HeaderId ?? 0,
				Token: PinningContextToken.Cos,
				Info: this.concate2StringsWithDelimiter(instanceHeaderInfo.HeaderCode, instanceHeaderInfo.HeaderDescription),
			},
			{
				Id: instanceHeaderInfo.BoqHeaderId ?? 0,
				Token: PinningContextToken.Boq,
				Info: '',
			},
		];
		this.platformPinningContextService.setPinningContext(pinningContext);
		// cloudDesktopPinningContextService.setContext(pinningContext, dataService); TODO
		// setTitleShowData(data);
	}

	/**
	 * request instance header info
	 * @param instanceHeaderId
	 * @private
	 */
	private async getInstanceHeaderInfo(instanceHeaderId: number) {
		return await this.http.get<InstanceHeaderProjectInfo>('constructionsystem/project/instanceheader/getprojectinfo', {
			params: {
				instanceHeaderId: instanceHeaderId,
			},
		});
	}

	public createDeepCopy() {
		if (this.isDeepCopyAllowed && this.getSelectedEntity()) {
			this.isDeepCopyAllowed = false;
			this.http.post$<CosMainComplete>('constructionsystem/main/instance/execute', this.getSelectedEntity()).subscribe((item) => {
				if (item && item.Instance) {
					this.syncCostGroups([item.Instance], [item]);
					this.append([item.Instance]);
					this.goToLast().then();
				}
				this.isDeepCopyAllowed = true;
			});
		}
	}

	/**
	 * sync cost groups data
	 * @param dtos
	 * @param completeDatas
	 */
	public syncCostGroups(dtos: ICosInstanceEntity[], completeDatas: CosMainComplete[]) {
		const syncCostGroupData: IsyncCostGroup = {
			dtos: dtos,
			Header2CostGroups: completeDatas.flatMap((item) => item.CostGroupToSave || []),
		};

		this.assignCostGroups(syncCostGroupData);
	}

	private assignCostGroups(syncCostGroupData: IsyncCostGroup) {
		// basicsCostGroupAssignmentService.process(readData, service, { ///todo
		// 	mainDataName: 'dtos',
		// 	attachDataName: 'Header2CostGroups',
		// 	dataLookupType: 'Header2CostGroups',
		// 	identityGetter: function (entity) {
		// 		return {
		// 			InstanceHeaderFk: entity.RootItemId,
		// 			Id: entity.MainItemId
		// 		};
		// 	}
	}

	public extendCustomFurtherFilters(token: string, value: string) {
		if (this.customFurtherFilter.length > 0) {
			const found = this.customFurtherFilter.find((item) => item.Token === token);
			if (found) {
				found.Value = value;
				return;
			}
		}
		this.customFurtherFilter.push({
			Token: token,
			Value: value,
		});
	}

	public getCustomFurtherFilters() {
		return this.customFurtherFilter;
	}

	/**
	 * TODO get instance header dto
	 * waiting for construction-system.project
	 */
	public async getInstanceHeaderDto() {
		if (this.instanceHeaderDto) {
			return this.instanceHeaderDto;
		}
		const selectedInstanceDto = this.getSelectedEntity();
		if (!selectedInstanceDto) {
			return null;
		}
		this.instanceHeaderDto = await this.http.get('constructionsystem/project/instanceheader/getInstanceHeaderById', {
			params: {
				cosInsHeaderId: selectedInstanceDto.InstanceHeaderFk,
			},
		});

		return this.instanceHeaderDto;
	}

	public get templateChange() {
		return this.templateChanged$;
	}

	/**
	 * TODO Waiting fro constructionSystemMasterTemplateCombobox lookup and constructionSystemMainHeaderService service
	 * @private
	 */
	private subscriptToTemplateChange() {
		this.templateChange.subscribe((templateChangeData) => {
			let isSelectStatementChanged = false;
			this.constructionSystemSharedTemplateLookupService.getItemByKeyAsync({ id: templateChangeData.templateId }).then((template) => {
				if (template) {
					templateChangeData.entity.SelectStatement = template.SelectStatement;
					isSelectStatementChanged = true;
				} else {
					const cosMaster = ServiceLocator.injector
						.get(ConstructionSystemMainHeaderListDataService)
						.getList()
						.filter((item) => (item.Id = templateChangeData.entity.HeaderFk));

					if (cosMaster.length > 0) {
						templateChangeData.entity.SelectStatement = cosMaster[0].SelectStatement;
						isSelectStatementChanged = true;
					}
				}
				if (isSelectStatementChanged) {
					this.setModified([templateChangeData.entity]);
					//service.fireSelectStatementChanged(null, args.entity); todo?? do not clear the logic now
				}
			});
		});
	}

	public async gotToEstimate() {
		if (!this.currentInstanceHeaderId) {
			return;
		}
		const instanceHeaderInfo = await this.getInstanceHeaderInfo(this.currentInstanceHeaderId);
		if (!instanceHeaderInfo.EstimateHeaderId) {
			console.error('estimateHeaderId NOT Exist! Therefore, [Go To Estimate] failed to work!!');
			return;
		}
		const items = await this.http.post<IEstimateCompositeEntity[]>('estimate/project/list', { projectFk: instanceHeaderInfo.ProjectId });
		const filteredItems = items.filter((item) => item.EstHeader.Id === instanceHeaderInfo.EstimateHeaderId);
		if (!filteredItems) {
			console.error('Filtered item not found! Therefore, [Go To Estimate] failed to work!!');
			return;
		}
		// $injector.get('estimateProjectRateBookConfigDataService').setClearDataFlag(false);
		// $injector.get('constructionSystemProjectInstanceHeaderService').setFilterByCurrentInstance(true); todo
		//filteredItems[0].projectInfo.ProjectNo = instanceHeaderInfo.ProjectNo; /// todo Is it necessary??
		//filteredItems[0].projectInfo.ProjectName = instanceHeaderInfo.ProjectName;
		///todo navigation seems not working
		await this.platformModuleNavigationService.navigate({
			internalModuleName: 'estimate.main',
			entityIdentifications: [{ id: filteredItems[0].Id }],
		});
	}

	public override takeOverUpdatedChildEntities(updated: CosMainComplete) {
		super.takeOverUpdatedChildEntities(updated);
		/// todo ModelValidateError
		if (updated.EstLineItemsToSave) {
			//$injector.get('constructionsystemMainLineItemService').gridRefresh();
		}
	}

	/**
	 * update status to modified
	 */
	public updateStatusToModified() {
		const selectedInstance = this.getSelectedEntity();
		if (selectedInstance) {
			const ignoreStatusIds = [CosInstanceStatus.New, CosInstanceStatus.Calculating, CosInstanceStatus.Calculated];
			if (selectedInstance.Id && ignoreStatusIds.indexOf(selectedInstance.Status) === -1) {
				selectedInstance.Status = CosInstanceStatus.Modified;
				this.entitiesUpdated(selectedInstance);
			}
		}
	}

	public updateIsUserModified(isModified: boolean) {
		const selectedInstance = this.getSelectedEntity();
		if (selectedInstance && selectedInstance.Id) {
			selectedInstance.IsUserModified = isModified;
			selectedInstance.Status = CosInstanceStatus.Modified; /// original is 26, but seems 26 is duplicated with 25
		}
	}

	public override async delete(entities: ICosInstanceEntity[] | ICosInstanceEntity) {
		const deleteEntities: ICosInstanceEntity[] = !Array.isArray(entities) ? [entities] : entities;
		type DeleteResponse = {
			Result: boolean;
			ValidationErrors: string[];
		};
		const deleteResponse = await this.http.post<DeleteResponse>('constructionsystem/main/instance/delete', deleteEntities);
		if (deleteResponse.Result) {
			const newList = this.getList().filter((item) => !deleteEntities.includes(item));
			this.setList(newList); /// todo this should use remove function, but it is not ready now
			return;
		}
		if (deleteResponse.ValidationErrors && deleteResponse.ValidationErrors.length > 0) {
			const modalOption = {
				headerText: 'constructionsystem.main.deleteInstanceWithLineItemDialog.title',
				bodyText: deleteResponse.ValidationErrors.join('\n'),
				defaultButtonId: StandardDialogButtonId.Yes,
			};
			const result = await this.msgDialogService.showYesNoDialog(modalOption);
			if (result?.closingButtonId === StandardDialogButtonId.Yes) {
				await this.http.post<boolean>('constructionsystem/main/instance/deleteInstanceWithLineItem', deleteEntities);
			}
		}
	}

	/**
	 * convert selected entity to Identification
	 * @param selected
	 */
	public convertSelectedToIdentification(selected: ICosInstanceEntity) {
		return this.converter.convert(selected);
	}

	public updateModuleHeaderInfo(instance?: ICosInstanceEntity) {
		const parts: string[] = [];

		const { ProjectNo, ProjectName } = this.currentProjectEntity;
		if (ProjectNo) {
			parts.push(`${ProjectNo} - ${ProjectName}`);
		}

		const header = this.currentInsHeaderEntity;
		if (header?.Code) {
			parts.push(`${header.Code} - ${header?.DescriptionInfo?.Translated}`);
		}

		const selected = this.getSelectedEntity();
		if (selected) {
			parts.push(`${selected.Code} - ${selected?.DescriptionInfo?.Translated}`);
			instance = undefined;
		}

		if (instance) {
			parts.push(`${instance.Code} - ${instance?.DescriptionInfo?.Translated}`);
		}

		// todo-allen: Waiting for cloudDesktopInfoService to be finished.
		// const entityText = parts.join(' / ');
		// cloudDesktopInfoService.updateModuleInfo('cloud.desktop.moduleDisplayNameConstructionSystemInstance', entityText);
	}

	public show2dObjectDialog() {
		this.modalDialogService.show(this.get2dObjectDialogModalOptions()).then();
	}

	private get2dObjectDialogModalOptions() {
		function checkIsModified() {
			return true;
			/// todo how to get modification?
			// var updateData = platformDataServiceModificationTrackingExtension.getModifications(parentService);
			// var hasModified = false;
			// var objectTemplateToSave = null;
			// if (Object.prototype.hasOwnProperty.call(updateData,'CosInsObjectTemplateToDelete') && updateData.CosInsObjectTemplateToDelete.length) {
			// 	hasModified = true;
			// }
			// else if (Object.prototype.hasOwnProperty.call(updateData,'CosInsObjectTemplateToSave') && updateData.CosInsObjectTemplateToSave.length) {
			// 	objectTemplateToSave = updateData.CosInsObjectTemplateToSave[0];
			// 	if ((Object.prototype.hasOwnProperty.call(objectTemplateToSave,'CosInsObjectTemplate') &&
			// 			objectTemplateToSave.CosInsObjectTemplate) ||
			// 		(Object.prototype.hasOwnProperty.call(objectTemplateToSave,'CosInsObjectTemplatePropertyToSave') &&
			// 			objectTemplateToSave.CosInsObjectTemplatePropertyToSave.length) ||
			// 		(Object.prototype.hasOwnProperty.call(objectTemplateToSave,'CosInsObjectTemplatePropertyToDelete') &&
			// 			objectTemplateToSave.CosInsObjectTemplatePropertyToDelete.length)) {
			// 		hasModified = true;
			// 	}
			// }
			// return hasModified;
		}

		return {
			backdrop: false,
			buttons: [
				{
					id: StandardDialogButtonId.Ok,
					fn: (event: MouseEvent, info: IClosingDialogButtonEventInfo<ICustomDialog<unknown, Instance2dObjectDialogComponent>, void>) => {
						if (checkIsModified()) {
							this.save().then(() => {
								const templateDataService = ServiceLocator.injector.get(ConstructionSystemMainObjectTemplateDataService);
								const currentEntity = this.getSelectedEntity();
								if (currentEntity !== null) {
									templateDataService.loadSubEntities(this.convertSelectedToIdentification(currentEntity)).then();
								}
							});
						}
						this.clearModifications(); /// todo:this cannot remove red mark
						info.dialog.close(StandardDialogButtonId.Ok);
					},
				},
				{
					id: StandardDialogButtonId.Cancel,
					caption: { key: 'ui.common.dialog.cancelBtn' },
					fn: (event: MouseEvent, info: IClosingDialogButtonEventInfo<ICustomDialog<unknown, Instance2dObjectDialogComponent>, void>) => {
						this.clearModifications();
						info.dialog.close(StandardDialogButtonId.Cancel);
					},
				},
			],
			headerText: {
				text: 'Maintain 2D Object Template & Property',
				key: 'constructionsystem.main.title2dObjectAndPropertyDialog',
			},
			id: 'Maintain2DObjectTemplate',
			bodyComponent: Instance2dObjectDialogComponent,
		};
	}

	public async setModelType() {
		const modelId = this.currentSelectedModelId;
		if (modelId) {
			const is2DModel = await this.is2d(modelId);
			const modelInfoResponse = await this.http.get<{ IsComposite: boolean }>('model/project/model/getbyid', {
				params: {
					id: modelId,
				},
			});
			this.is2DModel = is2DModel || (modelInfoResponse && modelInfoResponse.IsComposite);
		}
		return this.is2DModel;
	}

	private async is2d(modelId: number) {
		const url = `model/wdeviewer/info/is2d?modelId=${modelId}`;

		// Check if the response is cached
		if (this.requestCache.has(url)) {
			return this.requestCache.get(url)!;
		}

		// Make the HTTP request and cache the response
		const response = await this.http.get<boolean>(url);
		this.requestCache.set(url, response);
		return response;
	}
}

///todo sidebarSearchOptions
///todo showProjectHeader
/// todo setPinningContext
/// todo dynamic column
/// todo syncModelViewWithCheckedInstances
/// todo sync3DViewerIfSelectedIsChecked
/// todo handleUpdateDone ModelValidateError

/// delete
/// addSortCodeChangeInfo is useless
