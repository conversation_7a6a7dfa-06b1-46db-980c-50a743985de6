/*
 * Copyright(c) RIB Software GmbH
 */

import { ITranslationEntity } from '@libs/cloud/interfaces';
import { CompleteIdentification } from '@libs/platform/common';

/**
 * Cloud Translation Translation Complete
 */
export class CloudTranslationTranslationComplete implements CompleteIdentification<ITranslationEntity> {
	/**
	 * Id
	 */
	public Id: number = 0;

	/**
	 * Translation Entity Data
	 */
	public Datas: ITranslationEntity[] | null = [];
}
