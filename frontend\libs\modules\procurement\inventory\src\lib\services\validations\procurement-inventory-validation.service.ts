/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import {
	ValidationInfo,
	ValidationResult,
	IValidationFunctions,
	IEntityRuntimeDataRegistry
} from '@libs/platform/data-access';
import { ProcurementBaseValidationService } from '@libs/procurement/shared';
import { BasicsSharedMaterialLookupService } from '@libs/basics/shared';
import { ServiceLocator } from '@libs/platform/common';
import { IPrcInventoryEntity } from '../../model/entities/prc-inventory-entity.interface';
import { ProcurementInventoryGridDataService } from '../procurement-inventory-grid-data.service';
import { ProcurementInventoryHeaderDataService } from '../procurement-inventory-header-data.service';
import { IPrcInventoryHeaderEntity } from '../../model/models';

/**
 * Type definition for Material2Uoms array structure
 */
export type Material2UomItem = {
	UomFk: number;
	Quantity: number;
};

/**
 * Extended entity interface that includes Material2Uoms property
 */
export type IPrcInventoryEntityWithMaterial2Uoms = IPrcInventoryEntity & {
	Material2Uoms: Material2UomItem[] | null;
};

/**
 * Interface for material data returned from material lookup service
 */
interface IMaterialData {
	Id: number;
	MdcMaterialCatalogFk?: number | null;
	BasUomFk: number;
	Material2Uoms?: Material2UomItem[] | null;
	DescriptionInfo2?: {
		Translated?: string | null;
	} | null;
	MaterialStockFk?: number | null;
	MaterialStock2UomFk?: number | null;
	Cost?: number;
	PriceUnit?: number;
	FactorPriceUnit?: number;
	BasCurrencyFk?: number;
}

/**
 * Procurement Inventory validation service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementInventoryValidationService extends ProcurementBaseValidationService<IPrcInventoryEntity> {

	private readonly materialLookupService = inject(BasicsSharedMaterialLookupService);
	private dataService?: ProcurementInventoryGridDataService;
	private headerDataService?: ProcurementInventoryHeaderDataService;

	public constructor() {
		super();
	}

	protected generateValidationFunctions(): IValidationFunctions<IPrcInventoryEntity> {
		return {
			MdcMaterialFk: this.validateMdcMaterialFk,
			ActualQuantity: this.validateActualQuantity,
			Price: this.validatePrice,
			RecordedQuantity: this.validateRecordedQuantity,
			RecordedUomFk: this.validateRecordedUomFk,
			BasUomFk: this.validateBasUomFk,
			ClerkFk1: this.validateClerkFk1,
			ClerkFk2: this.validateClerkFk2,
			Quantity1: this.validateQuantity1,
			Quantity2: this.validateQuantity2
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrcInventoryEntity> {
		// Use ServiceLocator to avoid circular dependency
		if (!this.dataService) {
			this.dataService = ServiceLocator.injector.get(ProcurementInventoryGridDataService);
		}
		return this.dataService;
	}

	/**
	 * Gets the data service, ensuring it's initialized
	 */
	private getDataService(): ProcurementInventoryGridDataService {
		if (!this.dataService) {
			this.dataService = ServiceLocator.injector.get(ProcurementInventoryGridDataService);
		}
		return this.dataService;
	}

	/**
	 * Validates material selection and handles duplicate materials
	 */
	private async validateMdcMaterialFk(info: ValidationInfo<IPrcInventoryEntity>): Promise<ValidationResult> {
		const { entity, value, field } = info;
		const fieldName = field === 'MdcMaterialFk'
			? this.translationService.instant('procurement.inventory.mdcmaterialfk').toString()
			: 'Material';

		// Check if mandatory
		const mandatoryResult = this.validateIsRequired(info);
		if (!mandatoryResult.valid) {
			return mandatoryResult;
		}

		// Check for zero or null value
		if (!value || value === 0) {
			(entity as IPrcInventoryEntityWithMaterial2Uoms).Material2Uoms = null;
			entity.Description2 = null;
			return new ValidationResult(
				this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName }).toString()
			);
		}

		// Check for duplicate materials
		const items = this.getDataService().getList().filter(item => item.Id !== entity.Id);
		const existingItem = items.find(item => item.MdcMaterialFk === value);
		if (existingItem) {
			return new ValidationResult(
				this.translationService.instant('procurement.inventory.duplicatematerailmsg', { fieldName }).toString()
			);
		}

		// Load material data and update entity
		if (value && (value as number) > 0) {
			try {
				const material = await firstValueFrom(
					this.materialLookupService.getItemByKey({ id: value as number })
				);

				if (material) {
					await this.updateEntityWithMaterialData(entity, material, value as number);
				}
			} catch (error) {
				return new ValidationResult(
					this.translationService.instant('procurement.inventory.materialLoadError', { fieldName }).toString()
				);
			}
		}

		return new ValidationResult();
	}

	/**
	 * Updates entity with material data and calculates related values
	 */
	private async updateEntityWithMaterialData(
		entity: IPrcInventoryEntity,
		material: IMaterialData,
		materialFk: number
	): Promise<void> {
		if (!this.headerDataService) {
			this.headerDataService = ServiceLocator.injector.get(ProcurementInventoryHeaderDataService);
		}

		const selectedHeader = this.headerDataService.getSelectedEntity();
		if (!selectedHeader) {
			throw new Error('Should have selected parent entity');
		}

		// Update entity with material data
		entity.MdcMaterialFk = materialFk;
		entity.CatalogFk = material.MdcMaterialCatalogFk;
		entity.BasUomFk = entity.RecordedUomFk = material.BasUomFk;
		(entity as IPrcInventoryEntityWithMaterial2Uoms).Material2Uoms = material.Material2Uoms;

		// Set Description2 from material's DescriptionInfo2 (Further Description)
		if (material.DescriptionInfo2 && material.DescriptionInfo2.Translated) {
			entity.Description2 = material.DescriptionInfo2.Translated;
		} else {
			entity.Description2 = null;
		}

		// Validate UOM fields
		this.validateBasUomFk({ entity, value: entity.BasUomFk ?? undefined, field: 'BasUomFk' });
		this.validateRecordedUomFk({ entity, value: entity.RecordedUomFk ?? undefined, field: 'RecordedUomFk' });

		// Generate inventory data by material
		try {
			const response = await this.http.get<IPrcInventoryEntity>(
				`procurement/inventory/generatebymaterial?prjStockFk=${selectedHeader.PrjStockFk}&materialFk=${materialFk}`
			);

			if (response) {
				this.updateEntityWithGeneratedData(entity, response, material, selectedHeader);
			}
		} catch (error) {
			console.error('Error generating inventory data:', error);
		}
	}

	/**
	 * Updates entity with generated inventory data
	 */
	private updateEntityWithGeneratedData(
		entity: IPrcInventoryEntity,
		data: IPrcInventoryEntity | null,
		material: IMaterialData,
		selectedHeader: IPrcInventoryHeaderEntity
	): void {
		if (data) {
			// Update with existing stock data
			entity.PrjStockLocationFk = data.PrjStockLocationFk;
			entity.LotNo = data.LotNo;
			entity.ExpirationDate = data.ExpirationDate;
			entity.PpsProductFk = data.PpsProductFk;
			entity.StockQuantity = data.StockQuantity;
			entity.StockTotal = data.StockTotal;
			entity.StockProvisionTotal = data.StockProvisionTotal;
			entity.RecordedQuantity = entity.ActualQuantity = data.ActualQuantity;
			entity.ActualTotal = data.ActualTotal;
			entity.ActualProvisionTotal = data.ActualProvisionTotal;
		} else {
			// Calculate price using exchange rate
			this.calculatePriceWithExchangeRate(entity, material, selectedHeader);
		}

		this.getDataService().setModified(entity);
	}

	/**
	 * Calculates price with exchange rate
	 */
	private async calculatePriceWithExchangeRate(
		entity: IPrcInventoryEntity,
		material: IMaterialData,
		selectedHeader: IPrcInventoryHeaderEntity
	): Promise<void> {
		try {
			const rate = await this.getForeignToDocExchangeRate(
				selectedHeader.StockCurrencyFk ?? 0,
				material.BasCurrencyFk ?? 0,
				selectedHeader.StockProjectFk ?? 0
			);

			if (material && rate) {
				const cost = material.Cost ?? 0;
				const priceUnit = material.PriceUnit ?? 1;
				const factorPriceUnit = material.FactorPriceUnit ?? 1;
				const price = cost / (priceUnit * factorPriceUnit) / rate;
				entity.Price = price;
				entity.ActualTotal = price * (entity.ActualQuantity || 0);

				this.getDataService().setModified(entity);
			}
		} catch (error) {
			console.error('Error calculating price with exchange rate:', error);
		}
	}

	/**
	 * Gets foreign to document exchange rate
	 */
	private async getForeignToDocExchangeRate(
		documentCurrencyFk: number,
		currencyForeignFk: number,
		projectFk: number
	): Promise<number> {
		if (currencyForeignFk === documentCurrencyFk) {
			return 1;
		}

		try {
			const response = await this.http.get('procurement/common/exchangerate/ocrate', {
				params: {
					CurrencyForeignFk: currencyForeignFk.toString(),
					DocumentCurrencyFk: documentCurrencyFk.toString(),
					ProjectFk: projectFk.toString()
				}
			});
			return (response as { data: number }).data;
		} catch (error) {
			console.error('Error getting exchange rate:', error);
			return 1;
		}
	}

	/**
	 * Validates base UOM
	 */
	private validateBasUomFk(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value, field } = info;

		if (value === undefined || value === null || value === -1) {
			const uom = this.translationService.instant('cloud.common.entityUoM');
			const errorMsg = this.translationService.instant('cloud.common.emptyOrNullValueErrorMessage', { fieldName: uom });
			return new ValidationResult(errorMsg.toString());
		}

		// Set readonly state based on material selection
		const readOnly = !!entity.MdcMaterialFk;
		this.getDataService().setEntityReadOnlyFields(entity, [{ field, readOnly }]);

		return new ValidationResult();
	}

	/**
	 * Validates actual quantity
	 */
	private validateActualQuantity(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if ((value as number) < 0) {
			const errorMsg = this.translationService.instant('procurement.inventory.quantityNoNegative');
			return new ValidationResult(errorMsg.toString());
		}

		// Handle stock update warning
		if (entity.IsFromExistStock) {
			const errorMsg = this.translationService.instant('procurement.inventory.updateactualwarnning');
			const warningResult = new ValidationResult(errorMsg.toString());
			// Apply validation result to ActualTotal field
			this.getDataService().addInvalid(entity, {
				field: 'ActualTotal' as keyof IPrcInventoryEntity,
				result: warningResult
			});
		} else {
			entity.ActualTotal = (entity.Price || 0) * (value as number);
			if (value === 0) {
				entity.ActualProvisionTotal = 0;
			}
			this.getDataService().setModified(entity);
		}

		// Convert quantity
		this.conversionQuantity(entity, entity.RecordedUomFk, value as number, null);
		
		return new ValidationResult();
	}

	/**
	 * Validates price
	 */
	private validatePrice(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if (!entity.IsFromExistStock) {
			entity.ActualTotal = (value as number) * (entity.ActualQuantity || 0);
			this.getDataService().setModified(entity);
		}

		return new ValidationResult();
	}

	/**
	 * Validates recorded quantity
	 */
	private validateRecordedQuantity(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;
		this.conversionQuantity(entity, entity.RecordedUomFk, null, value as number);
		return new ValidationResult();
	}

	/**
	 * Validates recorded UOM
	 */
	private validateRecordedUomFk(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value, field } = info;

		const entityWithMaterial2Uoms = entity as IPrcInventoryEntityWithMaterial2Uoms;
		const readOnly = (!!entity.MdcMaterialFk && !entityWithMaterial2Uoms.Material2Uoms);
		this.getDataService().setEntityReadOnlyFields(entity, [
			{ field, readOnly },
			{ field: 'RecordedQuantity', readOnly }
		]);

		this.conversionQuantity(entity, value as number, entity.ActualQuantity ?? null, null);
		return new ValidationResult();
	}

	/**
	 * Validates clerk 1
	 */
	private validateClerkFk1(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		const readonly = value === null;
		if (readonly) {
			entity.Quantity1 = null;
			entity.ClerkFk1 = null;
			this.validateQuantity1({ entity, value: entity.Quantity1 ?? undefined, field: 'Quantity1' });
		}

		this.getDataService().setEntityReadOnlyFields(entity, [{ field: 'Quantity1', readOnly: readonly }]);
		return new ValidationResult();
	}

	/**
	 * Validates clerk 2
	 */
	private validateClerkFk2(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		const readonly = value === null;
		if (readonly) {
			entity.Quantity2 = null;
			entity.ClerkFk2 = null;
			this.validateQuantity2({ entity, value: entity.Quantity2 ?? undefined, field: 'Quantity2' });
		}

		this.getDataService().setEntityReadOnlyFields(entity, [{ field: 'Quantity2', readOnly: readonly }]);
		return new ValidationResult();
	}

	/**
	 * Validates quantity 1
	 */
	private validateQuantity1(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if (entity.ClerkFk2 === null) {
			if (entity.ClerkFk1 !== null) {
				entity.ActualQuantity = value as number;
			}
		} else {
			if (value !== null) {
				if (entity.Quantity2 !== null && value !== entity.Quantity2) {
					entity.ActualQuantity = null;
				} else {
					entity.ActualQuantity = value as number;
				}
			} else {
				if (value !== entity.Quantity2) {
					entity.ActualQuantity = entity.Quantity2;
				}
			}
		}

		entity.Quantity1 = value as number;
		this.checkDifferenceClerkQuantity(entity);
		this.getDataService().setModified(entity);

		return new ValidationResult();
	}

	/**
	 * Validates quantity 2
	 */
	private validateQuantity2(info: ValidationInfo<IPrcInventoryEntity>): ValidationResult {
		const { entity, value } = info;

		if (entity.ClerkFk1 === null) {
			if (entity.ClerkFk2 !== null) {
				entity.ActualQuantity = value as number;
			}
		} else {
			if (value !== null) {
				if (entity.Quantity1 !== null && value !== entity.Quantity1) {
					entity.ActualQuantity = null;
				} else {
					entity.ActualQuantity = value as number;
				}
			} else {
				if (value !== entity.Quantity1) {
					entity.ActualQuantity = entity.Quantity1;
				}
			}
		}

		entity.Quantity2 = value as number;
		this.checkDifferenceClerkQuantity(entity);
		this.getDataService().setModified(entity);

		return new ValidationResult();
	}

	/**
	 * Checks difference between clerk quantities
	 */
	private checkDifferenceClerkQuantity(entity: IPrcInventoryEntity): void {
		if (entity.Quantity1 !== null || entity.Quantity2 !== null) {
			if (entity.Quantity1 !== null && entity.Quantity2 !== null) {
				entity.DifferenceClerkQuantity = (entity.Quantity1 || 0) - (entity.Quantity2 || 0);
			} else {
				if (entity.Quantity1 !== null) {
					entity.DifferenceClerkQuantity = entity.Quantity1;
				} else {
					entity.DifferenceClerkQuantity = entity.Quantity2;
				}
			}
		} else {
			entity.DifferenceClerkQuantity = null;
		}
	}

	/**
	 * Converts quantity between different UOMs
	 */
	private conversionQuantity(
		entity: IPrcInventoryEntity,
		uom: number | null | undefined,
		quantity: number | null,
		recordedQuantity: number | null
	): void {
		let value = 1;

		const entityWithMaterial2Uoms = entity as IPrcInventoryEntityWithMaterial2Uoms;
		if (entityWithMaterial2Uoms.Material2Uoms && uom) {
			const uomItem = entityWithMaterial2Uoms.Material2Uoms.find((item) => item.UomFk === uom);
			if (uomItem) {
				value = uomItem.Quantity;
			}
		}

		if (quantity !== null && quantity !== undefined) {
			entity.RecordedQuantity = quantity * value;
		}

		if (recordedQuantity !== null && recordedQuantity !== undefined) {
			entity.ActualQuantity = value === 0 ? 0 : recordedQuantity / value;
		}
	}

}
