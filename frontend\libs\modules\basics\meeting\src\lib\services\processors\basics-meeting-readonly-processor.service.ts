/*
 * Copyright(c) RIB Software GmbH
 */
import { EntityReadonlyProcessorBase, ReadonlyFunctions } from '@libs/basics/shared';
import { IMtgHeaderEntity } from '@libs/basics/interfaces';
import { BasicsMeetingDataService } from '../basics-meeting-data.service';

/**
 * Basics Meeting readonly processor
 */
export class BasicsMeetingReadonlyProcessorService extends EntityReadonlyProcessorBase<IMtgHeaderEntity> {
	/**
	 *The constructor
	 */
	public constructor(protected dataService: BasicsMeetingDataService) {
		super(dataService);
	}

	public override generateReadonlyFunctions(): ReadonlyFunctions<IMtgHeaderEntity> {
		return {
			Code: () => {
				return true;
			},
			Recurrence: () => {
				return true;
			},
		};
	}

	protected override readonlyEntity(item: IMtgHeaderEntity): boolean {
		return this.dataService.isStatusReadonly(item);
	}
}
