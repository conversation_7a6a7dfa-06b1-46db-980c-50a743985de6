

import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo } from '@libs/platform/data-access';
import { IPpsItem2MdcMaterialEntity } from '../../model/models';
import { Injectable } from '@angular/core';
import { PpsUpstream2MaterialService } from './pps-upstream-2-material.service';

@Injectable({
	providedIn: 'root'
})
export class PpsUpstream2MaterialValidationService extends BaseValidationService<IPpsItem2MdcMaterialEntity> {

	public constructor(private dataService: PpsUpstream2MaterialService) {
		super();
	}

	protected override generateValidationFunctions(): IValidationFunctions<IPpsItem2MdcMaterialEntity> {
		return {
			MdcMaterialFk: [this.validateIsMandatory,this.validateIsUnique],
		};
	}

	protected override getLoadedEntitiesForUniqueComparison = (info: ValidationInfo<IPpsItem2MdcMaterialEntity>): IPpsItem2MdcMaterialEntity[] => {
		const itemList = this.dataService.getList();
		return itemList.filter((item) => {
			return (item as never)[info.field] === info.value && item.Id !== info.entity.Id;
		});
	};

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPpsItem2MdcMaterialEntity> {
		return this.dataService;
	}
}
