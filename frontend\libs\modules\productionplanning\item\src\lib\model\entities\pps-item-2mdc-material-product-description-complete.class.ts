import { CompleteIdentification } from '@libs/platform/common';
import { IPpsItem2MdcMaterialProductDescriptionEntity } from './pps-item-2mdc-material-product-description-entity.interface';
import { IPPSDescriptionParamEntity } from './pps-description-param-entity.interface';


export class PpsItem2MdcMaterialProductDescriptionComplete extends CompleteIdentification<PpsItem2MdcMaterialProductDescriptionComplete> {

	public MainItemId!: number;
	public MdcProductDescription!: IPpsItem2MdcMaterialProductDescriptionEntity;

	public MdcProductDescParamToSave?: IPPSDescriptionParamEntity[];

	public MdcProductDescParamToDelete?: IPPSDescriptionParamEntity[];
}