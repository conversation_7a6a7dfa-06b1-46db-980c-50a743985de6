<style>
	.boq-comparison .platform-form-container {
		min-width: 200px;
	}

	.boq-comparison .difference {
		background-color: yellow;
	}
</style>
<div class="flex-box flex-element flex-column fullwidth fullheight boq-comparison">
	<header class="modal-header main-color" data-ng-include="'modaldialog/modaldialog-header-template.html'"></header>
	<section class="modal-body">
		<div class="flex-element flex-basis-auto">
			<div data-platform-kendo-splitter-adjust class="flex-element filler border-none" option="{panes: [ { collapsible: true, size: '25%' },{ collapsible: true, size: '75%' }],orientation:'horizontal'}">
				<div id="ui-layout-east" class="flex-box flex-column">
					<div class="subview-container flex-element flex-box flex-column">
						<div class="toolbar">
							<h3 class="title font-bold">{{'boq.main.comparison.settings' | translate}}</h3>
						</div>
						<div class="flex-element flex-box flex-column subview-content">
							<div data-platform-form-container data-form-container-options="containerOptions" data-entity="entity"></div>
						</div>
					</div>
				</div>
				<div id="ui-layout-west" class="flex-box flex-column">
					<div class="subview-container flex-element flex-box flex-column fullheight">
						<div class="toolbar">
							<h3 class="title font-bold">{{'boq.main.comparison.boqStructure' | translate}}</h3>
							<div data-platform-menu-list data-list="tools"></div>
						</div>
						<div class="flex-element flex-box flex-column subview-content overflow-hidden">
							<platform-Grid data="gridData"></platform-Grid>
						</div>
					</div>
				</div>

			</div>
		</div>
	</section>
	<footer class="modal-footer">
		<button type="button" class="btn btn-default" data-ng-click="modalOptions.ok()"> {{'cloud.common.ok' | translate}}</button>
	</footer>
	<div cloud-common-overlay data-loading="loading"></div>
</div>