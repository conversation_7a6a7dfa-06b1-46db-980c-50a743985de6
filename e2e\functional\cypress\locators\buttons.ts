namespace Buttons {
	export enum ToolBar {
		ICO_REC_NEW = 'ico-rec-new',
		ICO_GROUP_COLUMNS = 'ico-group-columns',
		ICO_SEARCH_ALL = 'ico-search-all',
		ICO_SEARCH_COLUMN = 'ico-search-column',
		ICO_SETTINGS = 'ico-settings',
		ICO_MAXIMIZED_2 = 'ico-maximized2',
		ICO_MINIMIZED_2 = 'ico-minimized2',
		ICO_CONSTRUCTION_51 = 'ico-construction51',
		ICO_CLIPBOARD = 'ico-clipboard',
		ICO_FILTER = 'ico-filter',
		ICO_SELECTION = 'ico-selection',
		ICO_LINE_ITEM_FILTER = 'ico-line-item-filter',
		ICO_RECALCULATE = 'ico-recalculate',
		ICO_COPY_PASTE_DEEP = 'ico-copy-paste-deep',
		ICO_FILTER_BY_MODAL = 'ico-filter-by-model',
		ICO_SET_PRJ_CONTEXT = 'ico-set-prj-context',
		ICO_UPLOAD_CREATE = 'ico-upload-create',
		ICO_DOWNLOAD = 'ico-download',
		ICO_PREVIEW_FORM = 'ico-preview-form',
		ICO_CONTAINER_CONFIG = 'ico-container-config',
		ICO_BD_NEW = 'ico-db-new',
		ICO_DB_DELETE = 'ico-db-delete',
		ICO_NEW = 'ico-new',
		ICO_FLD_INS_BELOW = 'ico-fld-ins-below',
		ICO_CUT = 'ico-cut',
		ICO_COPY = 'ico-copy',
		ICO_COPY_LINE_ITEM = 'ico-copy-line-item',
		ICO_COPY_LINE_ITEM_REF = 'ico-copy-line-item-ref',
		ICO_PASTE = 'ico-paste',
		ICO_COPY_SETTINGS = 'ico-copy-settings',
		ICO_SUB_VB_VIEW = 'ico-sub-vb-new',
		ICO_WILDCARD_1 = 'ico-wildcard-1',
		ICO_UPLOAD = 'ico-upload',
		ICO_UPLOAD_CANCEL = 'ico-upload-cancel',
		ICO_DRA = 'ico-dra',
		ICO_RESET = 'ico-reset',
		ICO_PREVIEW_DATA = 'ico-preview-data',
		ICO_VIEW_SELECT = 'ico-view-select',
		ICO_RULE_EDITOR_CATCH_ALL = 'ico-rule-ediTor-catchall',
		ICO_RULE_EDITOR_ENHANCED = 'ico-rule-ediTor-enhanced',
		ICO_DELETE = 'ico-delete',
		ICO_LOCAL_VARIANT = 'ico-lOcal-variant',
		ICO_LOCAL_VARIANT_DISCARD = 'ico-lOcal-variant-discard',
		ICO_VIEW_ODS = 'ico-view-ods',
		ICO_VIEW_CAM_POS_DEL = 'ico-view-cam-pos-del',
		ICO_OPEN_1 = 'ico-open1',
		ICO_PLAY = 'ico-play',
		ICO_BREAK = 'ico-break',
		ICO_STOP = 'ico-stop',
		ICO_SLOWER = 'ico-slow',
		ICO_FAST = 'ico-fast',
		ICO_WILDCARD_2 = 'ico-wildcard-2',
		ICO_VIEW_CAM_POS = 'ico-view-cam-pos',
		ICO_VIEW_CAM_POS_ADD = 'ico-view-cam-pos-add',
		ICO_ZOOM_IN = 'ico-zoom-in',
		ICO_ZOOM_OUT = 'ico-zoom-out',
		ICO_ZOOM_FIT = 'ico-zoom-fit',
		ICO_REFRESH = 'ico-refresh',
		ICO_VIEW_SWITCH_INSIDE = 'ico-view-switch-inside',
		ICO_VIEW_SWITCH_OUTSIDE = 'ico-view-switch-outside',
		ICO_MENU_2 = 'ico-menu2',
		ICO_REFRESH_ALL = 'ico-refresh-all',
		ICO_ZOOM = 'ico-zoom',
		ICO_PIC_CHANGE = 'ico-pic-change',
		ICO_ADD_CUSTOMER_COMPANY = 'ico-add-customer-company',
		ICO_TEMPLATE_CONFIG = 'ico-template-config',
		ICO_UNDO = 'ico-undo',
		ICO_REDO = 'ico-redo',
		ICO_RENAME_VARIABLE = 'ico-rename-variable',
		ICO_JUMP_BACK = 'ico-jump-back',
		ICO_JUMP_TO_DEF = 'ico-jump-to-def',
		ICO_SHOW_TYPE_DOC = 'ico-show-type-doc',
		ICO_CODE_FOLD = 'ico-code-fold',
		ICO_CODE_UNFOLD = 'ico-code-unfold',
		ICO_TOGGLE_COMMENT = 'ico-toggle-comment',
		ICO_CODE_INDENT = 'ico-code-indent',
		ICO_CODE_FORMAT = 'ico-code-format',
		ICO_FIND = 'ico-find',
		ICO_FIND_CLEAR = 'ico-find-clear',
		ICO_FIND_PREVIOUS = 'ico-find-previous',
		ICO_FIND_NEXT = 'ico-find-next',
		ICO_REPLACE = 'ico-replace',
		ICO_REPLACE_ALL = 'ico-replace-all',
		ICO_JUMP_TO_LINE = 'ico-jump-to-line',
		ICO_INFO = 'ico-info',
		ICO_ERROR = 'ico-error',
		ICO_WARNING = 'ico-warning',
		ICO_RESULT_CALCULATION_SHOW = 'ico-result-calculation-show',
		ICO_TREE_EXPAND = 'ico-tree-expand',
		ICO_TREE_LEVEL_EXPAND = 'ico-tree-level-expand',
		ICO_TREE_COLLAPSE_ALL = 'ico-tree-collapse-all',
		ICO_TREE_EXPAND_ALL = 'ico-tree-expand-all',
		ICO_PRINT_PREVIEW = 'ico-print-preview',
		ICO_SDB_SEARCH_1 = 'ico-sdb-search1',
		ICO_SDB_SEARCH_2 = 'ico-sdb-search2',
		ICO_CRITERIA_SEARCH = 'ico-criteria-search',
		ICO_SDB_SEARCH_3 = 'ico-sdb-search3',
		ICO_INSTANCE_CALCULATE = 'ico-instance-calculate',
		CURRENT_VERSION_NG_PRISTINE = 'current-version ng-pristine',
		ICO_TOTAL = 'ico-total',
		ICO_GO_TO = 'ico-goto',
		ICO_PRICE_COPY_BOQ = 'ico-price-copy-boq',
		ACTIONS = 'actions ',
		ICO_TREE_LEVEL_COLLAPSE = 'ico-tree-level-collapse',
		ICO_PRICE_ADJUSTMENT = 'ico-price-adjustment',
		ICO_ESTIMATE_VERSION_CREATE = 'ico-estimate-version-create',
		REC_NEW_COPY = 'rec-new-copy',
		ICO_FILTER_CURRENT_VERSION = 'ico-filter-current-version',
		ICO_ESTIMATE_VERSION_RESTORE = 'ico-estimate-version-restore',
		ICO_REC_NEW_COPY = 'ico-rec-new-copy',
		ICO_COPY_ACTION_1_2 = 'ico-copy-action1-2',
		ICO_ON_OFF_ZERO = 'ico-on-off-zero',
		ICO_REC_NEW_DEEP_COPY = 'ico-rec-new-deepcopy',
		ICO_TOTAL_GRAND = 'ico-total-grand',
		ICO_CLOSE = 'ico-close',
		ICO_PROMOTE = 'ico-promote',
		ICO_DEMOTE = 'ico-demote',
		ICO_FILTER_BOQ = 'ico-filter-boq',
		ICO_FILTER_WIC_BOQ = 'ico-filter-wic-boq',
		ICO_FILTER_ASSEMBLY = 'ico-filter-assembly',
		ICO_LINK_ACTIVITIES_RELATIONSHIP = 'ico-link-activities-relationship',
		ICO_EMP_REPORT_SHOW_INACTIVE = 'ico-emp-report-show-inactive',
		SELECT_ITEM_VARIENT = 'ico-select-item-variant',
		ICO_SELECT_BOQ_VARIANT = 'ico-select-boq-variant',
		ICO_FILTER_OFF = 'ico-filter-off',
		ICO_COPY_LINE_ITEM_QUANTITY = 'ico-copy-line-item-quantity',
		ICO_PRINT = 'ico-print',
		ICO_WATCHLIST_ADD = 'ico-watchlist-add',
		ICO_NEW_CHANGE_ORDER = 'ico-new-change-order',
		ICO_NEW_CALL_OFF = 'ico-new-call-off',
		ICO_ESTIMATE = 'ico-estimate',
		ICO_AUTO_REFRESH = 'ico-auto-refresh',
		ACTION_BTN = 'action-btn',
		ICO_REC = 'ico-rec',
		ICO_EXCEED = 'ico-exceed',
		ICO_FILTER_ASSIGNED = 'ico-filter-assigned',
		ICO_FILTER_ASSIGNED_AND_NOT_ASSIGNED = 'ico-filter-assigned-and-notassigned',
		ICO_FILTER_NOT_ASSIGNED = 'ico-filter-notassigned',
		COPY_CONTRACT_AND_NON_CONTRACT_ITEM="Copy Contract & Non Contract Items",
		ICO_SEARCH= 'ico-search',
		ICO_UPDATE_ESTIMATE_FROM_BOQ="ico-update-estimate-from-boq",
		ICO_LINE_ITEM_FILTER_OFF= 'ico-line-item-filter-off',
		ICO_LINE_ITEM_NOT_LINKED= 'ico-line-item-not-linked',
		ICO_SELECTION_MULTI="ico-selection-multi",
		CONTROL_ICONS_ICO_INPUT_DELETE="control-icons ico-input-delete",
		ICO_RES_SHOW_ALL="ico-res-show-all"
	}

	export enum GridButtons {
		CARET = 'caret',
		ICO_GRID_ROW_START = 'ico-grid-row-start',
		UI_STRUCTURE_MENU="ui-structure-menu",
		EXPAND_NODE="expandnode",
		LEVEL_1="level1",
		LEVEL_2="level2",
		LEVEL_3="level3",
		DELETE_ITEM="deleteItem"
	}

	export enum IconButtons {
		ICO_COST_CODE = 'ico-cost-code',
		CARET = 'caret',
		ICO_INPUT_DELETE = 'ico-input-delete',
		ICO_INPUT_LOOKUP = 'ico-input-lookup',
		ICO_INPUT_ADD = 'ico-input-add',
		BTN_DEFAULT_TLB_ICONS_ICO_SEARCH = 'btn-default tlb-icons ico-search',
		BTN_DEFAULT_CONTROL_ICONS_ICO_MENU = 'btn-default control-icons ico-menu',
		ICO_SOURCE_LINE = 'ico-source-line',
		ICO_REFERENCE_LINE = 'ico-reference-line',
		ICO_MENU = 'ico-menu',
		GRID_CELL_ICO = 'gridcell-ico',
		ICO_SETTING_DOC = 'ico-settings-doc',
		ICO_DISCARD = 'ico-discard',
		ICO_CART = 'ico-cart',
		ICO_CART_ITEM_DELETE = 'ico-cart-item-delete',
		ICO_GO_TO = 'ico-goto',
		BTN_DEFAULT = 'btn-default',
		ICO_COMPANY = 'ico-company',
		ICO_MERGE = 'ico-merge',
		ICO_DOWN = 'ico-down',
		ICO_SEARCH = 'ico-search',
		LG_9_TEXT_RIGHT = 'lg-9 text-right',
		ICO_APPEND = 'ico-append',
		ICO_REC_DELETE = 'ico-rec-delete',
		ICO_CRITERION_NEW = 'ico-criterion-new',
		ICO_FLD_INS_BELOW = 'ico-fld-ins-below',
		ICO_SUB_FLD_NEW = 'ico-sub-fld-new',
		ICO_TREE_COLLAPSE = 'ico-tree-collapse',
		ICO_NEXT = 'ico-next',
		LOOKUP_ICO_DIALOG = 'lookup-ico-dialog',
		PREVIEW_BACK = 'preview-back',
		BTN_SUCCESS = 'btn-success',
		ICO_BOQ_ITEM_NEW = 'ico-boq-item-new',
		ICO_BOQ_ITEM = 'ico-boq-item',
		ICO_STATUS_16 = 'ico-status16',
		ICO_VIEW_ODS = 'ico-view-ods',
		SEARCH_BUTTON = 'search-button',
		BTN_BTN_DEFAULT_DROPDOWN_TOGGLE = 'btn btn-default dropdown-toggle',
		ICO_ACCORDION_ROOT = 'ico-accordion-root',
		ICO_INDICATOR_SEARCH = 'ico-indicator-search',
		CONTROL_ICONS = 'control-icons',
		ICO_TICK = 'ico-tick',
		APP_SMALL_ICONS = 'app-small-icons',
		OK_BTN_BTN_RETURN = 'ok btn btn-return',
		ICO_UP = 'ico-up',
		ICO_MATERIALS = 'ico-materials',
		ICO_SCHEDULING = 'ico-scheduling',
		ICO_REC_LAST = 'ico-rec-last',
		ICO_CLOSE = 'ico-close',
		ICO_TASK_TO_HAMMOCK = 'ico-task-to-hammock',
		ICO_PACKAGE = 'ico-package',
		BLOCK_IMAGE = 'block-image',
		ICO_BUSINESS_PARTNER = 'ico-business-partner',
		ICO_RUBRIC_PES = 'ico-rubric-pes',
		ICO_PRICE_UPDATE = 'ico-price-update',
		ICO_SELECTION_MULTI = 'ico-selection-multi',
		ICO_FILTER_BASED_JOB = 'ico-filter-based-job',
		ICO_FILTER_BASED_ESTIMATE = 'ico-filter-based-estimate',
		ICO_ESTIMATE = 'ico-estimate',
		ICO_PROCUREMENT_STRUCTURE = 'ico-procurement-structure',
		ICO_WORKFLOW_RUN = 'ico-workflow-run',
		YES_BTN_BTN_RETURN = 'yes btn btn-return',
		CANCEL_BTN_BTN_RETURN='cancel btn btn-default',
		ICO_GOTO_CONTEXT_MENU_ITEM="ico-goto context-menu-item",
		OK_BTN = 'ok btn',
		BTN_BTN_DEFAULT_NG_SCOPE="btn btn-default ng-scope",
		BASIC_CUSTOMIZE_BPD_STATUS_PROP_ACCESS_RIGHT_DESCRIPTOR_NO_ACCESS="basics.customize.accessrightdescriptor (No access)",
		BASIC_CUSTOMIZE_BPD_STATUS_PROP_ACCESS_RIGHT1_IDENTIFIED="basics.customize.bpdStatusPropAccessRight1 (Identified)",
		BTN = "btn",
		BTN_NAVIGATOR_BUTTON="btn navigator-button tlb-icons ico-goto",
	   ICO_REC_NEW="ICO_REC_NEW",
	   ICO_PERMISSION_LOCK="ico-permission-lock",
		BTN_SEARCH="btn-search"
	}

	export enum NavBar {
		SAVE = 'e2e-navbar-btn-save',
		REFRESH = 'e2e-navbar-btn-refresh',
		REFRESH_SELECTED_SELECTION = 'e2e-navbar-btn-refreshSelection',
		OPTION_MENU = 'dropdown-toggle navbar-btn ',
		MENU_BUTTON = 'menu-button',
	}

	export enum ButtonText {
		NEXT = 'Next',
		EXECUTE = 'Execute',
		GO_TO_ESTIMATE = 'Go To Estimate',
		GO_TO_ESTIMATE_V1 = 'Go To estimate',
		PREVIOUS = 'Previous',
		GO_TO_REQUISITION = 'Go To Requisition',
		REQUISITION = 'Requisition',
		OK = 'OK',
		SEARCH = 'search',
		YES = 'Yes',
		GO_TO_RFQ = 'Go To RfQ',
		GO_TO_QUOTE = 'Go To Quote',
		GO_TO_CONTRACT = 'Go To Contract',
		GO_TO_PES = 'Go To PES',
		GO_TO_PACKAGE = 'Go to package',
		GO_TO_BID = 'Go to Bid',
		REFRESH = 'Refresh',
		CREATE = 'Create',
		CLOSE = 'Close',
		HISTORY = 'History',
		GO_TO = 'Go To',
		CANCEL = 'Cancel',
		RECALCULATE = 'Recalculate',
		IGNORE = 'Ignore',
		NO = 'No',
		DEEP_COPY_INCLUDING_DEPENDENCIES = 'Copy record including dependencies',
		FINISH = 'Finish',
		ADD_TO_CURRENT_QUOTE = 'Add to Current Quote',
		ADD_TO_ALL_QUOTE = 'Add to All Quotes',
		PROCEED = 'Proceed',
		GO_TO_BILL = 'Go to Bill',
		COPY = 'Copy',
		PASTE = 'Paste',
		GO_TO_WIP = 'Go to WIP',
		PINNED_ITEM = 'Pin Project of Selected Item',
		UPDATE = 'Update',
		RENUMBER = 'Renumber',
		REPLACE = 'Replace',
		EVALUATION_DETAIL = 'Evaluation Detail',
		NEW_SUB_DIVISION = 'New Subdivision',
		GO_TO_PLANT = 'Go To Plant',
		GO_TO_MATERIAL = 'Go To Material',
		GO_TO_ESTIMATE_LINE_ITEM = 'Go To Estimate Line Item',
		FILTER_CURRENT_ESTIMATE = 'Filter Current Estimates',
		ACCEPT = 'Accept',
		BACK = 'Back',
		ACCOUNTING_JOURNALS = 'Accounting Journals',
		GO_TO_BUSINESS_PARTNER = 'Go To Business Partner',
		GENERATE = 'Generate',
		LOAD_UNSETTLED_DISPATCH_NOTES = 'Load unsettled Dispatch Notes',
		GO_TO_COST_CODES = 'Go To Cost Codes',
		RESTORE = 'Restore',
		DELETE_HIGHLIGHTED_VERSION = 'Delete Highlighted Version',
		SAVE_AND_OK = 'Save & OK',
		CURRENT_ESTIMATE = 'Current Estimates',
		UPDATE_PAYMENT_SCHEDULE_TARGET = 'Update payment schedule target',
		SHOW_DEPENDANT_DATA = 'Show Dependant Data',
		SEARCH_CAP = 'Search',
		CLEAR = 'Clear',
		CLOSE_SMALL = 'close',
		NEW_ORDER_CHANGE_RECORD = 'New Order Change Record',
		NEW_CALL_OFF_RECORD = 'New CallOff Record',
		GO_TO_PROCUREMENT_STRUCTURE = 'Go To Procurement Structure',
		GO_TO_WIP_SMALL = 'Go to Wip',
		OPEN_ESTIMATION = 'Open Estimation',
		EXPAND_SELECTED = 'Expand Selected',
		COLLAPSE_SELECTED="Collapse Selected",
		EXPAND_ALL="EXPAND ALL",
		COLLAPSE_ALL="COLLAPSE ALL",
		OPEN_BOQ = 'Open BoQ',
		REMOVE="Remove",
		GO_TO_BOQ="Go To BoQ",
		GO_TO_ESTIMATE_MAIN_LINE_ITEM_FROM_SCHEDULING="Go To estimate.main-line-item-from-scheduling",
		FILTERED_TOTAL="Filtered Total",
		GRAND_TOTAL="Grand Total",
        LINE_ITEM_TOTAL="Line Item Total",
		AUTO_REFRESH="Auto Refresh",
		DELETE="Delete",
		CREATE_NEW_ABSENCE="Create new Absence",
		SEND_ABSENCE_REQUEST="Send Absence Request",
		SYNCHRONIZE_QUANTITIES="Synchronize Quantities",
		GO_TO_SCHEDULE="Go to Schedule",
		SAVE_VIEW="Save View",
		INCLUDE_COST_CODE ="Include Cost Code",
		SELECT="Select",
		ASSIGN="Assign",
		GOTO_PACKAGE_CAMELCASE="Go To Package"

	}
}
export default Buttons;
