using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Linq.Dynamic;
using RIB.Visual.Platform.Core;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using Operation = RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Diagnostics;
using System.Reflection;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Cloud.Common.BusinessComponents;
using NLS = RIB.Visual.Project.CostCodes.Localization.Properties.Resources;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.CostCodes.ServiceFacade.WebApi;
using System.Threading;

namespace RIB.Visual.Project.CostCodes.BusinessComponents
{

	/// <summary>
	/// Project costcodes Business Logic should be placed here
	/// 
	/// class derived from platform LogicBase Class
	/// </summary>
	[Export(typeof(IProjectCostCodesLogic))]
	public class ProjectCostCodesLogic : RVPBizComp.LogicBase, IProjectCostCodesLogic
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public int GetDefaultLogisticJobIdByProject(int projectId)
		{
			var lgmJobLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();

			var jobId = lgmJobLogic.GetDefaultLogisticJobIdByProject(projectId);

			return jobId ?? -1;
		}

		/// <summary>
		/// Get a list of project costcodes
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns>The cost codes (matching the filter)</returns>
		public IEnumerable<PrjCostCodesEntity> GetList(int projectId)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbContext.Entities<PrjCostCodesEntity>().Where(i => i.ProjectFk == projectId).ToList();

				HandleEntities(entities, projectId);

				return entities;
			}
		}


		/// <summary>
		/// get cost codes search tree by filterString and mdcContextId
		/// </summary>
		/// <param name="filterString"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<PrjCostCodesEntity> GetSearchTree(string filterString, int? projectId = null)
		{
			ProjectCostCodeLookupTreeLogic costcodeLookupTreeLogic = new ProjectCostCodeLookupTreeLogic();

			var result = costcodeLookupTreeLogic.GetSearchList(filterString, projectId);

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetProjectCostCodesByIds(IEnumerable<int> ids)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbContext.Entities<PrjCostCodesEntity>().Where(e => ids.Contains(e.Id)).ToList();

				return entities;
			}
		}



		/// <summary>
		/// Get a list of project costcodes
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="jobIds"></param>
		/// <param name="costCodeIds"></param>
		/// <returns>The cost codes (matching the filter)</returns>
		public IEnumerable<PrjCostCodesEntity> GetList(int projectId, IEnumerable<int> jobIds, IEnumerable<int> costCodeIds)
		{
			Expression<Func<PrjCostCodesEntity, bool>> filter = e => e.ProjectFk == projectId;

			if (jobIds != null && jobIds.Any())
			{
				filter = filter.And(e => jobIds.Contains(e.LgmJobFk.Value));
			}

			if (costCodeIds != null && costCodeIds.Any())
			{
				filter = filter.And(e => e.MdcCostCodeFk.HasValue && costCodeIds.Contains(e.MdcCostCodeFk.Value));
			}

			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var query = dbContext.Entities<PrjCostCodesEntity>().Where(filter);

				var entities = query.ToList();

				HandleEntities(entities, projectId);

				return entities;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="jobIds"></param>
		/// <param name="costCodeIds"></param>
		/// <returns></returns>
		public IEnumerable<PrjCostCodesEntity> GetListWithJobRate(int projectId, IEnumerable<int> jobIds, IEnumerable<int> costCodeIds)
		{
			Expression<Func<PrjCostCodesEntity, bool>> costCodeFilter = e => e.ProjectFk == projectId;

			if (costCodeIds != null && costCodeIds.Any())
			{
				costCodeFilter = costCodeFilter.And(e => e.MdcCostCodeFk.HasValue && costCodeIds.Contains(e.MdcCostCodeFk.Value));
			}

			Expression<Func<ProjectCostCodesJobRateEntity, bool>> jobRateFilter = e => true;

			if (jobIds != null && jobIds.Any())
			{
				jobRateFilter = jobRateFilter.And(e => jobIds.Contains(e.LgmJobFk.Value));
			}

			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var costCodeQuery = dbContext.Entities<PrjCostCodesEntity>().Where(costCodeFilter).ToList();

				var prjcostcodeIds = costCodeQuery.Select(e => e.Id).ToList();

				jobRateFilter = jobRateFilter.And(e => prjcostcodeIds.Contains(e.ProjectCostCodeFk));

				var jobRateQuery = dbContext.Entities<ProjectCostCodesJobRateEntity>().Where(jobRateFilter).ToList();

				var query = costCodeQuery.Join(jobRateQuery, prjCostCode => prjCostCode.Id,
					rateJob => rateJob.ProjectCostCodeFk, (prjCostCode, rateJob) => new PrjCostCodesEntity()
					{
						Id = prjCostCode.Id,
						ProjectFk = prjCostCode.ProjectFk,
						MdcCostCodeFk = prjCostCode.MdcCostCodeFk,

						JobRateId = rateJob.Id, // 
						LgmJobFk = rateJob.LgmJobFk, // prjCostCode.LgmJobFk,
						Rate = rateJob.Rate, // prjCostCode.Rate,
						Extra = prjCostCode.Extra,
						Surcharge = prjCostCode.Surcharge,
						Contribution = prjCostCode.Contribution,
						DayWorkRate = rateJob.SalesPrice, //prjCostCode.DayWorkRate,
						CurrencyFk = rateJob.CurrencyFk, //prjCostCode.CurrencyFk,
						UomFk = prjCostCode.UomFk,
						FactorCosts = rateJob.FactorCosts, //prjCostCode.FactorCosts,
						RealFactorCosts = rateJob.RealFactorCosts, //prjCostCode.RealFactorCosts,
						FactorQuantity = rateJob.FactorQuantity, //prjCostCode.FactorQuantity,
						RealFactorQuantity = rateJob.RealFactorQuantity, //prjCostCode.RealFactorQuantity,
						Remark = prjCostCode.Remark,
						IsLabour = prjCostCode.IsLabour,
						IsRate = prjCostCode.IsRate,
						IsDefault = prjCostCode.IsDefault,
						InsertedAt = prjCostCode.InsertedAt,
						InsertedBy = prjCostCode.InsertedBy,
						UpdatedAt = prjCostCode.UpdatedAt,
						UpdatedBy = prjCostCode.UpdatedBy,
						Version = prjCostCode.Version,
						EstCostTypeFk = prjCostCode.EstCostTypeFk,
						FactorHour = rateJob.FactorHour, //prjCostCode.FactorHour,
						IsEditable = prjCostCode.IsEditable,
						CostCodeParentFk = prjCostCode.CostCodeParentFk,
						Code = prjCostCode.Code,
						DescriptionInfo = prjCostCode.DescriptionInfo,
						IsSubcontractor = prjCostCode.IsSubcontractor,
						Description2Info = prjCostCode.Description2Info,
						CostCodeTypeFk = prjCostCode.CostCodeTypeFk,
						CostCodePortionsFk = prjCostCode.CostCodePortionsFk,
						CostGroupPortionsFk = prjCostCode.CostGroupPortionsFk,
						AbcClassificationFk = prjCostCode.AbcClassificationFk,
						PrcStructureFk = prjCostCode.PrcStructureFk,
						UserDefined1 = prjCostCode.UserDefined1,
						UserDefined2 = prjCostCode.UserDefined2,
						UserDefined3 = prjCostCode.UserDefined3,
						UserDefined4 = prjCostCode.UserDefined4,
						UserDefined5 = prjCostCode.UserDefined5,
						CostCodeLevel1Fk = prjCostCode.CostCodeLevel1Fk,
						CostCodeLevel2Fk = prjCostCode.CostCodeLevel2Fk,
						CostCodeLevel3Fk = prjCostCode.CostCodeLevel3Fk,
						CostCodeLevel4Fk = prjCostCode.CostCodeLevel4Fk,
						CostCodeLevel5Fk = prjCostCode.CostCodeLevel5Fk,
						CostCodeLevel6Fk = prjCostCode.CostCodeLevel6Fk,
						CostCodeLevel7Fk = prjCostCode.CostCodeLevel7Fk,
						CostCodeLevel8Fk = prjCostCode.CostCodeLevel8Fk,
						SearchPattern = prjCostCode.SearchPattern,
						ContrCostCodeFk = prjCostCode.ContrCostCodeFk,
						IsBudget = prjCostCode.IsBudget,
						IsCost = prjCostCode.IsCost,
						IsChildAllowed = prjCostCode.IsChildAllowed,
						EfbType221Fk = prjCostCode.EfbType221Fk,
						EfbType222Fk = prjCostCode.EfbType222Fk,
						Co2Project = prjCostCode.Co2Project,
						Co2Source = prjCostCode.Co2Source,
						Co2SourceFk = prjCostCode.Co2SourceFk,

						JobRateInsertedAt = rateJob.InsertedAt,
						JobRateInsertedBy = rateJob.InsertedBy,
						JobRateUpdatedAt = rateJob.UpdatedAt,
						JobRateUpdatedBy = rateJob.UpdatedBy,
						JobRateVersion = rateJob.Version
					});

				var entities = query.ToList();

				HandleEntities(entities, projectId);

				return entities;
			}
		}

		/// <summary>
		/// Get a list of project costcodes
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="mdcCostCodeId"></param>
		/// <param name="jobFk"></param>
		/// <param name="code"></param>
		/// <returns>The cost codes (matching the filter)</returns>
		public IEnumerable<PrjCostCodesEntity> GetList(int projectId, int mdcCostCodeId, int jobFk, string code)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entities = this.Get(i => i.ProjectFk == projectId && (i.MdcCostCodeFk == mdcCostCodeId || i.Code == code), jobFk);
				//res = res.Where(x => x.LgmJobFk == jobFk);--..

				HandleEntities(entities, projectId);

				return entities;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetListByCode(string code, int projectId, int? jobId)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var res = dbContext.Entities<PrjCostCodesEntity>().Include(e => e.PrjCostCodeParent).Where(i => i.ProjectFk == projectId && i.Code == code).ToList();

				if (jobId > 0)
				{
					//return res.Where(x => x.LgmJobFk == jobId).ToList();
					new ProjectCostCodesJobRateLogic().HandleProjectCostCodes(res, jobId.Value);
				}

				return res;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="projectId"></param>
		private void HandleEntities(IEnumerable<PrjCostCodesEntity> entities, int projectId)
		{
			var lgmJobLogic = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var lgmJobs = lgmJobLogic.GetLogisticJobsByProject(projectId).ToList();
			var lgmJobDict = lgmJobs.GroupBy(x => x.Id).ToDictionary(x => x.Key, y => y.FirstOrDefault());
			SetBasCostCode(entities);
			foreach (var PrjCC in entities)
			{
				if (PrjCC.LgmJobFk.HasValue)
				{
					var lgmJob = lgmJobDict.GetValueOrDefault(PrjCC.LgmJobFk.Value);
					if (lgmJob != null)
					{
						PrjCC.JobCode = lgmJob.Code;
						PrjCC.JobDescription = lgmJob.DescriptionInfo.Description;
					}
				}
			}
		}

		/// <summary>
		/// Get a list of project costcodes
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns>The cost codes (matching the filter)</returns>
		public IEnumerable<IIdentifyable> GetPrjCostCodeList(int projectId)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbContext.Entities<PrjCostCodesEntity>().Where(i => i.ProjectFk == projectId).ToList();

				//var logic = new BasicsCostCodesLogic();

				//foreach (var PrjCC in entities)
				//{
				//	SetBasCostCode(PrjCC, logic);
				//}
				//return entities;

				var list = FilterByCostCodeType(entities).ToList();

				// append project CostCode those added by manually
				foreach (var entity in entities)
				{
					if (!entity.MdcCostCodeFk.HasValue)
					{
						list.Add(entity);
					}
				}

				return list;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="jobFks"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetProjectCostCodes(int projectId, List<int> jobFks = null)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<PrjCostCodesEntity>().Where(i => i.ProjectFk == projectId).ToList();

				if (jobFks != null && jobFks.Any())
				{
					new ProjectCostCodesJobRateLogic().AttachCostCodeJobRatesEntities(entities, jobFks);
					//entities = entities.Where(e => e.LgmJobFk.HasValue && jobFks.Contains(e.LgmJobFk.Value)).ToList();
				}

				entities.Translate(UserLanguageId, new Func<IProjectCostCodesEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
				entities.Translate(UserLanguageId, new Func<IProjectCostCodesEntity, DescriptionTranslateType>[] { e => e.Description2Info });

				return entities;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetEstimateProjectCostCodes(int projectId)
		{
			var list = GetProjectCostCodes(projectId);
			if (list.Any())
			{
				var costTypeIds = list.Where(x => x.CostCodeTypeFk.HasValue).Select(x => x.CostCodeTypeFk.Value).ToList();
				var estCostTypeIds = new BasicsCostCodesLogic().GetCostCodeTypes(x => costTypeIds.Contains(x.Id) && x.IsEstimateCc).Select(x => x.Id);
				list = list.Where(x => x.CostCodeTypeFk.HasValue && estCostTypeIds.Contains(x.CostCodeTypeFk.Value));
			}

			return list.ToList();
		}

		/// <summary>
		/// Get project cost code by current project and job
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// <param name="costCodeIds"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetProjectCostCodes(int projectId, int jobId, IEnumerable<int> costCodeIds)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var prjCostCodes = dbcontext.Entities<PrjCostCodesEntity>().Where(s => s.ProjectFk == projectId && s.MdcCostCodeFk.HasValue && costCodeIds.Contains(s.MdcCostCodeFk.Value)).ToList();

				//add project job rate
				new ProjectCostCodesJobRateLogic().HandleProjectCostCodes(prjCostCodes, jobId);

				//add mdcCostCode
				var mdcCostCodes = new BasicsCostCodesLogic().GetItemsByKey(costCodeIds);

				var mdcCostCodeDict = mdcCostCodes.GroupBy(x => x.Id).ToDictionary(x => x.Key, y => y.FirstOrDefault());

				foreach (var prjCc in prjCostCodes)
				{
					if (prjCc.MdcCostCodeFk.HasValue)
					{
						prjCc.BasCostCodeEntity = mdcCostCodeDict.GetValueOrDefault(prjCc.MdcCostCodeFk.Value);
					}
				}

				//add costCode price list of current job
				var jobEntity = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>().GetJobById(jobId);

				if(jobEntity != null && jobEntity.CostCodePriceVersionFk.HasValue)
				{
					var costCodePriceList = new BasicsCostCodePriceListLogic().GetSearchList(e => costCodeIds.Contains(e.CostCodeFk) && e.CostcodePriceVerFk == jobEntity.CostCodePriceVersionFk);

					var mdcCostCodeId2PriceListMap = costCodePriceList.GroupBy(e => e.CostCodeFk).ToDictionary(e => e.Key, e => e.FirstOrDefault());

					foreach (var prjCc in prjCostCodes)
					{
						if (prjCc.MdcCostCodeFk.HasValue && mdcCostCodeId2PriceListMap.ContainsKey(prjCc.MdcCostCodeFk.Value))
						{
							prjCc.CurrentCostCodePriceListEntity = mdcCostCodeId2PriceListMap[prjCc.MdcCostCodeFk.Value];
						}
					}
				}

				//handle project properties
				foreach (var prjCc in prjCostCodes)
				{
					prjCc.Rate = GetPropFormProjectToMaster(prjCc, e => e.Rate, i => i.Rate, j => j.Rate);
					prjCc.FactorCosts = GetPropFormProjectToMaster(prjCc, e => e.FactorCosts, i => i.FactorCost, j => j.FactorCosts);
					prjCc.RealFactorCosts = GetPropFormProjectToMaster(prjCc, e => e.RealFactorCosts, i => i.RealFactorCost, j => j.RealFactorCosts); 
					prjCc.FactorQuantity = GetPropFormProjectToMaster(prjCc, e => e.FactorQuantity, i => i.FactorQuantity, j => j.FactorQuantity);
					prjCc.RealFactorQuantity = GetPropFormProjectToMaster(prjCc, e => e.RealFactorQuantity, i => i.RealFactorQuantity, j => j.RealFactorQuantity);
					prjCc.FactorHour = GetPropFormProjectToMaster(prjCc, e => e.FactorHour, i => i.FactorHour, j => j.FactorHour);
					prjCc.CurrencyFk = GetCurrencyFk(prjCc);
					prjCc.DayWorkRate = GetPropFormProjectToMaster(prjCc, e => e.DayWorkRate, i => i.SalesPrice, j => j.DayWorkRate);
				}

				return prjCostCodes;
			}
		}

		private decimal GetPropFormProjectToMaster(PrjCostCodesEntity projectCostCodesEntity, Func<PrjCostCodesEntity, decimal> getPrjCostCodePropValue, Func<CostcodePriceListEntity, decimal> getPriceListPropValue, Func<CostCodeEntity, decimal> getMdcCostCodePropValue)
		{
			if(getPrjCostCodePropValue(projectCostCodesEntity) > 0)
			{
				return getPrjCostCodePropValue(projectCostCodesEntity);
			}

			if(projectCostCodesEntity.CurrentCostCodePriceListEntity != null && getPriceListPropValue(projectCostCodesEntity.CurrentCostCodePriceListEntity) > 0)
			{
				return getPriceListPropValue(projectCostCodesEntity.CurrentCostCodePriceListEntity);
			}

			if(projectCostCodesEntity.BasCostCodeEntity != null)
			{
				return getMdcCostCodePropValue(projectCostCodesEntity.BasCostCodeEntity);
			}

			return getPrjCostCodePropValue(projectCostCodesEntity);
		}

		private int? GetCurrencyFk(PrjCostCodesEntity projectCostCodesEntity)
		{
			if(projectCostCodesEntity.Rate > 0)
			{
				return projectCostCodesEntity.CurrencyFk;
			}

			if(projectCostCodesEntity.CurrentCostCodePriceListEntity != null && projectCostCodesEntity.CurrentCostCodePriceListEntity.Rate > 0)
			{
				return projectCostCodesEntity.CurrentCostCodePriceListEntity.CurrencyFk;
			}

			if(projectCostCodesEntity.BasCostCodeEntity != null)
			{
				return projectCostCodesEntity.BasCostCodeEntity.CurrencyFk;
			}

			return projectCostCodesEntity.CurrencyFk;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="costCodeIds"></param>
		/// <param name="jobFks"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetProjectCostCodes(int projectId, IEnumerable<int> costCodeIds, List<int> jobFks = null)
		{
			IEnumerable<PrjCostCodesEntity> entities = null;

			entities = Get(s => s.ProjectFk == projectId && s.MdcCostCodeFk.HasValue && costCodeIds.Contains(s.MdcCostCodeFk.Value)).ToList();

			if (jobFks != null && jobFks.Any())
			{
				new ProjectCostCodesJobRateLogic().AttachCostCodeJobRatesEntities(entities, jobFks);
				// entities = entities.Where(e => e.LgmJobFk.HasValue && jobFks.Contains(e.LgmJobFk.Value));
			}
			return entities;
		}

		/// <summary>
		/// Returns the project costcodes relationship matching the given filter
		/// </summary>
		/// <returns></returns>
		public IEnumerable<PrjCostCodesEntity> Get(Expression<Func<PrjCostCodesEntity, bool>> filter = null, int? jobFk = null, bool includeJobRate = false)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IEnumerable<PrjCostCodesEntity> entities = null;

				if (filter != null)
				{
					entities = dbcontext.GetFiltered(filter);
				}
				else
				{
					entities = dbcontext.Entities<PrjCostCodesEntity>();
				}

				if (includeJobRate)
				{
					AttachJobRate(entities, jobFk);
				}
				else
				{
					if (entities != null && entities.Any() && jobFk.HasValue && jobFk.Value > 0)
					{
						new ProjectCostCodesJobRateLogic().HandleProjectCostCodes(entities, jobFk.Value);
					}
				}

				return entities.ToList();
			}
		}

		private void AttachJobRate(IEnumerable<PrjCostCodesEntity> projectCostCodes, int? jobFk)
		{
			if (projectCostCodes == null || !projectCostCodes.Any())
			{
				return;
			}

			var projectCostCodeGroupList = projectCostCodes.Select((x, i) => new { Index = i, Value = x }).GroupBy(x => x.Index / 100).Select(x => x.Select(v => v.Value).ToList()).ToList();

			Parallel.ForEach(projectCostCodeGroupList, projectCostCodeGroup =>
			{
				var projectCostCodeIds = projectCostCodeGroup.Select(e => e.Id).ToList();

				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					Expression<Func<ProjectCostCodesJobRateEntity, bool>> filter = e => projectCostCodeIds.Contains(e.ProjectCostCodeFk);

					if (jobFk != null && jobFk.Value > 0)
					{
						filter = filter.And(e => e.LgmJobFk.HasValue && e.LgmJobFk == jobFk);
					}

					var jobRates = dbContext.Entities<ProjectCostCodesJobRateEntity>().Where(filter).ToList();

					AttachJobRateToPrjCostCode(projectCostCodeGroup, jobRates);
				}
			});
		}

		private void AttachJobRateToPrjCostCode(IEnumerable<PrjCostCodesEntity> projectCostCodes, IEnumerable<ProjectCostCodesJobRateEntity> jobRates)
		{
			if (projectCostCodes == null || !projectCostCodes.Any() || jobRates == null || !jobRates.Any())
			{
				return;
			}

			var jobRatesOfPrjCostCode = jobRates.GroupBy(e => e.ProjectCostCodeFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var prjCostCode in projectCostCodes)
			{
				if (!jobRatesOfPrjCostCode.ContainsKey(prjCostCode.Id))
				{
					continue;
				}

				prjCostCode.JobRates = jobRatesOfPrjCostCode[prjCostCode.Id].OfType<IProjectCostCodesJobRateEntity>().ToList();
			}
		}

		/// <summary>
		/// Creates a project cost codes entity, provides a unique db ID...
		/// </summary>
		/// <param name="data"></param>
		/// <returns>The new created project costcode</returns>
		public PrjCostCodesEntity Create(PrjCostCodeCreationData data)
		{
			if (data == null || data.ParentCostCode == null)
			{
				return new PrjCostCodesEntity();
			}
			var entity = CreateProjectCostCode() as PrjCostCodesEntity;
			entity = entity.CopyPropertyFrom(data.ParentCostCode, new[] { "Id", "DescriptionInfo" });
			entity.FactorCosts = 1;
			entity.FactorQuantity = 1;
			entity.CostCodeParentFk = data.ParentCostCode.Id;
			entity.ProjectFk = data.ProjectId;
			entity.IsChildAllowed = false;
			entity.MdcCostCodeFk = null;
			entity.DescriptionInfo = data.ParentCostCode.CopyTranslate(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo }).DescriptionInfo;
			entity.Description2Info = data.ParentCostCode.CopyTranslate(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.Description2Info }).Description2Info;
			PrjCostCodeGenerateCodeData codeData = new PrjCostCodeGenerateCodeData();
			codeData.ProjectId = data.ProjectId;
			codeData.JobId = null;
			codeData.NewCreatedItem = data.ParentCostCode;
			codeData.IsPrjAssembly = false;

			var response = GenerateCode(codeData);
			entity.Code = response.Item1;
			return entity;
		}

		/// <summary>
		/// Creates a project cost codes entity, provides a unique db ID...
		/// </summary>
		/// <returns>The new created project costcodes</returns>
		public PrjCostCodesEntity Create(PrjCostCodesEntity entity)
		{
			entity.Id = this.SequenceManager.GetNext("PRJ_PROJECT2MDC_CST_CDE");

			return entity;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public IProjectCostCodesEntity CreateProjectCostCode()
		{
			PrjCostCodesEntity entity = new PrjCostCodesEntity();

			entity.Id = this.SequenceManager.GetNext("PRJ_PROJECT2MDC_CST_CDE");

			return entity;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCodeDic"></param>
		public IProjectCostCodesEntity Save(IDictionary<string, object> costCodeDic)
		{
			try
			{
				var ent = new PrjCostCodesEntity();

				ent.CopyPropertyFrom<PrjCostCodesEntity>(costCodeDic, "Id");

				Create(ent);

				Save(ent);

				return ent;
			}
			catch (Exception e)
			{
				e.GetType();
				throw new Operation.BusinessLayerException()
				{
					ErrorCode = (int)Operation.ExceptionErrorCodes.BusinessFatalError,
					ErrorMessage = NLS.ERR_TheProjectcostcodeCouldNotBeSaved
				};
			}
		}

		private void ClearProjectCostCodeLevelInfo(PrjCostCodesEntity entity)
		{
			if (entity == null)
			{
				return;
			}

			entity.PrjCostCodeParent = null;

			entity.CostCodeLevel1Fk = null;
			entity.CostCodeLevel2Fk = null;
			entity.CostCodeLevel3Fk = null;
			entity.CostCodeLevel4Fk = null;
			entity.CostCodeLevel5Fk = null;
			entity.CostCodeLevel6Fk = null;
			entity.CostCodeLevel7Fk = null;
			entity.CostCodeLevel8Fk = null;
		}

		/// <summary>
		/// Saves a singles project costcodes without dependend data
		/// </summary>
		/// <param name="prjcostcode">The element to be saved</param>
		/// <returns></returns>
		public PrjCostCodesEntity Save(PrjCostCodesEntity prjcostcode)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				try
				{
					ClearProjectCostCodeLevelInfo(prjcostcode);

					prjcostcode.SaveTranslate(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
					prjcostcode.SaveTranslate(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.Description2Info });

					dbcontext.Save(prjcostcode);

					return prjcostcode;
				}
				catch
				{
					throw new Operation.BusinessLayerException()
					{
						ErrorCode = (int)Operation.ExceptionErrorCodes.BusinessFatalError,
						ErrorMessage = NLS.ERR_InvalidProjectCosCodeTheCodeIsEmpty
					};
				}
			}
		}

		/// <summary>
		/// Save detached project costcode relationship entity into database 
		/// </summary>
		/// <returns></returns>
		public IEnumerable<PrjCostCodesEntity> Save(IEnumerable<PrjCostCodesEntity> entities)
		{
			if (entities == null || !entities.Any())
			{
				return entities;
			}

			// TODO: lnt - set the PRJ_COST_CODE_LEVEL_FK as null
			foreach (var entity in entities)
			{
				ClearProjectCostCodeLevelInfo(entity);
				entity.PrjCostCodeChildren = null;
			}

			entities.SaveTranslate<PrjCostCodesEntity>(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				return dbContext.Save(entities);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> UpdateEntities(IEnumerable<IProjectCostCodesEntity> entities)
		{
			if (entities == null || !entities.Any())
			{
				return entities;
			}

			return Save(entities.OfType<PrjCostCodesEntity>().ToList());
		}

		/// <summary>
		/// saves a given list of project costcode entities
		/// </summary>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> Save(IEnumerable<IIdentifyable> ToSave)
		{
			try
			{
				var entities = new List<PrjCostCodesEntity>();

				foreach (var item in ToSave)
				{
					PrjCostCodesEntity ent = (PrjCostCodesEntity)item;

					if (ent == null || !ent.MdcCostCodeFk.HasValue)
					{
						continue;
					}

					if (ent.LgmJobFk.HasValue)
					{
						var exist = GetByMdcFkProjectFkJobFk(ent.MdcCostCodeFk.Value, ent.ProjectFk, ent.LgmJobFk.Value);
						if (exist != null && exist.Id > 0)
						{
							ent.Id = exist.Id;
							ent.Version = ((PrjCostCodesEntity)exist).Version;
						}
						else if (ent.Version == 0)
						{
							Create(ent);
						}

						entities.Add(ent);
					}
					else
					{
						var exist = GetByProjectIdAndCostCodeId(ent.ProjectFk, ent.MdcCostCodeFk.Value);
						if (exist != null && exist.Id > 0)
						{
							ent.Id = exist.Id;
							ent.Version = ((PrjCostCodesEntity)exist).Version;
						}
						else if (ent.Version == 0)
						{
							Create(ent);
						}

						entities.Add(ent);
					}
				}

				return Save(entities);
			}
			catch (Exception e)
			{
				e.GetType();
				throw new Operation.BusinessLayerException()
				{
					ErrorCode = (int)Operation.ExceptionErrorCodes.BusinessFatalError,
					ErrorMessage = NLS.ERR_TheProjectcostcodeCouldNotBeSaved
				};
			}
		}

		/// <summary>
		/// Deletes a project cost codes
		/// </summary>
		/// <param name="prjcostcode">The element to be deleted</param>
		/// <returns>database id of the deleted element</returns>
		public int Delete(PrjCostCodesEntity prjcostcode)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				try
				{
					int nID = prjcostcode.Id;

					dbcontext.Delete(prjcostcode);

					return nID;
				}
				catch
				{
					throw new Operation.BusinessLayerException()
					{
						ErrorCode = (int)Operation.ExceptionErrorCodes.BusinessFatalError,
						ErrorMessage = NLS.ERR_TheSelectedProjectCostcodeCanNotBeDeleted
					};
				}

			}
		}

		/// <summary>
		/// Deletes a project cost codes
		/// </summary>
		/// <returns></returns>
		public void Delete(IEnumerable<PrjCostCodesEntity> entities)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				dbcontext.Delete(entities);
			}
		}

		/// <summary>
		/// deletes a given list of project cost codes
		/// </summary>
		/// <returns></returns>
		public void Delete(IEnumerable<IIdentifyable> ToDelete)
		{
			var entities = new List<PrjCostCodesEntity>();

			foreach (var item in ToDelete)
			{
				PrjCostCodesEntity ent = (PrjCostCodesEntity)item;

				if (ent != null)
				{
					entities.Add(ent);
				}
			}

			Delete(entities);
		}

		/// <summary>
		/// set BasCostCodeEntity for a given project cost code
		/// </summary>
		/// <param name="prjCostCodes"></param>
		private void SetBasCostCode(IEnumerable<PrjCostCodesEntity> prjCostCodes)
		{
			var prjCostCodesIds = prjCostCodes.Where(e => e.MdcCostCodeFk.HasValue).Select(e => e.MdcCostCodeFk.Value);
			var mdcCostCodes = new BasicsCostCodesLogic().GetItemsByKey(prjCostCodesIds);
			var mdcCostCodeDict = mdcCostCodes.GroupBy(x => x.Id).ToDictionary(x => x.Key, y => y.FirstOrDefault());
			foreach (var prjCc in prjCostCodes)
			{
				if (prjCc.MdcCostCodeFk.HasValue)
				{
					prjCc.BasCostCodeEntity = mdcCostCodeDict.GetValueOrDefault(prjCc.MdcCostCodeFk.Value);
				}
			}

			var prjCostCodes2 = prjCostCodes.Where(x => x.MdcCostCodeFk.HasValue && x.LgmJobFk.HasValue).ToList();
			var lgmCostcCodePriceLogic = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var jobs = lgmCostcCodePriceLogic.GetJobByIds(prjCostCodes2.GroupBy(x => x.LgmJobFk.Value).Select(x => x.Key));
			var costCodePriceVersionFks = jobs.Select(x => x.CostCodePriceVersionFk).Distinct().ToList();
			var mdcCcPriceList = new BasicsCostCodePriceListLogic()
				.GetSearchList(e => prjCostCodesIds.Contains(e.CostCodeFk) && costCodePriceVersionFks.Contains(e.CostcodePriceVerFk));
			var mdcCcPriceDict = mdcCcPriceList.GroupBy(x => new { x.CostCodeFk, x.CostcodePriceVerFk })
				.ToDictionary(x => x.Key, y => y.FirstOrDefault());
			foreach (var item in prjCostCodes2)
			{
				if (item.BasCostCodeEntity != null)
				{
					var priceEntity = mdcCcPriceDict.GetValueOrDefault(new { CostCodeFk = item.MdcCostCodeFk.Value, CostcodePriceVerFk = item.JobCostCodePriceVersionFk ?? 0 });
					if (priceEntity != null)
					{
						item.BasCostCodeEntity.Rate = priceEntity.Rate;
						item.BasCostCodeEntity.DayWorkRate = priceEntity.SalesPrice;
					}
				}
			}
		}

		/// <summary>
		/// public simple save interface.
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="includeJobRate"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> SaveEntities(IEnumerable<IProjectCostCodesEntity> entities, bool includeJobRate = true)
		{
			if (entities == null || !entities.Any())
			{
				return entities;
			}

			if (!includeJobRate)
			{
				return this.Save(entities.OfType<PrjCostCodesEntity>().ToList());
			}

			var result = new List<IProjectCostCodesEntity>();

			/* save project cost code with no mdcCostCodeFk */
			var projectCostCodeNotMdcCostCodes = entities.Where(e => !e.MdcCostCodeFk.HasValue || !e.LgmJobFk.HasValue).OfType<PrjCostCodesEntity>().ToList();

			if (projectCostCodeNotMdcCostCodes != null && projectCostCodeNotMdcCostCodes.Any())
			{
				result.AddRange(this.Save(projectCostCodeNotMdcCostCodes));
			}

			/* save project cost code with mdcCostCodeFk */
			var projectId2ProjectCostCodes = entities.Where(e => e.MdcCostCodeFk.HasValue && e.LgmJobFk.HasValue).GroupBy(e => e.ProjectFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var item in projectId2ProjectCostCodes)
			{
				if (item.Value == null || !item.Value.Any())
				{
					continue;
				}

				var mdcCostCodeId2JobIdsList = item.Value.Select(e => new Tuple<int, int>(e.MdcCostCodeFk.Value, e.LgmJobFk.Value)).ToList();

				result.AddRange(this.SaveCostCodes(mdcCostCodeId2JobIdsList, item.Key, item.Value));
			}

			return entities;
		}

		/// <summary>
		/// Find Basic costcode from Id and save as project cost code
		/// </summary>
		/// <param name="list"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public void SaveBasCcAsPrjCc(Dictionary<int, string> list, int projectId)
		{
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var basLogic = new BasicsCostCodesLogic();

				foreach (var li in list)
				{
					var basCc = basLogic.GetCostCodeById(li.Key);
					var prjCcId = this.SequenceManager.GetNext("PRJ_PROJECT2MDC_CST_CDE");
					var entity = new PrjCostCodesEntity();
					CopyProperties(basCc, entity);
					entity.Id = prjCcId;
					entity.ProjectFk = projectId;
					entity.Version = 0;
					entity.MdcCostCodeFk = li.Key;

					ClearProjectCostCodeLevelInfo(entity);

					dbContext.Save(entity);
				}
			}
		}

		/// <summary>
		/// Copy properties from Basic cost code to project cost code
		/// </summary>
		/// <param name="source"></param>
		/// <param name="destination"></param>
		/// <returns></returns>
		public void CopyProperties(CostCodeEntity source, PrjCostCodesEntity destination)
		{
			// Iterate the Properties of the destination instance and  
			// populate them from their source counterparts  
			PropertyInfo[] destinationProperties = destination.GetType().GetProperties();

			foreach (PropertyInfo destinationPi in destinationProperties)
			{
				PropertyInfo sourcePi = source.GetType().GetProperty(destinationPi.Name);

				if (sourcePi != null)
				{
					destinationPi.SetValue(destination, sourcePi.GetValue(source, null), null);
				}
			}

			if (source != null && destination != null)
			{
				destination.DescriptionInfo =  source.DescriptionInfo;

				destination.Description2Info = source.Description2Info != null ? source.Description2Info : destination.Description2Info;
			}
		}

		/// <summary>
		/// Get By ProjectId and CostCodeId
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="costCodeId"></param>
		/// <returns></returns>
		//[Obsolete("This overload is obsolete and should be removed. Use the overload(GetByMdcFkProjectFkJobFk) that accepts projectId, costCodeId and jobId argument instead.", false)]
		public IIdentifyable GetByProjectIdAndCostCodeId(int projectId, int costCodeId)
		{
			return Get(s => s.ProjectFk == projectId && s.MdcCostCodeFk == costCodeId).FirstOrDefault();
		}

		/// <summary>
		/// Get by Id
		/// </summary>
		/// <param name="Id"></param>
		/// <returns></returns>
		public IProjectCostCodesEntity GetById(int Id)
		{
			return Get(s => s.Id == Id).FirstOrDefault();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="code"></param>
		/// <param name="includeJodRate"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		public IProjectCostCodesEntity GetByCode(int projectId, string code, bool includeJodRate = false, int? jobId = null)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entity = dbContext.Entities<PrjCostCodesEntity>().FirstOrDefault(e => e.ProjectFk == projectId && e.Code.ToUpper() == code.Trim().ToUpper());

				if (entity != null && includeJodRate)
				{
					AttachJobRate(new List<PrjCostCodesEntity>() { entity }, jobId);
				}

				return entity;
			}
		}

		/// <summary>
		/// Save costcodes per Project and Job
		/// </summary>
		/// <param name="costCodeJobIds"></param>
		/// <param name="projectId"></param>
		/// <param name="prjCostCodes"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> SaveCostCodes_Old(List<Tuple<int, int>> costCodeJobIds, int projectId, List<IProjectCostCodesEntity> prjCostCodes = null)
		{
			var prjCostCodes2Save = new List<PrjCostCodesEntity>();

			if (!costCodeJobIds.Any() || projectId <= 0)
			{
				return prjCostCodes2Save;
			}

			var ccIds = costCodeJobIds.Select(e => e.Item1).ToList();

			var mdcCostCodeList = new BasicsCostCodesLogic().GetItemsByKeyWithParent(ccIds);

			if (mdcCostCodeList == null || !mdcCostCodeList.Any())
			{
				return prjCostCodes2Save;
			}

			var mdcCostCodeIds = new List<Tuple<int, int>>();

			var existingPrjCostCodesMapping = new Dictionary<int, PrjCostCodesEntity>();

			var existingPrjCostCodes = Get(e => e.ProjectFk == projectId && e.MdcCostCodeFk.HasValue);

			foreach (var prjCc in existingPrjCostCodes)
			{
				if (prjCc.LgmJobFk.HasValue && prjCc.MdcCostCodeFk.HasValue)
				{
					mdcCostCodeIds.Add(new Tuple<int, int>(prjCc.MdcCostCodeFk.Value, prjCc.LgmJobFk.Value));
				}

				if (prjCc.MdcCostCodeFk.HasValue && !existingPrjCostCodesMapping.ContainsKey(prjCc.MdcCostCodeFk.Value))
				{
					existingPrjCostCodesMapping.Add(prjCc.MdcCostCodeFk.Value, prjCc);
				}
			}

			var mdc2PrjCostCodeIdMapping = new Dictionary<int, int>();

			foreach (var ccDic in costCodeJobIds.Distinct())
			{
				var item = new Tuple<int, int>(ccDic.Item1, ccDic.Item2);

				var currentJobId = item.Item2;

				var isValid = true;

				while (isValid)
				{
					if (!mdcCostCodeIds.Any(e => e.Item1 == item.Item1 && e.Item2 == item.Item2))
					{
						mdcCostCodeIds.Add(item);

						var prjCostCode = new PrjCostCodesEntity();
						var mdcCostCode = mdcCostCodeList.FirstOrDefault(e => e.Id == item.Item1);

						if (mdcCostCode != null)
						{
							var createdPrjCostCode = prjCostCodes != null ? prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == mdcCostCode.Id) : null;
							if (createdPrjCostCode == null)
							{
								var priceListEntity = GetMdcPriceByJobAndCostCode(costCodeId: item.Item1, jobId: item.Item2);
								CopyProperties(mdcCostCode, prjCostCode);
								prjCostCode = Create(prjCostCode);
								prjCostCode.ProjectFk = projectId;
								prjCostCode.MdcCostCodeFk = item.Item1;
								prjCostCode.LgmJobFk = item.Item2;
								prjCostCode.Rate = priceListEntity != null ? priceListEntity.Rate : prjCostCode.Rate;
								prjCostCode.FactorCosts = priceListEntity != null ? priceListEntity.FactorCost : prjCostCode.FactorCosts;
								prjCostCode.RealFactorCosts = priceListEntity != null ? priceListEntity.RealFactorCost : prjCostCode.RealFactorCosts;
								prjCostCode.FactorQuantity = priceListEntity != null ? priceListEntity.FactorQuantity : prjCostCode.FactorQuantity;
								prjCostCode.RealFactorQuantity = priceListEntity != null ? priceListEntity.RealFactorQuantity : prjCostCode.RealFactorQuantity;
								prjCostCode.FactorHour = priceListEntity != null ? priceListEntity.FactorHour : prjCostCode.FactorHour;
								prjCostCode.CurrencyFk = priceListEntity != null ? priceListEntity.CurrencyFk : prjCostCode.CurrencyFk;
								prjCostCode.Version = 0;
								prjCostCode.DayWorkRate = priceListEntity != null ? priceListEntity.SalesPrice : prjCostCode.DayWorkRate;
								//prjCostCode.Description = mdcCostCode.DescriptionInfo != null ? mdcCostCode.DescriptionInfo.Translated : prjCostCode.Description;
								//prjCostCode.Description2 = mdcCostCode.Description2Info != null ? mdcCostCode.Description2Info.Translated : prjCostCode.Description2;
							}
							else
							{
								prjCostCode = createdPrjCostCode as PrjCostCodesEntity;
								prjCostCode.CostCodeParentFk = mdcCostCode.CostCodeParentFk;
							}

							prjCostCodes2Save.Add(prjCostCode);

							if (!mdc2PrjCostCodeIdMapping.ContainsKey(mdcCostCode.Id))
							{
								mdc2PrjCostCodeIdMapping.Add(mdcCostCode.Id, prjCostCode.Id);
							}

							//get parent
							if (mdcCostCode.CostCodeParentFk.HasValue)
							{
								if (existingPrjCostCodesMapping.Any(e => e.Key == mdcCostCode.CostCodeParentFk.Value))
								{
									if (!mdc2PrjCostCodeIdMapping.ContainsKey(mdcCostCode.CostCodeParentFk.Value))
									{
										var parentPrjCostCode = existingPrjCostCodesMapping[mdcCostCode.CostCodeParentFk.Value];

										mdc2PrjCostCodeIdMapping.Add(mdcCostCode.CostCodeParentFk.Value, parentPrjCostCode.Id);
									}

									isValid = false;
								}
								else
								{
									item = new Tuple<int, int>(mdcCostCode.CostCodeParentFk.Value, currentJobId);

									isValid = true;
								}
							}
							else
							{
								isValid = false;
							}
						}

					}
					else
					{
						isValid = false;
					}
				}
			}
			if (prjCostCodes2Save.Count > 0)
			{
				foreach (var prjCostCode in prjCostCodes2Save)
				{
					if (prjCostCode == null)
					{
						continue;
					}

					if (prjCostCode.CostCodeParentFk.HasValue)
					{
						prjCostCode.CostCodeParentFk = mdc2PrjCostCodeIdMapping.ContainsKey(prjCostCode.CostCodeParentFk.Value)
								  ? (int?)mdc2PrjCostCodeIdMapping[prjCostCode.CostCodeParentFk.Value]
								  : null;

					}
				}

				prjCostCodes2Save.Reverse(0, prjCostCodes2Save.Count);

				Save(prjCostCodes2Save);
			}
			return prjCostCodes2Save;
		}

		private CostcodePriceListEntity GetMdcPriceByJobAndCostCode(int costCodeId, int jobId)
		{
			var lgmJobLogic = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var lgmJobVersionFk = lgmJobLogic.GetCostCodePriceVersionFkById(jobId);
			var mdcCcPriceList = new BasicsCostCodePriceListLogic().GetSearchList(e => e.CostCodeFk == costCodeId && e.CostcodePriceVerFk == lgmJobVersionFk);

			if (mdcCcPriceList != null && mdcCcPriceList.Any())
			{
				return mdcCcPriceList.FirstOrDefault();
			}
			return null;
		}

		private IEnumerable<CostcodePriceListEntity> GetMdcPriceByJobAndCostCodes(IEnumerable<int> costCodeIds, int jobId)
		{
			var lgmCostcCodePriceLogic = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var lgmCostcCodeVersionFk = lgmCostcCodePriceLogic.GetCostCodePriceVersionFkById(jobId);
			var mdcCcPriceList = new BasicsCostCodePriceListLogic().GetSearchList(e => costCodeIds.Contains(e.CostCodeFk) && e.CostcodePriceVerFk == lgmCostcCodeVersionFk);

			return mdcCcPriceList;
		}

		/// <summary>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// </summary>
		public IEnumerable<PrjCostCodesEntity> GetPrjCostCodesByJob(int projectId, int jobId)
		{
			//get prj costcode list per project and job list
			var prjCostCodesList = this.Get(e => e.ProjectFk == projectId, jobId);
			var costCodes = new BasicsCostCodesLogic().GetListByCompany();
			SetBasCostCode(prjCostCodesList);
			var result = prjCostCodesList;

			if (!prjCostCodesList.Any(e => e.Rate <= 0))
			{
				return result;
			}

			var lgmJobLogic = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var lgmJobs = lgmJobLogic.GetLogisticJobsByProject(projectId);

			var mdcCcPriceList = new BasicsCostCodePriceListLogic().GetList();

			var mdcPrjList = from pcc in prjCostCodesList
								  join cc in costCodes
								  on pcc.MdcCostCodeFk equals cc.Id
								  select new PrjCostCodesEntity
								  {
									  Id = pcc.Id,
									  ProjectFk = pcc.ProjectFk,
									  LgmJobFk = pcc.LgmJobFk,
									  MdcCostCodeFk = pcc.MdcCostCodeFk,
									  DayWorkRate = pcc.DayWorkRate,
									  Rate = pcc.Rate,
									  CurrencyFk = pcc.CurrencyFk,
									  UomFk = pcc.UomFk,
									  FactorCosts = pcc.FactorCosts,
									  RealFactorCosts = pcc.RealFactorCosts,
									  FactorQuantity = pcc.FactorQuantity,
									  RealFactorQuantity = pcc.RealFactorQuantity,
									  FactorHour = pcc.FactorHour,
									  EstCostTypeFk = cc.EstCostTypeFk,
									  IsLabour = pcc.IsLabour,
									  IsRate = pcc.IsRate,
									  IsEditable = pcc.IsEditable,
									  IsDefault = pcc.IsDefault,
									  Remark = pcc.Remark,
									  Version = pcc.Version,
									  BasCostCodeEntity = pcc.BasCostCodeEntity,
									  IsBudget = pcc.IsBudget,
									  IsCost = pcc.IsCost
								  };

			var prjCostCodesEntities = mdcPrjList as IList<PrjCostCodesEntity> ?? mdcPrjList.ToList();
			var items = from mdcprj in prjCostCodesEntities
							let mdcPrjcostCodeFk = mdcprj.MdcCostCodeFk
							where mdcPrjcostCodeFk.HasValue
							join lgmjob in lgmJobs
							on mdcprj.LgmJobFk equals lgmjob.Id
							let costCodePriceVersionFk = lgmjob.CostCodePriceVersionFk
							where costCodePriceVersionFk != null
							join mdcprc in mdcCcPriceList
							on new { mdcCostcodeFk = mdcPrjcostCodeFk.Value, ccPriceVerFk = costCodePriceVersionFk.Value } equals
							new { mdcCostcodeFk = mdcprc.CostCodeFk, ccPriceVerFk = mdcprc.CostcodePriceVerFk }
							join cc in costCodes
							on mdcprj.MdcCostCodeFk equals cc.Id
							select new PrjCostCodesEntity
							{
								Id = mdcprj.Id,
								Rate = mdcprj.Rate > 0 ? mdcprj.Rate : mdcprc.Rate > 0 ? mdcprc.Rate : cc.Rate,
								FactorCosts = mdcprj.FactorCosts > 0 ? mdcprj.FactorCosts : mdcprc.FactorCost > 0 ? mdcprc.FactorCost : cc.FactorCosts,
								RealFactorCosts = mdcprj.RealFactorCosts > 0 ? mdcprj.RealFactorCosts : mdcprc.RealFactorCost > 0 ? mdcprc.RealFactorCost : cc.RealFactorCosts,
								FactorQuantity = mdcprj.FactorQuantity > 0 ? mdcprj.FactorQuantity : mdcprc.FactorQuantity > 0 ? mdcprc.FactorQuantity : cc.FactorQuantity,
								RealFactorQuantity = mdcprj.RealFactorQuantity > 0 ? mdcprj.RealFactorQuantity : mdcprc.RealFactorQuantity > 0 ? mdcprc.RealFactorQuantity : cc.RealFactorQuantity,
								FactorHour = mdcprj.FactorHour > 0 ? mdcprj.FactorHour : mdcprc.FactorHour > 0 ? mdcprc.FactorHour : cc.FactorHour,
								CurrencyFk = mdcprj.Rate > 0 ? mdcprj.CurrencyFk : mdcprc.Rate > 0 ? mdcprc.CurrencyFk : cc.CurrencyFk,
								DayWorkRate = mdcprj.DayWorkRate > 0 ? mdcprj.DayWorkRate : mdcprc.SalesPrice > 0 ? mdcprc.SalesPrice : cc.DayWorkRate
							};

			if (items.Distinct().Any())
			{
				foreach (var mdcprj in prjCostCodesEntities)
				{
					var item = items.FirstOrDefault(i => mdcprj != null && i.Id == mdcprj.Id);
					if (item != null)
					{
						mdcprj.Rate = item.Rate;
						mdcprj.FactorCosts = item.FactorCosts;
						mdcprj.RealFactorCosts = item.RealFactorCosts;
						mdcprj.FactorQuantity = item.FactorQuantity;
						mdcprj.RealFactorQuantity = item.RealFactorQuantity;
						mdcprj.FactorHour = item.FactorHour;
						mdcprj.CurrencyFk = item.CurrencyFk;
						mdcprj.DayWorkRate = item.DayWorkRate;
					}
				}
			}
			result = prjCostCodesEntities.Distinct();
			return result;
		}

		/// <summary>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// </summary>
		public IEnumerable<CostCodeEntity> GetMdcPrjCostCodesByJob_Prev(int projectId, int? jobId)
		{
			//get prj costcode list per project and job list
			jobId = jobId ?? GetDefaultLogisticJobIdByProject(projectId);

			var costCodes = new BasicsCostCodesLogic().GetEstCostCodeListByCompany();//filter by cost code type

			var lgmJobLogic = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();

			var lgmJobs = lgmJobLogic.GetLogisticJobsByProject(projectId);

			var mdcCcPriceList = new BasicsCostCodePriceListLogic().GetList();

			var costCodeEntities = costCodes as IList<CostCodeEntity> ?? costCodes.ToList();

			var ccwithpricelist = from cc in costCodeEntities
										 join lgmjob in lgmJobs
										 on jobId equals lgmjob.Id
										 let costCodePriceVersionFk = lgmjob.CostCodePriceVersionFk
										 where costCodePriceVersionFk != null
										 join mdcprc in mdcCcPriceList
										 on new { mdcCostcodeFk = cc.Id, ccPriceVerFk = (int)costCodePriceVersionFk } equals
										 new { mdcCostcodeFk = mdcprc.CostCodeFk, ccPriceVerFk = mdcprc.CostcodePriceVerFk }
										 select new CostCodeEntity()
										 {
											 Id = cc.Id,
											 Rate = mdcprc != null ? mdcprc.Rate : cc.Rate,
											 FactorCosts = mdcprc != null ? mdcprc.FactorCost : cc.FactorCosts,
											 RealFactorCosts = mdcprc != null ? mdcprc.RealFactorCost : cc.RealFactorCosts,
											 FactorQuantity = mdcprc != null ? mdcprc.FactorQuantity : cc.FactorQuantity,
											 RealFactorQuantity = mdcprc != null ? mdcprc.RealFactorQuantity : cc.RealFactorQuantity,
											 FactorHour = mdcprc != null ? mdcprc.FactorHour : cc.FactorHour,
											 CurrencyFk = mdcprc != null ? mdcprc.CurrencyFk : cc.CurrencyFk,
											 DayWorkRate = mdcprc != null ? mdcprc.SalesPrice : cc.DayWorkRate
										 };

			var codeEntities = ccwithpricelist as IList<CostCodeEntity> ?? ccwithpricelist.ToList();

			if (codeEntities.Distinct().Any())
			{
				foreach (var cEntity in costCodeEntities)
				{
					var item = codeEntities.FirstOrDefault(i => cEntity != null && i.Id == cEntity.Id);
					if (item != null)
					{
						cEntity.Rate = item.Rate;
						cEntity.FactorCosts = item.FactorCosts;
						cEntity.RealFactorCosts = item.RealFactorCosts;
						cEntity.FactorQuantity = item.FactorQuantity;
						cEntity.RealFactorQuantity = item.RealFactorQuantity;
						cEntity.FactorHour = item.FactorHour;
						cEntity.CurrencyFk = item.CurrencyFk;
						cEntity.DayWorkRate = item.DayWorkRate;
					}
				}
			}
			var prjCostCodesEntities = this.Get(e => e.ProjectFk == projectId && e.LgmJobFk == jobId && e.MdcCostCodeFk.HasValue);//todo..
			if (!prjCostCodesEntities.Any())
			{
				return costCodeEntities;
			}

			var prjCostCodeWithRate = from prjcc in prjCostCodesEntities
											  let costCodeFk = prjcc.MdcCostCodeFk
											  where costCodeFk.HasValue
											  join lgmjob in lgmJobs
											  on prjcc.LgmJobFk equals lgmjob.Id //todo..
											  let costCodePriceVersionFk = lgmjob.CostCodePriceVersionFk
											  where costCodePriceVersionFk.HasValue
											  join mdcprc in mdcCcPriceList
											  on new { mdcCostcodeFk = costCodeFk.Value, ccPriceVerFk = costCodePriceVersionFk.Value } equals
											  new { mdcCostcodeFk = mdcprc.CostCodeFk, ccPriceVerFk = mdcprc.CostcodePriceVerFk }
											  join cc in costCodes
											  on costCodeFk equals cc.Id
											  select new PrjCostCodesEntity
											  {
												  Id = prjcc.Id,
												  Rate = prjcc != null ? prjcc.Rate : mdcprc != null ? mdcprc.Rate : cc.Rate,
												  FactorCosts = prjcc != null ? prjcc.FactorCosts : mdcprc != null ? mdcprc.FactorCost : cc.FactorCosts,
												  RealFactorCosts = prjcc != null ? prjcc.RealFactorCosts : mdcprc != null ? mdcprc.RealFactorCost : cc.RealFactorCosts,
												  FactorQuantity = prjcc != null ? prjcc.FactorQuantity : mdcprc != null ? mdcprc.FactorQuantity : cc.FactorQuantity,
												  RealFactorQuantity = prjcc != null ? prjcc.RealFactorQuantity : mdcprc != null ? mdcprc.RealFactorQuantity : cc.RealFactorQuantity,
												  FactorHour = prjcc != null ? prjcc.FactorHour : mdcprc != null ? mdcprc.FactorHour : cc.FactorHour,
												  CurrencyFk = prjcc != null ? prjcc.CurrencyFk : mdcprc != null ? mdcprc.CurrencyFk : cc.CurrencyFk,
												  DayWorkRate = prjcc != null ? prjcc.DayWorkRate : mdcprc != null ? mdcprc.SalesPrice : cc.DayWorkRate
											  };

			if (prjCostCodeWithRate.Distinct().Any())
			{
				foreach (var prjcc in prjCostCodesEntities)
				{
					var item = prjCostCodeWithRate.FirstOrDefault(i => prjcc != null && i.Id == prjcc.Id);
					if (item != null)
					{
						prjcc.Rate = item.Rate;
						prjcc.FactorCosts = item.FactorCosts;
						prjcc.RealFactorCosts = item.RealFactorCosts;
						prjcc.FactorQuantity = item.FactorQuantity;
						prjcc.RealFactorQuantity = item.RealFactorQuantity;
						prjcc.FactorHour = item.FactorHour;
						prjcc.CurrencyFk = item.CurrencyFk;
						prjcc.DayWorkRate = item.DayWorkRate;
					}
				}
			}

			var result = new List<CostCodeEntity>();

			//replace mdc cost code with project costcode values
			foreach (var cc in costCodeEntities)
			{
				var matchPrj = prjCostCodesEntities.FirstOrDefault(i => cc != null && i.MdcCostCodeFk == cc.Id);

				if (matchPrj == null) { continue; }

				cc.Rate = matchPrj.Rate;
				cc.DayWorkRate = matchPrj.DayWorkRate;
				cc.FactorCosts = matchPrj.FactorCosts;
				cc.RealFactorCosts = matchPrj.RealFactorCosts;
				cc.FactorQuantity = matchPrj.FactorQuantity;
				cc.RealFactorQuantity = matchPrj.RealFactorQuantity;
				cc.FactorHour = matchPrj.FactorHour;
				cc.UomFk = matchPrj.UomFk;
				cc.CurrencyFk = matchPrj.CurrencyFk;
				cc.IsLabour = matchPrj.IsLabour;
				cc.IsRate = matchPrj.IsRate;
				cc.IsEditable = matchPrj.IsEditable;
				cc.EstCostTypeFk = matchPrj.EstCostTypeFk;
				cc.FactorHour = matchPrj.FactorHour;
				cc.IsBudget = matchPrj.IsBudget;
				cc.IsCost = matchPrj.IsCost;
				cc.DescriptionInfo = matchPrj.DescriptionInfo;
				cc.Description2Info = matchPrj.Description2Info;
			}

			//get project costcodes without master costcode(MdcCostCodeFk = null)
			var onlyProjectCostCodes = GetOnlyPrjCostCodesForEstLookup_Prev(projectId, jobId);//todo ..

			result.AddRange(onlyProjectCostCodes);

			result.AddRange(costCodeEntities);

			var estResourceUpdateHelper = Injector.Get<IEstResourceUpdateHelper>();

			foreach (var item in result)
			{
				item.HourUnit = item.IsLabour ? (item.UomFk.HasValue ? estResourceUpdateHelper.GetUomConversionFactor(item.UomFk.Value) : 1) : 0;
			}
			return result;
		}

		/// <summary>
		/// Returns Estimate(Project/Master) item for given project id and costcode id
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="costcodeId"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		public CostCodeEntity GetMdcPrjEstCostCodeById(int projectId, int costcodeId, int? jobId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				jobId = jobId ?? GetDefaultLogisticJobIdByProject(projectId);

				var projectCostCode = Get(s => s.ProjectFk == projectId && s.MdcCostCodeFk == costcodeId, jobId).FirstOrDefault();

				var baseCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICostCodesInfoProviderLogic>();
				var baseCostCode = baseCostCodeLogic.GetCostCodesById(costcodeId, true);
				var entity = new RIB.Visual.Basics.CostCodes.BusinessComponents.CostCodeEntity();

				if (baseCostCode != null)
				{
					entity.Id = baseCostCode.Id;
					entity.Code = baseCostCode.Code;
					entity.DescriptionInfo = baseCostCode.DescriptionInfo;
					entity.Rate = baseCostCode.Rate;
					entity.UomFk = baseCostCode.UomFk;
					entity.CurrencyFk = baseCostCode.CurrencyFk;
					entity.FactorQuantity = baseCostCode.FactorQuantity;
					entity.FactorCosts = baseCostCode.FactorCosts;
					entity.RealFactorQuantity = baseCostCode.RealFactorQuantity;
					entity.RealFactorCosts = baseCostCode.RealFactorCosts;
					entity.FactorHour = baseCostCode.FactorHour;
					entity.IsLabour = baseCostCode.IsLabour;
					entity.IsRate = baseCostCode.IsRate;
					entity.IsEditable = baseCostCode.IsEditable;
					entity.DayWorkRate = baseCostCode.DayWorkRate;
					entity.EstCostTypeFk = baseCostCode.EstCostTypeFk;
					entity.IsInformation = baseCostCode.IsInformation;
					entity.UserDefinedcolValEntity = baseCostCode.UserDefinedcolValEntity;
					entity.Co2Project = baseCostCode.Co2Project;
					entity.Co2Source = baseCostCode.Co2Source;
					//entity.IsEstimateCostCode = baseCostCode.IsEstimateCostCode;
					//entity.IsRuleMarkupCostCode = baseCostCode.IsRuleMarkupCostCode;
				}

				if (projectCostCode != null)
				{
					if (projectCostCode.LgmJobFk != null)
					{
						entity.Rate = projectCostCode.Rate;
						entity.IsRate = projectCostCode.IsRate;

						entity.CurrencyFk = projectCostCode.CurrencyFk;
						entity.DayWorkRate = projectCostCode.DayWorkRate;
					}
					if (projectCostCode.UomFk != null)
					{
						entity.UomFk = (int)projectCostCode.UomFk;
					}
					entity.FactorQuantity = projectCostCode.FactorQuantity;
					entity.FactorCosts = projectCostCode.FactorCosts;
					entity.RealFactorQuantity = projectCostCode.RealFactorQuantity;
					entity.RealFactorCosts = projectCostCode.RealFactorCosts;
					entity.FactorHour = projectCostCode.FactorHour;
					entity.IsLabour = projectCostCode.IsLabour;
					entity.Co2Project = projectCostCode.Co2Project;
					entity.Co2Source = projectCostCode.Co2Source;
					//to do get from costcodejobrate entity
				}
				else
				{
					var priceListEntity = GetMdcPriceByJobAndCostCode(costcodeId, jobId.Value);
					entity.Rate = priceListEntity != null ? priceListEntity.Rate : entity.Rate;
					entity.FactorCosts = priceListEntity != null ? priceListEntity.FactorCost : entity.FactorCosts;
					entity.RealFactorCosts = priceListEntity != null ? priceListEntity.RealFactorCost : entity.RealFactorCosts;
					entity.FactorQuantity = priceListEntity != null ? priceListEntity.FactorQuantity : entity.FactorQuantity;
					entity.RealFactorQuantity = priceListEntity != null ? priceListEntity.RealFactorQuantity : entity.RealFactorQuantity;
					entity.FactorHour = priceListEntity != null ? priceListEntity.FactorHour : entity.FactorHour;
					entity.CurrencyFk = priceListEntity != null ? priceListEntity.CurrencyFk : null;
					entity.DayWorkRate = priceListEntity != null ? priceListEntity.SalesPrice : entity.DayWorkRate;
				}

				return entity;
			}
		}

		/// <summary>
		/// Returns Estimate(Project/Master) items for given project id and costcodes ids
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="costCodeIds"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		public IEnumerable<CostCodeEntity> GetMdcPrjEstCostCodesByIds(int projectId, IEnumerable<int> costCodeIds, int? jobId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				jobId = jobId ?? GetDefaultLogisticJobIdByProject(projectId);

				var projectCostCodes = costCodeIds != null ? Get(s => s.ProjectFk == projectId && s.MdcCostCodeFk.HasValue && costCodeIds.Contains(s.MdcCostCodeFk.Value), jobId) : null;

				var baseCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICostCodesInfoProviderLogic>();
				var baseCostCodes = baseCostCodeLogic.GetCostCodesByIds(costCodeIds);
				var entities = new List<RIB.Visual.Basics.CostCodes.BusinessComponents.CostCodeEntity>();
				var priceListEntities = new List<CostcodePriceListEntity>();

				if (baseCostCodes != null && baseCostCodes.Any())
				{
					foreach (var baseCostCode in baseCostCodes)
					{
						var entity = new RIB.Visual.Basics.CostCodes.BusinessComponents.CostCodeEntity();
						entity.Id = baseCostCode.Id;
						entity.Code = baseCostCode.Code;
						entity.DescriptionInfo = baseCostCode.DescriptionInfo;
						entity.Rate = baseCostCode.Rate;
						entity.UomFk = baseCostCode.UomFk;
						entity.CurrencyFk = baseCostCode.CurrencyFk;
						entity.FactorQuantity = baseCostCode.FactorQuantity;
						entity.FactorCosts = baseCostCode.FactorCosts;
						entity.RealFactorQuantity = baseCostCode.RealFactorQuantity;
						entity.RealFactorCosts = baseCostCode.RealFactorCosts;
						entity.FactorHour = baseCostCode.FactorHour;
						entity.IsLabour = baseCostCode.IsLabour;
						entity.IsRate = baseCostCode.IsRate;
						entity.IsEditable = baseCostCode.IsEditable;
						entity.DayWorkRate = baseCostCode.DayWorkRate;
						entity.EstCostTypeFk = baseCostCode.EstCostTypeFk;
						bool isExistInPrj = false;
						if (projectCostCodes != null && projectCostCodes.Any())
						{
							foreach (var projectCostCode in projectCostCodes)
							{
								if (projectCostCode.MdcCostCodeFk == entity.Id)
								{
									isExistInPrj = true;
									if (projectCostCode.LgmJobFk != null)
									{
										entity.Rate = projectCostCode.Rate;
										entity.IsRate = projectCostCode.IsRate;

										entity.CurrencyFk = projectCostCode.CurrencyFk;
										entity.DayWorkRate = projectCostCode.DayWorkRate;
									}
									if (projectCostCode.UomFk != null)
									{
										entity.UomFk = (int)projectCostCode.UomFk;
									}
									entity.FactorQuantity = projectCostCode.FactorQuantity;
									entity.FactorCosts = projectCostCode.FactorCosts;
									entity.RealFactorQuantity = projectCostCode.RealFactorQuantity;
									entity.RealFactorCosts = projectCostCode.RealFactorCosts;
									entity.FactorHour = projectCostCode.FactorHour;
									entity.IsLabour = projectCostCode.IsLabour;
								}
							}
						}

						if (!isExistInPrj)
						{
							priceListEntities = priceListEntities.Count > 0 ? priceListEntities : GetMdcPriceByJobAndCostCodes(costCodeIds, jobId.Value).ToList();
							foreach (var priceListEntity in priceListEntities)
							{
								if (priceListEntity.CostCodeFk == entity.Id)
								{
									entity.Rate = priceListEntity != null ? priceListEntity.Rate : entity.Rate;
									entity.FactorCosts = priceListEntity != null ? priceListEntity.FactorCost : entity.FactorCosts;
									entity.RealFactorCosts = priceListEntity != null ? priceListEntity.RealFactorCost : entity.RealFactorCosts;
									entity.FactorQuantity = priceListEntity != null ? priceListEntity.FactorQuantity : entity.FactorQuantity;
									entity.RealFactorQuantity = priceListEntity != null ? priceListEntity.RealFactorQuantity : entity.RealFactorQuantity;
									entity.FactorHour = priceListEntity != null ? priceListEntity.FactorHour : entity.FactorHour;
									entity.CurrencyFk = priceListEntity != null ? priceListEntity.CurrencyFk : null;
									entity.DayWorkRate = priceListEntity != null ? priceListEntity.SalesPrice : entity.DayWorkRate;
								}
							}
						}

						entities.Add(entity);
					}
				}

				return entities;
			}
		}

		/// <summary>
		/// Returns Estimate(Project/Master) item for given project id and costcode code
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="code"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		public CostCodeEntity GetMdcPrjEstCostCodeByCode(int projectId, string code, int? jobId)
		{
			jobId = jobId ?? GetDefaultLogisticJobIdByProject(projectId);

			return GetMdcPrjCostCodesByJob(projectId, (int)jobId).FirstOrDefault(e => e.Code.ToUpper() == code.ToUpper());
		}
		/// <summary>
		/// Return newly created project cost code
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public PrjCostCodesEntity CreateNewPrjCostCodeFromLookup(PrjCostCodeLookupCreationData data)
		{
			var ProjectCostCodeCreator = new ProjectCostCodeCreator();
			var jobId = data.JobFk.HasValue ? data.JobFk.Value : 0;
			var mdcCostCodeID = data.MdcCostCodeId;
			var result = ProjectCostCodeCreator.CreateProjectCostCodeByLookup(data.ProjectId, jobId, mdcCostCodeID, data.ParentCostCode, data.IsProjectCostCode);
			return (PrjCostCodesEntity)result;
		}

		/// <summary>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// </summary>
		public IEnumerable<IProjectCostCodesEntity> GetEstLookupPrjCostCodes(int projectId, int jobId)
		{
			return GetPrjCostCodesByJob(projectId, jobId);
		}

		/// <summary>
		/// Save costcodes per Project and Job from different project 
		/// </summary>
		/// <param name="costCodeDetailsToCopy"></param>
		/// <param name="targetPrjId"></param>
		/// <param name="targetJobId"></param>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> CopyPrjCostCodes(List<Tuple<int, int, int>> costCodeDetailsToCopy, int targetPrjId, int targetJobId)
		{
			var prjCostCodes2Save = new List<PrjCostCodesEntity>();

			if (costCodeDetailsToCopy != null && (costCodeDetailsToCopy.Any() && targetPrjId > 0 && targetJobId > 0))
			{
				var mdcCostCodeIds = new List<int>();

				foreach (var ccDic in costCodeDetailsToCopy)
				{
					var sourcePrjcc = Get(e => e.ProjectFk == ccDic.Item1 && e.LgmJobFk == ccDic.Item2 && e.MdcCostCodeFk == ccDic.Item3);
					if (sourcePrjcc == null || !sourcePrjcc.Any())
					{
						mdcCostCodeIds.Add(ccDic.Item3);
					}
					else
					{
						var matchEntity = Get(s => s.ProjectFk == targetPrjId && s.MdcCostCodeFk == ccDic.Item3 && s.LgmJobFk == targetJobId).FirstOrDefault();
						var matchSaveEntity = prjCostCodes2Save.FirstOrDefault(s => s.ProjectFk == targetPrjId && s.MdcCostCodeFk == ccDic.Item3 && s.LgmJobFk == targetJobId);
						if (matchSaveEntity != null)
						{
							continue;
						}

						if (matchEntity != null)
						{
							var source = sourcePrjcc.First();
							matchEntity.CurrencyFk = source.CurrencyFk;
							matchEntity.Contribution = source.Contribution;
							matchEntity.DayWorkRate = source.DayWorkRate;
							matchEntity.EstCostTypeFk = source.EstCostTypeFk;
							matchEntity.Extra = source.Extra;
							matchEntity.FactorCosts = source.FactorCosts;
							matchEntity.FactorHour = source.FactorHour;
							matchEntity.FactorQuantity = source.FactorQuantity;
							matchEntity.HourUnit = source.HourUnit;
							matchEntity.IsDefault = source.IsDefault;
							matchEntity.Rate = source.Rate;
							matchEntity.RealFactorCosts = source.RealFactorCosts;
							matchEntity.IsRate = source.IsRate;
							matchEntity.IsEditable = source.IsEditable;
							matchEntity.IsLabour = source.IsLabour;
							matchEntity.UomFk = source.UomFk;
							matchEntity.RealFactorQuantity = source.RealFactorQuantity;
							matchEntity.Surcharge = source.Surcharge;
						}
						else
						{
							var clone = (PrjCostCodesEntity)sourcePrjcc.First().Clone();
							clone = Create(clone);
							clone.ProjectFk = targetPrjId;
							clone.LgmJobFk = targetJobId;
							clone.MdcCostCodeFk = ccDic.Item3;
							clone.Version = 0;
							clone.UpdatedAt = null;
							clone.UpdatedBy = null;
							clone.InsertedAt = new DateTime();
							clone.InsertedBy = 0;
							matchEntity = clone;
						}
						prjCostCodes2Save.Add(matchEntity);
					}
				}

				if (mdcCostCodeIds.Any())
				{
					var mdcCostCodeList = new BasicsCostCodesLogic().GetItemsByKey(mdcCostCodeIds).ToList();

					foreach (var mdcCc in mdcCostCodeList)
					{
						if (mdcCc == null)
						{
							continue;
						}

						var matchEntity = Get(s => s.ProjectFk == targetPrjId && s.MdcCostCodeFk == mdcCc.Id && s.LgmJobFk == targetJobId).FirstOrDefault();
						var matchSaveEntity = prjCostCodes2Save.FirstOrDefault(s => s.ProjectFk == targetPrjId && s.MdcCostCodeFk == mdcCc.Id && s.LgmJobFk == targetJobId);
						if (matchEntity != null || matchSaveEntity != null)
						{
							continue;
						}

						var prjCostCode = new PrjCostCodesEntity();
						CopyProperties(mdcCc, prjCostCode);
						prjCostCode = Create(prjCostCode);
						prjCostCode.ProjectFk = targetPrjId;
						prjCostCode.MdcCostCodeFk = mdcCc.Id;
						prjCostCode.LgmJobFk = targetJobId;
						prjCostCode.Rate = mdcCc.Rate;
						prjCostCode.Version = 0;
						prjCostCode.UpdatedAt = null;
						prjCostCode.UpdatedBy = null;
						prjCostCode.InsertedAt = new DateTime();
						prjCostCode.InsertedBy = 0;
						prjCostCode.DayWorkRate = mdcCc.DayWorkRate;
						prjCostCodes2Save.Add(prjCostCode);
					}
				}
				if (!prjCostCodes2Save.Any())
				{
					return prjCostCodes2Save;
				}

				var distinctPrjCostCodes = prjCostCodes2Save.GroupBy(p => new { p.ProjectFk, p.LgmJobFk, p.MdcCostCodeFk }).Select(g => g.First()).ToList();
				Save(distinctPrjCostCodes);
			}
			return prjCostCodes2Save;
		}

		/// <summary>
		/// Get all jobs with same cost code
		/// </summary>
		/// <param name="costCodeFk"></param>
		/// <param name="projectFk"></param>
		/// <returns></returns>
		public IEnumerable<int> GetJobFksWithSameCostCode(int costCodeFk, int projectFk)
		{
			//return
			//	 Get(x => x.ProjectFk == projectFk && x.MdcCostCodeFk == costCodeFk).Where(x => x.LgmJobFk != null)
			//		  .Select(x => x.LgmJobFk.Value)
			//		  .ToList();

			var prjCostCodeIds = Get(x => x.MdcCostCodeFk.HasValue && (x.ProjectFk == projectFk && x.MdcCostCodeFk == costCodeFk)).Select(e => e.Id);

			return new ProjectCostCodesJobRateLogic().GetByFilter(e => prjCostCodeIds.Contains(e.ProjectCostCodeFk) && e.LgmJobFk.HasValue)
					  .Select(x => x.LgmJobFk.Value)
					  .ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="mdcCostCodeFk"></param>
		/// <param name="projectFk"></param>
		/// <param name="jobFk"></param>
		/// <returns></returns>
		public IProjectCostCodesEntity GetByMdcFkProjectFkJobFk(int mdcCostCodeFk, int projectFk, int jobFk)
		{
			return Get(x => x.ProjectFk == projectFk && x.MdcCostCodeFk == mdcCostCodeFk, jobFk).FirstOrDefault();
		}

		/// <summary>
		/// Get All CostCodes In This Project(with Different Job)
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="includeJobRates"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetAllCostCodesInProject(int projectId, bool includeJobRates = false)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var prjCostCodes = dbcontext.Entities<PrjCostCodesEntity>().Where(i => i.ProjectFk == projectId).ToList();

				if (includeJobRates)
				{
					var jobsOfProject = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>().GetLogisticJobsByProject(projectId);

					if (jobsOfProject != null && jobsOfProject.Any())
					{
						var jobIds = jobsOfProject.Select(e => e.Id).ToList();

						var jobRates = new ProjectCostCodesJobRateLogic().GetListByFilter(e => e.LgmJobFk.HasValue && jobIds.Contains(e.LgmJobFk.Value));

						foreach (var prjCostCode in prjCostCodes)
						{
							prjCostCode.JobRates = jobRates.Where(e => e.ProjectCostCodeFk == prjCostCode.Id).OfType<IProjectCostCodesJobRateEntity>().ToList();
						}
					}
				}

				return prjCostCodes;
			}
		}

		/// <summary>
		/// set BasCostCodeEntity for a given project cost code
		/// </summary>
		/// <returns></returns>
		private IEnumerable<PrjCostCodesEntity> FilterByCostCodeType(IEnumerable<PrjCostCodesEntity> entities)
		{
			var prjCostCodesEntities = entities.ToList();

			if (!prjCostCodesEntities.Any())
			{
				return prjCostCodesEntities;
			}

			var costCodelogic = new BasicsCostCodesLogic();

			var costcodes = costCodelogic.GetEstimateCostCodes(prjCostCodesEntities.Where(x => x.MdcCostCodeFk.HasValue).Select(x => x.MdcCostCodeFk.Value)).Select(x => x as CostCodeEntity).ToList();
			if (!costcodes.Any())
			{
				return prjCostCodesEntities;
			}

			ConcurrentBag<PrjCostCodesEntity> prjCostCodes = new ConcurrentBag<PrjCostCodesEntity>(prjCostCodesEntities);

			ConcurrentBag<CostCodeEntity> mdcCostCodes = new ConcurrentBag<CostCodeEntity>(costcodes);

			var result = new ConcurrentBag<PrjCostCodesEntity>();

			Parallel.ForEach(prjCostCodes, prjCc =>
			{
				var mdcCostCodeFk = prjCc.MdcCostCodeFk;

				prjCc.BasCostCodeEntity = mdcCostCodes.FirstOrDefault(e => e.Id == mdcCostCodeFk);

				if (prjCc.BasCostCodeEntity != null && prjCc.BasCostCodeEntity.CostCodeTypeFk.HasValue)
				{
					prjCc.IsEstimateCostCode = prjCc.BasCostCodeEntity.IsEstimateCostCode;
					prjCc.IsRuleMarkupCostCode = prjCc.BasCostCodeEntity.IsRuleMarkupCostCode;
					prjCc.IsInformation = prjCc.BasCostCodeEntity.IsInformation;

					result.Add(prjCc);
				}
			});

			return new List<PrjCostCodesEntity>(result);
		}

		/// <summary>
		/// Creates a project cost codes entity, provides a unique db ID...
		/// </summary>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> CreateBulkProjectCostCodes(IDictionary<int, List<int>> mdcCostCodeId2JobIdMap, int prjId)
		{
			var entitities = new List<PrjCostCodesEntity>();

			if (mdcCostCodeId2JobIdMap == null || !mdcCostCodeId2JobIdMap.Any())
			{
				return entitities;
			}

			/* get the project costcode in database */
			var existingCostCodeId2JobIdMap = GetProjectCostCodes(prjId).Where(e => e.LgmJobFk.HasValue).GroupBy(e => e.MdcCostCodeFk).ToDictionary(e => e.Key, e => e.Select(i => i.LgmJobFk).Distinct().ToList());

			var ids2Create = new Dictionary<int, List<int>>();

			foreach (var pair in mdcCostCodeId2JobIdMap)
			{
				var jobIdsToSave = pair.Value.Distinct().ToList();

				if (existingCostCodeId2JobIdMap.Any() && existingCostCodeId2JobIdMap.ContainsKey(pair.Key))
				{
					var existJobIds = existingCostCodeId2JobIdMap[pair.Key];

					jobIdsToSave.RemoveAll(e => existJobIds.Contains(e));
				}

				if (jobIdsToSave.Any())
				{
					ids2Create.Add(pair.Key, jobIdsToSave);
				}

			}

			if (!ids2Create.Any())
			{
				return entitities;
			}

			var totalJobIds = ids2Create.Values.Where(e => e != null && e.Any()).SelectMany(i => i).ToList();

			var ids = new Stack<int>(this.SequenceManager.GetNextList("PRJ_PROJECT2MDC_CST_CDE", totalJobIds.Count));

			foreach (var pair in ids2Create)
			{
				var jobIdsToSave = pair.Value.Distinct().ToList();

				foreach (var jobId in jobIdsToSave)
				{
					var entity = new PrjCostCodesEntity();

					entity.Id = ids.Pop();

					entity.ProjectFk = prjId;

					entity.MdcCostCodeFk = pair.Key;

					entity.LgmJobFk = jobId;

					if (!entitities.Any(e => e.ProjectFk == prjId && e.MdcCostCodeFk == pair.Key && e.LgmJobFk == jobId))
					{
						entitities.Add(entity);
					}
				}
			}
			return entitities;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="startId"></param>
		/// <param name="depth"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<PrjCostCodesEntity> GetTree(int startId, int depth, int projectId)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbContext.Entities<PrjCostCodesEntity>()
					 .Include(e => e.PrjCostCodeParent)
					 .Where(e => e.ProjectFk == projectId)
					 .OrderBy(e => e.Code)
					 .AsQueryable()
					 .ToList();

				HandleEntities(entities, projectId);

				/* filter by parent */
				entities = entities.Where(i => startId == 0 ? i.CostCodeParentFk == null : i.CostCodeParentFk == startId).ToList();

				TranslateChildren(entities);

				return entities;
			}
		}

		private void TranslateChildren(IEnumerable<PrjCostCodesEntity> items)
		{
			foreach (var item in items)
			{				
				item.Translate(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });				
				item.Translate(this.UserLanguageId, new Func<PrjCostCodesEntity, DescriptionTranslateType>[] { e => e.Description2Info });				

				if (item.PrjCostCodeChildren != null && item.PrjCostCodeChildren.Any())
				{
					TranslateChildren(item.PrjCostCodeChildren);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="setMdcCostCodeFkToPrjCostCode"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetListWithMdcCostCodeFk(int projectId, bool setMdcCostCodeFkToPrjCostCode = true)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = new Dictionary<int, int?>();

				var entities = dbContext.Entities<PrjCostCodesEntity>()
					 .Include(e => e.PrjCostCodeParent)
					 .Where(e => e.ProjectFk == projectId).AsQueryable()
					 .ToList();

				if (setMdcCostCodeFkToPrjCostCode)
				{
					var firstLevelOfEntities = entities.Where(e => !e.CostCodeParentFk.HasValue).ToList();

					foreach (var item in firstLevelOfEntities)
					{
						if (item.PrjCostCodeChildren != null && item.PrjCostCodeChildren.Any())
						{
							foreach (var child in item.PrjCostCodeChildren)
							{
								SetMdcCostCodeFkFromPrjCostCodeFk(child, item.MdcCostCodeFk);
							}
						}
					}
				}

				return entities;
			}
		}

		private void SetMdcCostCodeFkFromPrjCostCodeFk(PrjCostCodesEntity prjCostCode, int? parentMdcCostCodeFk)
		{
			if (prjCostCode == null)
			{
				return;
			}

			prjCostCode.MdcCostCodeFk = prjCostCode.MdcCostCodeFk.HasValue ? prjCostCode.MdcCostCodeFk : parentMdcCostCodeFk;

			if (prjCostCode.PrjCostCodeChildren != null && prjCostCode.PrjCostCodeChildren.Any())
			{
				foreach (var child in prjCostCode.PrjCostCodeChildren)
				{
					SetMdcCostCodeFkFromPrjCostCodeFk(child, prjCostCode.MdcCostCodeFk);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public CostCodeEntity GetEstCostCodeByFilter(PrjMdcCostCodeReadData data)
		{
			if (data == null)
			{
				return new CostCodeEntity();
			}

			var projectId = data.ProjectId;

			var jobId = data.JobId ?? GetDefaultLogisticJobIdByProject(projectId);

			var costCodeId = data.CurrentCostCodeId;

			if (data.HasMdcCostCode)
			{
				var costCode = GetMdcPrjEstCostCodeById(projectId, costCodeId, jobId);

				var projectCostCode = this.GetList(projectId, costCodeId, jobId, costCode.Code);
				if (projectCostCode != null && projectCostCode.Any())
				{
					costCode.IsBudget = projectCostCode.FirstOrDefault().IsBudget;
					costCode.IsCost = projectCostCode.FirstOrDefault().IsCost;
				}

				return costCode;

			}
			else
			{
				//var projectCostCode = Get(s => s.ProjectFk == projectId && s.Code.Equals(data.Code, StringComparison.CurrentCultureIgnoreCase), jobId).FirstOrDefault();
				var projectCostCode = Get(s => s.ProjectFk == projectId && s.Id == data.CurrentCostCodeId, jobId).FirstOrDefault();

				var entity = new RIB.Visual.Basics.CostCodes.BusinessComponents.CostCodeEntity();

				if (projectCostCode == null)
				{
					return entity;
				}

				var sourcePropertiesDic = projectCostCode.GetType().GetProperties().ToDictionary(e => e.Name, e => e.GetValue(projectCostCode));

				entity.CopyPropertyFrom(sourcePropertiesDic);

				entity.Id = projectCostCode.Id;

				return entity;
			}
		}

		/// <summary>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// </summary>
		public IEnumerable<CostCodeEntity> GetOnlyPrjCostCodesForEstLookup_Prev(int projectId, int? jobId)
		{
			//get prj costcode list per project and job list
			jobId = jobId ?? GetDefaultLogisticJobIdByProject(projectId);

			var prjCostCodesList = this.Get(e => e.ProjectFk == projectId && e.LgmJobFk == jobId);

			var prjCostCodesWithoutMdcCc = this.Get(e => e.ProjectFk == projectId && e.LgmJobFk == jobId && !e.MdcCostCodeFk.HasValue);

			var result = new List<CostCodeEntity>();

			//get project costcodes (MdcCostCodeFk = null)
			foreach (var prjCc in prjCostCodesWithoutMdcCc)
			{
				if (prjCc == null)
				{
					continue;
				}

				var newItem = new CostCodeEntity();

				var sourcePropertiesDic = prjCc.GetType().GetProperties().ToDictionary(e => e.Name, e => e.GetValue(prjCc));

				newItem.CopyPropertyFrom(sourcePropertiesDic);

				newItem.Id = prjCc.Id;

				newItem.IsOnlyProjectCostCode = true;

				newItem.CostCodeParentFk = GetParentMdcCostCodeId(prjCc, prjCostCodesList);

				newItem.DescriptionInfo = new DescriptionTranslateType(prjCc.DescriptionInfo.Description, prjCc.DescriptionInfo.Translated);

				newItem.Description2Info = new DescriptionTranslateType(prjCc.Description2Info.Description, prjCc.Description2Info.Translated);

				result.Add(newItem);
			}

			return result;
		}


		/// <summary>
		/// <param name="child"></param>
		/// <param name="allPrjCostCodes"></param>
		/// </summary>
		public int? GetParentMdcCostCodeId(PrjCostCodesEntity child, IEnumerable<PrjCostCodesEntity> allPrjCostCodes)
		{
			if (child == null || allPrjCostCodes == null || !allPrjCostCodes.Any())
			{
				return null;
			}

			var currentEntity = child;

			while (currentEntity != null && currentEntity.CostCodeParentFk.HasValue && currentEntity.MdcCostCodeFk == null)
			{
				var parent = allPrjCostCodes.FirstOrDefault(e => e.Id == currentEntity.CostCodeParentFk.Value);

				currentEntity = parent;

			}

			return currentEntity != null ? currentEntity.MdcCostCodeFk : null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectFk"></param>
		/// <param name="jobFk"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		public IProjectCostCodesEntity GetByProjectFkJobFkCode(int projectFk, int jobFk, string code)
		{
			return Get(x => x.ProjectFk == projectFk && x.Code.Equals(code, StringComparison.CurrentCultureIgnoreCase), jobFk, true).FirstOrDefault();
		}

		/// <summary>
		/// Save project cost codes without master cost codes per Project and Job
		/// </summary>
		/// <param name="sourcePrjCostCodeId2JobIdMap"></param>
		/// <param name="targetProjectId"></param>
		/// <param name="sourceProjectId"></param>
		/// <param name="sourceTargetJobIdMap"></param>
		/// <returns></returns>
		public IDictionary<int, int> CopyWithoutMdcCostCodes(IList<Tuple<int, int>> sourcePrjCostCodeId2JobIdMap, int targetProjectId, int? sourceProjectId = null, IDictionary<int, int> sourceTargetJobIdMap = null)
		{
			try
			{
				var oldNewPrjCostCodeIdMapping = new ProjectCostCodesCopyHelper(targetProjectId, sourceProjectId)
					 .SetSourceTargetJobIdMap(sourceTargetJobIdMap)
					 .CopyProjectCostCodes(sourcePrjCostCodeId2JobIdMap);

				return oldNewPrjCostCodeIdMapping;
			}
			catch (Exception ex)
			{
				throw new Operation.BusinessLayerException("Copy Project CostCodes Failed", ex, (int)Operation.ExceptionErrorCodes.BusinessFatalError);
			}

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCodeEntity"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public PrjCostCodesEntity CreateProjectCostCode(ICostCodeEntity costCodeEntity, int projectId)
		{
			if (costCodeEntity == null)
			{
				return null;
			}

			var projectCostCodeEntity = this.CreateProjectCostCode() as PrjCostCodesEntity;

			projectCostCodeEntity.ProjectFk = projectId;

			CopyInfoFromMasterCostCode((PrjCostCodesEntity)projectCostCodeEntity, costCodeEntity);

			return projectCostCodeEntity;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCodeEntity"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IProjectCostCodesEntity CreateNew(ICostCodeEntity costCodeEntity, int projectId)
		{
			return this.CreateProjectCostCode(costCodeEntity, projectId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCodeEntity"></param>
		/// <param name="costcodePriceListEntity"></param>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// <returns></returns>
		public IProjectCostCodesEntity CreateProjectCostCode(ICostCodeEntity costCodeEntity, ICostcodePriceListEntity costcodePriceListEntity, int projectId, int jobId)
		{
			if (costCodeEntity == null)
			{
				return null;
			}

			var projectCostCodeEntity = this.CreateProjectCostCode();

			projectCostCodeEntity.ProjectFk = projectId;

			projectCostCodeEntity.LgmJobFk = jobId;

			CopyInfoFromMasterCostCode((PrjCostCodesEntity)projectCostCodeEntity, costCodeEntity);

			CopyInfoFromCostCodePriceList((PrjCostCodesEntity)projectCostCodeEntity, costcodePriceListEntity);

			return projectCostCodeEntity;
		}

		private void CopyInfoFromCostCodePriceList(PrjCostCodesEntity projectCostCodeEntity, ICostcodePriceListEntity costcodePriceListEntity)
		{
			if (costcodePriceListEntity == null || projectCostCodeEntity == null)
			{
				return;
			}

			projectCostCodeEntity.Rate = costcodePriceListEntity.Rate;
			projectCostCodeEntity.FactorCosts = costcodePriceListEntity.FactorCost;
			projectCostCodeEntity.RealFactorCosts = costcodePriceListEntity.RealFactorCost;
			projectCostCodeEntity.FactorQuantity = costcodePriceListEntity.FactorQuantity;
			projectCostCodeEntity.RealFactorQuantity = costcodePriceListEntity.RealFactorQuantity;
			projectCostCodeEntity.FactorHour = costcodePriceListEntity.FactorHour;
			projectCostCodeEntity.CurrencyFk = costcodePriceListEntity.CurrencyFk;
			projectCostCodeEntity.DayWorkRate = costcodePriceListEntity.SalesPrice;
		}

		private void CopyInfoFromMasterCostCode(PrjCostCodesEntity projectCostCodeEntity, ICostCodeEntity costCodeEntity)
		{
			if (costCodeEntity == null || projectCostCodeEntity == null)
			{
				return;
			}

			projectCostCodeEntity.MdcCostCodeFk = costCodeEntity.Id;
			projectCostCodeEntity.Rate = costCodeEntity.Rate;
			projectCostCodeEntity.Surcharge = costCodeEntity.Surcharge;
			projectCostCodeEntity.Contribution = costCodeEntity.Contribution;
			projectCostCodeEntity.DayWorkRate = costCodeEntity.DayWorkRate;
			projectCostCodeEntity.CurrencyFk = costCodeEntity.CurrencyFk;
			projectCostCodeEntity.UomFk = costCodeEntity.UomFk;
			projectCostCodeEntity.FactorCosts = costCodeEntity.FactorCosts;
			projectCostCodeEntity.RealFactorCosts = costCodeEntity.RealFactorCosts;
			projectCostCodeEntity.FactorQuantity = costCodeEntity.FactorQuantity;
			projectCostCodeEntity.RealFactorQuantity = costCodeEntity.RealFactorQuantity;
			projectCostCodeEntity.Remark = costCodeEntity.Remark;
			projectCostCodeEntity.IsLabour = costCodeEntity.IsLabour;
			projectCostCodeEntity.IsRate = costCodeEntity.IsRate;
			projectCostCodeEntity.EstCostTypeFk = costCodeEntity.EstCostTypeFk;
			projectCostCodeEntity.FactorHour = costCodeEntity.FactorHour;
			projectCostCodeEntity.IsEditable = costCodeEntity.IsEditable;
			projectCostCodeEntity.Code = costCodeEntity.Code;
			projectCostCodeEntity.IsSubcontractor = costCodeEntity.IsSubcontractor;
			projectCostCodeEntity.Description2Info = costCodeEntity.Description2Info;
			projectCostCodeEntity.CostCodeTypeFk = costCodeEntity.CostCodeTypeFk;
			projectCostCodeEntity.CostCodePortionsFk = costCodeEntity.CostCodePortionsFk;
			projectCostCodeEntity.CostGroupPortionsFk = costCodeEntity.CostGroupPortionsFk;
			projectCostCodeEntity.AbcClassificationFk = costCodeEntity.AbcClassificationFk;
			projectCostCodeEntity.PrcStructureFk = costCodeEntity.PrcStructureFk;
			projectCostCodeEntity.UserDefined1 = costCodeEntity.UserDefined1;
			projectCostCodeEntity.UserDefined2 = costCodeEntity.UserDefined2;
			projectCostCodeEntity.UserDefined3 = costCodeEntity.UserDefined3;
			projectCostCodeEntity.UserDefined4 = costCodeEntity.UserDefined4;
			projectCostCodeEntity.UserDefined5 = costCodeEntity.UserDefined5;
			projectCostCodeEntity.SearchPattern = costCodeEntity.SearchPattern;
			projectCostCodeEntity.ContrCostCodeFk = costCodeEntity.ContrCostCodeFk;
			projectCostCodeEntity.IsBudget = costCodeEntity.IsBudget;
			projectCostCodeEntity.IsCost = costCodeEntity.IsCost;
			projectCostCodeEntity.IsChildAllowed = costCodeEntity.IsProjectChildAllowed;
			projectCostCodeEntity.EfbType221Fk = costCodeEntity.EfbType221Fk;
			projectCostCodeEntity.EfbType222Fk = costCodeEntity.EfbType222Fk;
			projectCostCodeEntity.DayWorkRate = costCodeEntity.DayWorkRate;
			projectCostCodeEntity.Co2Source = costCodeEntity.Co2Source;
			projectCostCodeEntity.Co2SourceFk = costCodeEntity.Co2SourceFk;
			projectCostCodeEntity.Co2Project = costCodeEntity.Co2Project;
			projectCostCodeEntity.VhbSheetDjcTypeFk = costCodeEntity.VhbSheetDjcTypeFk;
			projectCostCodeEntity.VhbSheetGcTypeFk = costCodeEntity.VhbSheetGcTypeFk;
			projectCostCodeEntity.IsConsortiumWork = costCodeEntity.IsConsortiumWork;
			projectCostCodeEntity.IsCorporateWork = costCodeEntity.IsCorporateWork;
			projectCostCodeEntity.IsSubcontractedWork = costCodeEntity.IsSubcontractedWork;

			projectCostCodeEntity.DescriptionInfo = costCodeEntity.DescriptionInfo;
		}

		/// <summary>
		/// Updates list of project cost codes with dependent data
		/// </summary>
		/// <param name="entities">The element to be saved</param>
		/// <returns></returns>
		public IEnumerable<PrjCostCodesCompleteEntity> Update(IEnumerable<PrjCostCodesCompleteEntity> entities)
		{
			try
			{
				var prjCostCodesJobRateLogic = new ProjectCostCodesJobRateLogic();
				var completeEntities = entities as PrjCostCodesCompleteEntity[] ?? entities.ToArray();
				foreach (var e in completeEntities)
				{
					if (e.PrjCostCodes != null)
					{
						Save(e.PrjCostCodes);
						if (e.PrjCostCodes.Version == 1)
						{
							var jobId = GetDefaultLogisticJobIdByProject(entities.FirstOrDefault().PrjCostCodes.ProjectFk);
							List<ProjectCostCodesJobRateEntity> projectCostCodesJobRateEntity = new List<ProjectCostCodesJobRateEntity>();
							projectCostCodesJobRateEntity.Add(prjCostCodesJobRateLogic.CreateIfNotExist(e.PrjCostCodes));
							projectCostCodesJobRateEntity.FirstOrDefault().LgmJobFk = jobId > 0 ? jobId : null;
							e.PrjCostCodesJobRateToSave = projectCostCodesJobRateEntity;
						}
					}
					if (e.PrjCostCodesJobRateToSave != null)
					{
						foreach (var prjCostCodesJobRate in e.PrjCostCodesJobRateToSave)
						{
							var jobRate = prjCostCodesJobRateLogic.GetByFilter(x => x.LgmJobFk == prjCostCodesJobRate.LgmJobFk && x.ProjectCostCodeFk == prjCostCodesJobRate.ProjectCostCodeFk).FirstOrDefault();
							if (jobRate is not null)
							{
								prjCostCodesJobRate.Id = jobRate.Id;
								prjCostCodesJobRate.InsertedBy = jobRate.InsertedBy;
								prjCostCodesJobRate.InsertedAt = jobRate.InsertedAt;
								prjCostCodesJobRate.Version = jobRate.Version;
								prjCostCodesJobRate.UpdatedAt = jobRate.UpdatedAt;
								prjCostCodesJobRate.UpdatedBy = jobRate.UpdatedBy;
							}
						}
						prjCostCodesJobRateLogic.Save(e.PrjCostCodesJobRateToSave);
					}

					if (e.PrjCostCodesJobRateToDelete != null)
					{
						prjCostCodesJobRateLogic.Delete(e.PrjCostCodesJobRateToDelete);
					}
				}

				return completeEntities;
			}
			catch (Exception e)
			{
				throw new Operation.BusinessLayerException(NLS.ERR_TheProjectcostcodeCouldNotBeSaved, e, (int)Operation.ExceptionErrorCodes.BusinessFatalError);
			}
		}

		private IEnumerable<PrjCostCodesEntity> GetItemsByProjectFk(int projectFk)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Entities<PrjCostCodesEntity>().Where(e => e.ProjectFk == projectFk).ToList();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCost2jobIds"></param>
		/// <param name="projectId"></param>
		/// <param name="prjCostCodes"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> SaveCostCodes(List<Tuple<int, int>> costCost2jobIds, int projectId, List<IProjectCostCodesEntity> prjCostCodes = null)
		{
			var result = new List<IProjectCostCodesEntity>();

			if (costCost2jobIds == null)
			{
				return result;
			}
			var isCopyFromSameProject = _sourceProjectId.HasValue ? _sourceProjectId.Value == projectId : true;

			var projectCostCodesToSave = new List<PrjCostCodesEntity>();

			var projectCostCodeJobRatesToSave = new List<ProjectCostCodesJobRateEntity>();

			var prjCostCodeUDPToSave = new List<CopyRequest>();

			/* get mdcCostCodes */
			var costCodeIds = costCost2jobIds.Select(e => e.Item1).Distinct().ToList();

			Dictionary<int, CostCodeEntity> mdcCostCodeDict = null;

			/* get prjCostCodes */
			var projectCostCodes = this.GetItemsByProjectFk(projectId).ToList();

			/* get source prjCostCodes */
			var sourceProjectCostCodes = _sourceProjectId.HasValue ? this.GetItemsByProjectFk(_sourceProjectId.Value).ToList() : new List<PrjCostCodesEntity>();

			var sourcePrjCostCodeIds = sourceProjectCostCodes != null && sourceProjectCostCodes.Any() ? sourceProjectCostCodes.Select(e => e.Id).ToArray() : new int[] { };

			/* get source prjCostCode Job Rate Entities */
			var sourceJobRateEntities = new ProjectCostCodesJobRateLogic().GetListByFilter(e => sourcePrjCostCodeIds.Contains(e.ProjectCostCodeFk)).ToList();

			/* get jobs */
			var jobIds = costCost2jobIds.Select(e => e.Item2).Distinct().ToList();

			var jobs = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>().GetJobByIds(jobIds);

			/* get costCodePriceLists */
			var costcodePriceLists = new List<CostcodePriceListEntity>();

			if (jobs != null && jobs.Any(e => e.CostCodePriceVersionFk.HasValue))
			{
				var costCodePriceVersionIds = jobs.Where(e => e.CostCodePriceVersionFk.HasValue).Select(e => e.CostCodePriceVersionFk.Value).Distinct().ToList();

				costcodePriceLists = new BasicsCostCodePriceListLogic().GetSearchList(e => costCodePriceVersionIds.Contains(e.CostcodePriceVerFk)).ToList();
			}

			var jobRates = new ProjectCostCodesJobRateLogic().GetListByFilter(e => e.LgmJobFk.HasValue && jobIds.Contains(e.LgmJobFk.Value)).ToList();

			/* process */
			var costCode2JobListMap = costCost2jobIds.GroupBy(e => e.Item1).ToDictionary(e => e.Key, e => e.Select(i => i.Item2).Distinct().ToList());

			foreach (var item in costCode2JobListMap)
			{
				/* check current project cost code is exist in database, if not create it and its parent */
				var projectCostCode = projectCostCodes.FirstOrDefault(e => e.MdcCostCodeFk.HasValue && e.MdcCostCodeFk.Value == item.Key);

				if (projectCostCode == null)
				{
					if (mdcCostCodeDict == null)
					{
						mdcCostCodeDict = new BasicsCostCodesLogic().GetListByCompany().ToDictionary(e => e.Id, e => e);
					}

					if (!mdcCostCodeDict.ContainsKey(item.Key))
					{
						continue;
					}

					var mdcCostCode = mdcCostCodeDict[item.Key];

					/* check whether the projectCostCode is provider as parameter */
					if (prjCostCodes != null && prjCostCodes.Any())
					{
						projectCostCode = prjCostCodes.OfType<PrjCostCodesEntity>().FirstOrDefault(e => e.MdcCostCodeFk == item.Key);

						if (projectCostCode != null)
						{
							projectCostCode.CostCodeParentFk = null;
						}
					}

					/* create a new one, if it is not provider as parameter */
					if (projectCostCode == null)
					{
						projectCostCode = this.CreateProjectCostCode(mdcCostCode, projectId);
					}

					var sourcePrjCostcode = !isCopyFromSameProject ? sourceProjectCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == item.Key && e.ProjectFk == _sourceProjectId) : null;

					if (sourcePrjCostcode != null && !isCopyFromSameProject)
					{
						projectCostCode.IsRate = sourcePrjCostcode.IsRate;
					}

					/* add to save */
					projectCostCodesToSave.Add(projectCostCode);

					/* add to cache */
					projectCostCodes.Add(projectCostCode);

					prjCostCodeUDPToSave.Add(new CopyRequest
					{
						SourceTableId = (int?)userDefinedColumnTableIds.BasicsCostCode,
						SourcePk1 = mdcCostCode.Id,
						TableId = (int)userDefinedColumnTableIds.ProjectCostCode,
						Pk1 = projectId,
						Pk2 = projectCostCode.Id
					});

					var currentProjectCostCode = projectCostCode;

					/* create parent project cost code */
					while (mdcCostCode != null && mdcCostCode.CostCodeParentFk.HasValue)
					{
						var parentProjectCostCode = projectCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == mdcCostCode.CostCodeParentFk.Value && e.ProjectFk == projectId);

						/* if parent is exist, break */
						if (parentProjectCostCode != null)
						{
							/* set parentFk */
							currentProjectCostCode.CostCodeParentFk = parentProjectCostCode.Id;

							break;
						}

						if (!mdcCostCode.CostCodeParentFk.HasValue || !mdcCostCodeDict.ContainsKey(mdcCostCode.CostCodeParentFk.Value))
						{
							break;
						}

						var parentMdcCostCode = mdcCostCodeDict[mdcCostCode.CostCodeParentFk.Value];

						/* check whether the projectCostCode is provider as parameter */
						if (prjCostCodes != null && prjCostCodes.Any())
						{
							parentProjectCostCode = prjCostCodes.OfType<PrjCostCodesEntity>().FirstOrDefault(e => e.MdcCostCodeFk == mdcCostCode.CostCodeParentFk.Value);
						}

						if (parentProjectCostCode == null)
						{
							parentProjectCostCode = this.CreateProjectCostCode(parentMdcCostCode, projectId);
						}

						/* set parentFk */
						currentProjectCostCode.CostCodeParentFk = parentProjectCostCode.Id;

						/* add to save */
						projectCostCodesToSave.Add(parentProjectCostCode);

						/* add to cache */
						projectCostCodes.Add(parentProjectCostCode);

						prjCostCodeUDPToSave.Add(new CopyRequest
						{
							SourceTableId = (int?)userDefinedColumnTableIds.BasicsCostCode,
							SourcePk1 = parentMdcCostCode.Id,
							TableId = (int)userDefinedColumnTableIds.ProjectCostCode,
							Pk1 = projectId,
							Pk2 = parentProjectCostCode.Id
						});

						mdcCostCode = parentMdcCostCode;

						currentProjectCostCode = parentProjectCostCode;
					}
				}

				/* add as result */
				result.Add(projectCostCode);

				projectCostCode.JobRates = new List<IProjectCostCodesJobRateEntity>();

				/* check whether the projectCostCodeJobRate is exist in database, if not create it */
				foreach (var currentJobId in item.Value)
				{
					var currentProjectCostCodeRate = jobRates.FirstOrDefault(e => e.LgmJobFk == currentJobId && e.ProjectCostCodeFk == projectCostCode.Id);

					if (currentProjectCostCodeRate == null)
					{
						var currentJob = jobs.FirstOrDefault(e => e.Id == currentJobId);

						if (currentJob == null)
						{
							continue;
						}

						/* check whether it is exist in parameter prjCostCodes */
						if (prjCostCodes != null && prjCostCodes.Any())
						{
							var prjCostCodeOfCurrentJob = prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == item.Key && e.LgmJobFk == currentJobId);

							if (prjCostCodeOfCurrentJob != null)
							{
								currentProjectCostCodeRate = new ProjectCostCodesJobRateLogic().Create(projectCostCode.Id, currentJobId);
								currentProjectCostCodeRate.Rate = prjCostCodeOfCurrentJob.Rate;
								currentProjectCostCodeRate.CurrencyFk = prjCostCodeOfCurrentJob.CurrencyFk;
								currentProjectCostCodeRate.FactorCosts = prjCostCodeOfCurrentJob.FactorCosts;
								currentProjectCostCodeRate.RealFactorCosts = prjCostCodeOfCurrentJob.RealFactorCosts;
								currentProjectCostCodeRate.FactorQuantity = prjCostCodeOfCurrentJob.FactorQuantity;
								currentProjectCostCodeRate.RealFactorQuantity = prjCostCodeOfCurrentJob.RealFactorQuantity;
								currentProjectCostCodeRate.FactorHour = prjCostCodeOfCurrentJob.FactorHour;
								currentProjectCostCodeRate.SalesPrice = prjCostCodeOfCurrentJob.DayWorkRate;
							}
						}

						/* create jobRate and get information from master cost code*/
						if (currentProjectCostCodeRate == null)
						{
							/* create a new projectCostCodeJobRate */
							var currentCostCodePriceList = currentJob.CostCodePriceVersionFk.HasValue ? costcodePriceLists.FirstOrDefault(e => e.CostcodePriceVerFk == currentJob.CostCodePriceVersionFk && e.CostCodeFk == item.Key) : null;

							if (mdcCostCodeDict == null)
							{
								mdcCostCodeDict = new BasicsCostCodesLogic().GetListByCompany().ToDictionary(e => e.Id, e => e);
							}

							CostCodeEntity currentMdcCostCode = null;
							mdcCostCodeDict.TryGetValue(item.Key, out currentMdcCostCode);

							var sourcePrjCostcode = !isCopyFromSameProject ? sourceProjectCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == item.Key && e.ProjectFk == _sourceProjectId) : null;

							if (sourcePrjCostcode != null && !isCopyFromSameProject)
							{


								var sourceJobIdItem = _source2TargetJobIdMapping.FirstOrDefault(e => e.Value == currentJobId);
								var sourceJobRateEntity = sourceJobRateEntities.FirstOrDefault(e => e.ProjectCostCodeFk == sourcePrjCostcode.Id && e.LgmJobFk.HasValue && e.LgmJobFk.Value == sourceJobIdItem.Key);

								var jobRateCopyData = new PrjCostCodeJobRateCopyData()
								{
									TargetPrjCostCodeId = projectCostCode.Id,
									TargetJobId = currentJobId,
									SourceJobRateEntity = sourceJobRateEntity,
									CostCodeEntity = currentMdcCostCode,
									CostCodePriceListEntity = currentCostCodePriceList
								};
								currentProjectCostCodeRate = new ProjectCostCodesJobRateLogic().CopyFrmSource(jobRateCopyData);
							}
							else
							{
								currentProjectCostCodeRate = new ProjectCostCodesJobRateLogic().Create(currentMdcCostCode, currentCostCodePriceList, projectCostCode.Id, currentJobId);
							}

							prjCostCodeUDPToSave.Add(new CopyRequest
							{
								SourceTableId = (int?)userDefinedColumnTableIds.BasicsCostCode,
								SourcePk1 = item.Key,
								TableId = (int)userDefinedColumnTableIds.ProjectCostCodeJobRate,
								Pk1 = projectId,
								Pk2 = currentProjectCostCodeRate.ProjectCostCodeFk,
								Pk3 = currentProjectCostCodeRate.LgmJobFk,
							});
						}

						/* add to save */
						projectCostCodeJobRatesToSave.Add(currentProjectCostCodeRate);

						/* add to cache */
						jobRates.Add(currentProjectCostCodeRate);
					}

					projectCostCode.JobRates.Add(currentProjectCostCodeRate);
				}
			}

			if (projectCostCodesToSave.Any())
			{
				this.Save(projectCostCodesToSave);
			}

			if (projectCostCodeJobRatesToSave.Any())
			{
				new ProjectCostCodesJobRateLogic().Save(projectCostCodeJobRatesToSave);
			}

			if (prjCostCodeUDPToSave.Any())
			{
				new UserDefinedColumnValueLogic().Copy(prjCostCodeUDPToSave, true);
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="sourcePrjCostCodeIds"></param>
		/// <param name="existSource2TargetPrjCostCodeIdMap"></param>
		/// <param name="sourceProjectId"></param>
		/// <param name="targetProjectId"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> CopyPrjCostCodeFromOtherProject(IEnumerable<int> sourcePrjCostCodeIds, int sourceProjectId, int targetProjectId, ConcurrentDictionary<int, int> existSource2TargetPrjCostCodeIdMap)
		{
			if (existSource2TargetPrjCostCodeIdMap == null)
			{
				existSource2TargetPrjCostCodeIdMap = new ConcurrentDictionary<int, int>();
			}

			if (sourcePrjCostCodeIds == null || !sourcePrjCostCodeIds.Any())
			{
				return new List<IProjectCostCodesEntity>();
			}

			var targetPrjCostCodeToSave = new List<PrjCostCodesEntity>();

			var sourcePrjCostCodes = this.GetItemsByProjectFk(sourceProjectId).ToList();

			var targetPrjCostCodes = this.GetItemsByProjectFk(targetProjectId).ToList();

			sourcePrjCostCodeIds = sourcePrjCostCodeIds.Distinct().ToList();

			int maxNumber = 10000;

			int index = 1;

			/* use to collect the sourcePrjCostCodeId to copy */
			var sourcePrjCostCodeIdsToCopy = new HashSet<int>();

			/* source prjCostCode to target prjCostCode mapping */
			var source2TargetPrjCostCodeMap = new Dictionary<PrjCostCodesEntity, PrjCostCodesEntity>();

			var sourcrPrjCostIdQueue = new Queue<int>(sourcePrjCostCodeIds);

			while (sourcrPrjCostIdQueue.Any() && index <= maxNumber)
			{
				var sourcePrjCostCodeId = sourcrPrjCostIdQueue.Dequeue();

				if (sourcePrjCostCodeIdsToCopy.Contains(sourcePrjCostCodeId))
				{
					continue;
				}

				/* exist mapping */
				if (existSource2TargetPrjCostCodeIdMap.ContainsKey(sourcePrjCostCodeId))
				{
					continue;
				}

				/* find source prjCostCode */
				var sourcePrjCostCode = sourcePrjCostCodes.FirstOrDefault(e => e.Id == sourcePrjCostCodeId);

				if (sourcePrjCostCode == null)
				{
					continue;
				}

				/* find target prjCostCode */
				var targetPrjCostCode = targetPrjCostCodes.FirstOrDefault(e => e.Code == sourcePrjCostCode.Code);

				if (targetPrjCostCode != null)
				{
					existSource2TargetPrjCostCodeIdMap.TryAdd(sourcePrjCostCodeId, targetPrjCostCode.Id);

					continue;
				}

				/* clone source prjCostCode to target prjCostCode */
				targetPrjCostCode = CopyPrjCostCode(sourcePrjCostCode, targetProjectId);

				/* for next process */
				targetPrjCostCodeToSave.Add(targetPrjCostCode);

				sourcePrjCostCodeIdsToCopy.Add(sourcePrjCostCodeId);

				source2TargetPrjCostCodeMap.Add(sourcePrjCostCode, targetPrjCostCode);

				/* copy parent prjCostCode*/
				if (sourcePrjCostCode.CostCodeParentFk.HasValue)
				{
					sourcrPrjCostIdQueue.Enqueue(sourcePrjCostCode.CostCodeParentFk.Value);
				}

				index++;
			}

			/* fill id to target prjCostCode */
			if (targetPrjCostCodeToSave.Any())
			{
				var newIds = new Stack<int>(this.SequenceManager.GetNextList("PRJ_PROJECT2MDC_CST_CDE", targetPrjCostCodeToSave.Count()));

				foreach (var item in targetPrjCostCodeToSave)
				{
					item.Id = newIds.Pop();
				}
			}

			/* add new mapping data to existSource2TargetPrjCostCodeIdMap */
			if (source2TargetPrjCostCodeMap.Any())
			{
				foreach (var item in source2TargetPrjCostCodeMap)
				{
					if (existSource2TargetPrjCostCodeIdMap.ContainsKey(item.Key.Id))
					{
						continue;
					}

					existSource2TargetPrjCostCodeIdMap.TryAdd(item.Key.Id, item.Value.Id);
				}
			}

			/* replace the CostCodeParentFk of target prjCostCode */
			if (source2TargetPrjCostCodeMap.Any())
			{
				foreach (var item in source2TargetPrjCostCodeMap)
				{
					if (!item.Key.CostCodeParentFk.HasValue || !existSource2TargetPrjCostCodeIdMap.ContainsKey(item.Key.CostCodeParentFk.Value))
					{
						continue;
					}

					item.Value.CostCodeParentFk = existSource2TargetPrjCostCodeIdMap[item.Key.CostCodeParentFk.Value];
				}
			}

			return this.Save(targetPrjCostCodeToSave);
		}

		private PrjCostCodesEntity CopyPrjCostCode(PrjCostCodesEntity sourcePrjCostCode, int targetProjectId)
		{
			var targetPrjCostCode = new PrjCostCodesEntity();

			targetPrjCostCode.CopyPropertyFrom(sourcePrjCostCode, new[] {
					"Id", "ProjectFk", "PrjCostCodeChildren", "PrjCostCodeParent", "ProjectCostCodeJobRateEntities" , "CostCodeParentFk",
					"CostCodeLevel1Fk", "CostCodeLevel2Fk", "CostCodeLevel3Fk", "CostCodeLevel4Fk", "CostCodeLevel5Fk", "CostCodeLevel6Fk", "CostCodeLevel7Fk", "CostCodeLevel8Fk" });

			targetPrjCostCode.ProjectFk = targetProjectId;

			return targetPrjCostCode;
		}

		/// <summary>
		/// Save Project CostCode's Job Rate Entitities
		/// </summary>
		/// <param name="prjCostCost2jobIds"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public void SavePrjCostCodesJobRates(List<Tuple<int, int>> prjCostCost2jobIds, int projectId)
		{
			var result = new List<PrjCostCodesEntity>();

			if (prjCostCost2jobIds == null || !prjCostCost2jobIds.Any()) { return; }

			/* get prjCostCodes */
			var prjCostCodeIds = prjCostCost2jobIds.Select(e => e.Item1).Distinct().ToList();

			var projectCostCodes = this.Get(e => prjCostCodeIds.Contains(e.Id)).ToList();

			/* get jobs */
			var jobIds = prjCostCost2jobIds.Select(e => e.Item2).Distinct().ToList();

			var jobs = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>().GetLogisticJobsByProject(projectId);

			var prjJobIds = jobs.Where(e => jobIds.Contains(e.Id)).Select(e => e.Id).ToList();

			foreach (var prjCostCode in projectCostCodes)
			{
				if (prjCostCode == null) { continue; }

				var items = prjCostCost2jobIds.Where(e => e.Item1 == prjCostCode.Id);

				foreach (var item in items)
				{
					if (item == null) { continue; }

					if (!result.Any(e => e.Id == prjCostCode.Id && e.LgmJobFk == item.Item2))
					{
						var entity = (PrjCostCodesEntity)prjCostCode.Clone();

						entity.LgmJobFk = item.Item2;

						result.Add(entity);
					}
				}

			}

			if (result.Any())
			{
				var costCodeJobRateLogic = new ProjectCostCodesJobRateLogic();

				var jobRates = costCodeJobRateLogic.CreateIfNotExist(result);

				if (jobRates != null && jobRates.Any())
				{
					costCodeJobRateLogic.Save(jobRates);
				}
			}
		}

		private void CalculateHourUnit(IEnumerable<CostCodeEntity> costCodes)
		{
			if (costCodes == null || !costCodes.Any())
			{
				return;
			}
			var estResourceUpdateHelper = Injector.Get<IEstResourceUpdateHelper>();
			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };
			var uomFks = costCodes.Where(x => x.IsLabour && x.UomFk.HasValue).Select(s => s.UomFk.Value).Distinct().ToArray();
			var uomConversionFactorsDict = estResourceUpdateHelper.GetUomConversionFactors(uomFks).GroupBy(item => item.Item1).ToDictionary(group => group.Key, group => group.First().Item3);
			Parallel.ForEach(costCodes, parallelOptions, item =>
			{
				item.HourUnit = item.IsLabour ? (item.UomFk.HasValue ? uomConversionFactorsDict.GetValueOrDefault(item.UomFk.Value, 1) : 1) : 0;
			});
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// <param name="isPrjAssembly"></param>
		/// <param name="filterBasicCCByIsActive"></param>
		/// <returns></returns>
		public IEnumerable<CostCodeEntity> GetMdcPrjCostCodesByJob(int projectId, int? jobId, bool isPrjAssembly = false, bool filterBasicCCByIsActive = false)
		{
			//get prj costcode list per project and job list
			if (jobId == null || jobId <= 0)
			{
				jobId = GetDefaultLogisticJobIdByProject(projectId);
			}

			/* get master cost costcode */
			IEnumerable<CostCodeEntity> mdcCostCodes = new List<CostCodeEntity>();
			Task mdcCostCodesTask = Task.Run(() =>
			{
				mdcCostCodes = new BasicsCostCodesLogic().GetEstCostCodeListByCompany(false, false, filterBasicCCByIsActive);//filter by cost code type
			});

			/* get project cost code */
			var prjCostCodes = this.GetProjectCostCodes(projectId);

			bool hasNoProjectCostCodes = false;
			if (prjCostCodes == null || !prjCostCodes.Any())
			{
				hasNoProjectCostCodes = true;
			}

			/* get project cost code job rate */
			var jobRates = jobId.HasValue && jobId > 0 ? new ProjectCostCodesJobRateLogic().GetListByFilter(e => e.LgmJobFk == jobId.Value) : new List<ProjectCostCodesJobRateEntity>();

			var costCodeTypes = new BasicsCustomizeCostCodeTypeLogic().GetListByFilter(e => e.IsEstimateCc).ToList();

			/* get costCodePriceList */
			IEnumerable<CostcodePriceListEntity> costCodePriceLists = null;

			if (jobId.HasValue && jobId > 0)
			{
				var jobEntity = RVPARB.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>().GetJobById(jobId.Value);

				if (jobEntity != null && jobEntity.CostCodePriceVersionFk.HasValue)
				{
					costCodePriceLists = new BasicsCostCodePriceListLogic().GetSearchList(e => e.CostcodePriceVerFk == jobEntity.CostCodePriceVersionFk.Value);
				}
			}

			Task.WaitAll(mdcCostCodesTask);

			if (mdcCostCodes != null && mdcCostCodes.Any())
			{

				ParallelOptions parallelOpt = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

				var disabledCostCodes = new ConcurrentBag<CostCodeEntity>();

				Parallel.ForEach(prjCostCodes.Where(x => x.MdcCostCodeFk.HasValue), parallelOpt, prjCostCode =>
				{
					if (!mdcCostCodes.Any(x => x.Id == prjCostCode.MdcCostCodeFk))
					{
						var costCodeEntity = new CostCodeEntity();
						costCodeEntity.Id = prjCostCode.MdcCostCodeFk.Value;
						costCodeEntity.Code = prjCostCode.Code;
						costCodeEntity.DescriptionInfo = prjCostCode.DescriptionInfo;
						costCodeEntity.Description2Info = prjCostCode.Description2Info;
						costCodeEntity.IsProjectChildAllowed = prjCostCode.IsChildAllowed;
						costCodeEntity.CurrencyFk = prjCostCode.CurrencyFk;
						costCodeEntity.Rate = prjCostCode.Rate;
						costCodeEntity.DayWorkRate = prjCostCode.Rate;
						costCodeEntity.FactorCosts = prjCostCode.FactorCosts;
						costCodeEntity.FactorHour = prjCostCode.FactorHour;
						costCodeEntity.FactorQuantity = prjCostCode.FactorQuantity;
						costCodeEntity.RealFactorCosts = prjCostCode.RealFactorCosts;
						costCodeEntity.CostCodeTypeFk = prjCostCode.CostCodeTypeFk;
						disabledCostCodes.Add(costCodeEntity);
					}
				});

				mdcCostCodes = mdcCostCodes.Concat(disabledCostCodes);

				Parallel.ForEach(mdcCostCodes, parallelOpt, mdcCostCode =>
				{
					/* get info from costCodePriceList */
					if (costCodePriceLists != null && costCodePriceLists.Any())
					{
						var costCodePriceListOfCurrent = costCodePriceLists.FirstOrDefault(e => e.CostCodeFk == mdcCostCode.Id);

						if (costCodePriceListOfCurrent != null)
						{
							mdcCostCode.Rate = costCodePriceListOfCurrent.Rate;
							mdcCostCode.FactorCosts = costCodePriceListOfCurrent.FactorCost;
							mdcCostCode.RealFactorCosts = costCodePriceListOfCurrent.RealFactorCost;
							mdcCostCode.FactorQuantity = costCodePriceListOfCurrent.FactorQuantity;
							mdcCostCode.RealFactorQuantity = costCodePriceListOfCurrent.RealFactorQuantity;
							mdcCostCode.FactorHour = costCodePriceListOfCurrent.FactorHour;
							mdcCostCode.CurrencyFk = costCodePriceListOfCurrent.CurrencyFk;
							mdcCostCode.DayWorkRate = costCodePriceListOfCurrent.SalesPrice;
							mdcCostCode.Co2Source = costCodePriceListOfCurrent.Co2Source;
							mdcCostCode.Co2Project = costCodePriceListOfCurrent.Co2Project;
							mdcCostCode.Co2SourceFk = costCodePriceListOfCurrent.Co2SourceFk;
						}
					}

					/* get info from projectCostCode */
					if (hasNoProjectCostCodes)
					{
						return;
					}

					var prjCostCodeOfCurrent = prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk.HasValue && e.MdcCostCodeFk == mdcCostCode.Id);

					if (prjCostCodeOfCurrent == null)
					{
						return;
					}

					mdcCostCode.UomFk = prjCostCodeOfCurrent.UomFk;
					mdcCostCode.IsLabour = prjCostCodeOfCurrent.IsLabour;
					mdcCostCode.IsRate = prjCostCodeOfCurrent.IsRate;
					mdcCostCode.IsEditable = prjCostCodeOfCurrent.IsEditable;
					mdcCostCode.EstCostTypeFk = prjCostCodeOfCurrent.EstCostTypeFk;
					mdcCostCode.IsBudget = prjCostCodeOfCurrent.IsBudget;
					mdcCostCode.IsCost = prjCostCodeOfCurrent.IsCost;
					mdcCostCode.IsRefereToProjectCostCode = true;
					mdcCostCode.OriginalPrjCostCodeId = prjCostCodeOfCurrent.Id;
					mdcCostCode.Remark = prjCostCodeOfCurrent.Remark;
					mdcCostCode.Co2Source = prjCostCodeOfCurrent.Co2Source;
					mdcCostCode.Co2Project = prjCostCodeOfCurrent.Co2Project;
					mdcCostCode.Co2SourceFk = prjCostCodeOfCurrent.Co2SourceFk;

					mdcCostCode.IsProjectChildAllowed =  prjCostCodeOfCurrent.IsChildAllowed;
		
					if (isPrjAssembly)
					{
						mdcCostCode.DayWorkRate = prjCostCodeOfCurrent.DayWorkRate;
						mdcCostCode.Rate = prjCostCodeOfCurrent.Rate;
						mdcCostCode.FactorCosts = prjCostCodeOfCurrent.FactorCosts;
						mdcCostCode.RealFactorCosts = prjCostCodeOfCurrent.RealFactorCosts;
						mdcCostCode.FactorQuantity = prjCostCodeOfCurrent.FactorQuantity;
						mdcCostCode.RealFactorQuantity = prjCostCodeOfCurrent.RealFactorQuantity;
						mdcCostCode.FactorHour = prjCostCodeOfCurrent.FactorHour;
						mdcCostCode.CurrencyFk = prjCostCodeOfCurrent.CurrencyFk;
						mdcCostCode.Co2Source = prjCostCodeOfCurrent.Co2Source;
						mdcCostCode.Co2Project = prjCostCodeOfCurrent.Co2Project;
						mdcCostCode.Co2SourceFk = prjCostCodeOfCurrent.Co2SourceFk;
					}

					/* get info from projectCostCodeRate */
					if (jobRates == null || !jobRates.Any())
					{
						return;
					}

					var prjCostCodeJobRateOfCurrent = jobRates.FirstOrDefault(e => e.ProjectCostCodeFk == prjCostCodeOfCurrent.Id);

					if (prjCostCodeJobRateOfCurrent == null)
					{
						return;
					}

					mdcCostCode.DayWorkRate = prjCostCodeJobRateOfCurrent.SalesPrice;
					mdcCostCode.Rate = prjCostCodeJobRateOfCurrent.Rate;
					mdcCostCode.FactorCosts = prjCostCodeJobRateOfCurrent.FactorCosts;
					mdcCostCode.RealFactorCosts = prjCostCodeJobRateOfCurrent.RealFactorCosts;
					mdcCostCode.FactorQuantity = prjCostCodeJobRateOfCurrent.FactorQuantity;
					mdcCostCode.RealFactorQuantity = prjCostCodeJobRateOfCurrent.RealFactorQuantity;
					mdcCostCode.FactorHour = prjCostCodeJobRateOfCurrent.FactorHour;
					mdcCostCode.CurrencyFk = prjCostCodeJobRateOfCurrent.CurrencyFk;
					mdcCostCode.Co2Source = prjCostCodeJobRateOfCurrent.Co2Source;
					mdcCostCode.Co2Project = prjCostCodeJobRateOfCurrent.Co2Project;
					mdcCostCode.Co2SourceFk = prjCostCodeJobRateOfCurrent.Co2SourceFk;

					if (mdcCostCode.CostCodeTypeFk.HasValue)
					{
						var costCodeType = costCodeTypes.FirstOrDefault(e => e.Id == mdcCostCode.CostCodeTypeFk.Value);
						if (costCodeType != null)
						{
							mdcCostCode.IsEstimateCostCode = costCodeType.IsEstimateCc;
						}
					}
				});
			}

			var result = new List<CostCodeEntity>(mdcCostCodes);

			// get project costcodes without master costcode(MdcCostCodeFk = null)
			result.AddRange(GetOnlyPrjCostCodesForEstLookup(jobRates, prjCostCodes, costCodeTypes));

			CalculateHourUnit(result);

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		public List<CostCodeSmallDto> AssignTempLookupIds(List<CostCodeSmallDto> items)
		{
			int cnt = 0;
			var itemDictionary = new ConcurrentDictionary<int, CostCodeSmallDto>();

			Parallel.ForEach(items, (item) =>
			{
				item.OriginalId = item.Id;
				item.OriginalCostCodeParentFk = item.CostCodeParentFk;
				item.Id = Interlocked.Increment(ref cnt);
				item.CostCodeParentFk = null;
				item.CostCodes = new List<CostCodeSmallDto>();
				itemDictionary.TryAdd(item.OriginalId, item);
			});

			Parallel.ForEach(items, (item) =>
			{
				if (item.OriginalCostCodeParentFk.HasValue)
				{
					AssignParent(item, itemDictionary);
				}
			});

			return items;
		}

		private static void AssignParent(CostCodeSmallDto item, ConcurrentDictionary<int, CostCodeSmallDto> itemDictionary)
		{
			if (item.OriginalCostCodeParentFk.HasValue && itemDictionary.TryGetValue(item.OriginalCostCodeParentFk.Value, out var matchedItem))
			{
				matchedItem.CostCodes = matchedItem.CostCodes ?? new List<CostCodeSmallDto>();
				item.CostCodeParentFk = matchedItem.Id;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="flatList"></param>
		/// <returns></returns>
		public List<CostCodeSmallDto> ConvertToTree(List<CostCodeSmallDto> flatList)
		{
			var dict = flatList.ToDictionary(cc => cc.Id, cc => cc);

			foreach (var costCode in flatList)
			{
				if (costCode.CostCodeParentFk.HasValue)
				{
					if (dict.TryGetValue(costCode.CostCodeParentFk.Value, out var parent))
					{
						if (parent.CostCodes == null)
						{
							parent.CostCodes = new List<CostCodeSmallDto>();
						}
						parent.CostCodes.Add(costCode);
					}
					else
					{
						costCode.CostCodeParentFk = null;
					}
				}
			}

			return flatList.Where(cc => !cc.CostCodeParentFk.HasValue).OrderBy(x => x.Code).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjCcJobRateEntities"></param>
		/// <param name="prjCostCodes"></param>
		/// <param name="costCodeTypes"></param>
		/// <returns></returns>
		private IEnumerable<CostCodeEntity> GetOnlyPrjCostCodesForEstLookup(IEnumerable<ProjectCostCodesJobRateEntity> prjCcJobRateEntities, IEnumerable<IProjectCostCodesEntity> prjCostCodes, IEnumerable<BasicsCustomizeCostCodeTypeEntity> costCodeTypes)
		{
			// should not only filter by jobrate - #145781
			//List<ProjectCostCodesJobRateEntity> prjCcJobRateEntities = new List<ProjectCostCodesJobRateEntity>();

			// get prj costcode list per project and job list
			//jobId ??= GetDefaultLogisticJobIdByProject(projectId);

			// prjCcJobRateEntities = new ProjectCostCodesJobRateLogic().GetListByFilter(e => e.LgmJobFk == jobId).ToList();


			//var prjCostCodeIdsByJob = prjCcJobRateEntities.Select(e => e.ProjectCostCodeFk);
			//prjCostCodesList = this.Get(e => e.ProjectFk == projectId && prjCostCodeIdsByJob.Contains(e.Id)).ToList();

			var prjCostCodesList = prjCostCodes.Select(e => e as PrjCostCodesEntity);

			var prjCostCodesWithoutMdcCc = prjCostCodesList.Where(e => !e.MdcCostCodeFk.HasValue);

			//var costCodeTypes = new BasicsCustomizeCostCodeTypeLogic().GetListByFilter(e => e.IsEstimateCc).ToList();

			var result = new ConcurrentBag<CostCodeEntity>();

			//get project costcodes (MdcCostCodeFk = null)

			ParallelOptions parallelOpt = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

			Parallel.ForEach(prjCostCodesWithoutMdcCc, parallelOpt, prjCc =>
			{
				if (prjCc == null)
				{
					return;
				}

				var newItem = new CostCodeEntity();

				var sourcePropertiesDic = prjCc.GetType().GetProperties().ToDictionary(e => e.Name, e => e.GetValue(prjCc));

				newItem.CopyPropertyFrom(sourcePropertiesDic);

				newItem.Id = prjCc.Id;
				newItem.Version = prjCc.Version;

				newItem.IsOnlyProjectCostCode = true;
				newItem.IsRefereToProjectCostCode = true;
				newItem.OriginalPrjCostCodeId = prjCc.Id;

				newItem.CostCodeParentFk = GetParentMdcCostCodeId(prjCc, prjCostCodesList);

            newItem.DescriptionInfo = prjCc.DescriptionInfo;

				newItem.Description2Info = prjCc.Description2Info;

				var prjCostCodeJobRateOfCurrent = prjCcJobRateEntities.FirstOrDefault(e => e.ProjectCostCodeFk == prjCc.Id);

				if (prjCostCodeJobRateOfCurrent != null)
				{
					newItem.Rate = prjCostCodeJobRateOfCurrent.Rate;
					newItem.FactorCosts = prjCostCodeJobRateOfCurrent.FactorCosts;
					newItem.RealFactorCosts = prjCostCodeJobRateOfCurrent.RealFactorCosts;
					newItem.FactorQuantity = prjCostCodeJobRateOfCurrent.FactorQuantity;
					newItem.RealFactorQuantity = prjCostCodeJobRateOfCurrent.RealFactorQuantity;
					newItem.FactorHour = prjCostCodeJobRateOfCurrent.FactorHour;
					newItem.CurrencyFk = prjCostCodeJobRateOfCurrent.CurrencyFk;
					newItem.DayWorkRate = prjCostCodeJobRateOfCurrent.SalesPrice;
					newItem.Co2Source = prjCostCodeJobRateOfCurrent.Co2Source;
					newItem.Co2Project = prjCostCodeJobRateOfCurrent.Co2Project;
					newItem.Co2SourceFk = prjCostCodeJobRateOfCurrent.Co2SourceFk;
				}

				var costCodeType = costCodeTypes.FirstOrDefault(e => prjCc.CostCodeTypeFk.HasValue && e.Id == prjCc.CostCodeTypeFk.Value);
				if (costCodeType != null)
				{
					newItem.IsEstimateCostCode = costCodeType.IsEstimateCc;
					newItem.IsInformation = costCodeType.IsInformation;
				}

				result.Add(newItem);
			});

			return result;
		}

		/// <summary>
		/// <param name="id"></param>
		/// </summary>
		public List<PrjCostCodesEntity> GetAllParents(int id)
		{
			var parents = new List<PrjCostCodesEntity>();

			var currentEntity = GetById(id);

			while (currentEntity != null && currentEntity.CostCodeParentFk.HasValue)
			{
				PrjCostCodesEntity parent = (PrjCostCodesEntity)GetById(currentEntity.CostCodeParentFk.Value);

				currentEntity = parent;

				parents.Add(parent);

			}

			return parents;
		}

		/// <summary>
		/// <param name="id"></param>
		/// </summary>
		public IEnumerable<PrjCostCodesEntity> GetCompleteHierarchy(int id)
		{
			var completeList = new List<PrjCostCodesEntity>();

			var currentEntity = GetById(id);

			while (currentEntity != null && currentEntity.CostCodeParentFk.HasValue)
			{
				PrjCostCodesEntity parent = (PrjCostCodesEntity)GetById(currentEntity.CostCodeParentFk.Value);

				currentEntity = parent;
			}

			var rootParent = currentEntity;

			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbContext.Entities<PrjCostCodesEntity>()
					 .Include(e => e.PrjCostCodeChildren)
					 .Where(e => e.ProjectFk == rootParent.ProjectFk);

				var firstLevelItem = entities.Where(e => e.Id == rootParent.Id);

				var flat = firstLevelItem.AsEnumerable().Flatten(e => e.PrjCostCodeChildren).ToList();

				completeList.AddRange(firstLevelItem);

				var prjCostCodesChildren = firstLevelItem.SelectMany(e => e.PrjCostCodeChildren);

				while (prjCostCodesChildren.Any())
				{
					completeList.AddRange(prjCostCodesChildren);

					prjCostCodesChildren = prjCostCodesChildren.SelectMany(e => e.PrjCostCodeChildren);
				}
			}

			return completeList;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="mdcCostCodeFks"></param>
		/// <returns></returns>
		public IEnumerable<IProjectCostCodesEntity> GetListByMdcCostCodeFks(int projectId, List<int> mdcCostCodeFks)
		{
			var entities = new List<PrjCostCodesEntity>();
			var parentEntities = new List<PrjCostCodesEntity>();
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				entities = dbContext.Entities<PrjCostCodesEntity>()
						.Include(e => e.PrjCostCodeParent)
						.Where(e => e.ProjectFk == projectId && e.MdcCostCodeFk.HasValue && mdcCostCodeFks.Contains(e.MdcCostCodeFk.Value)).AsQueryable()
						.ToList();

				if (entities.Any())
				{
					foreach (var item in entities)
					{
						if (item.CostCodeParentFk.HasValue)
						{
							var currentParent = GetById(item.CostCodeParentFk.Value);

							if (currentParent != null)
							{
								parentEntities.Add((PrjCostCodesEntity)currentParent);

								while (currentParent != null && currentParent.CostCodeParentFk.HasValue)
								{
									PrjCostCodesEntity parent = (PrjCostCodesEntity)GetById(currentParent.CostCodeParentFk.Value);

									currentParent = parent;
									parentEntities.Add(parent);
								}
							}

						}
					}
				}
			}
			entities.AddRange(parentEntities);
			return entities;
		}

		private ConcurrentDictionary<int, int> _source2TargetJobIdMapping = new ConcurrentDictionary<int, int>();
		private int? _sourceProjectId = null;

		/// <summary>
		/// Copy Source Project costcodes with Job Rates Entities
		/// </summary>
		/// <param name="mdcCostCode2JobIds"></param>
		/// <param name="targetProjectId"></param>
		/// <param name="sourceProjectId"></param>
		/// <param name="old2NewJobIdMap"></param>
		/// <returns></returns>
		public void CopyWithMdcCostCodes(List<Tuple<int, int>> mdcCostCode2JobIds, int targetProjectId, int? sourceProjectId, ConcurrentDictionary<int, int> old2NewJobIdMap)
		{
			_source2TargetJobIdMapping = old2NewJobIdMap;
			_sourceProjectId = sourceProjectId;
			SaveCostCodes(mdcCostCode2JobIds, targetProjectId);
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public Tuple<string, IEnumerable<CostCodeEntity>> GenerateCode(PrjCostCodeGenerateCodeData data)
		{
			int stepIncrement = 1;
			var itemsCodeList = new List<String>();
			IEnumerable<CostCodeEntity> items;

			if (data.JobId != null)
			{
				IEnumerable<CostCodeEntity> list = GetMdcPrjCostCodesByJob(data.ProjectId, data.JobId, data.IsPrjAssembly, true);
				itemsCodeList.AddRange(list.Where(i => i.CostCodeParentFk == data.NewCreatedItem.Id).Select(e=>e.Code));
				items = list;
			}
			else
			{
				IEnumerable<PrjCostCodesEntity> list = GetList(data.ProjectId);
				itemsCodeList.AddRange(list.Where(i => i.CostCodeParentFk == data.NewCreatedItem.Id).Select(e => e.Code));
				items = [];
			}			 

			// Build the initial part of the code
			string prevPart = data.NewCreatedItem.Code.EndsWith(".") ? data.NewCreatedItem.Code : data.NewCreatedItem.Code + ".";

			// Start with the first index
			int lastIndexNum = stepIncrement;
			string newCode = prevPart + lastIndexNum;

			// Loop until we find a unique code
			while (itemsCodeList.Contains(newCode) || itemsCodeList.Any(e => e.Equals(newCode, StringComparison.CurrentCultureIgnoreCase)))
			{
				lastIndexNum += stepIncrement;
				newCode = prevPart + lastIndexNum;
			}

			return new Tuple<String, IEnumerable<CostCodeEntity>>(newCode, items);
			
		}

		/// <summary>
		/// Get a costcode entity code Validation
		/// </summary>
		/// <param name="code"></param>
		/// <param name="projectId"></param>
		/// <param name="costcodeId"></param>
		/// <returns></returns>
		public bool IsUniqCode(string code, int projectId, int costcodeId)
		{
			IEnumerable<string> list = GetMdcPrjCostCodesByJob(projectId, null, false, true).Where(e => e.Id != costcodeId).Select(e => e.Code).ToList();

			bool isUnique = !list.Any(c => c.Equals(code, StringComparison.CurrentCultureIgnoreCase) );

			return isUnique;

		}
	}
}
