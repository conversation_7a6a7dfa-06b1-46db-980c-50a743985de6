(function (angular) {
	/* global globals */
	'use strict';

	const moduleName = 'basics.bank';
	angular.module(moduleName).service('basicsBankWizardService', BasicsBankWizardService);

	BasicsBankWizardService.$inject = ['platformSidebarWizardCommonTasksService', 'basicsBankMainService'];

	function BasicsBankWizardService(platformSidebarWizardCommonTasksService, basicsBankMainService) {

		const disableBank = function disableBank() {
			return platformSidebarWizardCommonTasksService.provideDisableInstance(basicsBankMainService, 'Disable Record', 'cloud.common.disableRecord', 'BankName',
				'basics.bank.disableDone', 'basics.bank.alreadyDisabled', 'bankname', 1);
		};
		this.disableBank = disableBank().fn;

		const enableBank = function enableBank() {
			return platformSidebarWizardCommonTasksService.provideEnableInstance(basicsBankMainService, 'Enable Record', 'cloud.common.enableRecord', 'BankName',
				'basics.bank.enableDone', 'basics.bank.alreadyEnabled', 'bankname', 2);
		};
		this.enableBank = enableBank().fn;
	}
})(angular);
