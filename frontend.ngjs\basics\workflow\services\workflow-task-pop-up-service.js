(function (angular) {
	'use strict';

	var intervalShort = 2000;

	function basicsWorkflowTaskPopUpService($interval, _, basicsWorkflowInstanceService, basicsWorkflowClientActionService, basicsWorkflowModuleUtilService, basicsCommonChangeStatusService, basicsWorkflowUtilityService, localStorageService, $rootScope) {
		var service = {};
		var interval = null;
		var popUpActive = false;
		var running = false;
		let currentOpenedTask;

		function setTaskListInStorage() {
			var taskListInStorage = localStorageService.getFromLocalStorage(localStorageService.getUserStorageKeyForNotShowList());
			_.forEach(basicsWorkflowInstanceService.task.taskIdList, function (item) {
				if (!_.includes(taskListInStorage, item)) {
					localStorageService.addToNotShowList(item);
				}
			});
		}

		function removeFromTaskList(id) {
			_.remove(basicsWorkflowInstanceService.task.list, function (item) {
				return item.Id === id;
			});
			basicsWorkflowInstanceService.task.listSeal = null;
		}

		function isDeleted(id) {
			return !_.find(basicsWorkflowInstanceService.task.list, {Id: id});
		}

		service.openAsPopUp = async function openAsPopUp(task, closeFn) {
			service.stop();

			//Asyncronously loads context for popup task.
			if (task && !task.IsContextLoaded) {
				task.Context = {};
				await basicsWorkflowUtilityService.loadContext(task);
			}

			basicsWorkflowInstanceService.prepareTask(task);
			popUpActive = true;
			currentOpenedTask = task;
			localStorageService.addToCurrentOpenedTaskIdList(task.Id);

			basicsWorkflowClientActionService.executeTask(task, closeFn)
				.then(function (diagResult) {
					closePopUp(diagResult, task);
				}, function () {
					service.start();
				});
		};

		function closePopUp(diagResult, task) {
			if (!task) {
				return;
			}
			removeFromTaskList(task.Id);
			popUpActive = false;
			if (diagResult && !diagResult.cancel) {
				localStorageService.removeFromNotShowList(task.Id);
				if (diagResult.actionEvent) {
					switch (diagResult.actionEvent) {
						case basicsWorkflowClientActionService.actionEvent.stop :
							basicsWorkflowInstanceService.stopWorkflow(task.WorkflowInstanceId).then(function () {
								if (angular.isFunction(basicsWorkflowModuleUtilService.refreshSelected)) {
									basicsWorkflowModuleUtilService.refreshSelected();
								}
							});
							break;
					}
				} else {
					if (diagResult.data) {
						var context = diagResult.data.context;
						task.Result = diagResult.data.result;
						if (diagResult.data.task && diagResult.data.task.Action && diagResult.data.task.Action.Description) {
							var actionButtonName = _.replace(diagResult.data.task.Action.Description, /\s/g, '');
							actionButtonName += 'ButtonResult';
							if (!context) {
								context = {};
							}
							if (!_.isString(context)) {
								context[actionButtonName] = task.Result;
							}
						}
						task.Context = context;
					}
					basicsWorkflowInstanceService.continueWorkflow(task).then(function () {
						if (angular.isFunction(basicsWorkflowModuleUtilService.refreshSelected)) {
							basicsWorkflowModuleUtilService.refreshSelected();
						}
					});
				}
			} else {
				localStorageService.addToNotShowList(task.Id);
				service.start();
			}
			currentOpenedTask = null;
			localStorageService.removeFromCurrentOpenedTaskIdList(task.Id);
		}

		$rootScope.$on('modelDialog.escPress', function (result) {
			result.cancel = true;
			closePopUp(result, currentOpenedTask);
		});

		service.makePopUp = function makePopUp(taskList) {
			if (_.isArray(taskList) && !_.isEmpty(taskList)) {
				setTaskListInStorage();
				var tasksWithPopUp = _.filter(taskList, function (item) {

					if (item && !item.IsPopup) {
						return false;
					}

					//Show only popups that aren't dismissed already
					return !localStorageService.isInNotShowList(item.Id) && item.Status === 2;
				});

				_.forEach(tasksWithPopUp, function (popUp) {
					popUp.pendingToOpen = true;
				});

				if (!popUpActive) {
					var popUp = _.find(tasksWithPopUp, function (task) {
						return !localStorageService.isInCurrentOpenedTaskIdList(task.Id);
					});

					if (popUp && !(localStorageService.isInNotShowList(popUp.Id) || isDeleted(popUp.Id))) {
						var closeFnFactory = function (id) {
							return function () {
								return isDeleted(id) || (localStorageService.isInNotShowList(id) && currentOpenedTask.Id !== id);
							};
						};
						service.openAsPopUp(popUp, closeFnFactory(popUp.Id));
					}
				} else {
					_.forEach(taskList, function (item) {
						if (item.pendingToOpen) {
							localStorageService.removeFromNotShowList(item.Id);
						}
					});
				}
			}
		};

		function popUpInterval() {
			basicsWorkflowInstanceService.getTaskList(basicsWorkflowInstanceService.task.showGroupTask)
				.then(service.makePopUp);
		}

		function popUpIntervalShort() {
			service.makePopUp(basicsWorkflowInstanceService.task.list);
		}

		function statusChangeInterval(scope, hasConfiguredWorkflows) {
			if (!popUpActive && hasConfiguredWorkflows) {
				interval = $interval(function () {
					popUpInterval({}, hasConfiguredWorkflows);
				}, intervalShort, 6);
			}
		}

		service.init = function () {
			basicsWorkflowInstanceService.getTaskList(basicsWorkflowInstanceService.task.showGroupTask, true);
			basicsWorkflowInstanceService.registerWorkflowCallback(popUpInterval);
			basicsCommonChangeStatusService.onStatusChanged.register(statusChangeInterval);
			service.start();
		};

		service.start = function () {
			if (!running) {
				running = true;
				$interval(popUpIntervalShort, intervalShort, 3);
			}
		};

		service.stop = function () {
			running = false;
		};

		return service;
	}

	angular.module('basics.workflow')
		.factory('basicsWorkflowTaskPopUpService', ['$interval', '_', 'basicsWorkflowInstanceService', 'basicsWorkflowClientActionService', 'basicsWorkflowModuleUtilService', 'basicsCommonChangeStatusService', 'basicsWorkflowUtilityService', 'basicsWorkflowLocalStorageService', '$rootScope', basicsWorkflowTaskPopUpService])
		.run(['$rootScope', 'platformIsPreInitState', 'basicsWorkflowTaskPopUpService', 'tokenAuthentication', function ($rootScope, platformIsPreInitState, basicsWorkflowTaskPopUpService, tokenAuthentication) {
			var unregisterDelegate = $rootScope.$on('$stateChangeSuccess', function () {
				var stateName = arguments[1].name;
				if (!platformIsPreInitState(stateName) && tokenAuthentication.isloggedIn()) {
					unregisterDelegate();
					basicsWorkflowTaskPopUpService.init();
				}
			});
		}]);

})(angular);
