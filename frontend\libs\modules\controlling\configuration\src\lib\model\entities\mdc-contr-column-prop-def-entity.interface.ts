/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { IMdcContrColumnPropDefEntityGenerated } from './mdc-contr-column-prop-def-entity-generated.interface';

export interface IMdcContrColumnPropDefEntity extends IMdcContrColumnPropDefEntityGenerated {
    CodeUpper: string;

    Selected: boolean;

    DataConfig: string;

	 Color?: number;

	SeriesType: number;

	itemId: number;

	ColumnType: string;

	RandValue1: number;
	RandValue2: number;
	RandValue3: number;
	RandValue4: number;
	RandValue5: number;
	RandValue6: number;
	RandValue7: number;
	RandValue8: number;
	RandValue9: number;
	RandValue10: number;

	RandBarValue1: number;
	RandBarValue2: number;
	RandBarValue3: number;
}

