/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable, inject } from '@angular/core';
import { QtoShareDetailDataService } from '@libs/qto/shared';
import { ISalesBillingQtoDetailEntity } from '../../model/entities/sales-billing-qto-detail-entity.interface';
import { SalesBillingQtoDetailComplete } from '../../model/complete-class/sales-billing-qto-detail-complete.class';
import { QtoShareBoqType } from '@libs/qto/shared';
import { SalesBillingQtoDetailReadonlyProcessor } from './processors/sales-billing-qto-detail-readonly-processor.service';
import { SalesBillingQtoDetailValidationService } from './validations/sales-billing-qto-detail-validation.service';
import { SalesBillingQtoHeaderDataService } from './sales-billing-qto-header-data.service';
import { ISalesBillingQtoHeaderEntity } from '../../model/entities/sales-billing-qto-header-entity.interface';
import { SalesBillingQtoHeaderComplete } from '../../model/complete-class/sales-billing-qto-header-complete.class';

/**
 * Sales Billing QTO Detail Data Service
 */
@Injectable({
	providedIn: 'root',
})
export class SalesBillingQtoDetailDataService extends QtoShareDetailDataService<
	ISalesBillingQtoDetailEntity,
	SalesBillingQtoDetailComplete,
	ISalesBillingQtoHeaderEntity,
	SalesBillingQtoHeaderComplete
> {
	public basRubricCategoryFk: number = -1;

	public constructor() {
		const qtoHeaderService = inject(SalesBillingQtoHeaderDataService);
		super(qtoHeaderService, {
			boqType: QtoShareBoqType.BillingBoq,
		});
	}

	protected override createReadonlyProcessor() {
		return new SalesBillingQtoDetailReadonlyProcessor(this);
	}

	protected override createDataValidationService() {
		return new SalesBillingQtoDetailValidationService(this);
	}
}
