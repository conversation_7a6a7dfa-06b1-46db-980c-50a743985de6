/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable, inject } from '@angular/core';
import { QtoShareDetailDataService } from '@libs/qto/shared';
import { ISalesBillingQtoDetailEntity } from '../../model/entities/sales-billing-qto-detail-entity.interface';
import { SalesBillingQtoDetailComplete } from '../../model/complete-class/sales-billing-qto-detail-complete.class';
import { QtoShareBoqType } from '@libs/qto/shared';
import { SalesBillingQtoDetailReadonlyProcessor } from './processors/sales-billing-qto-detail-readonly-processor.service';
import { SalesBillingQtoDetailValidationService } from './validations/sales-billing-qto-detail-validation.service';
import { SalesBillingBillsDataService } from '../sales-billing-bills-data.service';
import { IBilHeaderEntity } from '@libs/sales/interfaces';
import { BilHeaderComplete } from '../../model/complete-class/bil-header-complete.class';

/**
 * Sales Billing QTO Detail Data Service
 */
@Injectable({
	providedIn: 'root',
})
export class SalesBillingQtoDetailDataService extends QtoShareDetailDataService<
	ISalesBillingQtoDetailEntity,
	SalesBillingQtoDetailComplete,
	IBilHeaderEntity,
	BilHeaderComplete
> {
	public basRubricCategoryFk: number = -1;

	public constructor() {
		const billsDataService = inject(SalesBillingBillsDataService);
		super(billsDataService, {
			boqType: QtoShareBoqType.BillingBoq,
		});
	}

	protected override createReadonlyProcessor() {
		return new SalesBillingQtoDetailReadonlyProcessor(this);
	}

	protected override createDataValidationService() {
		return new SalesBillingQtoDetailValidationService(this);
	}
}
