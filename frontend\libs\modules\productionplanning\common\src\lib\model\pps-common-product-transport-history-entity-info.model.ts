import { IInitializationContext, prefixAllTranslationKeys } from '@libs/platform/common';
import { ILayoutConfiguration, ILayoutGroup } from '@libs/ui/common';
import { IPpsCommonProductHistoryEntity } from './entities/pps-common-product-history.interface';
import { ProjectSharedLookupOverloadProvider } from '@libs/project/shared';

export const GetPPSCommonProductTransportHistoryEntityLayout = async (ctx: IInitializationContext): Promise<ILayoutConfiguration<IPpsCommonProductHistoryEntity>> => {
	const allGroups: ILayoutGroup<IPpsCommonProductHistoryEntity>[] = [
		{
			gid: 'baseGroup',
			attributes: ['IsIncoming', 'Module', 'EntityCode', 'EntityDescription', 'PrjStockFk', 'PrjStockLocationFk', 'JobFk', 'TimeStamp'],
		},
	];
	return {
		groups: allGroups,
		overloads: {
			//TODO field type image is not supporting form framework for IsIncoming
			PrjStockFk: ProjectSharedLookupOverloadProvider.provideProjectStockLookupOverload(true),
			PrjStockLocationFk: ProjectSharedLookupOverloadProvider.provideProjectStockLocationLookupOverload(true),
		},
		labels: {
			...prefixAllTranslationKeys('productionplanning.common.product.', {
				IsIncoming: 'type',
				Module: 'module',
				TimeStamp: 'timeStamp',
			}),
			...prefixAllTranslationKeys('cloud.common.', {
				EntityCode: 'entityCode',
				EntityDescription: 'entityDescription',
			}),
			...prefixAllTranslationKeys('procurement.common.', {
				PrjStockFk: 'entityPrjStock',
				PrjStockLocationFk: 'entityPrjStockLocation',
			}),
			...prefixAllTranslationKeys('project.costcodes.', {
				JobFk: 'lgmJobFk',
			}),
		},
	};
};
