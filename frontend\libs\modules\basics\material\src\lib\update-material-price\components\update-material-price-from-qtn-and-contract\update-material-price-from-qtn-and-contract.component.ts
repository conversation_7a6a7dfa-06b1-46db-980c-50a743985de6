/*
 * Copyright(c) RIB Software GmbH
 */
import { Component, inject, Input, OnInit } from '@angular/core';
import {
	CollectionHelper,
	PlatformHttpService,
	PlatformTranslateService
} from '@libs/platform/common';
import {
	ConcreteFieldOverload,
	createLookup,
	FieldType,
	IControlContext,
	IGridConfiguration,
	UiCommonMessageBoxService,
} from '@libs/ui/common';
import {
	BasicsSharedCurrencyLookupService,
	BasicsSharedMaterialPriceVersionLookupService,
	BasicsSharedUomLookupService, BasicsSharedConStatusLookupService, BasicsSharedQuotationStatusLookupService, BasicsSharedDecimalPlacesEnum as decimalPlacesEnum
} from '@libs/basics/shared';
import { BusinessPartnerLookupService } from '@libs/businesspartner/shared';
import { BehaviorSubject } from 'rxjs';
import { BasicsMaterialRecordDataService } from '../../../material/basics-material-record-data.service';
import { BasicsMaterialUpdatePriceWizardOption } from '../../../model/enums/update-material-price-wizard-option.enum';
import { ProjectSharedLookupService } from '@libs/project/shared';
import {
	IBasicsMaterialQtnCon2PrcItemEntity,
	IBasicsMaterialUpdateMaterialPriceParamEntity,
} from '../../../model/entities/basics-material-update-price-entity.interface';
import { round } from 'mathjs';

@Component({
	selector: 'basics-material-update-material-price-from-qtn-and-contract',
	templateUrl: './update-material-price-from-qtn-and-contract.component.html',
	styleUrl: './update-material-price-from-qtn-and-contract.component.scss'
})
export class UpdateMaterialPriceFromQtnAndContractComponent implements OnInit {
	private readonly http = inject(PlatformHttpService);
	private readonly translateService = inject(PlatformTranslateService);
	private readonly materialDataService = inject(BasicsMaterialRecordDataService);
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	private readonly defaultValueOverloadSubject = new BehaviorSubject<ConcreteFieldOverload<IBasicsMaterialQtnCon2PrcItemEntity>>({
		type: FieldType.Text,
		readonly: true
	});

	@Input()
	protected priceForm!: IBasicsMaterialUpdateMaterialPriceParamEntity;

	public readonly lookups = {
		quoteStatusLookup: inject(BasicsSharedQuotationStatusLookupService),
		contractStatusLookup: inject(BasicsSharedConStatusLookupService),
		priceVersionLookup: inject(BasicsSharedMaterialPriceVersionLookupService),
		businessPartnerLookup: inject(BusinessPartnerLookupService),
		projectLookup: inject(ProjectSharedLookupService)
	};
	public readonly requiredDateFieldType = FieldType.DateUtc;
	public controlContextTemplate: IControlContext = {
		fieldId: '',
		readonly: false,
		validationResults: [],
		entityContext: {totalCount: 0},
		value: undefined
	};
	public quoteStartDateControlContext: IControlContext = {
		...this.controlContextTemplate,
		fieldId: 'quoteStartDateFieldId'
	};
	public quoteEndDateControlContext: IControlContext = {
		...this.controlContextTemplate,
		fieldId: 'quoteEndDateFieldId'
	};
	public contractStartDateControlContext: IControlContext = {
		...this.controlContextTemplate,
		fieldId: 'contractStartDateFieldId'
	};
	public contractEndDateControlContext: IControlContext = {
		...this.controlContextTemplate,
		fieldId: 'contractEndDateFieldId'
	};

	public gridConfig: IGridConfiguration<IBasicsMaterialQtnCon2PrcItemEntity> = {
		uuid: '87c664435ffc4146b8ccfc6dba2616df',
		columns: [],
		items: [],
		iconClass: null,
		skipPermissionCheck: true,
		enableColumnReorder: true,
		enableCopyPasteExcel: false,
		treeConfiguration: {
			parent: entity => {
				if (entity.Id) {
					return this.gridConfig?.items?.find(item => item.MdcMaterialFk === entity.Id) || null;
				}
				return null;
			},
			children: entity => {
				const list = CollectionHelper.Flatten(this.gridConfig?.items || [], (item) => {
					return item.Children || [];
				});
				return list.reduce((result: IBasicsMaterialQtnCon2PrcItemEntity[], item) => {
					if (entity.Id === item.MdcMaterialFk) {
						result.push(item);
					}
					return result;
				}, []) || [];
			}
		}
	};

	public async ngOnInit() {
		const defaultQtnStatus = await this.lookups.quoteStatusLookup.getDefaultAsync();
		const defaultContractStatus = await this.lookups.contractStatusLookup.getDefaultAsync();
		if (defaultQtnStatus) {
			this.priceForm.quoteStatusFks = [defaultQtnStatus.Id];
		}
		if (defaultContractStatus) {
			this.priceForm.contractStatusFks = [defaultContractStatus.Id];
		}
	}

	public constructor() {

		this.gridConfig = {
			...this.gridConfig,
			columns: [
				{
					id: 'selected',
					label: {key: 'basics.material.record.selected', text: 'Selected'},
					model: 'Selected',
					type: FieldType.Dynamic,
					overload: ctx => {
						let value: ConcreteFieldOverload<IBasicsMaterialQtnCon2PrcItemEntity> = {
							type: FieldType.Boolean,
							readonly: false
						};
						if (!ctx.entity?.TypeName) {
							value = {
								type: FieldType.Description,
								readonly: true
							};
						}
						this.defaultValueOverloadSubject.next(value);
						return this.defaultValueOverloadSubject;
					},
					//todo DEV-15667 formatter
					sortable: true,
					visible: true
				},
				{
					id: 'type',
					label: {key: 'basics.material.record.prcItemFromType', text: 'Type'},
					model: 'TypeName',
					readonly: true,
					sortable: true,
					type: FieldType.Description,
					visible: true
				},
				{
					id: 'code',
					label: {key: 'cloud.common.entityCode', text: 'Code'},
					model: 'Code',
					readonly: true,
					sortable: true,
					type: FieldType.Code,
					visible: true
				}, {
					id: 'description',
					label: {key: 'cloud.common.entityDescription', text: 'Description'},
					model: 'DescriptionInfo.Description',
					readonly: true,
					sortable: true,
					type: FieldType.Description,
					visible: true
				},
				{
					id: 'uom',
					label: {key: 'cloud.common.entityUoM', text: 'UoM'},
					model: 'BasUomFk',
					readonly: true,
					sortable: true,
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedUomLookupService
					}),
					visible: true
				},
				{
					id: 'unitRate',
					label: {key: 'basics.material.record.UnitRate', text: 'Unit Rate'},
					model: 'UnitRate',
					readonly: true,
					sortable: true,
					type: FieldType.Money,
					visible: true,
					change: (changeInfo) => {
						const entity = changeInfo.entity;
						const children = changeInfo.entity.Children || [];
						if (children.length > 0) {
							children.forEach(child => {
								if (entity.BasCurrencyFk === child.BasCurrencyFk && entity.BasUomFk === child.BasUomFk && entity.PriceUnit === child.PriceUnit) {
									child.Variance = child.UnitRate - entity.UnitRate;
								}
							});

						}
					}
				},
				{
					id: 'variance',
					label: {key: 'basics.common.variance', text: 'Variance'},
					model: 'Variance',
					readonly: true,
					sortable: true,
					type: FieldType.Money,
					visible: true
				}, {
					id: 'basCurrencyFk',
					label: {key: 'basics.common.CurrencyFk', text: 'Currency'},
					model: 'BasCurrencyFk',
					sortable: true,
					readonly: true,
					type: FieldType.Lookup,
					visible: true,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedCurrencyLookupService,
						showClearButton: true
					})
				}, {
					id: 'PriceUnit',
					label: {key: 'basics.common.PriceUnit', text: 'Price Unit'},
					model: 'PriceUnit',
					readonly: true,
					sortable: true,
					type: FieldType.Description
				}, {
					id: 'basUomPriceUnitFk',
					label: {key: 'basics.common.PriceUnitUoM', text: 'Price Unit UoM'},
					model: 'BasUomPriceUnitFk',
					sortable: true,
					readonly: true,
					type: FieldType.Lookup,
					visible: true,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedUomLookupService
					})
				}, {
					id: 'businessPartnerFk',
					label: {key: 'basics.common.BusinessPartner', text: 'Business Partner'},
					model: 'BusinessPartnerFk',
					sortable: true,
					type: FieldType.Lookup,
					readonly: true,
					visible: true,
					lookupOptions: createLookup({
						dataServiceToken: BusinessPartnerLookupService
					})
				}, {
					id: 'weighting',
					label: {key: 'basics.common.Weighting', text: 'Weighting'},
					model: 'Weighting',
					sortable: true,
					readonly: true,
					type: FieldType.Integer,
					visible: true
				}, {
					id: 'dateAsked',
					label: {key: 'basics.material.record.dateQuotedOrOrdered', text: 'Date Quoted/Ordered'},
					model: 'DateAsked',
					sortable: true,
					readonly: true,
					type: FieldType.DateUtc,
					visible: true
				}
			]
		};
	}

	private hasDateError(startDate: Date | null, endDate: Date | null): boolean {
		return startDate !== null && endDate !== null && startDate > endDate;
	}

	private getDateErrorText(dateHasError: boolean): string {
		return dateHasError ? this.translateService.instant({
			key: 'basics.material.updatePriceWizard.DateError',
			params: {
				'startDate': this.translateService.instant('basics.material.updatePriceWizard.startDate').text,
				'endDate': this.translateService.instant('basics.material.updatePriceWizard.endDate').text
			}
		}).text : '';
	}

	public quoteDateHasError() {
		const startDate = this.quoteStartDateControlContext.value as Date;
		const endDate = this.quoteEndDateControlContext.value as Date;
		return this.hasDateError(startDate, endDate);
	}

	public contractDateHasError() {
		const startDate = this.contractStartDateControlContext.value as Date;
		const endDate = this.contractEndDateControlContext.value as Date;
		return this.hasDateError(startDate, endDate);
	}

	public contractDateErrorText() {
		const dateHasError = this.contractDateHasError();
		return this.getDateErrorText(dateHasError);
	}

	public quoteDateErrorText() {
		const dateHasError = this.quoteDateHasError();
		return this.getDateErrorText(dateHasError);
	}

	public canSearch() {
		return !((this.priceForm.isCheckQuote && this.quoteDateHasError()) || (this.priceForm.isCheckContract && this.contractDateHasError()) || (!this.priceForm.isCheckQuote && !this.priceForm.isCheckContract));
	}


	public async search() {
		const searchOption = this.priceForm;
		searchOption.priceResultSet = [];
		searchOption.quoteStartDate = this.quoteStartDateControlContext.value as Date;
		searchOption.quoteEndDate = this.quoteEndDateControlContext.value as Date;
		searchOption.contractStartDate = this.contractStartDateControlContext.value as Date;
		searchOption.contractEndDate = this.contractEndDateControlContext.value as Date;
		const selectMaterials = this.materialDataService.getSelection();
		const materialList = this.materialDataService.getList();
		const scopeOption = searchOption.scopeOption;
		const catalogId = searchOption.catalogId;
		const resp = await this.http.post('basics/material/wizard/updatematerialprice/getfromquoteorcontract',
			{
				MaterialCatalogId: catalogId,
				QuoteStatusIds: searchOption.quoteStatusFks,
				ContractStatusIds: searchOption.contractStatusFks,
				QuoteStartDate: searchOption.quoteStartDate,
				QuoteEndDate: searchOption.quoteEndDate,
				ContractStartDate: searchOption.contractStartDate,
				ContractEndDate: searchOption.contractEndDate,
				IsCheckQuote: searchOption.isCheckQuote,
				IsCheckContract: searchOption.isCheckContract,
				Option: scopeOption,
				BusinessPartnerId: searchOption.businessPartnerId,
				ProjectId: searchOption.projectId,
				materials: scopeOption === BasicsMaterialUpdatePriceWizardOption.HighlightedMaterial ? selectMaterials : (scopeOption === BasicsMaterialUpdatePriceWizardOption.MaterialResultSet ? materialList : [])
			});
		if (resp) {
			const materialUpdateList = resp as IBasicsMaterialQtnCon2PrcItemEntity[];
			materialUpdateList.forEach(item => {
				if (item.Children.length > 0) {
					item.Children.forEach(child => {
						child.Variance = round(child.UnitRate - item.UnitRate, decimalPlacesEnum.decimalPlaces2);
						child.UnitRate = round(child.UnitRate, decimalPlacesEnum.decimalPlaces2);
						child.Selected = false;
					});
				}
				item.UnitRate = round(item.UnitRate, decimalPlacesEnum.decimalPlaces2);
			});
			this.priceForm.priceResultSet = materialUpdateList;
			this.gridConfig.items = materialUpdateList;
		} else {
			this.gridConfig.items = [];
			this.messageBoxService.showMsgBox('basics.material.updatePriceWizard.noItemsFound', 'cloud.common.informationDialogHeader',
				'ico-info');
		}
	}

}
