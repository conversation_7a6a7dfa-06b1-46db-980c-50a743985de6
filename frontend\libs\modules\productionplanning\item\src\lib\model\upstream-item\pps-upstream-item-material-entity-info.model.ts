import { EntityInfo } from '@libs/ui/business-base';
import { IPpsItem2MdcMaterialEntity} from '../models';
import { PpsUpstream2MaterialService } from '../../services/upstream-item/pps-upstream-2-material.service';
import { PpsUpstream2MaterialValidationService } from '../../services/upstream-item/pps-upstream-2-material-validation.service';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';
import { prefixAllTranslationKeys } from '@libs/platform/common';


export const PPS_UPSTREAM_MATERIAL_ENTITY_INFO = EntityInfo.create<IPpsItem2MdcMaterialEntity>({
	grid: {
		title: {key: 'productionplanning.item.ppsItem2MdcMaterial.listTitle'},
	},
	dataService: ctx => ctx.injector.get(PpsUpstream2MaterialService),
	validationService: ctx => ctx.injector.get(PpsUpstream2MaterialValidationService),
	dtoSchemeId: {moduleSubModule: 'ProductionPlanning.Item', typeName: 'PpsItem2MdcMaterialDto'},
	permissionUuid: 'sde4fbd7edsb345dfdr24v55e65ffgcu',
	layoutConfiguration: {
		groups: [
			{
				gid: 'baseGroup',
				attributes: ['MdcMaterialFk']
			}],
		overloads: {
			MdcMaterialFk: BasicsSharedLookupOverloadProvider.provideMaterialLookupOverload(true),
		},
		labels: {
			...prefixAllTranslationKeys('productionplanning.common.', {
				MdcMaterialFk: {key: 'mdcMaterialFk', text: '*Material'},
			}),
		},
	}
});