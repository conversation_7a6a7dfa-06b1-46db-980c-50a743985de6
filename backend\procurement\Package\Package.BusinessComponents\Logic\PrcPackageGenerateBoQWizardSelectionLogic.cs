using System;
using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Boq.Main.Core;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RVPC = RIB.Visual.Platform.Core;
using Resource = RIB.Visual.Procurement.Package.Localization.Properties.Resources;
using System.Linq.Expressions;
using System.Data.Entity.Infrastructure;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Platform.Core;
using System.IO;

namespace RIB.Visual.Procurement.Package.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public partial class PrcPackageGenerateBoQWizardLogic
	{
		private DbCompiledModel boqDbModel = RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder.DbModel;
		private Func<IQueryable<BoqItemEntity>, IQueryable<BoqItemEntity>> macthedBoqItemSortFunc = e => e.OrderByDescending(f => f.InsertedAt);

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public IEnumerable<SimulationEntity> GetSimulations(GetSimulationOptions options)
		{
			if (options == null)
			{
				throw new ArgumentNullException(nameof(options));
			}

			if (options.PackageLookupRequest == null)
			{
				ArgumentNullException.ThrowIfNull(options.PackageLookupRequest);
			}

			if (options.PackageLookupRequest.PageState == null)
			{
				ArgumentNullException.ThrowIfNull(options.PackageLookupRequest.PageState);
			}

			string type = null;
			var selectedBoqHeaderIds = new List<int>();
			var selectedboqItemIds = new List<int>();
			IEnumerable<SimulationEntity> results = new List<SimulationEntity>();
			
			if (options.SelectedWicBoqItemIds != null && options.SelectedWicBoqItemIds.Any())
			{
				type = "wic";
				foreach (var wicBoqItemId in options.SelectedWicBoqItemIds)
				{
					if (wicBoqItemId.PKey1.HasValue && !selectedBoqHeaderIds.Contains(wicBoqItemId.PKey1.Value))
					{
						selectedBoqHeaderIds.Add(wicBoqItemId.PKey1.Value);
					}

					if (!selectedboqItemIds.Contains(wicBoqItemId.Id))
					{
						selectedboqItemIds.Add(wicBoqItemId.Id);
					}
				}
			}

			if (options.SelectedPrjBoqItemIds != null && options.SelectedPrjBoqItemIds.Any())
			{
				type = "project";
				foreach (var prjBoqItemId in options.SelectedPrjBoqItemIds)
				{
					if (prjBoqItemId.PKey1.HasValue && !selectedBoqHeaderIds.Contains(prjBoqItemId.PKey1.Value))
					{
						selectedBoqHeaderIds.Add(prjBoqItemId.PKey1.Value);
					}

					if (!selectedboqItemIds.Contains(prjBoqItemId.Id))
					{
						selectedboqItemIds.Add(prjBoqItemId.Id);
					}
				}
			}

			if (options.SelectedLineItemPrcStructureIds != null && options.SelectedLineItemPrcStructureIds.Any())
			{
				type = "lineitem.prcstructure";
			}

			if (options.SelectedPrjBoqPrcStructureIds != null && options.SelectedPrjBoqPrcStructureIds.Any())
			{
				type = "prjboq.prcstructure";
				options.SelectedLineItemPrcStructureIds = options.SelectedPrjBoqPrcStructureIds; // project boq strcuture and line item structure use the same logic
			}

			if (options.SelectedResourceIds != null && options.SelectedResourceIds.Any())
			{
				type = "resource";
			}

			switch (type)
			{
				case "wic":
					{
						results = GetSimulationsForWicBoq(options, selectedboqItemIds);
					}
					break;
				case "project":
					{
						results = GetSimulationsForProjectBoq(options);
					}
					break;
				case "lineitem.prcstructure":
					{
						if (options.MatchedEstHeaderId2LineItemIdsMap == null || !options.MatchedEstHeaderId2LineItemIdsMap.Any())
						{
							break;
						}
						results = GetSimulationsForLineItemPrcStructure(options);
					}
					break;
				case "prjboq.prcstructure":
					{
						if (options.MatchedEstHeaderId2LineItemIdsMap == null || !options.MatchedEstHeaderId2LineItemIdsMap.Any())
						{
							break;
						}
						results = GetSimulationsForPrjBoqPrcStructure(options);
					}
					break;
				case "resource":
					{
						results = GetSimulationsForResource(options);
					}
					break;
				default:
					break;
			}

			return results;
		}

		private Data GetData(GetSimulationOptions options)
		{
			var packageLookupOption = new PackageWithUpdateOptionLookupOptions()
			{
				SortFunc = e => e.OrderByDescending(f => f.InsertedAt)
			};
			var packageLookupLogic = new PackageWithUpdateOptionLookupLogic(packageLookupOption);
			var packageWithUpdateOptons = packageLookupLogic.GetLookupSearchList(options.PackageLookupRequest).SearchList;
			var packageIds = packageWithUpdateOptons.CollectIds(e => e.Id);
			var packages = new PrcPackageLogic().GetSearchList(e => packageIds.Contains(e.Id));
			var subPackages = new PrcPackage2HeaderLogic().GetSearchList(e => packageIds.Contains(e.PrcPackageFk));
			var prcHeaderIds = subPackages.CollectIds(e => e.PrcHeaderFk);
			var prcBoqs = new PrcBoqLogic().GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk)).OrderByDescending(e => e.InsertedAt);
			var boqHeaderIds = prcBoqs.CollectIds(e => e.BoqHeaderFk);

			var data = new Data();
			var id2PackageDic = new Dictionary<int, PrcPackageEntity>();
			var prcHeaderId2SubPackageDic = new Dictionary<int, PrcPackage2HeaderEntity>();
			var boqHeaderId2PrcBoqDic = new Dictionary<int, PrcBoqEntity>();
			var id2PackageWithUpdateOptionsDic = new Dictionary<int, PackageWithUpdateOptionEntity>();
			foreach (var package in packages)
			{
				if (!id2PackageDic.ContainsKey(package.Id))
				{
					id2PackageDic.Add(package.Id, package);
				}
			}
			foreach (var subPackage in subPackages)
			{
				if (!prcHeaderId2SubPackageDic.ContainsKey(subPackage.PrcHeaderFk))
				{
					prcHeaderId2SubPackageDic.Add(subPackage.PrcHeaderFk, subPackage);
				}
			}
			foreach (var prcBoq in prcBoqs)
			{
				if (!boqHeaderId2PrcBoqDic.ContainsKey(prcBoq.BoqHeaderFk))
				{
					boqHeaderId2PrcBoqDic.Add(prcBoq.BoqHeaderFk, prcBoq);
				}
			}

			foreach (var package in packageWithUpdateOptons)
			{
				if (!id2PackageWithUpdateOptionsDic.ContainsKey(package.Id))
				{
					id2PackageWithUpdateOptionsDic.Add(package.Id, package);
				}
			}
			data.id2PackageDic = id2PackageDic;
			data.prcHeaderId2SubPackageDic = prcHeaderId2SubPackageDic;
			data.boqHeaderId2PrcBoqDic = boqHeaderId2PrcBoqDic;
			data.BoqHeaderIds = boqHeaderIds;
			data.id2PackageWithUpdateOptionDic = id2PackageWithUpdateOptionsDic;
			return data;
		}

		private Data GetNextPageData(int packageIndex, GetSimulationOptions options)
		{
			// if some wic boqs are not found, get next page package, and do the whole loop again until matched data are found or no valid data source
			var nextOptions = new GetSimulationOptions();
			nextOptions.CopyPropertyFrom(options);
			nextOptions.PackageLookupRequest.PageState.PageNumber = packageIndex;
			var data = GetData(nextOptions);
			return data;
		}

		private string GetMatchness(CreatePackageMatchnessType type)
		{
			var estimateCreateLogic = Injector.Get<IEstimateMainWizardLogic>();
			switch(type)
			{
				case CreatePackageMatchnessType.RoughMatched:
					return Resource.CreatePackage_MatchnessType_RoughMatched;
				default:
					return estimateCreateLogic.GetMatchness((int)type);
			}
		}

		private SimulationEntity GetNewSimulationData(IPrcPackageEntity newPackage, string defaultDescription)
		{
			return new SimulationEntity()
			{
				Selected = true,
				Matchness = GetMatchness(CreatePackageMatchnessType.New),
				MatchnessType = CreatePackageMatchnessType.New,
				Code = newPackage.Code,
				PackageDescription = defaultDescription,
				SubPackage = defaultDescription,
				PackageStatusFk = newPackage.PackageStatusFk,
				StructureCodeFk = newPackage.StructureFk.HasValue ? newPackage.StructureFk.Value : null,
				BasicStructureCodeFk = newPackage.StructureFk.HasValue ? newPackage.StructureFk.Value : null,
				ConfigurationFk = newPackage.ConfigurationFk.HasValue ? newPackage.ConfigurationFk.Value : 0,
				StructureConfigurationFk = newPackage.StructureFk.HasValue && newPackage.ConfigurationFk.HasValue ? newPackage.ConfigurationFk.Value : 0,
				ClerkPrcFk = newPackage.ClerkPrcFk.HasValue ? newPackage.ClerkPrcFk.Value : null,
				IsReadOnlyPackageCode = newPackage.IsReadOnlyPackageCode,
				Package = newPackage
			};
		}

		private MatchedPackgageInfo GetMatchedPackage(Data data, int boqHeaderFk, bool isForResource = false)
		{
			data.boqHeaderId2PrcBoqDic.TryGetValue(boqHeaderFk, out var prcBoq);
			if (prcBoq != null)
			{
				data.prcHeaderId2SubPackageDic.TryGetValue(prcBoq.PrcHeaderFk, out var subPackage);
				if (subPackage != null)
				{
					data.id2PackageDic.TryGetValue(subPackage.PrcPackageFk, out var package);
					if (package != null)
					{
						var matchedData = new MatchedPackgageInfo();
						matchedData.Package = package;
						matchedData.SubPackage = subPackage;
						matchedData.PrcBoq = prcBoq;

						if (isForResource &&
							data.id2PackageWithUpdateOptionDic.TryGetValue(subPackage.PrcPackageFk, out var packageWithUpdateOption) &&
							packageWithUpdateOption != null)
						{
							matchedData.PackageWithUpdateOption = packageWithUpdateOption;
						}
						return matchedData;
					}

				}
			}
			return null;
		}

		private void UpdateSimulationData(SimulationEntity simulation, MatchedPackgageInfo matchedData, CreatePackageMatchnessType type)
		{
			if (simulation == null || matchedData == null)
			{
				return;
			}

			simulation.MatchnessType = type;
			simulation.Matchness = GetMatchness(type);
			simulation.Selected = false;
			simulation.Code = null;
			if (matchedData.SubPackage != null)
			{
				simulation.SubPackageFk = matchedData.SubPackage.Id;
			}
			if (matchedData.Package != null)
			{
				var package = matchedData.Package;
				simulation.PackageCodeFk = package.Id;
				simulation.packageCode = package.Code;
				simulation.StructureCodeFk = package.StructureFk.HasValue ? package.StructureFk.Value : null;
				simulation.PackageStatusFk = package.PackageStatusFk;
				simulation.ConfigurationFk = package.ConfigurationFk.HasValue ? package.ConfigurationFk.Value : 0;
				simulation.StructureConfigurationFk = package.StructureFk.HasValue && package.ConfigurationFk.HasValue ?
					package.ConfigurationFk.Value : 0;
				simulation.ClerkPrcFk = package.ClerkPrcFk.HasValue ? package.ClerkPrcFk.Value : null;
				simulation.Package = package;
				if (matchedData.PackageWithUpdateOption != null)
				{
					simulation.BoqStructureOption4SourceResources = matchedData.PackageWithUpdateOption.ResourceBoqStructure;
				}
			}
		}

		private SelectedBoqItemData GetSelectedBoqItemData(IEnumerable<RVPC.IdentificationData> selectedBoqItemIds)
		{
			var boqItemLogic = new BoqItemLogic();
			var selectedBoqHeaderIds = new List<int>();
			var selectedBoqHeaderId2ItemIdsMap = new Dictionary<int, List<int>>();

			foreach (var item in selectedBoqItemIds)
			{
				if (!item.PKey1.HasValue)
				{
					continue;
				}
				selectedBoqHeaderIds.Add(item.PKey1.Value);
				if (!selectedBoqHeaderId2ItemIdsMap.TryGetValue(item.PKey1.Value, out var itemIdList))
				{
					itemIdList = new List<int>();
					selectedBoqHeaderId2ItemIdsMap.Add(item.PKey1.Value, itemIdList);
				}
				itemIdList.Add(item.Id);
			}

			var selectedBoqRootItems = boqItemLogic.GetPureBoqRootItem(selectedBoqHeaderIds);
			var boqHeaderId2BoqRootItemMap = new Dictionary<int, BoqItemEntity>();
			foreach (var item in selectedBoqRootItems)
			{
				if (!boqHeaderId2BoqRootItemMap.TryGetValue(item.BoqHeaderFk, out var rootItem))
				{
					boqHeaderId2BoqRootItemMap.Add(item.BoqHeaderFk, item);
				}
			}
			var id2SelectedBoqItemMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var selectedBoqitems = new List<BoqItemEntity>();
			foreach (var keyValue in selectedBoqHeaderId2ItemIdsMap)
			{
				var temp = boqItemLogic.GetPureBoqItemsById(keyValue.Key, keyValue.Value);
				if (temp != null && temp.Any())
				{
					selectedBoqitems.AddRange(temp);
					foreach (var item in temp)
					{
						var tempBoqId = new RVPC.IdentificationData()
						{
							Id = item.Id,
							PKey1 = item.BoqHeaderFk
						};
						if (!id2SelectedBoqItemMap.TryGetValue(tempBoqId, out var boqItem))
						{
							id2SelectedBoqItemMap.Add(tempBoqId, item);
						}
					}
				}
			}

			return new SelectedBoqItemData()
			{
				BoqHeaderId2BoqRootItemMap = boqHeaderId2BoqRootItemMap,
				Id2BoqItemMap = id2SelectedBoqItemMap,
				SelectedBoqItems = selectedBoqitems,
				BoqHeaderId2BoqItemIdListMap = selectedBoqHeaderId2ItemIdsMap
			};
		}

		private IEnumerable<SimulationEntity> GetSimulationsForWicBoq(GetSimulationOptions options, IEnumerable<int> selectedboqItemIds)
		{
			var packageLogic = new PrcPackageLogic();
			var boqItemLogic = new BoqItemLogic();
			var wicBoqId2SimulationsMap = new Dictionary<RVPC.IdentificationData, SimulationEntity>();
			var pageSize = options.PackageLookupRequest.PageState.PageSize;
			var newPackage = packageLogic.GetDefaultPackage(options.DefaultProjectId, options.DefaultConfigurationId, options.DefaultStructureId, true, true);

			var selectedBoqItemData = GetSelectedBoqItemData(options.SelectedWicBoqItemIds);
			var boqHeaderId2WicBoqRootItemMap = selectedBoqItemData != null ? selectedBoqItemData.BoqHeaderId2BoqRootItemMap : new Dictionary<int, BoqItemEntity>();
			var id2WicBoqItemMap = selectedBoqItemData != null ? selectedBoqItemData.Id2BoqItemMap : new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var index = 1;
			foreach (var item in options.SelectedWicBoqItemIds)
			{
				if (!item.PKey1.HasValue)
				{
					continue;
				}

				// create default simulation data
				var simulation = GetNewSimulationData(newPackage, options.DefaultDescription);
				simulation.Id = ++index;
				simulation.BoqHeaderFk = item.PKey1.Value;
				simulation.BoqItemFk = item.Id;
				if (boqHeaderId2WicBoqRootItemMap.TryGetValue(item.PKey1.Value, out var boqRootItem))
				{
					simulation.BoqHeader = boqRootItem.Reference;
				}
				if (id2WicBoqItemMap.TryGetValue(item, out var boqItem))
				{
					simulation.BoqReference = boqItem.Reference;
					simulation.BoqBriefInfo = boqItem.BriefInfo;
				}
				wicBoqId2SimulationsMap.Add(item, simulation);
			}

			var packageIndex = 0;
			var simulationCount = wicBoqId2SimulationsMap.Count;
			var simulationUpdatedCount = 0;
			// paging to get package
			var data = GetData(options);
			// if packages are found, go ahead
			while (data != null && data.id2PackageDic.Any())
			{
				var boqHeaderIds = data.BoqHeaderIds;
				var boqHeaderIdIndex = 0;
				var boqHeaderIdGroup = Math.Ceiling((decimal)boqHeaderIds.Count() / pageSize);
				var currentWicBoqGroupIndex = 0;
				var wicBoqGroup = Math.Ceiling((decimal)selectedboqItemIds.Count() / pageSize);

				// paging to search by boq header ids. if wic boq are found, stop the loop
				while (boqHeaderIdIndex < boqHeaderIdGroup && simulationCount > simulationUpdatedCount)
				{
					var partialBoqHeaderIds = boqHeaderIds.Skip(boqHeaderIdIndex * pageSize).Take(pageSize).ToList();
					// paging to search by wic boq.
					while (currentWicBoqGroupIndex < wicBoqGroup && simulationCount > simulationUpdatedCount)
					{
						var partialWicBoqItemIds = selectedboqItemIds.Skip(currentWicBoqGroupIndex * pageSize).Take(pageSize).ToList();
						var matchedBoqItems = boqItemLogic.GetSearchList(e => partialBoqHeaderIds.Contains(e.BoqHeaderFk) &&
							e.BoqItemWicItemFk.HasValue && partialWicBoqItemIds.Contains(e.BoqItemWicItemFk.Value), sortFunc: macthedBoqItemSortFunc);
						if (matchedBoqItems.Any())
						{
							foreach (var item in matchedBoqItems)
							{
								var temp = new RVPC.IdentificationData()
								{
									Id = item.BoqItemWicItemFk.Value,
									PKey1 = item.BoqItemWicBoqFk.Value,
								};

								// if matched package is found, update the simulation data. only new data need to update
								if (wicBoqId2SimulationsMap.TryGetValue(temp, out var simulation) && simulation != null &&
									simulation.MatchnessType == CreatePackageMatchnessType.New)
								{
									simulationUpdatedCount++;
									var matchedData = GetMatchedPackage(data, item.BoqHeaderFk);
									UpdateSimulationData(simulation, matchedData, CreatePackageMatchnessType.PerfectlyMatched);
								}

								if (simulationCount <= simulationUpdatedCount)
								{
									break;
								}
							}
						}
						currentWicBoqGroupIndex++; // next wic boq group
					}
					boqHeaderIdIndex++; // next boq header group
				}

				packageIndex++;
				if (simulationCount > simulationUpdatedCount)
				{
					data = GetNextPageData(packageIndex, options);
				}
				else
				{
					data = null;
				}
			}
			return wicBoqId2SimulationsMap.Values.ToList();
		}

		private IEnumerable<SimulationEntity> GetSimulationsForProjectBoq(GetSimulationOptions options)
		{
			var packageLogic = new PrcPackageLogic();
			var boqItemLogic = new BoqItemLogic();
			var simulationList = new Dictionary<ProjectBoqData, SimulationEntity>();
			var boqHeaderId2PrjBoqRootItemMap = new Dictionary<int, BoqItemEntity>();
			IEnumerable<BoqItemEntity> projectBoqItems = new List<BoqItemEntity>();
			var boqLineTypeIds = new List<int>();
			var boqUomIds = new List<int>();
			var pageSize = options.PackageLookupRequest.PageState.PageSize;
			var newPackage = packageLogic.GetDefaultPackage(options.DefaultProjectId, options.DefaultConfigurationId, options.DefaultStructureId, true, true);
			var index = 1;

			var selectedBoqItemData = GetSelectedBoqItemData(options.SelectedPrjBoqItemIds);
			if (selectedBoqItemData != null)
			{
				boqHeaderId2PrjBoqRootItemMap = selectedBoqItemData.BoqHeaderId2BoqRootItemMap;
				projectBoqItems = selectedBoqItemData.SelectedBoqItems;
			}

			foreach (var item in projectBoqItems)
			{
				if (item.BoqLineTypeFk == (int)BoqLineType.Note ||
					item.BoqLineTypeFk == (int)BoqLineType.DesignDescription ||
					item.BoqLineTypeFk == (int)BoqLineType.TextElement ||
					item.BoqLineTypeFk == (int)BoqLineType.SubDescription)
				{
					continue;
				}

				boqLineTypeIds.Add(item.BoqLineTypeFk);
				boqUomIds.Add(item.BasUomFk);
				string boqHeaderRef = null;
				if (boqHeaderId2PrjBoqRootItemMap.TryGetValue(item.BoqHeaderFk, out var rootItem))
				{
					boqHeaderRef = rootItem.Reference;
				}
				var projectBoqTemp = new ProjectBoqData()
				{
					Reference = item.Reference,
					Description = item.BriefInfo.Translated,
					UomFk = item.BasUomFk,
					BoqLineItemTypeFk = item.BoqLineTypeFk,
					BoqHeaderRef = boqHeaderRef
				};

				if (simulationList.TryGetValue(projectBoqTemp, out var simulation))
				{
					continue;
				}

				// create default simulation data
				simulation = GetNewSimulationData(newPackage, options.DefaultDescription);
				simulation.Id = ++index;
				simulation.BoqReference = item.Reference;
				simulation.BoqBriefInfo = item.BriefInfo;
				simulation.BoqHeaderFk = item.BoqHeaderFk;
				simulation.BoqItemFk = item.Id;
				simulation.BoqUomFk = item.BasUomFk;
				simulation.BoqLineTypeFk = item.BoqLineTypeFk;
				simulation.BoqHeader = boqHeaderRef;
				
				simulationList.Add(projectBoqTemp, simulation);
			}
			boqLineTypeIds = boqLineTypeIds.Distinct().ToList();

			var packageIndex = 0;
			var simulationCount = simulationList.Count;
			var simulationUpdatedCount = 0;
			// paging to get package
			var data = GetData(options);
			var packageBoqHeaderId2BoqItemMap = new Dictionary<int, IBoqItemEntity>();
			// if packages are found, go ahead
			while (data != null && data.id2PackageDic.Any())
			{
				var boqHeaderIds = data.BoqHeaderIds;
				var boqHeaderIdIndex = 0;
				var boqHeaderIdGroup = Math.Ceiling((decimal)boqHeaderIds.Count() / pageSize);
				// paging to search by boq header ids. if wic boq are found, stop the loop
				while (boqHeaderIdIndex < boqHeaderIdGroup && simulationCount > simulationUpdatedCount)
				{
					var partialBoqHeaderIds = boqHeaderIds.Skip(boqHeaderIdIndex * pageSize).Take(pageSize).ToList();
					// paging to search by filters
					var matchedIndex = 0;
					var packageBoqRootItems = boqItemLogic.GetPureBoqRootItem(partialBoqHeaderIds);
					foreach (var item in packageBoqRootItems)
					{
						if (item.BoqLineTypeFk != (int)BoqLineType.Root)
						{
							continue;
						}
						var boqHeaderId = item.BoqHeaderFk;
						if (!packageBoqHeaderId2BoqItemMap.ContainsKey(boqHeaderId))
						{
							packageBoqHeaderId2BoqItemMap.Add(boqHeaderId, item);
						}
					}
					Expression<Func<BoqItemEntity, bool>> boqItemFilterExpression = e => boqLineTypeIds.Contains(e.BoqLineTypeFk) && boqUomIds.Contains(e.BasUomFk) && partialBoqHeaderIds.Contains(e.BoqHeaderFk);
					var matchedBoqItems = boqItemLogic.GetItemsByPaging(boqDbModel, matchedIndex, pageSize, boqItemFilterExpression, macthedBoqItemSortFunc);
					while (matchedBoqItems != null && matchedBoqItems.Any())
					{
						foreach (var item in matchedBoqItems)
						{
							string boqHeaderRef = null;
							if (packageBoqHeaderId2BoqItemMap.TryGetValue(item.BoqHeaderFk, out var rootItem))
							{
								boqHeaderRef = rootItem.Reference;
							}
							var projectBoqTemp = new ProjectBoqData()
							{
								Reference = item.Reference,
								Description = item.BriefInfo.Translated,
								UomFk = item.BasUomFk,
								BoqLineItemTypeFk = item.BoqLineTypeFk,
								BoqHeaderRef = boqHeaderRef
							};
							if (simulationList.TryGetValue(projectBoqTemp, out var simulation) && simulation != null &&
								simulation.MatchnessType == CreatePackageMatchnessType.New)
							{
								simulationUpdatedCount++;
								var matchedData = GetMatchedPackage(data, item.BoqHeaderFk);
								UpdateSimulationData(simulation, matchedData, CreatePackageMatchnessType.RoughMatched);
							}

							if (simulationCount <= simulationUpdatedCount)
							{
								break;
							}
						}

						if (simulationCount > simulationUpdatedCount)
						{
							matchedIndex++;
							matchedBoqItems = boqItemLogic.GetItemsByPaging(boqDbModel, matchedIndex, pageSize, boqItemFilterExpression, macthedBoqItemSortFunc);
						}
						else
						{
							matchedBoqItems = null;
						}
					}

					boqHeaderIdIndex++;
				}

				packageIndex++;
				if (simulationCount > simulationUpdatedCount)
				{
					data = GetNextPageData(packageIndex, options);
				}
				else
				{
					data = null;
				}
			}

			return simulationList.Values.ToList();
		}

		private List<SimulationEntity> GetSimulationsForLineItemPrcStructure(GetSimulationOptions options, IEnumerable<IEstLineItemEntity> matchedLineItems = null)
		{
			var packageLogic = new PrcPackageLogic();
			var boqItemLogic = new BoqItemLogic();
			if (matchedLineItems == null)
			{
				matchedLineItems = GetLineItemsByEstHeader2LineItemIdsMap(options.MatchedEstHeaderId2LineItemIdsMap);
			}
			var uoms = new List<int>();
			var hasNAStructureFk = options.SelectedLineItemPrcStructureIds.Any(e => e == 0);
			var pageSize = options.PackageLookupRequest.PageState.PageSize;
			var newPackage = packageLogic.GetDefaultPackage(options.DefaultProjectId, options.DefaultConfigurationId, options.DefaultStructureId, true, true);
			var index = 1;
			// it is used for collect the packge ids which matched line items
			// key: structure id; value: {key: line item data, value: matched package id}
			var structureId2LineItemWithPackageIdsMap = new Dictionary<int, Dictionary<LineItemData, List<int>>>();

			var simulationList = new Dictionary<int, SimulationEntity>();
			var descendantId2parentIdMap = new Dictionary<int, int>();
			var selectedPrcStructureIds = new List<int>();

			foreach (var keyValue in options.SelectedParentId2DescendantIdsMap)
			{
				var parentIdStr = keyValue.Key;
				if (int.TryParse(parentIdStr, out var parentId))
				{
					selectedPrcStructureIds.Add(parentId);
					var descendantIds = keyValue.Value;
					if (descendantIds == null || !descendantIds.Any())
					{
						continue;
					}

					foreach (var descendantId in descendantIds)
					{
						if (descendantId == null)
						{
							continue;
						}

						if (!descendantId2parentIdMap.TryGetValue(descendantId.Id, out var pId))
						{
							descendantId2parentIdMap.Add(descendantId.Id, parentId);
						}
					}
				}
			}

			var selectedPrcStructures = new PrcStructureLogic().GetSearchList(e => selectedPrcStructureIds.Contains(e.Id));
			// selected prc structurefk map: key: structure id, value: structure
			var selectedPrcStructuresMap = selectedPrcStructures.ToDictionary(e => e.Id, e => e);
			foreach (var id in selectedPrcStructureIds)
			{
				selectedPrcStructuresMap.TryGetValue(id, out var structure);
				if	(id != 0 && structure == null)
				{
					continue;
				}

				// create default simulation data
				if (id != 0)
				{
					var description = structure.DescriptionInfo != null ? structure.DescriptionInfo.Translated : options.DefaultDescription;
					var simulation = GetNewSimulationData(newPackage, description);
					simulation.Id = ++index;
					simulation.StructureCodeFk = id;
					simulation.BasicStructureCodeFk = id;
					simulationList.Add(id, simulation);
				}
				else // id == 0, it means the structure is N/A in selection
				{
					var description = options.DefaultDescription;
					var simulation = GetNewSimulationData(newPackage, description);
					simulation.Id = ++index;
					simulation.StructureCodeFk = null;
					simulation.BasicStructureCodeFk = id;
					simulationList.Add(id, simulation);
				}
			}

			var lineItemNeed2MatchCount = 0;
			foreach (var lineItem in matchedLineItems)
			{
				if (!hasNAStructureFk && !lineItem.PrcStructureFk.HasValue)
				{
					continue;
				}

				if (hasNAStructureFk && !lineItem.PrcStructureFk.HasValue)
				{
					const int structureId = 0;
					if (!structureId2LineItemWithPackageIdsMap.TryGetValue(structureId, out var lineItemDic))
					{
						lineItemDic = new Dictionary<LineItemData, List<int>>();
						structureId2LineItemWithPackageIdsMap.Add(structureId, lineItemDic);
					}
					var tempLineItem = new LineItemData()
					{
						UomFk = lineItem.BasUomFk,
						Description = lineItem.DescriptionInfo.Translated
					};

					if (!lineItemDic.TryGetValue(tempLineItem, out var hasMatchedPackageIds))
					{
						lineItemNeed2MatchCount++;
						lineItemDic.Add(tempLineItem, new List<int>());
					}
					uoms.Add(lineItem.BasUomFk);
				}

				if (lineItem.PrcStructureFk.HasValue)
				{
					var structureId = lineItem.PrcStructureFk.Value;
					if (!descendantId2parentIdMap.TryGetValue(structureId, out structureId))
					{
						continue;
					}
					// group line items according to the line item's strucutrefk					
					if (!structureId2LineItemWithPackageIdsMap.TryGetValue(structureId, out var lineItemDic))
					{
						lineItemDic = new Dictionary<LineItemData, List<int>>();
						structureId2LineItemWithPackageIdsMap.Add(structureId, lineItemDic);
					}
					var tempLineItem = new LineItemData()
					{
						UomFk = lineItem.BasUomFk,
						Description = lineItem.DescriptionInfo.Translated
					};

					if (!lineItemDic.TryGetValue(tempLineItem, out var hasMatchedPackageIds))
					{
						lineItemNeed2MatchCount++;
						lineItemDic.Add(tempLineItem, new List<int>());
					}
					uoms.Add(lineItem.BasUomFk);
				}	
			}

			uoms = uoms.Distinct().ToList();

			var packageIndex = 0;
			// paging to get package
			var data = GetData(options);
			var packageId2PackageInfoData = new Dictionary<int, MatchedPackgageInfo>();
			var isAllMatchedPackageFound = false;
			var lineItemMatchedCount = 0;

			// if packages are found, go ahead
			while (data != null && data.id2PackageDic.Any())
			{
				var boqHeaderIds = data.BoqHeaderIds;
				var boqHeaderIdIndex = 0;
				var boqHeaderIdGroup = Math.Ceiling((decimal)boqHeaderIds.Count() / pageSize);
				// paging to search by boq header ids. if matched data are found, stop the loop
				while (boqHeaderIdIndex < boqHeaderIdGroup && !isAllMatchedPackageFound)
				{
					var partialBoqHeaderIds = boqHeaderIds.Skip(boqHeaderIdIndex * pageSize).Take(pageSize).ToList();
					// paging to search by filters
					var matchedIndex = 0;
					Expression<Func<BoqItemEntity, bool>> boqItemFilterExpression =
						e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position && uoms.Contains(e.BasUomFk) && partialBoqHeaderIds.Contains(e.BoqHeaderFk);
					var matchedBoqItems = boqItemLogic.GetItemsByPaging(boqDbModel, matchedIndex, pageSize, boqItemFilterExpression, macthedBoqItemSortFunc);
					while (matchedBoqItems != null && matchedBoqItems.Any())
					{
						foreach (var item in matchedBoqItems)
						{
							var tempLineItem = new LineItemData()
							{
								UomFk = item.BasUomFk,
								Description = item.BriefInfo.Translated
							};

							// collect the matched package data to line item
							var matchedData = GetMatchedPackage(data, item.BoqHeaderFk);
							if (matchedData != null && matchedData.Package != null)
							{
								var packageId = matchedData.Package.Id;
								if (!packageId2PackageInfoData.TryGetValue(packageId, out var matchedPackgage))
								{
									packageId2PackageInfoData.Add(packageId, matchedData);
								}
								foreach (var keyValue in structureId2LineItemWithPackageIdsMap)
								{
									if (keyValue.Value.TryGetValue(tempLineItem, out var list) && !list.Exists(e => e == packageId))
									{
										list.Add(packageId);
										if (list.Count == 1)
										{
											lineItemMatchedCount++;
											if (lineItemNeed2MatchCount == lineItemMatchedCount)
											{
												isAllMatchedPackageFound = true;
												break;
											}
										}
									}
								}
							}

							if (isAllMatchedPackageFound)
							{
								break;
							}
						}

						if (!isAllMatchedPackageFound)
						{
							matchedIndex++;
							matchedBoqItems = boqItemLogic.GetItemsByPaging(boqDbModel, matchedIndex, pageSize, boqItemFilterExpression, macthedBoqItemSortFunc);
						}
						else
						{
							matchedBoqItems = null;
						}
					}

					boqHeaderIdIndex++;
				}

				packageIndex++;
				if (!isAllMatchedPackageFound)
				{
					data = GetNextPageData(packageIndex, options);
				}
				else
				{
					data = null;
				}
			}

			foreach (var strucId2simulation in simulationList)
			{
				var structureId = strucId2simulation.Key;
				var simulation = strucId2simulation.Value;
				// mark how many line items the package is matched.
				var packageId2Count = new Dictionary<int, int>();
				if (!structureId2LineItemWithPackageIdsMap.TryGetValue(structureId, out var lineItemWithPackageIds))
				{
					continue;
				}

				foreach (var packageIds in lineItemWithPackageIds.Select(e => e.Value))
				{
					foreach (var id in packageIds)
					{
						if (!packageId2Count.TryGetValue(id, out var count))
						{
							packageId2Count.Add(id, 0);
						}
						packageId2Count[id]++;
					}
				}

				// pick the package id which has more package boq item lines matched the grouped line item.
				int? matcheckedPackageId = null;
				int? matchedCount = null;
				foreach (var keyValue in packageId2Count)
				{
					if (!matcheckedPackageId.HasValue)
					{
						matcheckedPackageId = keyValue.Key;
						matchedCount = keyValue.Value;
						continue;
					}
					var tempPackageId = keyValue.Key;
					var tempCount = keyValue.Value;
					if (tempCount > matchedCount ||
						(tempCount == matchedCount && tempPackageId > matcheckedPackageId))
					{
						matcheckedPackageId = tempPackageId;
					}
				}

				if (matcheckedPackageId.HasValue && packageId2PackageInfoData.TryGetValue(matcheckedPackageId.Value, out var matchedData))
				{
					UpdateSimulationData(simulation, matchedData, CreatePackageMatchnessType.RoughMatched);
				}
			}
			return simulationList.Values.ToList();
		}

		private IEnumerable<SimulationEntity> GetSimulationsForPrjBoqPrcStructure(GetSimulationOptions options)
		{
			var boqItemLogic = new BoqItemLogic();
			var matchedLineItems = GetLineItemsByEstHeader2LineItemIdsMap(options.MatchedEstHeaderId2LineItemIdsMap);
			var boqHeaderId2BoqItemIdsMap = new Dictionary<int, List<int>>();
			var boqId2LineItemsMap = new Dictionary<RVPC.IdentificationData, List<IEstLineItemEntity>>();
			
			foreach (var matchedLineItem in matchedLineItems)
			{
				if (!matchedLineItem.BoqHeaderFk.HasValue || !matchedLineItem.BoqItemFk.HasValue)
				{
					continue;
				}

				if (!boqHeaderId2BoqItemIdsMap.TryGetValue(matchedLineItem.BoqHeaderFk.Value, out var boqItemIds))
				{
					boqItemIds = new List<int>();
					boqHeaderId2BoqItemIdsMap.Add(matchedLineItem.BoqHeaderFk.Value, boqItemIds);
				}

				boqItemIds.Add(matchedLineItem.BoqItemFk.Value);

				var tempBoqId = new RVPC.IdentificationData()
				{
					Id = matchedLineItem.BoqItemFk.Value,
					PKey1 = matchedLineItem.BoqHeaderFk.Value,
				};
				if (!boqId2LineItemsMap.TryGetValue(tempBoqId, out var tempLineItems))
				{
					tempLineItems = new List<IEstLineItemEntity>();
					boqId2LineItemsMap.Add(tempBoqId, tempLineItems);
				}
				tempLineItems.Add(matchedLineItem);
			}

			var boqItems = new List<IBoqItemEntity>();
			foreach (var keyValue in boqHeaderId2BoqItemIdsMap)
			{
				var tempList = boqItemLogic.GetPureBoqItemsById(keyValue.Key, keyValue.Value);
				if (tempList != null && tempList.Any())
				{
					boqItems.AddRange(tempList);
				}
			}

			foreach (var boqItem in boqItems)
			{
				var tempBoqId = new RVPC.IdentificationData()
				{
					Id = boqItem.Id,
					PKey1 = boqItem.BoqHeaderFk
				};
				if (boqId2LineItemsMap.TryGetValue(tempBoqId, out var tempLineItems))
				{
					foreach (var lineItem in tempLineItems)
					{
						lineItem.PrcStructureFk = boqItem.PrcStructureFk; // project boq strcuture and line item structure use the same logic
					}
				}
			}

			return GetSimulationsForLineItemPrcStructure(options, matchedLineItems);
		}

		private IEnumerable<SimulationEntity> GetSimulationsForResource(GetSimulationOptions options)
		{
			var packageLogic = new PrcPackageLogic();
			var boqItemLogic = new BoqItemLogic();
			var resourceLogic = Injector.Get<IEstimateMainResourceLogic>();
			var resourceIds = new List<Tuple<int, int, int>>();
			foreach (var id in options.SelectedResourceIds)
			{
				if (!id.PKey1.HasValue || !id.PKey2.HasValue)
				{
					continue;
				}
				resourceIds.Add(new Tuple<int, int, int>(id.PKey1.Value, id.PKey2.Value, id.Id));
			}
			var resources = resourceLogic.GetResourcesByIdsAndHeaderId(resourceIds);
			var simulationList = new Dictionary<ResourceData, List<SimulationEntity>>();
			var pageSize = options.PackageLookupRequest.PageState.PageSize;
			var newPackage = packageLogic.GetDefaultPackage(options.DefaultProjectId, options.DefaultConfigurationId, options.DefaultStructureId, true, true);
			var index = 1;
			var uomIds = new List<int>();
			var simulationCount = 0;
			foreach (var resource in resources)
			{
				var resourceData = new ResourceData()
				{
					Description = resource.DescriptionInfo.Translated,
					UomFk = resource.BasUomFk
				};

				// create default simulation data
				var simulation = GetNewSimulationData(newPackage, options.DefaultDescription);
				simulation.Id = index++;
				simulation.EstHeaderFk = resource.EstHeaderFk;
				simulation.EstLineItemFk = resource.EstLineItemFk;
				simulation.ResourceFk = resource.Id;
				simulation.BoqStructureOption4SourceResources = 1;
				simulation.IsSkipBoqPositionAsDivisionBoq = true;
				if (!simulationList.TryGetValue(resourceData, out var simulates))
				{
					simulates = new List<SimulationEntity>();
					simulationList.Add(resourceData, simulates);
				}
				simulates.Add(simulation);
				simulationCount++;
				uomIds.Add(resource.BasUomFk);
			}

			uomIds = uomIds.Distinct().ToList();

			var packageIndex = 0;
			var simulationUpdatedCount = 0;
			// paging to get package
			var data = GetData(options);

			// if packages are found, go ahead
			while (data != null && data.id2PackageDic.Any())
			{
				var boqHeaderIds = data.BoqHeaderIds;
				var boqHeaderIdIndex = 0;
				var boqHeaderIdGroup = Math.Ceiling((decimal)boqHeaderIds.Count() / pageSize);
				// paging to search by boq header ids. if wic boq are found, stop the loop
				while (boqHeaderIdIndex < boqHeaderIdGroup && simulationCount > simulationUpdatedCount)
				{
					var partialBoqHeaderIds = boqHeaderIds.Skip(boqHeaderIdIndex * pageSize).Take(pageSize).ToList();
					// paging to search by filters
					var matchedIndex = 0;
					Expression<Func<BoqItemEntity, bool>> boqItemFilterExpression = e => e.BoqLineTypeFk == (int)BoqLineType.Position && uomIds.Contains(e.BasUomFk) && partialBoqHeaderIds.Contains(e.BoqHeaderFk);
					var matchedBoqItems = boqItemLogic.GetItemsByPaging(boqDbModel, matchedIndex, pageSize, boqItemFilterExpression, macthedBoqItemSortFunc);

					while (matchedBoqItems != null && matchedBoqItems.Any())
					{
						foreach (var item in matchedBoqItems)
						{
							var resourceData = new ResourceData()
							{
								Description = item.BriefInfo.Translated,
								UomFk = item.BasUomFk
							};

							if (simulationList.TryGetValue(resourceData, out var simulates) && simulates != null && simulates.Any())
							{
								foreach (var simulation in simulates)
								{
									if (simulation.MatchnessType != CreatePackageMatchnessType.New)
									{
										continue;
									}
									simulationUpdatedCount++;
									var matchedData = GetMatchedPackage(data, item.BoqHeaderFk, true);
									UpdateSimulationData(simulation, matchedData, CreatePackageMatchnessType.RoughMatched);
								}
							}

							if (simulationCount <= simulationUpdatedCount)
							{
								break;
							}
						}

						if (simulationCount > simulationUpdatedCount)
						{
							matchedIndex++;
							matchedBoqItems = boqItemLogic.GetItemsByPaging(boqDbModel, matchedIndex, pageSize, boqItemFilterExpression, macthedBoqItemSortFunc);
						}
						else
						{
							matchedBoqItems = null;
						}
					}

					boqHeaderIdIndex++;
				}

				packageIndex++;
				if (simulationCount > simulationUpdatedCount)
				{
					data = GetNextPageData(packageIndex, options);
				}
				else
				{
					data = null;
				}
			}

			var simulations = new List<SimulationEntity>();
			foreach (var keyValue in simulationList)
			{
				simulations.AddRange(keyValue.Value);
			}
			return simulations;
		}

		private sealed class Data
		{
			/// <summary>
			/// 
			/// </summary>
			public IEnumerable<int> BoqHeaderIds { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, PrcPackageEntity> id2PackageDic { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, PrcPackage2HeaderEntity> prcHeaderId2SubPackageDic { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, PrcBoqEntity> boqHeaderId2PrcBoqDic { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, PackageWithUpdateOptionEntity> id2PackageWithUpdateOptionDic { get; set; }
		}

		private sealed class SelectedBoqItemData
		{
			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, BoqItemEntity> BoqHeaderId2BoqRootItemMap { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public Dictionary<RVPC.IdentificationData, BoqItemEntity> Id2BoqItemMap { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, List<int>> BoqHeaderId2BoqItemIdListMap { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public IEnumerable<BoqItemEntity> SelectedBoqItems { get; set; }
		}

		/// <summary>
		/// Properties of line item to match
		/// </summary>
		private struct LineItemData
		{
			/// <summary>
			/// 
			/// </summary>
			public string Description { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int UomFk { get; set; }
		}

		/// <summary>
		/// Properties of project boq item to match
		/// </summary>
		private struct ProjectBoqData
		{
			/// <summary>
			/// 
			/// </summary>
			public string Reference { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public string Description { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int UomFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int BoqLineItemTypeFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public string BoqHeaderRef { get; set; }

		}

		private struct ResourceData
		{
			/// <summary>
			/// 
			/// </summary>
			public string Description { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? UomFk { get; set; }
		}

		private sealed class MatchedPackgageInfo
		{
			/// <summary>
			/// 
			/// </summary>
			public PrcPackageEntity Package { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public PrcPackage2HeaderEntity SubPackage { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public PrcBoqEntity PrcBoq { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public PackageWithUpdateOptionEntity PackageWithUpdateOption { get; set; }
		}
	}
}
