/**
 * $Id:  $
 * Copyright (c) RIB Software SE
 */

(function () {
	'use strict';
	/* global _, globals */

	let moduleName = 'project.crewmix';
	/**
     * @ngdoc service
     * @name projectCrewMixUpdateWagesFromMasterWizardService
     * @description Project CrewMix module update wages from master CrewMix wizard service
     */

	angular.module(moduleName).factory('projectCrewMixUpdateWagesFromMasterWizardService', ['$http', '$injector',
		'platformModalFormConfigService',
		function ($http, $injector) {

			let service = {};

			service.updateWagesFromMaster = function updateWagesFromMaster() {
				let projectCrewMixMainService = $injector.get('projectCrewMixMainService');
				let platformTranslateService = $injector.get('platformTranslateService');
				let platformModalFormConfigService = $injector.get('platformModalFormConfigService');
				let platformSidebarWizardConfigService = $injector.get('platformSidebarWizardConfigService');
				let basicsEfbsheetsMainService = $injector.get('basicsEfbsheetsMainService');
				let $translate = $injector.get('$translate');
				let $http = $injector.get('$http');
				let title = 'project.crewmix.updateWages';
				let projectMainService = $injector.get('projectMainService');
				let updateWagesConfig = {
					title: $translate.instant(title),
					dataItem: {
						selectedScope: 'HighlightedCrewMix'
					},
					formConfiguration: {
						fid: 'project.crewmix.updateWages',
						version: '0.1.1',
						showGrouping: true,
						groups: [
							{
								gid: 'selectedScope',
								header: 'Select scope for Crew Mixes',
								header$tr$: 'project.crewmix.selectCrewMixeScope',
								visible: true,
								isOpen: true,
								attributes: [],
								sortOrder: 1
							}
						],
						rows: [
							{
								gid: 'selectedScope',
								rid: 'selectedScope',
								label: 'Select Crew Mixes Scope',
								label$tr$: 'project.crewmix.selectCrewMixeScope',
								type: 'radio',
								model: 'selectedScope',
								sortOrder: 0,
								options: {
									labelMember: 'label',
									valueMember: 'Value',
									groupName: 'scope',
									items: [
										{
											Id: 0,
											label: 'Select Highlighted Crew Mix',
											label$tr$: 'project.crewmix.selectHighlightedCrewMix',
											Value: 'HighlightedCrewMix'
										},
										{
											Id: 1,
											label: 'Select Current Crew Mix',
											label$tr$: 'project.crewmix.currentResultSet',
											Value: 'CurrentResultSet'
										},
										{
											Id: 2,
											label: 'Select Entire Crew Mix',
											label$tr$: 'project.crewmix.selectEntireCrewMix',
											Value: 'EntireCrewMix'
										}
									]
								}
							}
						]
					},

					handleOK: function handleOK(result) {
						if (!result || !result.ok || !result.data) {
							return;
						}

						let scopeMap = {
							'HighlightedCrewMix': 0,
							'CurrentResultSet': 1,
							'EntireCrewMix': 2
						};

						let crewMixScope = scopeMap[result.data.selectedScope];

						let selectedCrewMixes = [];

						if (crewMixScope === 0) {
							selectedCrewMixes = projectCrewMixMainService.getSelectedEntities();
						} else if (crewMixScope === 1) {
							selectedCrewMixes = projectCrewMixMainService.getList();
						} else if (crewMixScope === 2) {
							selectedCrewMixes = []; // This will come from backend
						}

						selectedCrewMixes = Array.isArray(selectedCrewMixes) ? selectedCrewMixes : [selectedCrewMixes];
						let mainItemIds = selectedCrewMixes.length
							? selectedCrewMixes.map(entity => parseInt(entity?.Id)).filter(id => !isNaN(id))
							: [];

						if (crewMixScope === 0 && !mainItemIds.length) {
							showNoSelectionMessage();
							return;
						}

						let postData = {
							'selectedItemIds': mainItemIds,
							'crewMixScope': crewMixScope,
							'projectId': projectMainService.getSelected().Id
						};

						function updateCrewMixes() {
							return $http.post(globals.webApiBaseUrl + 'project/crewmix/crewmixes/updatewages', postData)
								.then(function (response) {

									$injector.get('basicsEfbSheetsWageGroupLookupDataService').clearCache();
									$injector.get('basicsEfbSheetsSurchargeLookupDataService').clearCache();
									$injector.get('basicsEfbSheetsAdditionalCostLookupDataService').clearCache();
									$injector.get('basicsEfbSheetsNonwageCostsLookupDataService').clearCache();

									projectMainService.refresh();
									projectCrewMixMainService.addList(response.data);
									projectCrewMixMainService.fireListLoaded();

									projectCrewMixMainService.setSelected({}).then(function () {
										let list = projectCrewMixMainService.getList();
										let updatedCrewMix = _.find(list, { Id: mainItemIds[0] });

										if (updatedCrewMix) {
											projectCrewMixMainService.setSelected(updatedCrewMix);
										}
										showSuccessMessage();
									});
								});
						}

						basicsEfbsheetsMainService.updateAndExecute(updateCrewMixes);
					}
				};

				function showSuccessMessage() {
					let platformModalService = $injector.get('platformModalService');
					let modalOptions = {
						headerTextKey: $translate.instant('project.crewmix.updateWages'),
						bodyTextKey: $translate.instant('project.crewmix.updateWagesSuccess'),
						showOkButton: true,
						iconClass: 'ico-info'
					};
					platformModalService.showDialog(modalOptions);
				}

				function showNoSelectionMessage() {
					let platformModalService = $injector.get('platformModalService');
					let modalOptions = {
						headerTextKey: $translate.instant('project.crewmix.updateWages'),
						bodyTextKey: $translate.instant('project.crewmix.noSelection'),
						showOkButton: true,
						iconClass: 'ico-warning'
					};
					platformModalService.showDialog(modalOptions);
				}

				platformTranslateService.translateFormConfig(updateWagesConfig.formConfiguration);
				updateWagesConfig.scope = platformSidebarWizardConfigService.getCurrentScope();

				platformModalFormConfigService.showDialog(updateWagesConfig);

			};

			return service;
		}
	]);
})();

