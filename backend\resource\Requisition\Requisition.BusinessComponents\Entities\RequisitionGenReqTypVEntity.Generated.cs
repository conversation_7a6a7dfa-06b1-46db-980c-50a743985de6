﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Resource.Requisition.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Resource.Requisition.BusinessComponents.RequisitionGenReqTypVEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("RES_GEN_REQ_TYP_V")]
    public partial class RequisitionGenReqTypVEntity : ICloneable
    {
        /// <summary>
        /// Initialize a new RequisitionGenReqTypVEntity object.
        /// </summary>
        public RequisitionGenReqTypVEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ResRequisitionFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_REQUISITION_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ResRequisitionFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ResTypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_TYPE_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ResTypeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Duration in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DURATION", TypeName = "int", Order = 3)]
        public virtual int? Duration {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasUomdayFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOMDAY_FK", TypeName = "int", Order = 4)]
        public virtual int? BasUomdayFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ResTyperequestedFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_TYPEREQUESTED_FK", TypeName = "int", Order = 5)]
        public virtual int? ResTyperequestedFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LgmDispatcherGroupFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LGM_DISPATCHER_GROUP_FK", TypeName = "int", Order = 6)]
        public virtual int? LgmDispatcherGroupFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsPlantRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISPLANTREQUESTED", TypeName = "bit", Order = 7)]
        public virtual bool? IsPlantRequested {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsDriverRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDRIVERREQUESTED", TypeName = "bit", Order = 8)]
        public virtual bool? IsDriverRequested {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsCraneRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISCRANEREQUESTED", TypeName = "bit", Order = 9)]
        public virtual bool? IsCraneRequested {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsTruckRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISTRUCKREQUESTED", TypeName = "bit", Order = 10)]
        public virtual bool? IsTruckRequested {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SkillOfRequestedTypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_SKILL_OFREQUESTED_FK", TypeName = "int", Order = 11)]
        public virtual int? SkillOfRequestedTypeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Isrequestedentireperiod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREQUESTEDENTIREPERIOD", TypeName = "bit", Order = 12)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isrequestedentireperiod {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Necessaryoperators in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("NECESSARYOPERATORS", TypeName = "int", Order = 13)]
        public virtual int Necessaryoperators {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CalCalendarFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CAL_CALENDAR_FK", TypeName = "int", Order = 14)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CalCalendarFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ResSkillResTypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_SKILL_RES_TYPE_FK", TypeName = "int", Order = 15)]
        public virtual int? ResSkillResTypeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ResSkillFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_SKILL_FK", TypeName = "int", Order = 16)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ResSkillFk {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            RequisitionGenReqTypVEntity obj = new RequisitionGenReqTypVEntity();
            obj.Id = Id;
            obj.ResRequisitionFk = ResRequisitionFk;
            obj.ResTypeFk = ResTypeFk;
            obj.Duration = Duration;
            obj.BasUomdayFk = BasUomdayFk;
            obj.ResTyperequestedFk = ResTyperequestedFk;
            obj.LgmDispatcherGroupFk = LgmDispatcherGroupFk;
            obj.IsPlantRequested = IsPlantRequested;
            obj.IsDriverRequested = IsDriverRequested;
            obj.IsCraneRequested = IsCraneRequested;
            obj.IsTruckRequested = IsTruckRequested;
            obj.SkillOfRequestedTypeFk = SkillOfRequestedTypeFk;
            obj.Isrequestedentireperiod = Isrequestedentireperiod;
            obj.Necessaryoperators = Necessaryoperators;
            obj.CalCalendarFk = CalCalendarFk;
            obj.ResSkillResTypeFk = ResSkillResTypeFk;
            obj.ResSkillFk = ResSkillFk;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(RequisitionGenReqTypVEntity clonedEntity);

    }


}
