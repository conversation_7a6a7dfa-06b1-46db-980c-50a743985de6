-------------------------------------------------------
-- Ignore Errors:	Off
-- JIRA Number:		DEV-47568
-- Script Type: 	Required Schema Change
-- Reason: 			Basics Bank - Finalize IsLIve development started in 25.1 
-- Install On: 		Trunk, Release 25.2
-------------------------------------------------------

-- Provide Wizard to Disable Bank
DECLARE @BAS_WIZARD_PK_ID_AFFECTED INT;

EXEC [dbo].[BAS_WIZARD_INSERT_UPDATE_PROC]
    @ID = 1295,
    @WIZARDGUID = '09d06dc2449e4596bebdd14672956e52',
    @NAME = 'Disable Bank',
    @DESCRIPTION = 'Disable Bank',
    @TRANSLATIONKEY = NULL,
    @PK_ID_AFFECTED = @BAS_WIZARD_PK_ID_AFFECTED OUTPUT;

SELECT @BAS_WIZARD_PK_ID_AFFECTED;

-- Provide Wizard to Enable Bank
EXEC [dbo].[BAS_WIZARD_INSERT_UPDATE_PROC]
    @ID = 1296,
    @WIZARDGUID = '7fc78eafa5344243abb0ccc643ec1ec9',
    @NAME = 'Enable Bank',
    @DESCRIPTION = 'Enable Bank',
    @TRANSLATIONKEY = NULL,
    @PK_ID_AFFECTED = @BAS_WIZARD_PK_ID_AFFECTED OUTPUT;

SELECT @BAS_WIZARD_PK_ID_AFFECTED;


-- Add wizard to diverse modules i.e. add to BAS_WIZARD2MODULE
DECLARE	@BAS_WIZARD2MODULE_PK_ID_AFFECTED INT

--- Disable Bank
EXEC	[dbo].[BAS_WIZARD2MODULE_INSERT_UPDATE_PROC]
			@ID = 2045,						 
			@BAS_MODULE_INTERNAL_NAME ='basics.bank',
			@WIZARDGUID ='09d06dc2449e4596bebdd14672956e52',
			@PK_ID_AFFECTED = @BAS_WIZARD2MODULE_PK_ID_AFFECTED OUTPUT
SELECT	@BAS_WIZARD2MODULE_PK_ID_AFFECTED

--- Enable Bank
EXEC	[dbo].[BAS_WIZARD2MODULE_INSERT_UPDATE_PROC]
			@ID = 2046,						 
			@BAS_MODULE_INTERNAL_NAME ='basics.bank',
			@WIZARDGUID ='7fc78eafa5344243abb0ccc643ec1ec9',
			@PK_ID_AFFECTED = @BAS_WIZARD2MODULE_PK_ID_AFFECTED OUTPUT
SELECT	@BAS_WIZARD2MODULE_PK_ID_AFFECTED
