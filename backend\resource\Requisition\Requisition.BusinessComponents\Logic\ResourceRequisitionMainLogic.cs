using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Transactions;

using Newtonsoft.Json.Linq;

using RIB.Visual.Platform.Core;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;

using RIB.Visual.Basics.Core.Core;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.BusinessComponents;

using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Common.Core.Final;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.BusinessComponents;

using RIB.Visual.Basics.Config.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;

using RIB.Visual.Resource.Common.BusinessComponents;
using RIB.Visual.Resource.Type.BusinessComponents;

using NLS = RIB.Visual.Resource.Requisition.Localization.Properties.Resources;

using RIB.Visual.Resource.Skill.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using CCB = RIB.Visual.Cloud.Common.BusinessComponents;
using System.ComponentModel.Composition;
using RIB.Visual.Basics.Company.BusinessComponents;

using RIB.Visual.Resource.Requisition.Common;
using CompanyContextAssert = RIB.Visual.Resource.Common.BusinessComponents.CompanyContextAssert;
using RIB.Visual.Platform.Common;
using System.Threading.Channels;
using RIB.Visual.Basics.LookupData.BusinessComponents;

namespace RIB.Visual.Resource.Requisition.BusinessComponents
{
	/// <summary>
	/// Reservation Business Logic should be placed here
	/// 
	/// </summary>
	[Export("Resource.Requisition.RequisitionEntity", typeof(IDataBaseLogic))]
	[Export(typeof(IResRequisitionLogic))]
	[Export("resrequisitionstatus", typeof(IChangeStatus))]
	[EntityStatus("RES_REQUISITIONSTATUS", "resource.requisition", "Requisition Status")]
	public class ResourceRequisitionMainLogic : EntityProvidingUpdateLogic<RequisitionEntity, IdentificationData>,
		IUpdateCompleteData, IResRequisitionLogic, IDataBaseLogic, ICreateEntityFacade, IChangeStatus, IEntityAggregator
	{
		/// <summary>
		/// The singleton identifier instance for the <see cref="RequisitionEntity"/> type.
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<RequisitionEntity>> IdentifierInstance =
			IdentifierFactory.Create<RequisitionEntity>("Id");


		/// <summary>
		/// ReservationLogic constructor
		/// </summary>
		public ResourceRequisitionMainLogic()
		{
			Identifier = IdentifierInstance.Value;
			PermissionGUID = "291a21ca7ab94d549d2d0c541ec09f5d";

			CompleteUpdater = this;

			OrderByExpressions = new[]
			{
				OrderTerm.Create(e => e.Id)
			};
			OrderByKey = e => Identifier.GetEntityIdentification(e);

			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
			CompleteUpdater = this;

			FilterByPermission = (context, query) =>
			{
				var userId = BusinessApplication.BusinessEnvironment.CurrentContext.UserId;

				// Project permissions
				return query.Join(new DbFunctions(context).UserAccess(userId),
						outer => outer.Id, inner => inner.Id, (outer, inner) => outer);
			};

			PredefinedAddtionalFilter = (context, query) =>
			{
				return query.Where(r => !r.IsDeleted);
			};
		}

		/// <summary>
		/// Gets DbModel.
		/// </summary>
		/// <returns/>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Method to get one sequence id
		/// </summary>
		/// <returns/>
		protected override string GetEntityTableName()
		{
			return "RES_REQUISITION";
		}

		/// <summary>
		/// Create an entity via creation data.
		/// </summary>
		/// <param name="creationData"/>
		/// <returns/>
		public override RequisitionEntity Create(IdentificationData creationData)
		{
			var requisition = CreateRequisition();

			if (creationData.PKey1.HasValue)
			{
				requisition.JobFk = creationData.PKey1.Value;
			}
			if (creationData.PKey2.HasValue)
			{
				requisition.ProjectFk = creationData.PKey2.Value;
			}
			if (creationData.PKey3.HasValue)
			{
				requisition.TypeFk = creationData.PKey3.Value;
			}

			var date = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
			requisition.RequestedFrom = new DateTime(date.Year, date.Month, date.Day, 0, 0, 1, DateTimeKind.Utc);
			requisition.RequestedTo = new DateTime(date.Year, date.Month, date.Day, 23, 59, 59, DateTimeKind.Utc);


			return requisition;
		}

		/// <summary>
		/// Override to get all not provided values defaulted
		/// </summary>
		/// <param name="creationData"></param>
		/// <param name="requisition"></param>
		protected override void InitMissingDefaults(JObject creationData, ref RequisitionEntity requisition)
		{
			var currentContext = BusinessApplication.BusinessEnvironment.CurrentContext;
			var companyInfoProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<ICompanyInfoProvider>();
			var companyInfo = companyInfoProvider.GetInstanceInfo(currentContext.ClientId);

			requisition.CompanyFk = companyInfo.Id;
			requisition.ResourceContextFk = CompanyContextAssert.GetResourceContextOrThrowException(companyInfo);

			var date = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
			var from = new DateTime(date.Year, date.Month, date.Day, 0, 0, 1, DateTimeKind.Utc);
			var to = new DateTime(date.Year, date.Month, date.Day, 23, 59, 59, DateTimeKind.Utc);

			InitIfNotInCreationData(creationData, "RequestedFrom", (p, d) => p.RequestedFrom = d,
				from, ref requisition);
			InitIfNotInCreationData(creationData, "RequestedTo", (p, d) => p.RequestedTo = d,
				to, ref requisition);

			InitIfNotInCreationData(creationData, "RequisitionStatusFk", (p, i) => p.RequisitionStatusFk = i,
				GeRequisitionDefaultValue("basics.customize.resrequisitionstatus"), ref requisition);
			InitIfNotInCreationData(creationData, "RequisitionTypeFk", (p, i) => p.RequisitionTypeFk = i,
				GeRequisitionDefaultValue("basics.customize.resrequisitiontype"), ref requisition);
			InitIfNotInCreationData(creationData, "RequisitionGroupFk", (p, i) => p.RequisitionGroupFk = i,
				GeRequisitionDefaultValue("basics.customize.resourcerequisitiongroup"), ref requisition);
			InitIfNotInCreationData(creationData, "RequisitionPriorityFk", (p, i) => p.RequisitionPriorityFk = i,
				GeRequisitionDefaultValue("basics.customize.resourcerequisitionpriority"), ref requisition);
		}

		private int GeRequisitionDefaultValue(string identifier)
		{
			var defAccess = BusinessApplication.BusinessEnvironment.GetExportedValue<IDefaultEntityProvider>(identifier);
			var def = defAccess.GetDefaultForCurrentContext();
			if (def != null)
			{
				return def.Id;
			}

			return 0;
		}

		/// <summary>
		/// Create multiple requisitions via amount of counter parameter
		/// </summary>
		/// <param name="amountCounter"></param>
		/// <returns></returns>
		public List<RequisitionEntity> CreateMultipleRequisitions(int amountCounter)
		{
			RVPBizComp.Permission.Ensure(PermissionGUID, Permissions.Create);

			var nextIdList = this.SequenceManager.GetNextList(GetEntityTableName(), amountCounter);
			List<RequisitionEntity> newRequisitionList = new List<RequisitionEntity>();
			foreach (var id in nextIdList)
			{
				var entity = new RequisitionEntity();
				entity.Id = id;
				newRequisitionList.Add(entity);
			}
			return newRequisitionList;
		}

		/// <summary>
		/// Default validation will not check anything.
		/// </summary>
		/// <param name="entity"/>
		public override void Validate(RequisitionEntity entity)
		{
		}

		/// <summary>
		/// Default validation will not check anything.
		/// </summary>
		/// <param name="entities"/>
		public override void Validate(IEnumerable<RequisitionEntity> entities)
		{
			ValidateProjectFk(entities);
		}

		/// <summary>
		/// Performs some post-processing on retrieved entities before they are returned.
		/// </summary>
		/// <param name="entities">The enumeration of entities.
		///               This will not be <see langword="null"/> and will not contain <see langword="null"/> as an element.</param>
		/// <remarks>
		/// <para>
		/// This method post-processes all retrieved entities right before they are returned.
		///               It may be overridden to include sub-entities or transient data.
		/// </para>
		/// </remarks>
		protected override void PostProcess(IEnumerable<RequisitionEntity> entities)
		{
			var requisitions = entities.ToArray();
			var requisitionIds = requisitions.Select(e => new IdentificationData() { Id = e.Id }).ToArray();

			// In case more properties are needed as transient fields, please enhance the view for performance reasons
			var requisitionInfos = new ResourceRequisitionInformationLogic().GetByIds(requisitionIds);

			//Flat properties
			foreach (var requisition in requisitions)
			{
				var requisitionInfo = requisitionInfos.FirstOrDefault(pi => pi.Id == requisition.Id);
				if (requisitionInfo != null)
				{
					requisition.IsReadOnlyStatus = requisitionInfo.IsReadOnly;
					requisition.ScheduleFk = requisitionInfo.ScheduleFk;
					requisition.JobGroupFk = requisitionInfo.JobGroupFk;
					requisition.ProjectChangeFk = requisitionInfo.ProjectChangeFk;
					requisition.ProjectChangeStatusFk = requisitionInfo.ProjectChangeStatusFk;
					requisition.JobSiteFk = requisitionInfo.JobSiteFk;
					requisition.ResTypeIsBulk = requisitionInfo.ResourceTypeIsBulk;
					requisition.ResTypeIsSmallTools = requisitionInfo.ResourceTypeIsSmallTools;
					requisition.DispatcherGroupFk = requisitionInfo.DispatcherGroupFk;
					requisition.PreferredResourceSiteFk = requisitionInfo.ResourceSiteFk;
					requisition.ReservedFrom = requisitionInfo.ReservedFrom;
					requisition.ReservedTo = requisitionInfo.ReservedTo;
					requisition.OnSiteFrom = requisitionInfo.OnSiteFrom;
					requisition.OnSiteTo = requisitionInfo.OnSiteTo;
					requisition.ProjectStockFk = requisitionInfo.StockProjectFk;
					requisition.CompanyFk = requisitionInfo.ProjectCompanyFk;
					requisition.PlannedStart = requisitionInfo.PlannedStart;
					requisition.PlannedEnd = requisitionInfo.PlannedEnd;
					requisition.IsTimeEnhancement = requisitionInfo.IsTimeEnhancement;
					requisition.IsRequestBizPartner = requisitionInfo.IsRequestBizPartner;
					requisition.IsRequestProjectDoc = requisitionInfo.IsRequestProjectDoc;
					requisition.EstimateQuantity = requisitionInfo.EstimateQuantity;
					requisition.EstWorkOperationTypeFk = requisitionInfo.EstWorkOperationTypeFk;
					requisition.Requisition2RequisitionFk = requisitionInfo.Requisition2RequisitionFk;
					requisition.CanBeDeleted = !requisitionInfo.ReservedFrom.HasValue && !requisitionInfo.ReservedTo.HasValue && !requisitionInfo.LgmDispatchHeaderMapFk.HasValue
														&& !requisitionInfo.ResRequisitionFormDataFk.HasValue && !requisitionInfo.ResRequisitionMapperFk.HasValue; 

				}
			}

			//Project should be created only once
			var projectData = new List<RequisitionProjectData>();
			var requisitionsByProjects = requisitions.GroupBy(r => r.ProjectFk).ToDictionary(e => e.Key, e => e.ToArray());
			foreach (var requisitionsByProject in requisitionsByProjects)
			{
				RequisitionInformationEntity requisitionInfo = null;
				var firstReq = requisitionsByProject.Value.FirstOrDefault();
				if (firstReq != null)
				{
					requisitionInfo = requisitionInfos.FirstOrDefault(pi => pi.Id == firstReq.Id);
				}

				if (requisitionInfo != null)
				{
					var project = new RequisitionProjectData()
					{
						Id = firstReq.ProjectFk,
						CompanyFk = requisitionInfo.ProjectCompanyFk,
						ProjectNo = requisitionInfo.ProjectNo,
						ProjectName = requisitionInfo.ProjectName,
						ProjectName2 = requisitionInfo.ProjectName2,
						CurrencyFk = requisitionInfo.ProjectCurrencyFk,
						BusinessPartnerFk = requisitionInfo.ProjectBusinessPartnerFk,
						IsLive = requisitionInfo.ProjectIsLive,
						Userdefined1 = requisitionInfo.ProjectUserDefined01,
						Userdefined2 = requisitionInfo.ProjectUserDefined02,
						AddressFk = requisitionInfo.ProjectAddressFk
					};

					projectData.Add(project);

					foreach (var requisition in requisitionsByProject.Value)
					{
						requisition.Project = project;
					}
				}
			}

			var jobData = new List<RequisitionJobData>();
			var requisitionsByJobs = requisitions.GroupBy(r => r.JobFk).ToDictionary(e => e.Key, e => e.ToArray());
			foreach (var requisitionsByJob in requisitionsByJobs)
			{
				RequisitionInformationEntity requisitionInfo = null;
				var firstReq = requisitionsByJob.Value.FirstOrDefault();
				if (firstReq != null)
				{
					requisitionInfo = requisitionInfos.FirstOrDefault(pi => pi.Id == firstReq.Id);
				}

				if (requisitionInfo != null)
				{
					var job = new RequisitionJobData()
					{
						Id = firstReq.JobFk,
						LogisticContextFk = requisitionInfo.JobContextFk,
						JobTypeFk = requisitionInfo.JobTypeFk,
						RubricCategoryFk = requisitionInfo.JobRubricCategoryFk,
						ProjectFk = firstReq.ProjectFk,
						CompanyFk = requisitionInfo.ProjectCompanyFk,
						CalendarFk = requisitionInfo.JobCalendarFk,
						IsLive = requisitionInfo.JobIsLive,
						IsProjectDefault = requisitionInfo.JobIsProjectDefault,
						IsMaintenance = requisitionInfo.JobIsMaintenance,
						Code = requisitionInfo.JobCode,
						DescriptionInfo = new DescriptionTranslateTypeDto
						{
							Description = requisitionInfo.JobDescription
						},
						ControllingUnitFk = requisitionInfo.JobControllingUnitFk,
						CostCodePriceVersionFk = requisitionInfo.JobCostCodePriceVersionFk,
						CostCodePriceListFk = requisitionInfo.JobCostCodePriceListFk,
						AddressFk = requisitionInfo.JobAddressFk,
						DeliveryAddressBlobFk = requisitionInfo.JobDeliveryAddressBlobFk,
						DeliveryAddressRemark = requisitionInfo.JobDeliveryAddressRemark,
						BusinessPartnerFk = requisitionInfo.JobBusinessPartnerFk,
						ValidFrom = requisitionInfo.JobValidFrom,
						ValidTo = requisitionInfo.JobValidTo,
						Address = null,
						Userdefined1 = requisitionInfo.JobUserDefined01,
						Userdefined2 = requisitionInfo.JobUserDefined02,
						PlantFk = requisitionInfo.JobPlantFk
					};
					jobData.Add(job);

					foreach (var requisition in requisitionsByJob.Value)
					{
						requisition.Job = job;
					}
				}
			}

			if (jobData.Any() || projectData.Any())
			{
				var addressIds = new List<int>();
				if (jobData.Any())
				{
					addressIds.AddRange(jobData.CollectIds(j => j.AddressFk));
				}
				if (projectData.Any())
				{
					addressIds.AddRange(projectData.CollectIds(j => j.AddressFk));
				}

				var addLogic = new AddressLogic();
				var addresses = addLogic.FillAddressReferenceData(addressIds).ToArray();

				if (jobData.Any())
				{
					foreach (var job in jobData)
					{
						if (job.AddressFk.HasValue)
						{
							job.Address = addresses.FirstOrDefault(a => a.Id == job.AddressFk.Value);
						}
					}
				}

				if (projectData.Any())
				{
					foreach (var project in projectData)
					{
						if (project.AddressFk.HasValue)
						{
							project.Address = addresses.FirstOrDefault(a => a.Id == project.AddressFk.Value);
						}
					}
				}
			}

			SetSkills(entities);//?
		}

		private void RecalculateQuantityAndReservedTo(IEnumerable<RequisitionEntity> requisitionEntities)
		{
			var calendarLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ICalendarAccess>();
			var calendar = calendarLogic.GetList();
			var calendarYearIds = calendar.Where(e => e.BasUomYearFk.HasValue).Select(e => e.BasUomYearFk.Value).Distinct();
			var calendarMonthIds = calendar.Where(e => e.BasUomMonthFk.HasValue).Select(e => e.BasUomMonthFk.Value).Distinct();
			var calendarWeekIds = calendar.Where(e => e.BasUomWeekFk.HasValue).Select(e => e.BasUomWeekFk.Value).Distinct();
			var calendarDayIds = calendar.Select(e => e.BasUomDayFk).Distinct();
			var calendarHourIds = calendar.Select(e => e.BasUomHourFk).Distinct();
			var calendarMinuteIds = calendar.Where(e => e.BasUomMinuteFk.HasValue).Select(e => e.BasUomMinuteFk.Value).Distinct();

			var newRequisitionIds = requisitionEntities.Select(e => new IdentificationData() { Id = e.Id }).ToList();
			var oldRequisitionEntities = GetCoresByIds(newRequisitionIds).ToList();

			foreach (var requisition in requisitionEntities)
			{
				var old = oldRequisitionEntities.Find(f => f.Id == requisition.Id);
				if (old == null)
				{
					// old not found
					continue;
				}
				if (calendarYearIds.Any() && calendarYearIds.Contains(requisition.UomFk))
				{
					if (Math.Round(requisition.Quantity, 6) != old.Quantity || requisition.RequestedFrom != old.RequestedFrom)
					{
						requisition.RequestedTo = requisition.RequestedFrom.AddYears((int)requisition.Quantity);
					}
					else if (requisition.UomFk != old.UomFk || requisition.RequestedTo != old.RequestedTo)
					{
						// TODO
						requisition.Quantity = (decimal)(requisition.RequestedTo - requisition.RequestedFrom).TotalDays / 365;
					}
				}
				else if (calendarMonthIds.Any() && calendarMonthIds.Contains(requisition.UomFk))
				{
					if (Math.Round(requisition.Quantity, 6) != old.Quantity || requisition.RequestedFrom != old.RequestedFrom)
					{
						requisition.RequestedTo = requisition.RequestedFrom.AddMonths((int)requisition.Quantity);
					}
					else if (requisition.UomFk != old.UomFk || requisition.RequestedTo != old.RequestedTo)
					{
						// TODO
						requisition.Quantity = (decimal)(requisition.RequestedTo - requisition.RequestedFrom).TotalDays / 30;
					}
				}
				else if (calendarWeekIds.Any() && calendarWeekIds.Contains(requisition.UomFk))
				{
					if (Math.Round(requisition.Quantity, 6) != old.Quantity || requisition.RequestedFrom != old.RequestedFrom)
					{
						requisition.RequestedTo = requisition.RequestedFrom.AddDays((double)requisition.Quantity * 7);
					}
					else if (requisition.UomFk != old.UomFk || requisition.RequestedTo != old.RequestedTo)
					{
						requisition.Quantity = (decimal)(requisition.RequestedTo - requisition.RequestedFrom).TotalDays / 7;
					}
				}
				else if (calendarDayIds.Any() && calendarDayIds.Contains(requisition.UomFk))
				{
					if (Math.Round(requisition.Quantity, 6) != old.Quantity || requisition.RequestedFrom != old.RequestedFrom)
					{
						requisition.RequestedTo = requisition.RequestedFrom.AddDays((double)requisition.Quantity);
					}
					else if (requisition.UomFk != old.UomFk || requisition.RequestedTo != old.RequestedTo)
					{
						requisition.Quantity = (decimal)(requisition.RequestedTo - requisition.RequestedFrom).TotalDays;
					}
				}
				else if (calendarHourIds.Any() && calendarHourIds.Contains(requisition.UomFk))
				{
					if (Math.Round(requisition.Quantity, 6) != old.Quantity || requisition.RequestedFrom != old.RequestedFrom)
					{
						requisition.RequestedTo = requisition.RequestedFrom.AddHours((double)requisition.Quantity);
					}
					else if (requisition.UomFk != old.UomFk || requisition.RequestedTo != old.RequestedTo)
					{
						requisition.Quantity = (decimal)(requisition.RequestedTo - requisition.RequestedFrom).TotalHours;
					}
				}
				else if (calendarMinuteIds.Any() && calendarMinuteIds.Contains(requisition.UomFk))
				{
					if (Math.Round(requisition.Quantity, 6) != old.Quantity || requisition.RequestedFrom != old.RequestedFrom)
					{
						requisition.RequestedTo = requisition.RequestedFrom.AddMinutes((double)requisition.Quantity);
					}
					else if (requisition.UomFk != old.UomFk || requisition.RequestedTo != old.RequestedTo)
					{
						requisition.Quantity = (decimal)(requisition.RequestedTo - requisition.RequestedFrom).TotalMinutes;
					}
				}
			}
		}

		private void SetSkills(IEnumerable<RequisitionEntity> requisitionEntities)
		{
			var requisitionRequiredSkillLogic = new RequisitionRequiredSkillLogic();
			var resourceSkillLogic = new ResourceSkillLogic();

			var requisitionIds = requisitionEntities.CollectIds(e => e.Id);
			var requisitionRequiredSkillEntities = requisitionRequiredSkillLogic.GetByFilter(e => requisitionIds.Contains(e.RequisitionFk)).ToList();
			var requiredSkillEntities = resourceSkillLogic.GetSkillEntities(requisitionRequiredSkillEntities.Select(e => e.SkillFk).Distinct().ToList());

			foreach (var requisitionEntity in requisitionEntities)
			{

				requisitionEntity.RequiredSkillList = requiredSkillEntities
						.Where(e =>
								requisitionRequiredSkillEntities
								.Where(f => f.RequisitionFk == requisitionEntity.Id).Select(f => f.SkillFk)
								.Distinct()
								.ToList()
								.Contains(e.Id))
						.ToList();

			}
		}

		private RequisitionEntity CreateRequisition()
		{
			return CreateRequisitions(1).First();
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="count"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> CreateRequisitions(int count)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);
			var resourceContextFk = CompanyContextAssert.GetResourceContextOrThrowException();
			var def = new BasicsCustomizeResRequisitionStatusLogic().GetDefault();
			var deff = new BasicsCustomizeResRequisitionTypeLogic().GetDefault();
			var currentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			var result = new List<RequisitionEntity>();
			foreach (var id in GetNewIds(count))
			{
				var entity = new RequisitionEntity();
				entity.Id = id;
				entity.ResourceContextFk = resourceContextFk;
				if (def != null)
				{
					entity.RequisitionStatusFk = def.Id;
				}

				if (deff != null)
				{
					entity.RequisitionTypeFk = deff.Id;
				}
				RelationDefaultValueSetter.Handle(entity, new List<Tuple<string, Action<RequisitionEntity, int>, int?>>() {
					new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.resrequisitiontype", (e, i) => e.RequisitionTypeFk = i, null),
					new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.resourcerequisitiongroup", (e, i) => e.RequisitionGroupFk = i, null),
					new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.resourcerequisitionpriority", (e, i) => e.RequisitionPriorityFk = i, null),
					new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.rubriccategory", (e, i) => e.RubricCategoryFk = i, RubricConstant.ResourceRequisition)
				});
				entity.RequestedFrom = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
				entity.RequestedTo = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Utc);
				entity.CompanyFk = currentContext.ClientId;
				result.Add(entity);
			}
			return result.ToArray();
		}

		/// <summary>
		/// Create new Requisition
		/// </summary>
		/// <param name="projectFk">id of project</param>
		/// <param name="materialFk">id of material</param>
		/// <param name="jobFk">id of logistic job</param>
		/// <returns></returns>
		IResRequisitionEntity IResRequisitionLogic.CreateRequisition(int? projectFk, int? materialFk, int? jobFk)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);
			var entity = new RequisitionEntity();
			entity.Id = this.SequenceManager.GetNext(GetEntityTableName());
			entity.ResourceContextFk = CompanyContextAssert.GetResourceContextOrThrowException();
			var def = new BasicsCustomizeResRequisitionStatusLogic().GetDefault();


			if (def != null)
			{
				entity.RequisitionStatusFk = def.Id;
			}
			entity.RequestedFrom = DateTime.UtcNow;
			entity.RequestedTo = DateTime.UtcNow;
			var currentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			entity.CompanyFk = currentContext.ClientId;
			if (projectFk.HasValue)
			{
				entity.ProjectFk = projectFk.Value;
			}
			if (jobFk.HasValue)
			{
				entity.JobFk = jobFk.Value;
			}
			entity.MaterialFk = materialFk;
			return entity;
		}

		/// <summary>
		/// update requisitions
		/// </summary>
		/// <param name="resRequisitionEntities"></param>
		/// <returns></returns>
		void IResRequisitionLogic.Update(IEnumerable<IResRequisitionEntity> resRequisitionEntities)
		{
			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				Save(resRequisitionEntities.Cast<RequisitionEntity>());

				transaction.Complete();
			}
		}

		/// <summary>
		/// save requisitions
		/// </summary>
		/// <param name="reqIds"></param>
		/// <returns></returns>
		void IResRequisitionLogic.SaveRequisitionWithStatusIsFullyConfirmed(IEnumerable<int> reqIds)
		{
			var idents = reqIds.Select(id => new IdentificationData() { Id = id }).ToArray();
			var requisitions = GetByIds(idents).ToList();
			var reqStatusLogic = new BasicsCustomizeResRequisitionStatusLogic();

			foreach (var requisition in requisitions)
			{
				var reqStatuses = reqStatusLogic.GetByFilter(e => e.Id == requisition.RequisitionStatusFk && e.IsConfirmed);
				var newReqStatus = reqStatusLogic.GetByFilter(e => e.IsConfirmed).FirstOrDefault();
				requisition.RequisitionStatusFk = newReqStatus.Id;
			}
			Save(requisitions);
		}

		/// <summary>
		/// Set the correct flag to the requsition for identifcation of a requisition type
		/// </summary>
		/// <param name="requisition"></param>
		/// <returns></returns>
		void IResRequisitionLogic.DetermineRequisitionTypeIdentification(IResRequisitionEntity requisition)
		{
			var typedRequisition = requisition as RequisitionEntity;
			if (typedRequisition == null || typedRequisition.Version == 0)
			{
				if (requisition.TypeFk.HasValue || requisition.ResourceFk.HasValue)
				{
					requisition.RequisitionTypeFk = ResourceRequisitionConstants.RequisitionTypeResource;
				}
				else if (requisition.MaterialFk.HasValue)
				{
					requisition.RequisitionTypeFk = ResourceRequisitionConstants.RequisitionTypeMaterial;
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="reservationInfo"></param> 
		/// <returns>Number of records for which reservations have been made</returns>
		public IEnumerable<RequisitionEntity> ReserveMaterialsInStockAndAdjustState(RequisitionMaterialReservationEntity reservationInfo)
		{
			return DoReserveMaterialsInStock(reservationInfo, true);
		}

		/// <summary>
		/// </summary>
		/// <param name="reservationInfo"></param>
		/// <returns>Number of records for which reservations have been made</returns>
		public IEnumerable<RequisitionEntity> ReserveMaterialsInStock(RequisitionMaterialReservationEntity reservationInfo)
		{
			return DoReserveMaterialsInStock(reservationInfo, false);
		}

		private IEnumerable<RequisitionEntity> DoReserveMaterialsInStock(RequisitionMaterialReservationEntity reservationInfo, bool changeState)
		{
			var requisitionsToProcess = new List<RequisitionEntity>();
			var materialItemsWOStock = new List<RequisitionitemEntity>();

			if (reservationInfo.Requisitions != null && reservationInfo.Requisitions.Any())
			{
				requisitionsToProcess.AddRange(reservationInfo.Requisitions);

				var requisitionItems = ManageChangeStatus(requisitionsToProcess, changeState);

				ProvideMaterialReservationForRequisitionHeader(requisitionsToProcess);

				ProvideAndSaveMaterialReservationForReqItem(requisitionsToProcess, requisitionItems);

				foreach (var reqGroupItem in requisitionItems.GroupBy(item => item.RequisitionFk).ToDictionary(g => g.Key, g => g.ToArray()))
				{
					var requisition = requisitionsToProcess.SingleOrDefault(r => r.Id == reqGroupItem.Key);
					if (requisition != null)
					{
						requisition.RequisitonItemsWOStock = reqGroupItem.Value.Where(item => !item.StockFk.HasValue && item.MaterialFk.HasValue).ToArray();
						materialItemsWOStock.AddRange(requisition.RequisitonItemsWOStock);
					}
				}

				Save(requisitionsToProcess);
			}

			EvaluateMaterialCode(materialItemsWOStock);

			return requisitionsToProcess;
		}

		private void EvaluateMaterialCode(List<RequisitionitemEntity> materialItemsWOStock)
		{
			if (materialItemsWOStock != null && !materialItemsWOStock.IsNullOrEmpty())
			{
				var materialLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetMaterialLogic>();
				var materialIds = materialItemsWOStock.CollectIds(ri => ri.MaterialFk).ToArray();
				var materials = materialLogic.GetMaterialsByIds(materialIds).ToArray();

				foreach (var item in materialItemsWOStock)
				{
					var material = materials.FirstOrDefault(m => m.Id == item.MaterialFk.Value);
					if (material != null)
					{
						item.MaterialCode = material.Code;
					}
					else
					{
						item.MaterialCode = string.Format("DB Id of Materia is {0}", item.MaterialFk.Value);
					}
				}
			}
		}


		private RequisitionitemEntity[] ManageChangeStatus(List<RequisitionEntity> selectedRequisitions, bool changeState)
		{
			var changeIdList = new List<int>();

			// status can be changed if Req Header has Material and Stock if no children defined => If children (Req Item) existing => then all Req Items needs Material and Stock but Header needs no
			//	valid values Material, Stock etc. 
			var headerIds = selectedRequisitions.Select(e => e.Id).ToList();

			var allReqItems = new RequisitionitemLogic().GetByFilter(e => headerIds.Contains(e.RequisitionFk)).ToArray();

			foreach (var req in selectedRequisitions)
			{
				// has children? 
				var res1 = allReqItems.FirstOrDefault(e => e.RequisitionFk == req.Id);
				if (res1 == null)
				{
					// header has no children => then check if is valid
					if (req.MaterialFk.HasValue && req.StockFk.HasValue && req.RequisitionTypeFk.HasValue && req.RequisitionTypeFk.Value == 2)
					{
						changeIdList.Add(req.Id);
					}
					else
					{
						req.HasNoMaterials = true;
						req.HasNoStockAssigned = true;
					}
				}
				else
				{
					var res2 = allReqItems.Any(e => e.RequisitionFk == req.Id && e.MaterialFk.HasValue && e.StockFk.HasValue);

					if (res2)
					{
						changeIdList.Add(req.Id);

					}
					else
					{
						req.HasNoMaterials = true;
						req.HasNoStockAssigned = true;
					}
				}
			}

			if (changeState && changeIdList != null && changeIdList.Any())
			{
				SetRequisitionStatusByFullyCovered(selectedRequisitions.Where(e => changeIdList.Contains(e.Id)).ToList());
			}

			return allReqItems;
		}
		private void ManageReservationForRequisitionItems(RequisitionitemEntity[] reqItemsWithMaterialAndStock, List<RequisitionEntity> requisitionsToProcess)
		{
			foreach (var requisition in requisitionsToProcess)
			{
				var materialItemsWithStock = reqItemsWithMaterialAndStock.Where(m => m.StockFk.HasValue && m.RequisitionFk == requisition.Id).ToList();
				if (materialItemsWithStock.Any())
				{
					var groupedByStock = materialItemsWithStock.GroupBy(m => m.StockFk.Value).ToDictionary(g => g.Key, g => g.ToList());

					foreach (var stockGroup in groupedByStock)
					{
						DoReserveMaterialsInStockForRequisitionItem(stockGroup.Key, stockGroup.Value, requisition);
					}
				}
			}
		}

		private void CancelExistingReservationsForReqHeader(IEnumerable<RequisitionEntity> listToProcess)
		{
			var resultReservationIds = listToProcess.CollectIds(e => e.ReservationId).ToArray();

			if (resultReservationIds.Any())
			{
				var stockTransactions = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IStockTransactionLogic>("Procurement.Stock.IStockTransactionLogic");
				stockTransactions.CancelReservation(resultReservationIds);
				foreach (var req in listToProcess)
				{
					req.ReservationId = null;
				}
			}
		}

		private void CancelExistingReservationsForReqItem(IEnumerable<RequisitionitemEntity> listToProcess)
		{
			var resultReservationIds = listToProcess.CollectIds(e => e.ReservationId).Distinct().ToArray();

			if (resultReservationIds.Any())
			{
				var stockTransactions = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IStockTransactionLogic>("Procurement.Stock.IStockTransactionLogic");

				foreach (var req in listToProcess)
				{
					req.ReservationId = null;
				}
			}
		}

		private void ProvideMaterialReservationForRequisitionHeader(List<RequisitionEntity> requisitionsToProcess)
		{
			// for Header => RequisitionTypeFk must be material => ID 2 => Notice system table RES_REQUISITION_TYPE
			var reservList = requisitionsToProcess.Where(req => req.MaterialFk.HasValue && req.StockFk.HasValue && req.RequisitionTypeFk.HasValue && req.RequisitionTypeFk.Value == 2).ToList();

			if (reservList != null && reservList.Any())
			{
				CancelExistingReservationsForReqHeader(reservList);
				ManageReservationForRequisitions(reservList);
			}
		}

		private void ManageReservationForRequisitions(IEnumerable<RequisitionEntity> requisitionsWithMaterialAndStock)
		{
			if (requisitionsWithMaterialAndStock.Any())
			{
				var groupedByStock = requisitionsWithMaterialAndStock.GroupBy(r => r.StockFk.Value).ToDictionary(g => g.Key, g => g.ToList());

				foreach (var groupWithStock in groupedByStock)
				{
					DoReserveMaterialsInStock(groupWithStock.Key, groupWithStock.Value);
				}
			}
		}

		private void ProvideAndSaveMaterialReservationForReqItem(List<RequisitionEntity> requisitionsToProcess, RequisitionitemEntity[] materialItems)
		{
			if (materialItems != null && materialItems.Any())
			{
				CancelExistingReservationsForReqItem(materialItems);
				ManageReservationForRequisitionItems(materialItems, requisitionsToProcess);
				new RequisitionitemLogic().Save(materialItems);
			}
		}

		private void SetRequisitionStatusByFullyCovered(List<RequisitionEntity> requisitions)
		{
			// Get Statustabel by FullyCorvered flag is checked
			var statusIsFullyCoveredList = new BasicsCustomizeResRequisitionStatusLogic().GetListByFilter(s => s.IsFullyCovered).ToArray();
			int lowestSorting = statusIsFullyCoveredList.Min(s => s.Sorting);
			var highestStatusEntity = statusIsFullyCoveredList.FirstOrDefault(x => x.Sorting == lowestSorting);
			// Current Requisitions 
			foreach (var currentRequisition in requisitions)
			{
				if (currentRequisition.RequisitionStatusFk != highestStatusEntity.Id)
				{
					currentRequisition.RequisitionStatusFk = highestStatusEntity.Id;
				}
			}
		}

		/// <summary>
		/// update requisitions
		/// </summary>
		/// <param name="scheduleUpdates"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionUpdateEntity> DoUpdate(IEnumerable<RequisitionUpdateEntity> scheduleUpdates)
		{
			var res = new List<RequisitionUpdateEntity>();
			using (
				TransactionScope transaction = TransactionScopeFactory.Create())
			{
				var requisitionDocumentLogic = new ResourceRequisitionDocumentLogic();
				foreach (var item in scheduleUpdates)
				{
					if (item.Requisitions != null)
					{
						RecalculateQuantityAndReservedTo(item.Requisitions);
						item.Requisitions = Save(item.Requisitions);
					}

					if (item.RequisitionDocumentToSave != null && item.RequisitionDocumentToSave.Any())
					{
						item.RequisitionDocumentToSave = requisitionDocumentLogic.Save(item.RequisitionDocumentToSave);
					}

					if (item.RequisitionDocumentToDelete != null && item.RequisitionDocumentToDelete.Any())
					{
						requisitionDocumentLogic.Delete(item.RequisitionDocumentToDelete);

						FileArchiveDocLogic fileArchiveDocLogic = new FileArchiveDocLogic();
						fileArchiveDocLogic.DeleteArchiveDocuments(item.RequisitionDocumentToDelete.CollectIds(e => e.FileArchiveDocFk).ToList());
					}

					EntityUpdateDispatcher.Handle(item);

					res.Add(item);
				}
				transaction.Complete();
			}

			return res;
		}

		/// <summary>
		/// cascade delete
		/// </summary>
		/// <param name="requisitionToDelete"></param>
		public void DeleteRequisition(IEnumerable<RequisitionEntity> requisitionToDelete)
		{
			if (requisitionToDelete != null && requisitionToDelete.Any())
			{
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					// delete the dependances
					DeleteDependancies(requisitionToDelete);
					// delete requisition
					this.Delete(requisitionToDelete);

				}
			}
		}

		private void DeleteDependancies(IEnumerable<RequisitionEntity> requisitions)
		{
			var requiredSkillLogic = new RequisitionRequiredSkillLogic();
			var reqDocumentLogic = new ResourceRequisitionDocumentLogic();
			var reqItemLogic = new RequisitionitemLogic();

			var requisitionIds = requisitions.Select(e => e.Id).ToList();
			//delete requiredSkills 
			requiredSkillLogic.DeleteSkillsByRequisition(requisitionIds);

			//delete requisition items
			reqItemLogic.DeleteItemsByRequisition(requisitionIds);

			// delete documents.
			reqDocumentLogic.DeleteDocumentsByRequisition(requisitionIds);
		}



		IEnumerable<IIdentifyable> IUpdateCompleteData.HandleUpdate(IEnumerable<IIdentifyable> completeData)
		{
			var resTypes2Update = completeData.OfType<RequisitionUpdateEntity>().ToList();

			return this.DoUpdate(resTypes2Update);
		}

		IIdentifyable IDataBaseCreateLogic.Create(IdentificationData identificationData)
		{
			return Create(identificationData);
		}


		void IDataBaseLogic.Delete(IEnumerable<IIdentifyable> toDelete)
		{
			IEnumerable<RequisitionEntity> list = toDelete.ToList().OfType<RequisitionEntity>();
			base.Delete(list);
		}

		IEnumerable<IIdentifyable> IDataBaseLogic.Save(IEnumerable<IIdentifyable> toSave)
		{
			IEnumerable<RequisitionEntity> list = toSave.ToList().OfType<RequisitionEntity>();
			if (list.Any())
			{
				return base.Save(list);
			}
			IEnumerable<RequisitionUpdateEntity> listComplete = toSave.ToList().OfType<RequisitionUpdateEntity>();
			foreach (var completeRequisition in listComplete)
			{
				if (completeRequisition.Requisition != null)
				{
					var singleRequisitionList = new List<RequisitionEntity>() { completeRequisition.Requisition };
					if (completeRequisition.Requisitions != null)
					{
						completeRequisition.Requisitions = completeRequisition.Requisitions.Concat(singleRequisitionList);
					}
					else
					{
						completeRequisition.Requisitions = singleRequisitionList;
					}
				}
			}
			return Update(listComplete);
		}


		/// <summary>
		/// Save process of entity.
		/// </summary>
		/// <param name="entity"/><param name="dbContext"/>
		protected override void SavePostProcessing(RequisitionEntity entity, RVPBizComp.DbContext dbContext)
		{
			var entities = new List<RequisitionEntity>();
			entities.Add(entity);
			PostProcess(entities);


			dbContext.ExecuteStoredProcedure("RES_REQUISITREFSNULL_SP", entity.Id);
			dbContext.ExecuteStoredProcedure("RES_REQUISITION_PROC", entity.Id);
			DoPostProcessRequisitions(entities, dbContext);
		}

		/// <summary>
		/// Save process of entity.
		/// </summary>
		/// <param name="entities"/><param name="dbContext"/>
		protected override void SavePostProcessing(IEnumerable<RequisitionEntity> entities, RVPBizComp.DbContext dbContext)
		{
			SetUOMs(entities);
			PostProcess(entities);

			var reservCreateLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IDataBaseCreateLogic>("Resource.Reservation.ReservationEntity");
			foreach (var entity in entities)
			{
				dbContext.ExecuteStoredProcedure("RES_REQUISITREFSNULL_SP", entity.Id);
				dbContext.ExecuteStoredProcedure("RES_REQUISITION_PROC", entity.Id);
			}

			DoPostProcessRequisitions(entities, dbContext);
			var entArray = entities.ToArray();
		}

		private void DoPostProcessRequisitions(IEnumerable<RequisitionEntity> entities, RVPBizComp.DbContext dbContext)
		{
			var needsReservation = entities.Where(e => e.IsLinkedFixToReservation && e.Version == 1).ToArray();

			if (needsReservation != null && needsReservation.Any())
			{
				var reservationLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IResReservationLogic>();

				var reservations = reservationLogic.CreateFor(needsReservation);
				reservationLogic.Save(reservations);
			}
		}

		/// <summary>
		/// Gets search context for flat list.
		/// </summary>
		/// <param name="request">The filter request</param>
		/// <param name="response">The filter response</param>
		/// <returns>The SearchSpecification instance</returns>
		protected override SearchSpecification<RequisitionEntity, IdentificationData> GetListSearchContext(
			FilterRequest request, out FilterResponse response)
		{
			var context = new SearchSpecification<RequisitionEntity, IdentificationData>(request, OrderByKey,
				OrderTerm.CreateQuerySorter(OrderByExpressions));
			context.SearchPatternPredicateFunc = filterVal => "SearchPattern.Contains(\"" + filterVal + "\")";

			if (context.FilterIn.UseCurrentClient.HasValue && context.FilterIn.UseCurrentClient.Value)
			{
				IContext currentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
				ICompanyInfoProvider companyInfo = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ICompanyInfoProvider>();
				ICompanyInfo company = companyInfo.GetInstanceInfo(currentContext.ClientId);

				context.UseCurrentClientPredicate = e => e.CompanyFk == currentContext.ClientId;
			}
			else
			{
				var resContextd = CompanyContextAssert.GetResourceContextOrThrowException();
				context.UseCurrentClientPredicate = e => e.ResourceContextFk == resContextd;
			}

			int? projectId = null;
			if (request.HasPinningContext)
			{
				var identificationData = request.GetPinningItem("project.main");
				if (identificationData != null)
				{
					projectId = identificationData.Value.Id;
				}
			}
			if (projectId.HasValue)
			{
				int prjId = projectId.Value;
				context.CustomPredicate = e => e.ProjectFk == prjId;
			}

			response = context.FilterOut;

			return context;
		}

		/// <summary>
		/// Evaluation of pinning context and further filters, with is in many cases not required. The default does nothing therefore.
		/// </summary>
		/// <param name="query">A query to find entities in the database</param><param name="pinningItem">Array of pinning item, to reduce number of matching entities</param><param name="tokenValueFilter">Array of IdentificationData, to reduce number of matching entities</param>
		/// <returns/>
		protected override IQueryable<RequisitionEntity> EvaluatePinningItemAndFurtherFilter(
			IQueryable<RequisitionEntity> query,
			FilterRequest.PinningItem[] pinningItem,
			FilterRequest.TokenValueFilter[] tokenValueFilter)
		{
			if (pinningItem != null)
			{
				var firstOrDefault = pinningItem.FirstOrDefault(e => e.Token == "project.main");
				if (firstOrDefault != null)
				{
					var projectId = firstOrDefault.Id.Id;

					if (projectId > 0)
					{
						query = query.Where(e => e.ProjectFk == projectId);
					}
				}
			}
			return query;
		}

		/// <summary>
		/// Change unit status
		/// </summary>
		/// <param name="entityId"></param>
		/// <param name="newStatusId"></param>
		/// <returns></returns>
		public RequisitionEntity SetRequisitionStatus(int entityId, int newStatusId)
		{
			return SetEntityStatusAndSave(entityId, newStatusId, (e, i) => e.RequisitionStatusFk = i);
		}

		/// <summary>
		/// Change status
		/// </summary>
		/// <param name="resIds"></param>
		/// <returns></returns>
		public RequisitionEntity SetReqStatusToIsFullyCovered(IEnumerable<int> resIds)
		{
			// get reservations
			var reservationLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IResReservationLogic>();
			var reservation = reservationLogic.GetReservationsByIds(resIds).FirstOrDefault();

			if (reservation == null)
			{
				throw new Exception("Please save the reservation first");
			}

			var requisition = this.GetById(new IdentificationData() { Id = reservation.RequisitionFk });

			var reqStatusLogic = new BasicsCustomizeResRequisitionStatusLogic();
			var reqStatuses = reqStatusLogic.GetByFilter(e => e.Id == requisition.RequisitionStatusFk && e.IsFullyCovered);

			if (reqStatuses.Any())
			{
				throw new Exception("The selected reservation has already status `Is fully covered`");
			}

			var newReqStatus = reqStatusLogic.GetByFilter(e => e.IsFullyCovered).FirstOrDefault();

			if (newReqStatus == null)
			{
				throw new Exception("A status with the flag `Is fully covered` not found!");
			}

			requisition.RequisitionStatusFk = newReqStatus.Id;
			return Save(requisition);

		}


		/// <summary>
		/// SaveRequisitionsWithStatusIsReopend
		/// </summary>
		/// <param name="ids"></param>
		public void SaveRequisitionsWithStatusIsReopend(IEnumerable<int> ids)
		{
			var requisitionStatusLogic = new BasicsCustomizeResRequisitionStatusLogic();
			var reopenedStatus = requisitionStatusLogic.GetByFilter(e => e.IsReopened).FirstOrDefault();

			if (reopenedStatus != null)
			{
				var identifications = ids.Select(id => new IdentificationData() { Id = id }).ToArray();

				var requisitions = this.GetByIds(identifications).ToList();

				foreach (var entity in requisitions)
				{
					entity.RequisitionStatusFk = reopenedStatus.Id;
					entity.ReservedTo = null;
					entity.ReservedFrom = null;
				}

				this.Save(requisitions);
			}
			else
			{
				throw new BusinessLayerException(NLS.ERR_NotDeleteDueToMissingReopenedStates);
			}
		}


		/// <summary>
		/// SetUomFromResourceTypes
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="resourceTypeIds"></param>
		/// <param name="resTypeLogic"></param>
		public void SetUomFromResourceTypes(RequisitionEntity entity, int[] resourceTypeIds, IResTypeLogic resTypeLogic)
		{
			if (resourceTypeIds.Any() && entity.TypeFk.HasValue)
			{
				var matchedResourcesTyp = resTypeLogic.GetTypesById(resourceTypeIds);
				if (matchedResourcesTyp.Any())
				{
					entity.UomFk = matchedResourcesTyp.FirstOrDefault(e => e.Id == entity.TypeFk).UoMFk;
				}
			}
		}

		/// <summary>
		/// SetUomFromResource
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="resourceIds"></param>
		/// <param name="resourceLogic"></param>
		public void SetUomFromResource(RequisitionEntity entity, int[] resourceIds, IResourceMasterLogic resourceLogic)
		{
			if (resourceIds.Any() && entity.ResourceFk.HasValue)
			{
				var matchedResources = resourceLogic.GetByIds(resourceIds);
				if (matchedResources.Any())
				{
					entity.UomFk = matchedResources.FirstOrDefault(e => e.Id == entity.ResourceFk).UomBasisFk;
				}
			}
		}

		/// <summary>
		/// SetUomFromMaterial
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="materialIds"></param>
		/// <param name="materialLogic"></param>
		public void SetUomFromMaterial(RequisitionEntity entity, int[] materialIds, IGetMaterialLogic materialLogic)
		{
			if (materialIds.Any() && entity.MaterialFk.HasValue)
			{
				var matchedMaterials = materialLogic.GetMaterialsByIds(materialIds);
				if (matchedMaterials.Any())
				{
					entity.UomFk = matchedMaterials.FirstOrDefault(e => e.Id == entity.MaterialFk).UomFk;
				}
			}
		}

		private void SetUOMs(IEnumerable<RequisitionEntity> entities)
		{
			var materialLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetMaterialLogic>();
			var resourceLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IResourceMasterLogic>();
			var resTypeLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IResTypeLogic>();
			var materialIds = entities.Select(e => e.MaterialFk).OfType<int>().Distinct().ToArray();
			var resourceIds = entities.Select(e => e.ResourceFk).OfType<int>().Distinct().ToArray();
			var resourceTypeIds = entities.Select(e => e.TypeFk).OfType<int>().Distinct().ToArray();

			foreach (var entity in entities)
			{
				if (entity.MaterialFk.HasValue && entity.UomFk == 0)
				{
					SetUomFromMaterial(entity, materialIds, materialLogic);
				}
				if (entity.ResourceFk.HasValue && !entity.MaterialFk.HasValue && entity.UomFk == 0)
				{
					SetUomFromResource(entity, resourceIds, resourceLogic);
				}
				if (entity.TypeFk.HasValue && !entity.ResourceFk.HasValue && !entity.MaterialFk.HasValue && entity.UomFk == 0)
				{
					SetUomFromResourceTypes(entity, resourceTypeIds, resTypeLogic);
				}
			}
		}


		/// <summary>
		/// EvaluateGroupingFilter for Structure Container 
		/// </summary>
		/// <param name="filterRequest"></param>
		/// <param name="dbContext"></param>
		/// <param name="query"></param>
		/// <returns></returns>
		protected override IQueryable<RequisitionEntity> EvaluateGroupingFilter(FilterRequest filterRequest, DbContext dbContext, IQueryable<RequisitionEntity> query)
		{
			if (filterRequest.GroupingFilter != null)
			{
				var groupingLogic = new GroupingHierarchyLogic();
				var filterOut = new FilterResponse<int>();
				FilterRequest<int> filterIn = new FilterRequest<int>()
				{
					EnhancedFilterDef = filterRequest.EnhancedFilterDef,
					ExecutionHints = filterRequest.ExecutionHints,
					GroupingFilter = filterRequest.GroupingFilter,
					IncludeNonActiveItems = filterRequest.IncludeNonActiveItems,
					IncludeResultIds = filterRequest.IncludeResultIds,
					IsEnhancedFilter = filterRequest.IsEnhancedFilter,
					PageNumber = filterRequest.PageNumber,
					PageSize = filterRequest.PageSize,
					Pattern = filterRequest.Pattern,
					ProjectContextId = filterRequest.ProjectContextId,
					UseCurrentClient = filterRequest.UseCurrentClient
				};
				var execInfo = new FilterExecutionInfo<int>(filterIn, filterOut);
				string requestId = null;
				groupingLogic.GetGroupSelection(execInfo, filterIn, ref requestId, new CCB.FurtherKeysProvider());
				query = query.Where(e => e.DdTempIdsEntities.Any(d => d.RequestId == requestId));
			}
			return query;
		}

		#region ICreateEntityFacade members

		/// <summary>
		/// Create a requsition and return the new object as an bunch of key value pairs
		/// </summary>
		IDictionary<string, object> ICreateEntityFacade.Create()
		{
			var requisition = CreateRequisition();
			var objectDic = ToDictionary(requisition);

			return objectDic;
		}

		/// <summary>
		/// Name of the entity, for IEntityFacade
		/// </summary>
		string IEntityFacade.Name
		{
			get { return "Requisition"; }
		}

		/// <summary>
		/// UUID to clearly determine the entity provider, for IEntityFacade
		/// </summary>
		string IEntityFacade.Id
		{
			get { return EntitiyFacadeIdentifier.RESOURCE_REQUISITION; }
		}

		/// <summary>
		/// Module name
		/// </summary>
		string IEntityFacade.ModuleName
		{
			get { return "resource.requisition"; }
		}

		/// <summary>
		/// Get Entity Properties, for IEntityFacade
		/// </summary>
		string[] IEntityFacade.Properties
		{
			get { return _entityProperties.GetPropertyNames(); }
		}

		/// <summary>
		/// Get a ActivityEntity by Id, for IEntityFacade
		/// </summary>
		IDictionary<string, object> IEntityFacade.Get(int id)
		{
			var entity = GetById(new IdentificationData { Id = id });
			var objectDic = ToDictionary(entity);
			return objectDic;
		}

		/// <summary>
		/// Save a ActivityEntity, for IEntityFacade
		/// </summary>
		IDictionary<string, object> IEntityFacade.Save(IDictionary<string, object> entityDictionary)
		{
			var id = entityDictionary.GetId<int>();
			var entity = GetById(new IdentificationData { Id = id });

			if (entity == null)
			{
				entity = new RequisitionEntity();
				entity.Id = id;
			}
			entity.SetObject(entityDictionary, _entityProperties);

			if (entity.Version == 0)
			{
				object fkObject;
				int fkValue;
				if (entityDictionary.TryGetValue("CompanyFk", out fkObject) && Int32.TryParse(fkObject.ToString(), out fkValue))
				{
					entity.CompanyFk = fkValue;
				}
				if (entityDictionary.TryGetValue("ResourceContextFk", out fkObject) && Int32.TryParse(fkObject.ToString(), out fkValue))
				{
					entity.ResourceContextFk = fkValue;
				}
			}

			return ToDictionary(Save(entity));
		}

		private IDictionary<string, object> ToDictionary(RequisitionEntity entity)
		{
			var objectDic = entity.AsDictionary(_entityProperties);

			return objectDic;
		}

		private static readonly ConvertProperties _entityProperties = new ConvertProperties()
			.Add("Id", true)
			.Add("Description")
			.Add("ProjectFk")
			.Add("ResourceFk")
			.Add("RequisitionStatusFk")
			.Add("TypeFk")
			.Add("Quantity")
			.Add("UomFk")
			.Add("RequestedFrom")
			.Add("RequestedTo")
			.Add("ReservedFrom", true)
			.Add("ReservedTo", true)
			.Add("JobFk")
			.Add("CommentText")
			.Add("ActivityFk")
			.Add("TrsRequisitionFk")
			.Add("IsLinkedFixToReservation")
			.Add("PpsEventFk", "ProductionEventFk")
			 .Add("CompanyFk", true)//Handled for version == 0 correctly in the save method
			.Add("ResourceContextFk", true)//Handled for version == 0 correctly in the save method
			.Add("UserDefinedText01")
			.Add("UserDefinedText02")
			.Add("UserDefinedText03")
			.Add("UserDefinedText04")
			.Add("UserDefinedText05");

		#endregion

		#region IResRequisitionLogic

		/// <summary>
		/// GetRequisitionsByIds
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByIds(IEnumerable<int> ids)
		{
			if (ids.IsNullOrEmpty())
			{
				return Array.Empty<IResRequisitionEntity>();
			}
			var idArray = ids.ToArray();
			return this.GetCoresByFilter(e => idArray.Contains(e.Id));
		}

		/// <summary>
		/// Get requisitionIds by activity
		/// </summary>
		/// <param name="activityId"></param>
		/// <returns></returns>
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByActivity(int activityId)
		{
			return this.GetCoresByFilter(e => e.ActivityFk == activityId).ToList().ToArray();
		}

		/// <summary>
		/// Get requisitionIds by PpsEvent
		/// </summary>
		/// <param name="ppsEventId"></param>
		/// <returns></returns>
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByPpsEvent(int ppsEventId)
		{
			return this.GetCoresByFilter(e => e.PpsEventFk == ppsEventId).ToArray();
		}

		/// <summary>
		/// Get requisitionIds by PpsEvents
		/// </summary>
		/// <param name="ppsEventIds"></param>
		/// <returns></returns>
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByPpsEvents(IEnumerable<int> ppsEventIds)
		{
			if (ppsEventIds.IsNullOrEmpty())
			{
				return Array.Empty<IResRequisitionEntity>();
			}
			return this.GetCoresByFilter(e => e.PpsEventFk.HasValue && ppsEventIds.Contains(e.PpsEventFk.Value)).ToArray();
		}

		/// <summary>
		/// Get requisitionIds by PpsEvent
		/// </summary>
		/// <param name="trsRequisitionId"></param>
		/// <returns></returns>
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByTrsRequisition(int trsRequisitionId)
		{
			return this.GetCoresByFilter(e => e.TrsRequisitionFk == trsRequisitionId).ToArray();
		}

		/// <summary>
		/// GetRequisitionsByProject
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<IResRequisitionEntity> GetRequisitionsByProject(int projectId)
		{
			return this.GetCoresByFilter(e => e.ProjectFk == projectId).ToArray();
		}

		/// <summary>
		/// Get resource requisition ids by transport requisition
		/// </summary>
		/// <param name="trsRequisitionIds">transport requisition ids</param>
		/// <returns></returns>
		/// \since 2018-02-23 (16:39), by zov
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByTrsRequisitions(IEnumerable<int> trsRequisitionIds)
		{
			return this.GetCoresByFilter(r => r.TrsRequisitionFk.HasValue && trsRequisitionIds.Contains(r.TrsRequisitionFk.Value))
						.ToList();
		}

		#endregion

		/// <summary>
		/// GetForPlanningBoard
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> GetForPlanningBoard(PlanningBoardFilter filter)
		{
			var resourceContextId = CompanyContextAssert.GetResourceContextOrThrowException();

			PlanningboardFilterLogic filterLogic = new PlanningboardFilterLogic();
			ModuleLogic moduleLogic = new ModuleLogic();
			var reqStatuses = new BasicsCustomizeResRequisitionStatusLogic().GetByFilter(e => e.IsFullyCovered).Select(e => e.Id).ToList();
			var firstOrDefault = moduleLogic.GetFilteredModules(e => e.InternalName == filter.ModuleName).FirstOrDefault();
			var moduleId = -1;
			if (firstOrDefault != null)
			{
				moduleId = firstOrDefault.Id;
			}
			IEnumerable<PlanningboardFilterEntity> configuredResourceTypes = filterLogic.GetPlanningBoardFilterByModule(moduleId).ToList();

			using (var dbContext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
			{//e.RequisitionTypeFk 1 and 3 are supported, both for plant
				IQueryable<RequisitionEntity> query = dbContext.Entities<RequisitionEntity>();
				query = query.Where(e => e.ResourceContextFk == resourceContextId && !e.MaterialFk.HasValue && (e.RequisitionTypeFk == 1 || e.RequisitionTypeFk == 3) && (e.ResourceFk.HasValue || e.TypeFk.HasValue));

				if (filter.IgnoreIsFullyCovered == true)
				{//Means, ignore when state is set to a fully covered state
					query = query.Where(e => !reqStatuses.Contains(e.RequisitionStatusFk));
				}

				TimeSpan ts = filter.To - filter.From;
				if (ts.Days == 0)
				{
					query = query.Where(e => false);
				}
				else
				{
					query = query.Where(e => e.RequestedFrom <= filter.To && e.RequestedTo >= filter.From);
				}

				if (configuredResourceTypes.Any())
				{
					var configuredResourceTypesIds = configuredResourceTypes.Select(e => e.TypeFk);
					query = query.Where(e => !e.TypeFk.HasValue || configuredResourceTypesIds.Contains(e.TypeFk.Value));
				}

				if (filter.ResourceIdList != null && filter.ResourceIdList.Any())
				{
					query = query.Where(e => !e.ResourceFk.HasValue || filter.ResourceIdList.Contains(e.ResourceFk.Value));
				}

				if (filter.ResourceTypeIdList != null && filter.ResourceTypeIdList.Any())
				{
					query = query.Where(e => !e.TypeFk.HasValue || filter.ResourceTypeIdList.Contains(e.TypeFk.Value));
				}

				if (filter.ResourceProjectIdList != null && filter.ResourceProjectIdList.Any())
				{
					query = query.Where(e => filter.ResourceProjectIdList.Contains(e.ProjectFk));
				}

				if (filter.DispatcherGroupFk.HasValue)
				{
					var dispGroups = new BasicsCustomizeLogisticsDispatcherGroup2GroupLogic().GetCooperatingGroups(filter.DispatcherGroupFk.Value).ToArray();
					var resTypes = new ResourceTypeMainLogic().GetListByFilter(rt => rt.DispatcherGroupFk.HasValue && dispGroups.Contains(rt.DispatcherGroupFk.Value)).ToArray();
					var typesIds = resTypes.Select(e => e.Id).ToArray();

					query = query.Where(e => e.TypeFk.HasValue && typesIds.Contains(e.TypeFk.Value));
				}

				if (filter.ProjectFk.HasValue)
				{
					var projectId = filter.ProjectFk.Value;

					query = query.Where(e => e.ProjectFk == projectId);
				}

				var result = query.ToArray();

				if (filter.IgnoreIsNotFullyCovered == true)
				{//Means, ignore already when a reservation is assigned
					var reservationLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IResReservationLogic>();
					var reservations = reservationLogic.GetReservationsByRequisitions(result).ToArray();
					var requisitionFks = reservations.CollectIds(e => e.RequisitionFk).ToArray();

					result = result.Where(e => !requisitionFks.Contains(e.Id)).ToArray();
				}

				PostProcess(result);
				return result;
			}
		}
		private bool CheckForMoreThenOneReservations(IEnumerable<RequisitionEntity> requisitions, IEnumerable<IResReservationEntity> reservations)
		{
			return requisitions.Any(req => reservations.Where(res => res.RequisitionFk == req.Id).Count() > 1);
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="requisitions"></param>
		/// <returns></returns>
		public string Merge(IEnumerable<RequisitionEntity> requisitions)
		{
			var reservationlogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IResReservationLogic>();
			var reservations = reservationlogic.GetReservationsByRequisitions(requisitions);
			var numberOfReservations = reservations.Count();
			RequisitionEntity mainRequistion;
			if (numberOfReservations > 1)
			{
				return null;
			}
			else if(numberOfReservations == 1)
			{
				mainRequistion = requisitions.FirstOrDefault(req => reservations.Where(res => res.RequisitionFk == req.Id).Any());
			}
			else
			{
				mainRequistion = requisitions.FirstOrDefault(req => req.RequestedFrom == requisitions.Select(req => req.RequestedFrom).Min());
			}
			var childRequistions = requisitions.Where(req => req.Id != mainRequistion.Id);
			var req2ReqLogic = new Requisition2RequisitionLogic();
			var newReq2Reqs = req2ReqLogic.MultiCreate(childRequistions.Count());
			var childReqAndReq2Reqs = childRequistions.EqualSizeJoinByIndex(newReq2Reqs, (req, req2req) => new { Requisition = req, Requistion2Requistion = req2req });
			foreach (var childReqAndReq2Req in childReqAndReq2Reqs)
			{
				childReqAndReq2Req.Requistion2Requistion.RequisitionFk = mainRequistion.Id;
				childReqAndReq2Req.Requistion2Requistion.RequisitionLinkedFk = childReqAndReq2Req.Requisition.Id;
				childReqAndReq2Req.Requistion2Requistion.IsReference = true;
				childReqAndReq2Req.Requistion2Requistion.IsTimeEnhancement = true;
				childReqAndReq2Req.Requisition.IsDeleted = true;
			}
			mainRequistion.RequestedTo = requisitions.Max(req => req.RequestedTo);
			req2ReqLogic.Save(newReq2Reqs);
			Save(requisitions);
			return mainRequistion.Code;
		}

		/// <summary>
		/// gets requisitions by filter
		/// </summary>
		/// <param name="jobFk"></param>
		/// <param name="projectFk"></param>
		/// <param name="resourceFk"></param>
		/// <param name="requestedFrom"></param>
		/// <param name="requestedTo"></param>
		/// /// <param name="resourceTypeFk"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> GetLookupListByFilter(int? jobFk, int? projectFk, int? resourceFk,
			DateTime? requestedFrom, DateTime? requestedTo, int? resourceTypeFk)
		{
			List<RequisitionEntity> result = null;

			using (var dbContext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
			{
				IQueryable<RequisitionEntity> query = dbContext.Entities<RequisitionEntity>();

				if (jobFk > 0)
				{
					query = query.Where(e => e.JobFk == jobFk);
				}
				if (resourceFk.HasValue)
				{
					query = query.Where(e => e.ResourceFk == resourceFk.Value);
				}
				if (projectFk.HasValue)
				{
					query = query.Where(e => e.ProjectFk == projectFk.Value);
				}
				if (requestedFrom.HasValue)
				{
					query = query.Where(e => DateTime.Compare(e.RequestedFrom, requestedFrom.Value) >= 0);
				}
				if (requestedTo.HasValue)
				{
					query = query.Where(e => DateTime.Compare(e.RequestedTo, requestedTo.Value) <= 0);
				}
				if (resourceTypeFk.HasValue)
				{
					query = query.Where(e => e.TypeFk == resourceTypeFk.Value);
				}

				var resContextd = CompanyContextAssert.GetResourceContextOrThrowException();
				query.Where(e => e.ResourceContextFk == resContextd);

				// Always exclude deleted entities via centralized filter
				query = PredefinedAddtionalFilter(dbContext, query);

				result = query.ToList();
			}

			PostProcess(result);

			return result;
		}

		/// <summary>
		/// gets requisitions for lookup by context
		/// </summary>
		/// <returns></returns>
		private IEnumerable<RequisitionEntity> GetLookupList()
		{
			var today = DateTime.Today.AddDays(-365);

			return GetLookupListByFilter(null, null, null, today, null, null);
		}

		#region IChangeStatus implementation

		/// <summary>
		/// ChangeStatus
		/// </summary>
		/// <param name="identification"></param>
		/// <param name="statusId"></param>
		EntityBase IChangeStatus.ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			return SetEntityStatusAndSave(identification.Id, statusId, (e, i) => e.RequisitionStatusFk = i);
		}

		/// <summary>
		/// GetCurrentStatus
		/// </summary>
		/// <param name="identification"></param>
		/// <returns></returns>
		int IChangeStatus.GetCurrentStatus(IStatusIdentifyable identification)
		{
			return this.GetByFilter(e => e.Id == identification.Id).First().RequisitionStatusFk;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			throw new NotImplementedException();
		}

		#endregion IChangeStatus implementation

		#region Aggregator

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			var updateEntity = to as RequisitionUpdateEntity;
			if (updateEntity != null && toBeAggregated.Any())
			{
				DoAggregate(updateEntity, relInfo, toBeAggregated);
			}
		}

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IIdentifyable toBeAggregated)
		{
			var updateEntity = to as RequisitionUpdateEntity;
			if (updateEntity != null && toBeAggregated != null)
			{
				DoAggregate(updateEntity, relInfo, new List<IIdentifyable>() { toBeAggregated });
			}
		}

		IIdentifyable IEntityAggregator.ProvideCompleteEntity()
		{
			return new RequisitionUpdateEntity();
		}

		/// <summary>
		/// Gives access to the instance being responsible for creating complete entities. base implmentation returns null, which is appropriate return for all leave entities
		/// </summary>
		/// <returns>
		/// null
		/// </returns>
		protected override IEntityAggregator ProvideAggregator()
		{
			return this;
		}

		/// <summary>
		/// DoValidateCopies
		/// </summary>
		/// <param name="copies"></param>
		/// <param name="responsible"></param>
		protected override void DoValidateCopies(IEnumerable<RequisitionEntity> copies, IEntityRelationInfo responsible)
		{
			var defStatus = new BasicsCustomizeResRequisitionStatusLogic().GetByFilter(s => s.IsDefault == true).ToArray();
			int minSorting = defStatus.Select(s => s.Sorting).Min();
			foreach (var item in copies)
			{
				item.ReservationId = null;
				item.ReservedFrom = null;
				item.ReservedTo = null;
				if (defStatus.Any())
				{
					item.RequisitionStatusFk = defStatus.Where(s => s.Sorting == minSorting).FirstOrDefault().Id;
				}
				item.HasToGenerateCode = true;
				item.Code = String.Empty;
				if (!item.RequisitionTypeFk.HasValue)
				{
					if (item.TypeFk.HasValue)
					{
						item.RequisitionTypeFk = 1;
					}
					else if (item.MaterialFk.HasValue)
					{
						item.RequisitionTypeFk = 2;
					}
				}
			}
		}

		/// <summary>
		/// Takes over a list of entities and appends it to the passed update entity
		/// </summary>
		/// <param name="updateEntity">Complete entity to build up</param>
		/// <param name="relInfo">Information about content of toBeAggregated</param>
		/// <param name="toBeAggregated">Entities which need to be added to updateEntity</param>
		/// protected virtual IEntityAggregator ProvideAggregator(
		protected void DoAggregate(RequisitionUpdateEntity updateEntity, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			switch (relInfo.GetIdentifier())
			{
				case "resource.requisition.requisition":
					updateEntity.Requisitions = toBeAggregated.OfType<RequisitionEntity>();
					updateEntity.RequisitionId = updateEntity.Requisitions.FirstOrDefault().Id;
					break;
				case "resource.requisition.requiredskill":
					updateEntity.RequiredSkillsToSave = toBeAggregated.OfType<RequisitionRequiredSkillEntity>();
					break;
				case "resource.requisition.requisitionitem":
					updateEntity.RequiredSkillsToSave = toBeAggregated.OfType<RequisitionRequiredSkillEntity>();
					break;

			}
		}

		#endregion

		/// <summary>
		/// Gets lookup search list by request.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public override LookupSearchResponse<RequisitionEntity> GetLookupSearchList(RIB.Visual.Basics.Common.Core.Final.LookupSearchRequest request)
		{
			var response = new Basics.Common.Core.LookupSearchResponse<RequisitionEntity>();
			if (request != null && request.RequestIds != null && request.RequestIds.Any()) //get by ids
			{
				response.SearchList = GetByIds(request.RequestIds);
				response.RecordsFound = response.RecordsRetrieved = response.SearchList.Count();
			}
			else if (request == null)
			{
				response.SearchList = GetLookupList().ToArray();
				response.RecordsFound = response.RecordsRetrieved = response.SearchList.Count();
			}
			else
			{
				response = GetLookupSearchList4FlatList(request);
			}

			return response;
		}

		/// <summary>
		/// Provides extension for subclass should build specific filer for lookup data.
		/// </summary>
		/// <param name="commonQuery"/><param name="request"/><param name="context"/>
		/// <returns/>
		protected override IQueryable<RequisitionEntity> DoBuildLookupSearchFilter(IQueryable<RequisitionEntity> commonQuery, LookupSearchRequest request, DbContext context)
		{
			var resContextd = CompanyContextAssert.GetResourceContextOrThrowException();
			commonQuery = commonQuery.Where(e => e.ResourceContextFk == resContextd);

			if (request.FilterKey == "resourcerequisitionfilter")
			{
				var jobFk = request.TryGetParameterValueAsInt("jobFk");
				var projectFk = request.TryGetParameterValueAsInt("projectFk");
				var resourceFk = request.TryGetParameterValueAsInt("resourceFk");
				var requestedFrom = request.TryGetDateTimeUtc("requestedFrom");
				var requestedTo = request.TryGetDateTimeUtc("requestedTo");

				if (jobFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.JobFk == jobFk);
				}
				if (projectFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.ProjectFk == projectFk);
				}
				if (resourceFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.ResourceFk == resourceFk);
				}
				if (requestedFrom.HasValue)
				{
					commonQuery = commonQuery.Where(e => DateTime.Compare(e.RequestedFrom, requestedFrom.Value) >= 0);
				}

				if (requestedTo.HasValue)
				{
					commonQuery = commonQuery.Where(e => DateTime.Compare(e.RequestedTo, requestedTo.Value) <= 0);
				}

			}
			if (request.FilterKey == "resourcerequisitionalterfilter")
			{
				var jobFk = request.TryGetParameterValueAsInt("jobFk");
				var projectFk = request.TryGetParameterValueAsInt("projectFk");
				var resourceFk = request.TryGetParameterValueAsInt("resourceFk");
				var requestedFrom = request.TryGetDateTimeUtc("requestedFrom");
				var requestedTo = request.TryGetDateTimeUtc("requestedTo");

				if (jobFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.JobFk == jobFk);
				}
				if (projectFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.ProjectFk == projectFk);
				}
				if (resourceFk.HasValue)
				{
					commonQuery = commonQuery.Where(e => e.ResourceFk == resourceFk);
				}
				if (requestedFrom.HasValue)
				{
					commonQuery = commonQuery.Where(e => DateTime.Compare(e.RequestedFrom, requestedFrom.Value) >= 0);
				}

				if (requestedTo.HasValue)
				{
					commonQuery = commonQuery.Where(e => DateTime.Compare(e.RequestedTo, requestedTo.Value) <= 0);
				}
								
				commonQuery = commonQuery.Where(e => e.ReservedFrom == null);
				

			}
			if (!String.IsNullOrEmpty(request.SearchText))
			{
				// specify fields to search
				var opts = new QueryFilterOptions<RequisitionEntity> { SearchText = request.SearchText, IgnoreSearchPattern = true };
				opts.Add(request.SearchFields);

				commonQuery = commonQuery.JoinTrAndFilter(context, opts);
			}
			return commonQuery;
		}

		private void ValidateProjectFk(IEnumerable<RequisitionEntity> entities)
		{
			var jobIds = entities.Select(r => r.JobFk).Distinct();

			var jobAccess = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var jobs = jobAccess.GetJobByIds(jobIds);

			foreach (var req in entities)
			{
				var job = jobs.FirstOrDefault(j => j.Id == req.JobFk);
				if (job != null)
				{
					req.ProjectFk = job.ProjectFk;
				}


			}
		}

		private void DoReserveMaterialsInStock(int stock, IEnumerable<RequisitionEntity> requisitions)
		{
			var stockTransactions = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IStockTransactionLogic>("Procurement.Stock.IStockTransactionLogic");
			var materialTransactions = new List<IStockTransactionEntity>();

			var stockTxnLogic = new BasicsCustomizePrcStockTransactionTypeLogic();
			var stockTxnType = stockTxnLogic.GetByFilter(t => t.IsLive && t.IsReservation && !t.IsDelta && !t.IsReceipt).FirstOrDefault();
			if (stockTxnType == null)
			{
				stockTxnType = stockTxnLogic.GetByFilter(t => t.IsDefault).FirstOrDefault();
			}

			var stockTxnTypeId = stockTxnType.Id;

			if (requisitions != null && requisitions.Any())
			{
				var reservationIds = stockTransactions.NextReservationIds(requisitions.Count()).ToArray();
				var newTransactions = stockTransactions.CreateTransactionInProjectStock(stock, requisitions.Select(mat => mat.MaterialFk.Value), e => { e.ReservationId = 1; e.PrcStocktransactiontypeFk = stockTxnTypeId; }).ToList();
				int index = 0;

				foreach (var record in requisitions)
				{
					var materialTxn = newTransactions.FirstOrDefault(tx => tx.MdcMaterialFk == record.MaterialFk);
					newTransactions.Remove(materialTxn);

					materialTxn.PrjStockFk = stock;
					materialTxn.BasUomFk = record.UomFk;
					materialTxn.DocumentDate = record.RequestedFrom;
					materialTxn.Quantity = record.Quantity;
					materialTxn.Total = 0.0m;
					materialTxn.DispatchHeaderFk = null;
					materialTxn.DispatchRecordFk = null;
					materialTxn.ReservationId = reservationIds[index];
					record.ReservationId = reservationIds[index];

					materialTransactions.Add(materialTxn);
					++index;
				}

				stockTransactions.Update(materialTransactions);
			}
		}

		private void DoReserveMaterialsInStockForRequisitionItem(int stock, IEnumerable<RequisitionitemEntity> requisitionItems, RequisitionEntity parent)
		{
			var stockTransactions = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IStockTransactionLogic>("Procurement.Stock.IStockTransactionLogic");
			var materialTransactions = new List<IStockTransactionEntity>();

			var stockTxnLogic = new BasicsCustomizePrcStockTransactionTypeLogic();
			var stockTxnType = stockTxnLogic.GetByFilter(t => t.IsLive && t.IsReservation && !t.IsDelta && !t.IsReceipt).FirstOrDefault();
			if (stockTxnType == null)
			{
				stockTxnType = stockTxnLogic.GetByFilter(t => t.IsDefault).FirstOrDefault();
			}

			var stockTxnTypeId = stockTxnType.Id;

			if (requisitionItems != null && requisitionItems.Any())
			{
				var reservationIds = stockTransactions.NextReservationIds(requisitionItems.Count()).ToArray();
				var newTransactions = stockTransactions.CreateTransactionInProjectStock(stock, requisitionItems.Select(mat => mat.MaterialFk.Value), e => { e.ReservationId = 1; e.PrcStocktransactiontypeFk = stockTxnTypeId; }).ToList();
				int index = 0;

				foreach (var record in requisitionItems)
				{
					var materialTxn = newTransactions.FirstOrDefault(tx => tx.MdcMaterialFk == record.MaterialFk);
					newTransactions.Remove(materialTxn);

					materialTxn.PrjStockFk = stock;
					materialTxn.BasUomFk = record.UomFk;
					materialTxn.DocumentDate = parent.RequestedFrom;
					materialTxn.Quantity = record.Quantity;
					materialTxn.Total = 0.0m;
					materialTxn.DispatchHeaderFk = null;
					materialTxn.DispatchRecordFk = null;
					materialTxn.ReservationId = reservationIds[index];
					record.ReservationId = reservationIds[index];

					materialTransactions.Add(materialTxn);
					++index;
				}

				stockTransactions.Update(materialTransactions);
			}
		}
		private void InitializeDispatchHeader(CreateDispatchNotesCache cache)
		{
			cache.DispatchHeader.Job1Fk = cache.Data.PerformingJobId;
			cache.DispatchHeader.Job2Fk = cache.Data.ReceivingJobId;
			cache.DispatchHeader.RubricCategoryFk = cache.Data.RubricCategoryId;
			if (cache.DefaultHeaderStatus != null)
			{
				cache.DispatchHeader.DispatchStatusFk = cache.DefaultHeaderStatus.Id;
			}
			else
			{
				throw new BusinessLayerException("No Dispatch Header Default Status for the passed Rubric Category was found!");
			}

			var matchedReq = cache.Requisitions.FirstOrDefault(req => req.Id == cache.Data.FocusedRequisitionId);

			if (matchedReq != null && !matchedReq.Description.IsNullOrEmpty())
			{
				cache.DispatchHeader.Description = matchedReq.Description;
			}

			if (matchedReq != null && matchedReq.ClerkOwnerFk.HasValue)
			{
				cache.DispatchHeader.ClerkRequesterFk = matchedReq.ClerkOwnerFk.Value;
			}

			if (cache.ReceivingJob.ClerkResponsibleFk.HasValue)
			{
				cache.DispatchHeader.ClerkReceiverFk = cache.ReceivingJob.ClerkResponsibleFk.Value;
			}
			cache.DispatchHeader.DocumentDate = DateTime.Now;
			cache.DispatchHeader.EffectiveDate = cache.Data.DateEffective;

			//Handle Dispatch Header Code is manged by the SavePreProcessing in DispatchHeaderLogic don't necessary here
			//BAS_COMPANY_FK, DISPATCH_HEADER_TYPE and BAS_CLERK_DISPATCHER_FK are set in the Create Header already
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		protected override void SavePreProcessing(RequisitionEntity entity)
		{
			var numberLogic = new BasicsCompanyNumberLogic();
			if (entity.RubricCategoryFk.HasValue && entity.HasToGenerateCode)
			{
				if (numberLogic.HasToCreateCompanyNumber(entity.RubricCategoryFk.Value))
				{
					entity.Code = SequenceNumberForCode(entity);
				}
				else if (String.IsNullOrEmpty(entity.Code))
				{
					if (numberLogic.CanCreateCompanyNumber(entity.RubricCategoryFk.Value))
					{
						entity.Code = SequenceNumberForCode(entity);
					}
				}
			}

			if (String.IsNullOrEmpty(entity.Code))
			{
				string guidString = System.Guid.NewGuid().ToString();
				entity.Code = guidString.Substring(0, 16);
			}
		}

		private string SequenceNumberForCode(RequisitionEntity entity)
		{
			var project = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>().GetProjectById((int)entity.ProjectFk);
			RequisitionEntity[] dispatchHeaders = new RequisitionEntity[] { entity };
			var sequenceConfig = new CompanySequenceConfig("resource.requisition.requisition")
			{
				ProjectFk = entity.ProjectFk,
				ProjectNo = project.ProjectNo
			};
			return entity.Code = new BasicsCompanyNumberLogic().CreateCompanyNumber(entity.RubricCategoryFk.Value, sequenceConfig);
		}

		/// <summary>
		/// Processes entities before they get validated and saved.
		/// </summary>
		/// <param name="entities">The entities.</param><seealso cref="M:RIB.Visual.Basics.Common.BusinessComponents.Final.EntityUpdateLogic`2.SavePreProcessing(`0)"/><seealso cref="M:SavePostProcessing"/>
		protected override void SavePreProcessing(IEnumerable<RequisitionEntity> entities)
		{
			foreach (var entity in entities)
			{
				SavePreProcessing(entity);
			}
		}

		private void InitializeDispatchRecordForMaterial(CreateDispatchNotesCache cache)
		{
			const int recordTypeMat = 3;
			cache.ControlParameter.DispatchRecord.MaterialFk = cache.ControlParameter.Requisition.MaterialFk;
			// ReSharper disable once ConditionIsAlwaysTrueOrFalse
			if (cache.ControlParameter.Article != null)
			{

				if (string.IsNullOrEmpty(cache.ControlParameter.Article.DescriptionInfo1.Translated))
				{
					cache.ControlParameter.Article.DescriptionInfo1.Translated = cache.ControlParameter.Article.DescriptionInfo1.Description;
				}

				cache.ControlParameter.DispatchRecord.Description = cache.ControlParameter.Article.DescriptionInfo1.Translated;
				cache.ControlParameter.DispatchRecord.UoMFk = cache.ControlParameter.Article.UomFk;
				cache.ControlParameter.DispatchRecord.PrcStructureFk = cache.ControlParameter.MaterialGroup.PrcStructureFk;
			}
			else
			{
				cache.ControlParameter.DispatchRecord.Description = cache.ControlParameter.Requisition.Description;
				cache.ControlParameter.DispatchRecord.UoMFk = cache.ControlParameter.Requisition.UomFk;
			}
			if (cache.ControlParameter.Requisition.ReservationId.HasValue && cache.Transactions.Any())
			{
				var trans = cache.Transactions.FirstOrDefault(e => e.MdcMaterialFk == cache.ControlParameter.Requisition.MaterialFk.Value && e.ReservationId == cache.ControlParameter.Requisition.ReservationId.Value);
				if (trans != null)
				{
					cache.ControlParameter.DispatchRecord.PrjStockFk = trans.PrjStockFk;
				}
			}
			cache.ControlParameter.DispatchRecord.RecordTypeFk = recordTypeMat;
			cache.ControlParameter.DispatchRecord.RecordNo = cache.GetNextNo();
			cache.ControlParameter.DispatchRecord.Quantity = cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.DeliveredQuantity = cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.AcceptedQuantity = cache.ControlParameter.Requisition.Quantity;
			//dispRecordsToSave.Add(dispRecord);
			//if (cache.FirstFullyCoveredFk.HasValue)
			//{
			//	cache.ControlParameter.Requisition.RequisitionStatusFk = cache.FirstFullyCoveredFk.Value;
			//}
			//cache.Count += cache.Step;
		}
		private bool InitRequistion(CreateDispatchNotesCache cache)
		{
			if (cache.FirstFullyCoveredFk.HasValue && cache.ControlParameter.Requisition is RequisitionEntity)
			{
				(cache.ControlParameter.Requisition as RequisitionEntity).RequisitionStatusFk = cache.FirstFullyCoveredFk.Value;
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// CreateDispatchNotesFromMaterialReservation
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public string CreateDispatchingForMaterialReservation(CreateDispatchNoteFromMaterialReservationData data)
		{
			//var step = 10;


			var currentContext = BusinessApplication.BusinessEnvironment.CurrentContext;
			var dispHeaderLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IDispatchingHeaderLogic>();
			var dispRecordLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IDispatchingRecordLogic>();
			var stockTransactions = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IStockTransactionLogic>("Procurement.Stock.IStockTransactionLogic");

			var cache = new CreateDispatchNotesCache(data);
			cache.Step = 10;
			cache.Count = 10;

			if (cache.Requisitions.Any())
			{
				using (TransactionScope transaction = TransactionScopeFactory.Create())
				{
					cache.DispatchHeader = dispHeaderLogic.Create(new IdentificationData()) as IDispatchingHeaderEntity;
					InitializeDispatchHeader(cache);
					cache.AddToTransferCache(cache.DispatchHeader);
					foreach (var requisition in cache.Requisitions
						.Select(req => req as ICreateDispatchNotesRequisition)
						.Concat(cache.RequisitionItems
							.Select(item => item as ICreateDispatchNotesRequisition)))
					{
						cache.InitControlParameter(requisition);
						if (requisition.MaterialFk.HasValue)
						{
							// create material record
							cache.ControlParameter.DispatchRecord = dispRecordLogic.Create(new IdentificationData()
							{
								PKey1 = cache.DispatchHeader.Id,
								PKey2 = currentContext.ClientId
							}) as IDispatchingRecordEntity;
							InitializeDispatchRecordForMaterial(cache);
							cache.AddToTransferCache(cache.ControlParameter.DispatchRecord);
							if (InitRequistion(cache))
							{
								cache.AddToTransferCache(cache.ControlParameter.Requisition);
							}
						}
						else if (requisition is RequisitionEntity && cache.ControlParameter.ResourceParts.Any())
						{
							foreach (var resourceParts in cache.ControlParameter.ResourceParts)
							{
								if (cache.ControlParameter.ResourceType.IsSmallTools)
								{
									//if resource type is small tools generate for every resource part the coresp
									foreach (var index in Enumerable.Range(0, (int)Math.Ceiling(cache.ControlParameter.Requisition.Quantity)))
									{
										cache.ControlParameter.InitPartsControlParameter(resourceParts, cache);
										cache.ControlParameter.DispatchRecord = dispRecordLogic.Create(new IdentificationData()
										{
											PKey1 = cache.DispatchHeader.Id,
											PKey2 = currentContext.ClientId
										}) as IDispatchingRecordEntity;
										InitializeDispatchRecordForResource(cache);
										cache.AddToTransferCache(cache.ControlParameter.DispatchRecord);
									}
								}
								else
								{
									cache.ControlParameter.InitPartsControlParameter(resourceParts, cache);
									cache.ControlParameter.DispatchRecord = dispRecordLogic.Create(new IdentificationData()
									{
										PKey1 = cache.DispatchHeader.Id,
										PKey2 = currentContext.ClientId
									}) as IDispatchingRecordEntity;
									InitializeDispatchRecordForResource(cache);
									cache.AddToTransferCache(cache.ControlParameter.DispatchRecord);
								}
							}
							if (InitRequistion(cache))
							{
								cache.AddToTransferCache(cache.ControlParameter.Requisition);
							}
						}
						else if (data.PlantId.HasValue)
						{
							cache.ControlParameter.InitPlantControlParameter(cache);
							cache.ControlParameter.DispatchRecord = dispRecordLogic.Create(new IdentificationData()
							{
								PKey1 = cache.DispatchHeader.Id,
								PKey2 = currentContext.ClientId
							}) as IDispatchingRecordEntity;
							InitializeDispatchRecordForPlant(cache);
							cache.AddToTransferCache(cache.ControlParameter.DispatchRecord);
							if (InitRequistion(cache))
							{
								cache.AddToTransferCache(cache.ControlParameter.Requisition);
							}
						}
					}
					cache.SynchronizeDispatchingHeader();
					//the persited header is needed for price calculation method for the dispatch records
					if (cache.DispatchRecordsToSave.Any())
					{
						cache.DispatchRecordsToSave = dispRecordLogic.GetPrices(cache.DispatchRecordsToSave).ToList();
					}
					cache.SynchronizeDispatchingRecord();
					stockTransactions.CancelReservation(cache.Requisitions.Where(e => e.ReservationId.HasValue)
						.Select(e => e.ReservationId.Value).Distinct().ToArray());
					transaction.Complete();
				}
				cache.SynchronizeRequistions();
			}
			return cache.DispatchHeader.Code;
		}

		private void InitializeDispatchRecordForPlant(CreateDispatchNotesCache cache)
		{
			const int recordTypePlant = 2;
			cache.ControlParameter.DispatchRecord.DispatchHeaderFk = cache.DispatchHeader.Id;
			cache.ControlParameter.DispatchRecord.RecordNo = cache.GetNextNo();
			cache.ControlParameter.DispatchRecord.RecordTypeFk = recordTypePlant;
			cache.ControlParameter.DispatchRecord.PlantFk = cache.ControlParameter.PartsControlParameter.Plant.Id;
			cache.ControlParameter.DispatchRecord.WorkOperationTypeFk = cache.ControlParameter.PartsControlParameter.WorkOperationTypeId;
			cache.ControlParameter.DispatchRecord.DateEffective = cache.DispatchHeader.EffectiveDate;
			cache.ControlParameter.DispatchRecord.Quantity = cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.DeliveredQuantity = cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.AcceptedQuantity = cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.UoMFk = cache.ControlParameter.PartsControlParameter.Plant.UoMFk;
			cache.ControlParameter.DispatchRecord.ArticleCode = cache.ControlParameter.PartsControlParameter.Plant.Code;
			cache.ControlParameter.DispatchRecord.Description = cache.ControlParameter.PartsControlParameter.Plant.Description;
			cache.ControlParameter.DispatchRecord.PrcStructureFk = cache.ControlParameter.PartsControlParameter.Plant.ProcurementStructureFk;
		}

		private void InitializeDispatchRecordForResource(CreateDispatchNotesCache cache)
		{
			cache.ControlParameter.DispatchRecord.DispatchHeaderFk = cache.DispatchHeader.Id;
			cache.ControlParameter.DispatchRecord.RecordNo = cache.GetNextNo();
			cache.ControlParameter.DispatchRecord.RecordTypeFk = 2;
			cache.ControlParameter.DispatchRecord.PlantFk = cache.ControlParameter.PartsControlParameter.ResourcePart.PlantFk;
			cache.ControlParameter.DispatchRecord.WorkOperationTypeFk = cache.ControlParameter.PartsControlParameter.WorkOperationTypeId;
			cache.ControlParameter.DispatchRecord.DateEffective = cache.DispatchHeader.EffectiveDate;
			cache.ControlParameter.DispatchRecord.Quantity = cache.ControlParameter.ResourceType.IsSmallTools ? 1 : cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.DeliveredQuantity = cache.ControlParameter.ResourceType.IsSmallTools ? 1 : cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.AcceptedQuantity = cache.ControlParameter.ResourceType.IsSmallTools ? 1 : cache.ControlParameter.Requisition.Quantity;
			cache.ControlParameter.DispatchRecord.UoMFk = cache.ControlParameter.PartsControlParameter.Plant.UoMFk;
			cache.ControlParameter.DispatchRecord.ArticleCode = cache.ControlParameter.PartsControlParameter.Plant.Code;
			cache.ControlParameter.DispatchRecord.Description = cache.ControlParameter.PartsControlParameter.Plant.Description;
			cache.ControlParameter.DispatchRecord.PrcStructureFk = cache.ControlParameter.PartsControlParameter.Plant.ProcurementStructureFk;
		}

		/// <summary>
		///
		/// </summary>
		public IEnumerable<Newtonsoft.Json.Linq.JObject> ContainerDefinition
		{
			get { throw new NotImplementedException(); }
		}

		/// <summary>
		/// Is Prefered Resource Mandatory?
		/// </summary>
		/// <param name="resourceTypeFk"></param>
		/// <returns></returns>
		public bool IsPreferedResourceMandatory(int resourceTypeFk)
		{
			var resourceType = new ResourceTypeMainLogic().GetByFilter(type => type.Id == resourceTypeFk);
			return resourceType.Any() ? resourceType.First().IsSmallTools : false;

		}
		/// <summary>
		/// Reserve nCount of Ids for creation of nCount Requisitions 
		/// </summary>
		/// <param name="nCount"></param>
		/// <returns></returns>
		public IEnumerable<int> GetSequenceIds(int nCount)
		{
			return nCount == 0 ? Enumerable.Empty<int>() : this.SequenceManager.GetNextList(GetEntityTableName(), nCount);
		}
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.CreateRequisitions(Int32 count)
		{
			Permission.Ensure(PermissionGUID, Permissions.Create);
			var newIds = GetSequenceIds(count);
			var def = new BasicsCustomizeResRequisitionStatusLogic().GetDefault();
			var result = new List<RequisitionEntity>();
			var currentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			foreach (var Id in newIds)
			{
				var entity = new RequisitionEntity();
				if (def != null)
				{
					entity.RequisitionStatusFk = def.Id;
				}
				entity.Id = Id;
				entity.ResourceContextFk = CompanyContextAssert.GetResourceContextOrThrowException();
				entity.RequestedFrom = DateTime.UtcNow;
				entity.RequestedTo = DateTime.UtcNow;

				entity.CompanyFk = currentContext.ClientId;
				result.Add(entity);
			}
			return result;
		}

		/// <summary>
		/// Get all Resource requisitions to a list of jobs provided
		/// </summary>
		/// <param name="jobFks"></param>
		/// <returns></returns>
		IEnumerable<IResRequisitionEntity> IResRequisitionLogic.GetRequisitionsByJobsFks(IEnumerable<int> jobFks)
		{
			return GetByFilter(e => jobFks.Contains(e.JobFk));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="resCode"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<IResRequisitionEntity> GetResCode(string resCode)
		{
			return GetListByFilter(e => e.Code == resCode).ToList();
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="nCount"></param>
		/// <returns></returns>
		public IEnumerable<int> GetNewIds(int nCount)
		{
			return nCount == 0 ? Enumerable.Empty<int>() : this.SequenceManager.GetNextList(GetEntityTableName(), nCount);
		}
		private class RequisitionChangeInfo
		{
			public RequisitionEntity Requisition { get; set; }
			public RequisitionChangeReqdateInfoVEntity Info { get; set; }
			public RequisitionChangeInfo(RequisitionEntity requisition, RequisitionChangeReqdateInfoVEntity info)
			{
				this.Requisition = requisition;
				this.Info = info;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> ChangeRequestedDate(RequisitionChangeRequestedDateReq request)
		{
			var packInfos = new List<RequisitionChangeInfo>();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var requisitionIds = new int[] { request.RequisitionFk };
				var changes = GetPrimaryRequisitionsFor(dbcontext, requisitionIds);

				if (changes.Any(ch => ch.ParentResRequisitionFk != null))
				{
					throw new BusinessLayerException("You have to select the root requitition in order to change");
				}

				DoChangeRequestedDate(requisitionIds, request.RequestedFrom, changes, dbcontext, ref packInfos);
			}

			return packInfos != null ? packInfos.Select(pi => pi.Requisition) : Enumerable.Empty<RequisitionEntity>();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="requisitions"></param>
		/// <param name="requestedFrom"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> ChangeRequestedDate(IEnumerable<RequisitionEntity> requisitions, DateTime requestedFrom)
		{
			var changedRequisitions = new List<RequisitionEntity>();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var requisitionIds = requisitions.CollectIds(r => r.Id).ToArray();
				var changes = GetPrimaryRequisitionsFor(dbcontext, requisitionIds);

				//Use case is triggered differently, so we filter out
				var rootChanges = changes.Where(ch => !ch.ParentResRequisitionFk.HasValue).ToArray();
				if (rootChanges.Any())
				{
					var packInfos = new List<RequisitionChangeInfo>();
					requisitionIds = rootChanges.CollectIds(rc => rc.PrimaryResRequisitionFk).ToArray();
					DoChangeRequestedDate(requisitionIds, requestedFrom, rootChanges, dbcontext, ref packInfos);

					if (packInfos != null && packInfos.Any(pi => pi.Requisition != null))
					{
						changedRequisitions.AddRange(packInfos.Where(p => p.Requisition != null).Select(pi => pi.Requisition).ToArray());
					}
				}

				var unhandled = requisitions.Where(uc => changedRequisitions.All(rr => rr.Id != uc.Id)).ToArray();
				if (unhandled.Any())
				{
					var handled = DoChangeRequestedDate(unhandled, requestedFrom);
					changedRequisitions.AddRange(handled);
				}

				return changedRequisitions;
			}
		}

		private void DoChangeRequestedDate(int[] requisitionIds, DateTime requestedFrom, Requisition2RequisitionInfoVEntity[] changes, RVPBizComp.DbContext dbcontext, ref List<RequisitionChangeInfo> packInfos)
		{
			var requisitionLogic = new ResourceRequisitionMainLogic();
			var secondaryChangeIds = changes.
				Where(c => c.ChildResRequisitionFk.HasValue).
				Select(c => c.ChildResRequisitionFk.Value).
				ToArray();
			var secondaryChanges = dbcontext.
				Entities<Requisition2RequisitionInfoVEntity>().
				Where(e => secondaryChangeIds.Contains(e.PrimaryResRequisitionFk)).
				ToArray();
			var tertiaryChangeIds = secondaryChanges.Where(c => c.ChildResRequisitionFk.HasValue).Select(c => c.ChildResRequisitionFk.Value).ToArray();
			var requisitionIdsToChange = secondaryChangeIds.Concat(tertiaryChangeIds).Concat(requisitionIds).Distinct().ToArray();
			var requisitionsToChange = requisitionLogic.GetByFilter(r => requisitionIdsToChange.Contains(r.Id)).ToArray();
			var changeInfos = dbcontext.Entities<RequisitionChangeReqdateInfoVEntity>().Where(e => requisitionIdsToChange.Contains(e.PrimaryResRequisitionFk)).ToArray();
			packInfos.AddRange(requisitionsToChange.Select(req => new RequisitionChangeInfo(req, changeInfos.First(info => info.PrimaryResRequisitionFk == req.Id))).ToArray());
			var nonWorkingDayChecks = GetCalendarWorkingDayCheck(packInfos);

			foreach (int requisitionId in requisitionIds)
			{
				var rootEndDate = ChangeRootRequisition(packInfos.First(r => r.Requisition.Id == requisitionId), requestedFrom, nonWorkingDayChecks);

				foreach (var packInfo in packInfos.Where(r => secondaryChangeIds.Contains(r.Requisition.Id)))
				{
					ChangeSecondaryRequisition(packInfo, requestedFrom, packInfos.First(i => i.Requisition.Id == packInfo.Info.ParentResRequisitionFk), nonWorkingDayChecks);
				}
				foreach (var packInfo in packInfos.Where(r => tertiaryChangeIds.Contains(r.Requisition.Id)))
				{
					ChangeTertiaryRequisition(packInfo, requestedFrom, packInfos.First(i => i.Requisition.Id == packInfo.Info.ParentResRequisitionFk), nonWorkingDayChecks);
				}
			}
		}

		private RequisitionEntity[] DoChangeRequestedDate(RequisitionEntity[] requisitions, DateTime requestedFrom)
		{
			var nwds = GetJobIdToCalendarWorkingDayCheck(requisitions);

			foreach (var requisition in requisitions)
			{
				DoChangeRequisitionPeriodUsingCalendar(requisition, requestedFrom, nwds);
			}

			return requisitions;
		}

		private Tuple<int, INonWorkingDayCheck>[] GetJobIdToCalendarWorkingDayCheck(RequisitionEntity[] requisitions)
		{
			var workingDayChecks = new List<Tuple<int, INonWorkingDayCheck>>();
			var calendarLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ICalendarUtilitiesLogic>();
			var jobLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();

			var jobIds = requisitions.Select(r => r.JobFk).ToArray();
			var jobs = jobLogic.GetJobByIds(jobIds);
			var calendarIds = jobs.Select(r => r.CalendarFk).ToArray();

			var filter = new UtilitiesDataFilter();
			filter.Start = requisitions.Min(r => r.RequestedFrom).AddDays(-365);
			filter.End = requisitions.Min(r => r.RequestedTo).AddDays(+365);

			foreach (var calendarId in calendarIds)
			{
				if (calendarId >= 0)
				{
					filter.Calendar = calendarId;
					var nwd = calendarLogic.GetNonWorkingDayChecker(filter);
					foreach (var job in jobs)
					{
						if (job.CalendarFk == calendarId)
						{
							workingDayChecks.Add(new Tuple<int, INonWorkingDayCheck>(job.Id, nwd));
						}
					}
				}
			}

			return workingDayChecks.ToArray();
		}

		private DateTime DoChangeRequisitionPeriodUsingCalendar(RequisitionEntity requisition, DateTime requestedFrom, Tuple<int, INonWorkingDayCheck>[] nonWorkingDayChecks)
		{
			var nwd = nonWorkingDayChecks.First(n => n.Item1 == requisition.JobFk).Item2;
			var endDate = nwd.GetWorkingDayNDayAfter(requestedFrom, (int)(requisition.RequestedTo - requisition.RequestedFrom).TotalDays - nwd.CountNonWorkingDayInPeriod(requisition.RequestedFrom, requisition.RequestedTo));
			requisition.RequestedFrom = requestedFrom;
			requisition.RequestedTo = endDate;
			return endDate;
		}

		private Tuple<int, INonWorkingDayCheck>[] GetCalendarWorkingDayCheck(List<RequisitionChangeInfo> packInfos)
		{
			var workingDayChecks = new List<Tuple<int, INonWorkingDayCheck>>();
			var calendarLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ICalendarUtilitiesLogic>();

			var calendarIds = packInfos.Select(c => c.Info.CalCalendarFk ?? -1).ToArray();
			var filter = new UtilitiesDataFilter();
			filter.Start = packInfos.Min(c => c.Requisition.RequestedFrom).AddDays(-365);
			filter.End = packInfos.Min(c => c.Requisition.RequestedTo).AddDays(+365);

			foreach (var calendarId in calendarIds)
			{
				if (calendarId >= 0)
				{
					filter.Calendar = calendarId;

					workingDayChecks.Add(new Tuple<int, INonWorkingDayCheck>(calendarId, calendarLogic.GetNonWorkingDayChecker(filter)));
				}
			}

			return workingDayChecks.ToArray();
		}

		private Requisition2RequisitionInfoVEntity[] GetPrimaryRequisitionsFor(RVPBizComp.DbContext dbcontext, int[] requisitionIds)
		{
			return dbcontext.Entities<Requisition2RequisitionInfoVEntity>().
				Where(e => requisitionIds.Contains(e.PrimaryResRequisitionFk)).
				ToArray();
		}

		private DateTime ChangeRootRequisition(RequisitionChangeInfo rootReqInfo, DateTime requestedFrom, Tuple<int, INonWorkingDayCheck>[] nonWorkingDayChecks)
		{
			var calendar = rootReqInfo.Info.CalCalendarFk.Value;
			var nwd = nonWorkingDayChecks.First(n => n.Item1 == calendar).Item2;
			var endDate = nwd.GetWorkingDayNDayAfter(requestedFrom, (int)GetDurationInDays(rootReqInfo.Info) - 1);
			rootReqInfo.Requisition.RequestedFrom = requestedFrom;
			rootReqInfo.Requisition.RequestedTo = endDate;
			return endDate;
		}

		private void ChangeSecondaryRequisition(RequisitionChangeInfo secReqInfo, DateTime requestedFrom, RequisitionChangeInfo parentInfo, Tuple<int, INonWorkingDayCheck>[] nonWorkingDayChecks)
		{
			var calendar = secReqInfo.Info.CalCalendarFk.Value;
			var nwd = nonWorkingDayChecks.First(n => n.Item1 == calendar).Item2;
			secReqInfo.Requisition.RequestedFrom = requestedFrom;
			if (secReqInfo.Info.IsSkillDemand.Value)
			{
				secReqInfo.Requisition.RequestedTo = nwd.GetWorkingDayNDayAfter(requestedFrom, (int)GetDurationInDays(secReqInfo.Info) - 1);
			}
			else
			{
				secReqInfo.Requisition.RequestedTo = secReqInfo.Info.Isrequestedentireperiod ?
					parentInfo.Requisition.RequestedTo :
					nwd.GetWorkingDayNDayAfter(requestedFrom, (int)GetDurationInDays(secReqInfo.Info) - 1);
			}
		}

		private void ChangeTertiaryRequisition(RequisitionChangeInfo tertiaryReqInfo, DateTime requestedFrom, RequisitionChangeInfo parentInfo, Tuple<int, INonWorkingDayCheck>[] nonWorkingDayChecks)
		{
			var calendar = tertiaryReqInfo.Info.CalCalendarFk.Value;
			var nwd = nonWorkingDayChecks.First(n => n.Item1 == calendar).Item2;
			tertiaryReqInfo.Requisition.RequestedFrom = requestedFrom;
			if (tertiaryReqInfo.Info.IsSkillDemand.Value)
			{
				tertiaryReqInfo.Requisition.RequestedTo = nwd.GetWorkingDayNDayAfter(requestedFrom, (int)GetDurationInDays(tertiaryReqInfo.Info) - 1);
			}
			else
			{
				throw new BusinessLayerException(String.Format("Something went wrong: Requisition \"{0}\" is tertiary demand can only be a skill demand!", tertiaryReqInfo.Requisition.Code));
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="generate"></param>
		/// <returns></returns>
		private decimal GetDurationInDays(RequisitionChangeReqdateInfoVEntity generate)
		{
			switch (generate.ResWotBasUomFk)
			{
				case var value when value == generate.BasUomhourFk:
					return generate.ResQuantity.Value / generate.Workhoursperday.Value;
				case var value when value == generate.BasUomdayFk:
					return generate.ResQuantity.Value;
				case var value when value == generate.BasUommonthFk:
					return generate.ResQuantity.Value / generate.Workhourspermonth.Value * generate.Workhoursperday.Value;
				default:
					return generate.ResQuantity.Value;
			}
		}

		
		/// <summary>
		/// Creates an Requisition entity, provides a unique db ID...
		/// </summary>
		/// <returns>The new created Requisition from initial data</returns>
		public List<RequisitionEntity> CreateRequisitionData(RequisitionEntity data)
		{
			Permission.Ensure("291a21ca7ab94d549d2d0c541ec09f5d", Permissions.Create);

			var requisitions = new List<RequisitionEntity>();

			var resourceTypeMainLogic = new ResourceTypeMainLogic();
			var resType = resourceTypeMainLogic.GetByFilter(r => r.Id == data.TypeFk).FirstOrDefault();

			bool isSmallTool = resType?.IsSmallTools == true;
			bool isOperator = data.RequisitionTypeFk == 4;

			// Duplication logic
			decimal quantity = data.Quantity;

			if ((isSmallTool || isOperator) && quantity > 1)
			{
				for (int i = 0; i < quantity; i++)
				{
					requisitions.Add(CreateSingleRequisition(data, 1)); // Create with Quantity = 1
				}
			}
			else
			{
				requisitions.Add(CreateSingleRequisition(data, (Int32)data.Quantity)); // Regular single requisition
			}

			return requisitions;

		}

		// Helper method to build a single requisition
		private RequisitionEntity CreateSingleRequisition(RequisitionEntity data, int quantity)
		{
			var id = this.SequenceManager.GetNext("RES_REQUISITION");

			var entity = new RequisitionEntity { Id = id };

			var currentContext = BusinessApplication.BusinessEnvironment.CurrentContext;
			var companyInfoProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<ICompanyInfoProvider>();
			var companyInfo = companyInfoProvider.GetInstanceInfo(currentContext.ClientId);

			entity.CompanyFk = companyInfo.Id;
			entity.ResourceContextFk = CompanyContextAssert.GetResourceContextOrThrowException(companyInfo);

			entity.ProjectFk = data.ProjectFk;
			entity.RequisitionTypeFk = data.RequisitionTypeFk;
			entity.MdcControllingUnitFk = data.MdcControllingUnitFk;
			entity.JobFk = data.JobFk;
			entity.TypeFk = data.TypeFk;
			entity.WorkOperationTypeFk = data.WorkOperationTypeFk;
			entity.Description = data.Description;
			entity.RequestedFrom = data.RequestedFrom;
			entity.RequestedTo = data.RequestedTo;
			entity.DropPointFk = data.DropPointFk;
			entity.CommentText = data.CommentText;
			entity.Quantity = quantity;
			entity.SkillFk = data.SkillFk;

			RelationDefaultValueSetter.Handle(entity, new List<Tuple<string, Action<RequisitionEntity, int>, Func<RequisitionEntity, int>>>() {
			new Tuple<string, Action<RequisitionEntity, int>, Func<RequisitionEntity, int>>("basics.customize.rubriccategory", (e, i) => e.RubricCategoryFk = i, e => RubricConstant.ResourceRequisition),
			new Tuple<string, Action<RequisitionEntity, int>, Func<RequisitionEntity, int>>("basics.customize.resrequisitionstatus", (e, i) => e.RequisitionStatusFk = i,null),
			new Tuple<string, Action<RequisitionEntity, int>, Func<RequisitionEntity, int>>("basics.customize.resourcerequisitiongroup", (e, i) => e.RequisitionGroupFk = i,null),
			new Tuple<string, Action<RequisitionEntity, int>, Func<RequisitionEntity, int>>("basics.customize.resourcerequisitionpriority", (e, i) => e.RequisitionPriorityFk = i,null)
		});

			entity.HasToGenerateCode = true;

			// Save and return the entity
			return Save(entity);
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectfk"></param>
		/// <returns></returns>
		public int? DefaultLogisticJobIsLive(int projectfk)
		{
			var jobLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
         var job = jobLogic.GetLogisticJobsByProject(projectfk).FirstOrDefault(j => j.IsLive);
			if (job != null)
			{
				return job.Id;
			}
			else
			{
				return null;
				//throw new BusinessLayerException("No default Logistic Job found!");
			}
		}

		/// <summary>
		/// Create an entity via creation data.
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> CreateAlternativeFor(IEnumerable<RequisitionEntity> entities)
		{
			var entity = entities.First();
			List<RequisitionEntity> newlyCreated = new List<RequisitionEntity>();
			int? requisitionStatus = null;
			var resRequisitionStatusLogic = new BasicsCustomizeResRequisitionStatusLogic();
			var requisition2RequisitionLogic = new Requisition2RequisitionLogic();

			for (int i = 0; i < (int)entity.Quantity; i++)
			{
				var requisition = (RequisitionEntity)entity.Clone();
				var id = this.SequenceManager.GetNext("RES_REQUISITION");
				requisition.Id = id;
				requisition.Quantity = 1;
				requisition.ResourceFk = null;
				requisition.TypeFk = entity.TypeFk;
				requisition.Version = 0;
				requisition.IsLinkedFixToReservation = false;

				newlyCreated.Add(requisition);
			}

			var reqsToSave = new List<RequisitionEntity>();
			reqsToSave.AddRange(newlyCreated);
			
			
			var isCanceledStatus = resRequisitionStatusLogic.GetByFilter(c => c.IsCanceled == true).FirstOrDefault();
			if (isCanceledStatus != null)
			{
				requisitionStatus = isCanceledStatus.Id;
				entity.RequisitionStatusFk = requisitionStatus.Value;
			}

			((IChangeStatus)this).ChangeStatus(new StatusIdentifyable
			{
				Id = entity.Id,
				PKey1 = 0,
				PKey2 = 0,
				OldStatusId = entity.RequisitionStatusFk,
				CurrentStatusId = requisitionStatus.Value,
				StatusField = "RequisitionStatusFk"
			}, requisitionStatus.Value);			

			Save(reqsToSave);

			var links = newlyCreated
				 .Select((newReq, index) => new Tuple<RequisitionEntity, RequisitionEntity>(entity, newReq))
				 .ToArray();

			var req2ReqEntities = requisition2RequisitionLogic.MultiCreate(links.Length);
			var req2ReqsFilled = links.EqualSizeJoinByIndex(
			req2ReqEntities,
			(pair, linkEntity) => {
				linkEntity.RequisitionFk = pair.Item1.Id;
				linkEntity.RequisitionLinkedFk = pair.Item2.Id;
				linkEntity.IsReference = false;
				linkEntity.IsTimeEnhancement = false;
				linkEntity.InsertedAt = DateTime.Now;
				linkEntity.InsertedBy = entity.InsertedBy;
				linkEntity.Version = 0;
				return linkEntity;
			}).ToList();
		
			requisition2RequisitionLogic.Save(req2ReqsFilled);


			var items = new List<RequisitionEntity>();
			items.Add(entity);
			return items;

		}
	}
}
