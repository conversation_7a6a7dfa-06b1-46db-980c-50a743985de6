/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { IReadonlyParentService, ProcurementBaseValidationService } from '@libs/procurement/shared';
import { CompleteIdentification, IEntityIdentification } from '@libs/platform/common';
import { ProcurementCommonCertificateDataService } from './procurement-common-certificate-data.service';
import { IPrcCertificateEntity } from '@libs/procurement/interfaces';
import { inject } from '@angular/core';
import { ProcurementCommonCertificateLayoutService } from './procurement-common-certificate-layout.service';
import { BasicsSharedCertificateTypeLookupService } from '@libs/basics/shared';

/**
 * Procurement common Certificate validation service
 */
export abstract class ProcurementCommonCertificateValidationService<T extends IPrcCertificateEntity, PT extends IEntityIdentification, PU extends CompleteIdentification<PT>> extends ProcurementBaseValidationService<T> {
	private readonly certificateTypeLookupService = inject(BasicsSharedCertificateTypeLookupService);
	private readonly layoutService = inject(ProcurementCommonCertificateLayoutService);

	/**
	 *
	 * @param dataService
	 * @param parentDataService
	 */
	protected constructor(
		protected dataService: ProcurementCommonCertificateDataService<T, PT, PU>,
		protected parentDataService: IReadonlyParentService<PT, PU>,
	) {
		super();
	}

	protected generateValidationFunctions(): IValidationFunctions<T> {
		return {
			BpdCertificateTypeFk: this.validateBpdCertificateTypeFk,
			GuaranteeCostPercent: this.validateGuaranteeCostPercent,
			ValidFrom: this.validateIsValidTimeSpanFrom,
			ValidTo: this.validateIsValidTimeSpanTo,
			//todo -- validateEntity
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<T> {
		return this.dataService;
	}

	private validateBpdCertificateTypeFk(info: ValidationInfo<T>) {
		const validateResult = this.validateIsLocalUnique(info);
		if (validateResult.valid) {
			const certificateType = this.certificateTypeLookupService.cache.getItem(info.value as number);
			if (certificateType) {
				info.entity.IsValued = certificateType.IsValued;
				this.dataService.readonlyProcessor.process(info.entity);
			}
		}
		return validateResult;
	}

	private validateGuaranteeCostPercent(info: ValidationInfo<T>) {
		this.dataService.recalculateAmountExp(info.entity, info.value as number);
		return new ValidationResult();
	}

	protected override getEntityService = () => this.dataService;

	protected override getTranslationResource = () => this.layoutService.fieldLabels;

	protected override getMissingTimeSpanInfo = (info: ValidationInfo<T>): ValidationInfo<T> | undefined => {
		switch (info.field) {
			case 'ValidFrom':
				return new ValidationInfo(info.entity, info.entity.ValidTo ?? undefined, 'ValidTo');
			case 'ValidTo':
				return new ValidationInfo(info.entity, info.entity.ValidFrom ?? undefined, 'ValidFrom');
			default:
				return new ValidationInfo(info.entity, info.value, info.field);
		}
	};
}
