using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Reflection.Metadata;
using System.Text.RegularExpressions;
using System.Transactions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Boq.Main.Common;
using RIB.Visual.Boq.Main.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Procurement.Package.Localization.Properties;
using BOQMAIN = RIB.Visual.Boq.Main.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPC = RIB.Visual.Platform.Core;

namespace RIB.Visual.Procurement.Package.BusinessComponents
{
	/// <summary>
	/// Prc Package Generate BoQ Wizard Logic
	/// </summary>
	[Export(typeof(IPackageGenerateBoQWizardLogic))]
	[PartCreationPolicy(CreationPolicy.NonShared)]
	public partial class PrcPackageGenerateBoQWizardLogic : LogicBase, IPackageGenerateBoQWizardLogic
	{
		#region for create boq from estimate line items
		private List<IControllingUnitEntity> controllingUnits = new List<IControllingUnitEntity>();
		private int? _packageTaxCodeFk = null;
		#endregion for create boq from estimate line items

		private string moduleName = "procurement.package";

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public int UpdatePackaegBoq(CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);
			switch (context.Source)
			{
				case UpdateOptionForBoq.Criteria.ProjectBoq:
					return GeneratePackageBoQFromProjectBoQ(context);
				case UpdateOptionForBoq.Criteria.WicBoq:
					return GeneratePackageBoQFromWicBoQ(context);
				case UpdateOptionForBoq.Criteria.LineItem:
					CreatePackageBoqFromLineItem(context);
					return 1;
				case UpdateOptionForBoq.Criteria.Resource:
					UpdateToPackageBoqFromResource(context);
					return 1;
				default:
					return 1;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		private int GeneratePackageBoQFromProjectBoQ(CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);
			var lineItemIdData = Sql4GetLineItemBySpecificPackage(context);

				if (!lineItemIdData.Any())
				{
					return -1; // there is no line item linking to a package or indicated a project boq item
				}

				var prcItemAssignmentLogic = new PrcItemAssignmentLogic();
				var lineItemLogic = Injector.Get<IEstimateMainLineItemLogic>();

			#region Get line items for recalculate the cost total
			var estHeaderId2LineItemIdsMap = new Dictionary<int, List<int>>();
			foreach(var idData in lineItemIdData)
			{
				if (!idData.PKey1.HasValue)
				{
					continue;
				}
				var lineItemId = idData.Id;
				var estHeaderId = idData.PKey1.Value;
				if (!estHeaderId2LineItemIdsMap.ContainsKey(estHeaderId))
				{
					estHeaderId2LineItemIdsMap.Add(estHeaderId, new List<int>() { lineItemId });
				}
				else
				{
					estHeaderId2LineItemIdsMap[estHeaderId].Add(lineItemId);
				}
			}

			var lineItems = GetLineItemsByEstHeader2LineItemIdsMap(estHeaderId2LineItemIdsMap);

			#endregion Get line items for recalculate the cost total

				var boqItemsLinkedEstimateLine = lineItems.ToEntities(e => new BoQItemLinkedEstimateLineItem(e, BoqSource.Project));
				IEnumerable<BoQItemLinkedEstimateLineItem> linkedEstimateLines = boqItemsLinkedEstimateLine.ToList();
				IEnumerable<PrcItemAssignmentEntity> validAssignments = new List<PrcItemAssignmentEntity>();
				IEnumerable<IScriptEstResource> validResources4Cost = new List<IScriptEstResource>();
				IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLines = new List<IEstLineItemEnhanceEntity>();
				IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLinesWithoutResources = new List<IEstLineItemEnhanceEntity>();
				var allItemAssignments = prcItemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
				var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();
				FetchValidData(linkedEstimateLines, ref validAssignments, ref validResources4Cost, ref validLinkedEstimateLines, ref validLinkedEstimateLinesWithoutResources, context.DoesUpdateBudgetOnly4AssignmentExist, allItemAssignments);

				if ((validResources4Cost == null || !validResources4Cost.Any()) && (validLinkedEstimateLines == null || !validLinkedEstimateLines.Any()))
				{
					return 1; // TODO chi: right?
				}

			#region recalculate line items and resources when line item is option is true and is option IT is false
			var validLinkedEstimateLinesIsOptional = validLinkedEstimateLines.Where(e => e.IsOptional && !e.IsOptionalIT).ToList();
			var validLinkedEstimateLinesIsOptionalIdData = validLinkedEstimateLinesIsOptional.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.EstHeaderFk }, e => true);
			var validResourcesIsOptional = new List<IScriptEstResource>();
			if (validResources4Cost != null && validResources4Cost.Any())
			{
				foreach (var res in validResources4Cost)
				{
					var temp = new RVPC.IdentificationData()
					{
						Id = res.EstLineItemFk,
						PKey1 = res.EstHeaderFk
					};
					if (!validLinkedEstimateLinesIsOptionalIdData.TryGetValue(temp, out var hasValue))
					{
						continue;
					}
					validResourcesIsOptional.Add(res);
				}
			}
			RecalculateCostTotalIfLineItemIsOptional(validLinkedEstimateLinesWithoutResources, validResourcesIsOptional);
			#endregion recalculate line items and resources when line item is optional is true and is optional IT is false

			#region update QuantityTarget according to Quantity Source
			foreach (var lineItem in validLinkedEstimateLines)
			{
				if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
				{
					lineItem.QuantityTarget = lineItem.WqQuantityTarget;
				}
				else if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemQuantityTotal)
				{
					lineItem.QuantityTarget = lineItem.QuantityTotal;
				}
			}
			#endregion update QuantityTarget according to Quantity Source

				Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap = null;
				Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap = null;
				BuildLineItemMaps(validLinkedEstimateLinesWithoutResources, validResources4Cost, out lineItemId2ResourcesMap, false, out lineItemId2MergedPropMap);

				IEnumerable<IBoqItemEntity> boqItems = GetBoqItemsFromLineItems(validLinkedEstimateLines, (lineItem, boqItem) =>
				{
					lineItem.BoqItemUomId = boqItem.BasUomFk;
					lineItem.BoqItemFactor = boqItem.Factor;
				});

				var mergedLineItems = MergeLineItem4ProjectBoq(boqItemsLinkedEstimateLine, boqItems, context, lineItemId2MergedPropMap);
				return GeneratePackageBoQ(mergedLineItems, validLinkedEstimateLines, lineItemId2ResourcesMap, validAssignments, context, allValidItemAssignments);
		}


		private IList<IEstLineItemEnhanceEntity> MergeLineItem4ProjectBoq(IEnumerable<BoQItemLinkedEstimateLineItem> lineItems, IEnumerable<IBoqItemEntity> boqItems, CreateOrUpdatePackageBoqContext context, IDictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap)
		{
			CheckContext(context);

			IDictionary<object, IEstLineItemEnhanceEntity> boqItemId2LineItem = new Dictionary<object, IEstLineItemEnhanceEntity>();
			var updateOption = context.UpdateOption;
			var isConsideredQtyRel = GetBooleanValue(updateOption.IsConsideredQtyRel);

			foreach (var lineitem in lineItems)
			{
				// cost total from resource
				if (lineItemId2MergedPropMap != null && lineItemId2MergedPropMap.ContainsKey(new { EstHeaderFk = lineitem.EstHeaderFk, EstLineItemFk = lineitem.Id }))
				{
					lineitem.CostTotal = lineItemId2MergedPropMap[new { EstHeaderFk = lineitem.EstHeaderFk, EstLineItemFk = lineitem.Id }].CostTotal;
					lineitem.Budget = lineItemId2MergedPropMap[new { EstHeaderFk = lineitem.EstHeaderFk, EstLineItemFk = lineitem.Id }].BudgetTotal;
				}
				else
				{
					continue;
				}

				if (boqItemId2LineItem.ContainsKey(new { BoqHeaderFk = lineitem.BoqHeaderFk.Value, BoqItemFk = lineitem.BoqItemFk.Value }))
				{
					var temp = boqItemId2LineItem[new { BoqHeaderFk = lineitem.BoqHeaderFk.Value, BoqItemFk = lineitem.BoqItemFk.Value }];

					// if the quantity from line item, merge quantity
					if (updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemAQ ||
						updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
					{
						if (lineitem.BasUomTargetFk == lineitem.BoqItemUomId && !IsIgnoreLineItemQuantity(isConsideredQtyRel, lineitem))
						{
							temp.QuantityTarget += lineitem.QuantityTarget;
						}
					}
					else if (updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemQuantityTotal && lineitem.BasUomFk == lineitem.BoqItemUomId && !IsIgnoreLineItemQuantity(isConsideredQtyRel, lineitem))
					{
						temp.QuantityTarget += lineitem.QuantityTarget;
					}
					// merge cost total
					temp.CostTotal += lineitem.CostTotal;
					temp.Budget += lineitem.Budget;
				}
				else
				{
					// quantity from line item
					if (updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemAQ ||
						updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
					{
						if (lineitem.BasUomTargetFk != lineitem.BoqItemUomId || IsIgnoreLineItemQuantity(isConsideredQtyRel, lineitem))
						{
							lineitem.QuantityTarget = 0;
						}
					}
					// quantity from boq
					else if (updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.BoqAQWQ) // quantity from boq
					{
						lineitem.QuantityTarget = 0;
						lineitem.QuantityAdj = 0;
						if (boqItems != null && boqItems.Any())
						{
							var boqItem = boqItems.FirstOrDefault(e => e.Id == lineitem.BoqItemFk.Value && e.BoqHeaderFk == lineitem.BoqHeaderFk.Value);
							if (boqItem != null)
							{
								lineitem.QuantityTarget = boqItem.Quantity;
								lineitem.QuantityAdj = boqItem.QuantityAdj;
							}
						}
					}
					else if (updateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemQuantityTotal && (lineitem.BasUomFk != lineitem.BoqItemUomId || IsIgnoreLineItemQuantity(isConsideredQtyRel, lineitem)))
					{
						lineitem.QuantityTarget = 0;
					}

					var copiedLineItem = lineitem.Copy();
					boqItemId2LineItem.Add(new { BoqHeaderFk = lineitem.BoqHeaderFk.Value, BoqItemFk = lineitem.BoqItemFk.Value }, copiedLineItem);
				}
			}
			return boqItemId2LineItem.Values.ToList();
		}

		private IEnumerable<RVPC.IdentificationData> Sql4GetLineItemBySpecificPackage(CreateOrUpdatePackageBoqContext context)
		{
			const string sql = "SELECT a.ID as Id, a.EST_HEADER_FK as PKey1 FROM EST_LINE_ITEM AS a" +
					" INNER JOIN EST_RESOURCE AS b ON b.EST_LINE_ITEM_FK = a.ID" +
					" INNER JOIN PRC_ITEMASSIGNMENT AS c ON c.EST_HEADER_FK = b.EST_HEADER_FK AND c.EST_LINE_ITEM_FK = b.EST_LINE_ITEM_FK AND c.EST_RESOURCE_FK = b.ID AND c.PRC_ITEMASSIGNMENT_FK is null" +
					" AND c.PRC_PACKAGE_FK = @packageId" +
					" WHERE a.ISTEMP = 0 AND a.ISDISABLED = 0 AND a.BOQ_ITEM_FK IS NOT NULL AND a.EST_HEADER_FK = @estHeaderId" +
					" UNION ALL" +
					" SELECT a.ID as Id, a.EST_HEADER_FK as PKey1 FROM EST_LINE_ITEM AS a" +
					" INNER JOIN PRC_ITEMASSIGNMENT AS c ON c.EST_HEADER_FK = a.EST_HEADER_FK AND c.EST_LINE_ITEM_FK = a.ID AND c.EST_RESOURCE_FK IS NULL AND c.PRC_ITEMASSIGNMENT_FK is null" +
					" WHERE a.ISTEMP = 0 AND a.ISDISABLED = 0 AND a.BOQ_ITEM_FK IS NOT NULL AND c.PRC_PACKAGE_FK = @packageId AND a.EST_HEADER_FK = @estHeaderId";

			using (DbContext dbContext = new DbContext(ModelBuilder.DbModel))
			{
				return dbContext.SqlQuery<RVPC.IdentificationData>(sql, new SqlParameter("@packageId", context.PackageId), new SqlParameter("@estHeaderId", context.EstHeaderId)).ToList();
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		private int GeneratePackageBoQFromWicBoQ(CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);

			PrcItemAssignmentLogic prcItemAssignmentLogic = new PrcItemAssignmentLogic();
			IEnumerable<BoQItemLinkedEstimateLineItem> boqItemsLinkedEstimateLineTemp = GetBoqItemLinkedEstLineItemByPackageId(context);

			#region Get line items for recalculate the cost total
			var estHeaderId2LineItemIdsMap = new Dictionary<int, List<int>>();
			var estIdData2LineItemMap = new Dictionary<RVPC.IdentificationData, BoQItemLinkedEstimateLineItem>();
			foreach (var item in boqItemsLinkedEstimateLineTemp)
			{
				var lineItemId = item.Id;
				var estHeaderId = item.EstHeaderFk;
				var temp = new RVPC.IdentificationData()
				{
					Id = lineItemId,
					PKey1 = estHeaderId
				};

				if (!estHeaderId2LineItemIdsMap.ContainsKey(estHeaderId))
				{
					estHeaderId2LineItemIdsMap.Add(estHeaderId, new List<int>() { lineItemId });
				}
				else
				{
					estHeaderId2LineItemIdsMap[estHeaderId].Add(lineItemId);
				}

				if (!estIdData2LineItemMap.TryGetValue(temp, out var lineItem))
				{
					estIdData2LineItemMap.Add(temp, item);
				}
			}
			var lineItems = GetLineItemsByEstHeader2LineItemIdsMap(estHeaderId2LineItemIdsMap);
			#endregion Get line items for recalculate the cost total

			var boqItemsLinkedEstimateLine = lineItems.ToEntities(e => new BoQItemLinkedEstimateLineItem(e, BoqSource.Wic));
			foreach (var item in boqItemsLinkedEstimateLine)
			{
				var temp = new RVPC.IdentificationData()
				{
					Id = item.Id,
					PKey1 = item.EstHeaderFk
				};
				if (!estIdData2LineItemMap.TryGetValue(temp, out var linkedLineItem))
				{
					continue;
				}
				item.BoqItemUomId = linkedLineItem.BoqItemUomId;
				item.BoqItemFactor = linkedLineItem.BoqItemFactor;
			}
			IEnumerable<BoQItemLinkedEstimateLineItem> linkedEstimateLines = boqItemsLinkedEstimateLine.ToList();
			IEnumerable<PrcItemAssignmentEntity> validAssignments = new List<PrcItemAssignmentEntity>();
			IEnumerable<IScriptEstResource> validResources4Cost = new List<IScriptEstResource>();
			IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLines = new List<IEstLineItemEnhanceEntity>();
			IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLinesWithoutResources = new List<IEstLineItemEnhanceEntity>();
			var allItemAssignments = prcItemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();
			FetchValidData(linkedEstimateLines, ref validAssignments, ref validResources4Cost, ref validLinkedEstimateLines, ref validLinkedEstimateLinesWithoutResources, context.DoesUpdateBudgetOnly4AssignmentExist, allItemAssignments);

			if ((validResources4Cost == null || !validResources4Cost.Any()) && (validLinkedEstimateLines == null || !validLinkedEstimateLines.Any()))
			{
				return -1; // TODO chi: right?
			}

			#region recalculate line items and resources when line item is option is true and is option IT is false
			var validLinkedEstimateLinesIsOptional = validLinkedEstimateLines.Where(e => e.IsOptional && !e.IsOptionalIT).ToList();
			var validLinkedEstimateLinesIsOptionalIdData = validLinkedEstimateLinesIsOptional.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.EstHeaderFk }, e => true);
			var validResourcesIsOptional = new List<IScriptEstResource>();
			if (validResources4Cost.Any())
			{
				foreach (var res in validResources4Cost)
				{
					var temp = new RVPC.IdentificationData()
					{
						Id = res.EstLineItemFk,
						PKey1 = res.EstHeaderFk
					};
					if (!validLinkedEstimateLinesIsOptionalIdData.TryGetValue(temp, out var hasValue))
					{
						continue;
					}
					validResourcesIsOptional.Add(res);
				}
			}
			RecalculateCostTotalIfLineItemIsOptional(validLinkedEstimateLinesWithoutResources, validResourcesIsOptional);
			#endregion recalculate line items and resources when line item is optional is true and is optional IT is false

			#region update QuantityTarget according to Quantity Source
			foreach (var lineItem in validLinkedEstimateLines)
			{
				if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
				{
					lineItem.QuantityTarget = lineItem.WqQuantityTarget;
				}
			}
			#endregion update QuantityTarget according to Quantity Source

			Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap = null;
			Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap = null;
			BuildLineItemMaps(validLinkedEstimateLinesWithoutResources, validResources4Cost, out lineItemId2ResourcesMap, false, out lineItemId2MergedPropMap);
			var mergedLineItems = MergeLineItem4WicBoq(boqItemsLinkedEstimateLine, context, lineItemId2MergedPropMap);
			return GeneratePackageBoQ(mergedLineItems, validLinkedEstimateLines, lineItemId2ResourcesMap, validAssignments, context, allValidItemAssignments);
		}

		private IEnumerable<IEstLineItemEnhanceEntity> MergeLineItem4WicBoq(IEnumerable<BoQItemLinkedEstimateLineItem> lineItems, CreateOrUpdatePackageBoqContext context, IDictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap)
		{
			CheckContext(context);

			if (lineItems == null || !lineItems.Any() || lineItemId2MergedPropMap == null)
			{
				return new List<IEstLineItemEnhanceEntity>();
			}

			IDictionary<object, IEstLineItemEnhanceEntity> boqItemId2LineItem = new Dictionary<object, IEstLineItemEnhanceEntity>();

			var isConsideredQtyRel = GetBooleanValue(context.UpdateOption.IsConsideredQtyRel);

			foreach (var lineitem in lineItems)
			{
				// cost total from resource
				if (lineItemId2MergedPropMap.ContainsKey(new { EstHeaderFk = lineitem.EstHeaderFk, EstLineItemFk = lineitem.Id }))
				{
					lineitem.CostTotal = lineItemId2MergedPropMap[new { EstHeaderFk = lineitem.EstHeaderFk, EstLineItemFk = lineitem.Id }].CostTotal;
					lineitem.Budget = lineItemId2MergedPropMap[new { EstHeaderFk = lineitem.EstHeaderFk, EstLineItemFk = lineitem.Id }].BudgetTotal;

				}
				else
				{
					continue;
				}

				if (boqItemId2LineItem.ContainsKey(new { BoqHeaderFk = lineitem.BoqHeaderFk.Value, BoqItemFk = lineitem.BoqItemFk.Value }))
				{
					var temp = boqItemId2LineItem[new { BoqHeaderFk = lineitem.BoqHeaderFk.Value, BoqItemFk = lineitem.BoqItemFk.Value }];

					if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemAQ ||
						context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
					{
						if (lineitem.BasUomTargetFk == lineitem.BoqItemUomId && !IsIgnoreLineItemQuantity(isConsideredQtyRel, lineitem))
						{
							temp.QuantityTarget += lineitem.QuantityTarget;
						}
					}
					// merge cost total
					temp.CostTotal += lineitem.CostTotal;
					temp.Budget += lineitem.Budget;
				}
				else
				{
					if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemAQ ||
						context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
					{
						if (lineitem.BasUomTargetFk != lineitem.BoqItemUomId || IsIgnoreLineItemQuantity(isConsideredQtyRel, lineitem))
						{
							lineitem.QuantityTarget = 0;
						}
					}

					var copiedLineItem = lineitem.Copy();
					boqItemId2LineItem.Add(new { BoqHeaderFk = lineitem.BoqHeaderFk.Value, BoqItemFk = lineitem.BoqItemFk.Value }, copiedLineItem);
				}
			}
			return boqItemId2LineItem.Values.ToList();
		}

		private IEnumerable<BoQItemLinkedEstimateLineItem> GetBoqItemLinkedEstLineItemByPackageId(CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);
			const string filteredLineItem = "SELECT a.ID, a.EST_HEADER_FK, a.BOQ_WIC_CAT_FK, a.WIC_BOQ_ITEM_FK, a.WIC_BOQ_HEADER_FK FROM EST_LINE_ITEM AS a" + //"SELECT a.ID AS ID, a.QUANTITY_TARGET, a.WBS_QUANTITY, a.COST_TOTAL, a.BOQ_WIC_CAT_FK, a.WIC_BOQ_ITEM_FK, a.WIC_BOQ_HEADER_FK, a.BAS_UOM_TARGET_FK, a.EST_HEADER_FK, a.EST_QTY_REL_BOQ_FK, a.PRC_PACKAGE_FK, a.BUDGET FROM EST_LINE_ITEM AS a" +
				" INNER JOIN EST_RESOURCE AS b ON b.EST_LINE_ITEM_FK = a.ID" +
				" INNER JOIN PRC_ITEMASSIGNMENT AS c ON c.EST_HEADER_FK = b.EST_HEADER_FK AND c.EST_LINE_ITEM_FK = b.EST_LINE_ITEM_FK AND c.EST_RESOURCE_FK = b.ID AND c.PRC_ITEMASSIGNMENT_FK is null" +
				" AND c.PRC_PACKAGE_FK = @packageId" +
				" WHERE a.ISTEMP = 0 AND a.ISDISABLED = 0 AND a.EST_HEADER_FK = @estHeaderId" +
				" UNION ALL" +
				" SELECT a.ID, a.EST_HEADER_FK, a.BOQ_WIC_CAT_FK, a.WIC_BOQ_ITEM_FK, a.WIC_BOQ_HEADER_FK FROM EST_LINE_ITEM AS a" +
				" INNER JOIN PRC_ITEMASSIGNMENT AS c ON c.EST_HEADER_FK = a.EST_HEADER_FK AND c.EST_LINE_ITEM_FK = a.ID AND c.EST_RESOURCE_FK IS NULL AND c.PRC_ITEMASSIGNMENT_FK is null" +
				" WHERE a.ISTEMP = 0 AND a.ISDISABLED = 0 AND c.PRC_PACKAGE_FK = @packageId AND a.EST_HEADER_FK = @estHeaderId";

			var sql = "SELECT LINEITEM.ID AS Id, LINEITEM.EST_HEADER_FK as EstHeaderFk, BOQITEM.BAS_UOM_FK AS BoqItemUomId, BOQITEM.FACTOR AS BoqItemFactor FROM FILTEREDLINEITEM AS LINEITEM" +
				" INNER JOIN BOQ_ITEM AS BOQITEM ON BOQITEM.ID = LINEITEM.WIC_BOQ_ITEM_FK AND BOQITEM.BOQ_HEADER_FK = LINEITEM.WIC_BOQ_HEADER_FK" +
				" WHERE LINEITEM.BOQ_WIC_CAT_FK IS NOT NULL";

			sql = string.Format("WITH FILTEREDLINEITEM AS ({0}) {1}", filteredLineItem, sql);

			using (DbContext dbContext = new DbContext(ModelBuilder.DbModel))
			{
				return dbContext.SqlQuery<BoQItemLinkedEstimateLineItem>(sql, new SqlParameter("@packageId", context.PackageId),
					new SqlParameter("@estHeaderId", context.EstHeaderId)).ToList();
			}
		}

		/// <summary>
		/// Generate Package BoQ
		/// </summary>
		/// <param name="boqItemsLinkedEstimateLine"></param>
		/// <param name="linkedEstimateLines"></param>
		/// <param name="lineItemId2ResourcesMap"></param>
		/// <param name="assignmentItemsForUpdateOperation"></param>
		/// <param name="context"></param>
		/// <param name="itemAssignmentsExist"></param>
		/// <param name="nodeBoqItemsToSave"></param>
		/// <param name="dragNDropInfo"></param>
		/// <returns></returns>
		private int GeneratePackageBoQ(
			IEnumerable<IEstLineItemEnhanceEntity> boqItemsLinkedEstimateLine,
			IEnumerable<IEstLineItemEnhanceEntity> linkedEstimateLines,
			IDictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap,
			IEnumerable<PrcItemAssignmentEntity> assignmentItemsForUpdateOperation,
			CreateOrUpdatePackageBoqContext context,
			IEnumerable<PrcItemAssignmentEntity> itemAssignmentsExist,
			IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSave = null,
			DragNDropInfo dragNDropInfo = null)
		{
			CheckContext(context);

			var isFromProject = context.Source == UpdateOptionForBoq.Criteria.ProjectBoq;
			var package = context.Package ?? new PrcPackageLogic().GetItemByKey(context.PackageId);
			var projectLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var projectInfo = projectLogic.GetProjectById(package.ProjectFk);
			var exchangerate = new ProcurementCommonExchangeRateLogic().GetExchangeRateOc(package.CurrencyFk, projectInfo.CurrencyFk, projectInfo.Id, true);
			BoqItemLogic boqItemLogic = new BoqItemLogic();
			var boqItemLogicInterface = Injector.Get<IBoqItemLogic>();
			var prcItemAssignmentLogic = new PrcItemAssignmentLogic();
			var itemAssignmentBoqMap = new Dictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>>();
			var itemAssignmentChildrenMap = new Dictionary<int, List<PrcItemAssignmentEntity>>();
			var itemAssignmentEstimateMap = new Dictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>>();
			var boqItem2CostGroupLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IBoqItem2CostGroupLogic>();
			boqItemLogic.SetCurrentExchangeRateHc2Oc(package.ExchangeRate);

			foreach (var lineitem in boqItemsLinkedEstimateLine)
			{
				lineitem.CostTotal = lineitem.CostTotal / exchangerate.Value;
				lineitem.PackageTaxCodeFk = package.TaxCodeFk;
			}

			if (itemAssignmentsExist != null && itemAssignmentsExist.Any())
			{
				foreach (var itemAssign in itemAssignmentsExist)
				{
					if (itemAssign.BoqHeaderFk.HasValue && itemAssign.BoqItemFk.HasValue)
					{
						if (!itemAssign.PrcItemAssignmentFk.HasValue)
						{
							var boqId = new RVPC.IdentificationData() { Id = itemAssign.BoqItemFk.Value, PKey1 = itemAssign.BoqHeaderFk.Value };
							List<PrcItemAssignmentEntity> list = null;
							if (!itemAssignmentBoqMap.TryGetValue(boqId, out list))
							{
								itemAssignmentBoqMap.Add(boqId, new List<PrcItemAssignmentEntity>() { itemAssign });
							}
							else
							{
								list.Add(itemAssign);
							}
						}
						else
						{
							if (!itemAssignmentChildrenMap.ContainsKey(itemAssign.PrcItemAssignmentFk.Value))
							{
								itemAssignmentChildrenMap.Add(itemAssign.PrcItemAssignmentFk.Value, new List<PrcItemAssignmentEntity>() { itemAssign });
							}
							else
							{
								itemAssignmentChildrenMap[itemAssign.PrcItemAssignmentFk.Value].Add(itemAssign);
							}
						}
					}

					var estimateId = new RVPC.IdentificationData() { Id = itemAssign.EstLineItemFk, PKey1 = itemAssign.EstHeaderFk };
					List<PrcItemAssignmentEntity> list2 = null;
					if (!itemAssignmentEstimateMap.TryGetValue(estimateId, out list2))
					{
						itemAssignmentEstimateMap.Add(estimateId, new List<PrcItemAssignmentEntity>() { itemAssign });
					}
					else
					{
						list2.Add(itemAssign);
					}
				}
			}

			IList<int> linkedBoQHeaderIds = boqItemsLinkedEstimateLine.CollectIds(e => e.BoqHeaderFk).ToList();

			PrcBoqLogic prcBoqLogic = new PrcBoqLogic();
			IEnumerable<PrcBoqEntity> prcBoqList;
			if (dragNDropInfo != null && dragNDropInfo.IsForcedToCreateBoq)
			{
				prcBoqList = prcBoqLogic.GetList(e => e.PrcHeaderFk == context.PrcHeaderId && e.BoqHeaderFk == dragNDropInfo.TargetBoqHeaderId);
			}
			else
			{
				prcBoqList = prcBoqLogic.GetList(context.PrcHeaderId);
			}
			List<PrcBoqExtendedEntity> prcBoQExtendedList = new List<PrcBoqExtendedEntity>();
			if (prcBoqList != null)
			{
				foreach (var prcBoq in prcBoqList)
				{
					var prcBoqExtended = prcBoqLogic.GetPrcBoqExtended(prcBoq);
					prcBoQExtendedList.Add(prcBoqExtended);
				}
			}

			IList<int> sourceBoQItemIds = boqItemsLinkedEstimateLine.CollectIds(e => e.BoqItemFk).ToList();
			IList<BoqItemEntity> sourceBoQItems = boqItemLogic.GetBoqItems(e => sourceBoQItemIds.Contains(e.Id)).ToList();
			var sourceBoqItemIdData = sourceBoQItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }, e => e);

			var rootBoQItems = boqItemLogicInterface.GetPureBoqRootItemsByHeaderIds(linkedBoQHeaderIds);

			List<IEstLineItemEnhanceEntity> boqItemsForCreate = new List<IEstLineItemEnhanceEntity>();
			List<IEstLineItemEnhanceEntity> boqItemsForMerge = new List<IEstLineItemEnhanceEntity>();
			Dictionary<int, IEnumerable<PrcBoqExtendedEntity>> targetExistBoQExtendedDic = new Dictionary<int, IEnumerable<PrcBoqExtendedEntity>>();
			List<BoqItemInfo4CreatingBoqCostGroup> sourceItem4CostGroupList = new List<BoqItemInfo4CreatingBoqCostGroup>();
			List<IBoqItem2CostGroupEntity> boqItem2CostGroupsToCreate = new List<IBoqItem2CostGroupEntity>();

			foreach (var sourceBoQHeaderId in linkedBoQHeaderIds)
			{
				if (dragNDropInfo != null && dragNDropInfo.IsForcedToCreateBoq)
				{
					if (dragNDropInfo.SourceBoqHeaderId == sourceBoQHeaderId)
					{
						List<IEstLineItemEnhanceEntity> boqItemsForOneHeader = boqItemsLinkedEstimateLine.Where(e => e.BoqHeaderFk.Value == sourceBoQHeaderId).ToList();
						var sourcePrcBoQExtendedList = prcBoQExtendedList.Where(e => e.BoqHeader.Id == dragNDropInfo.TargetBoqHeaderId).ToList();
						if (sourcePrcBoQExtendedList.Any() && !targetExistBoQExtendedDic.ContainsKey(sourceBoQHeaderId))
						{
							boqItemsForMerge.AddRange(boqItemsForOneHeader);
							targetExistBoQExtendedDic.Add(sourceBoQHeaderId, sourcePrcBoQExtendedList);
						}
						// if no prc boq is found in package, don't do anything
					}
				}
				else if (isFromProject)
				{
					List<IEstLineItemEnhanceEntity> boqItemsForOneHeader = boqItemsLinkedEstimateLine.Where(e => e.BoqHeaderFk.Value == sourceBoQHeaderId).ToList();
					var linkedboqItem = boqItemsForOneHeader.FirstOrDefault();
					var temp = new RVPC.IdentificationData();
					if (linkedboqItem != null)
					{
						temp.Id = linkedboqItem.Id;
						temp.PKey1 = linkedboqItem.EstHeaderFk;
					}
					if (itemAssignmentEstimateMap.TryGetValue(temp, out var itemAssigns))
					{
						var assignBoqHeaderIds = itemAssigns.Where(e => !e.PrcItemAssignmentFk.HasValue).CollectIds(e => e.BoqHeaderFk);
						var prcBoqs = prcBoQExtendedList.Where(e => assignBoqHeaderIds.Contains(e.BoqHeader.Id)).ToList();
						boqItemsForMerge.AddRange(boqItemsForOneHeader);
						if (!targetExistBoQExtendedDic.ContainsKey(sourceBoQHeaderId))
						{
							targetExistBoQExtendedDic.Add(sourceBoQHeaderId, prcBoqs);
						}
					}
					else
					{
						var sourcePrcBoQExtendedList = prcBoQExtendedList.Where(e => rootBoQItems.Any(g => g.BoqHeaderFk == sourceBoQHeaderId && g.BoqNumber == e.BoqRootItem.Reference)).ToList();
						if (sourcePrcBoQExtendedList.Any())
						{
							boqItemsForMerge.AddRange(boqItemsForOneHeader);
							if (!targetExistBoQExtendedDic.ContainsKey(sourceBoQHeaderId))
							{
								targetExistBoQExtendedDic.Add(sourceBoQHeaderId, sourcePrcBoQExtendedList);
							}
						}
						else
						{
							boqItemsForCreate.AddRange(boqItemsForOneHeader);
						}
					}
				}
				else
				{
					List<PrcBoqExtendedEntity> sourcePrcBoQExtendedList;
					List<IEstLineItemEnhanceEntity> boqItemsForOneHeader = boqItemsLinkedEstimateLine.Where(e => e.BoqHeaderFk.Value == sourceBoQHeaderId).ToList();
					sourcePrcBoQExtendedList = prcBoQExtendedList.Where(e => rootBoQItems.Any(g => g.BoqHeaderFk == sourceBoQHeaderId && g.Id == e.BoqRootItem.BoqItemWicItemFk)).ToList();
					if (sourcePrcBoQExtendedList.Any())
					{
						boqItemsForMerge.AddRange(boqItemsForOneHeader);
						if (!targetExistBoQExtendedDic.ContainsKey(sourceBoQHeaderId))
						{
							targetExistBoQExtendedDic.Add(sourceBoQHeaderId, sourcePrcBoQExtendedList);
						}
					}
					else
					{
						boqItemsForCreate.AddRange(boqItemsForOneHeader);
					}
				}
			}

			if (!boqItemsForCreate.Any() && !boqItemsForMerge.Any())
			{
				return -1;
			}

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				using (DbContext dbContext = new DbContext(ModelBuilder.DbModel))
				{
					if (dragNDropInfo != null && dragNDropInfo.IsForcedToCreateBoq)
					{
						if (boqItemsForMerge.Any())
						{
							//merge BoQ
							Dictionary<int, int> sourceToTargetIdMapping = new Dictionary<int, int>();
							List<int> targetHeaderIds = new List<int>();
							IList<int> sourceBoQHeaderIds = boqItemsForMerge.CollectIds(e => e.BoqHeaderFk).ToList();
							BoqHeaderLogic boqHeaderLogic = new BoqHeaderLogic();
							var sourceBoqId2TargetBoqId = new Dictionary<int, int>();

							for (Int32 i = 0; i < sourceBoQHeaderIds.Count; i++)
							{
								Int32 sourceId = sourceBoQHeaderIds[i];

								IEnumerable<PrcBoqExtendedEntity> sourcePrcBoQExtendedList = targetExistBoQExtendedDic[sourceId];
								PrcBoqExtendedEntity targetPrcBoQExtended = sourcePrcBoQExtendedList.ElementAt(0);

								int targetHeaderId = targetPrcBoQExtended.BoqHeader.Id;
								targetHeaderIds.Add(targetHeaderId);
								if (!sourceToTargetIdMapping.ContainsKey(sourceId))
								{
									sourceToTargetIdMapping.Add(sourceId, targetHeaderId);
								}
								BoqHeaderEntity sourceBoQHeaderItem = boqHeaderLogic.GetEntityById(targetHeaderId);
								IList<IEstLineItemEnhanceEntity> linkedList = boqItemsForMerge.Where(e => e.BoqHeaderFk.Value == sourceId).ToList();

								if (targetPrcBoQExtended.BoqHeader.BoqStructureFk != sourceBoQHeaderItem.BoqStructureFk)
								{
									return -4;
								}

								if (!dragNDropInfo.SourceBoqItemId2NewBoqItemIdMap.IsNullOrEmpty())
								{
									var targetRootBoqItem = UpdatePackageBoqItems(boqItemLogic, linkedList, targetPrcBoQExtended.BoqHeader, dragNDropInfo);
									if (targetRootBoqItem != null)
									{
										var boqCalculateLogic = new BoqItemCalculateLogic(moduleName, package.Id, package.ExchangeRate, package.TaxCodeFk, package.BpdVatGroupFk);
										var calculationResult = boqCalculateLogic.CalculateBoqTreeAndPriceconditions(targetRootBoqItem);
										var targetRootBoqItems = new List<BoqItemEntity>() { targetRootBoqItem };
										var boqItemFlatList = targetRootBoqItems.Flatten(e => e.BoqItemChildren).ToList();
										using (var boqDbContext = new DbContext(BOQMAIN.ModelBuilder.DbModel))
										{
											new BulkSaveHelper().BulkUpdate(boqDbContext, boqItemFlatList);
										}
										if (calculationResult != null && calculationResult.BoqPriceconditionChanged != null)
										{
											var logic = new BoqPriceconditionLogic();
											var insertConditionItems = calculationResult.BoqPriceconditionChanged.Where(e => e.Version == 0).ToList();
											var updateConditionItems = calculationResult.BoqPriceconditionChanged.Where(e => e.Version > 0).ToList();
											logic.BulkUpdateList(updateConditionItems);
											logic.BulkInsertList(insertConditionItems);
										}
										sourceBoqId2TargetBoqId = dragNDropInfo.SourceBoqItemId2NewBoqItemIdMap as Dictionary<int, int>;
									}
								}
							}

							var prcItemsAssignmentToCreate = new List<PrcItemAssignmentEntity>();
							foreach (var boqItemLinkedEstimate in linkedEstimateLines)
							{
								var resources = lineItemId2ResourcesMap[new { EstHeaderFk = boqItemLinkedEstimate.EstHeaderFk, EstLineItemFk = boqItemLinkedEstimate.Id }];
								if (resources == null)
								{
									continue;
								}

								if (resources.Any())
								{
									foreach (var resource in resources)
									{
										var prcItemAssignment = new PrcItemAssignmentEntity();
										prcItemAssignment.EstHeaderFk = boqItemLinkedEstimate.EstHeaderFk;
										prcItemAssignment.EstLineItemFk = boqItemLinkedEstimate.Id;
										prcItemAssignment.PrcPackageFk = context.PackageId;
										prcItemAssignment.EstResourceFk = resource.Id;

										if (sourceToTargetIdMapping.ContainsKey(boqItemLinkedEstimate.BoQHeaderId) &&
											sourceBoqId2TargetBoqId.ContainsKey(boqItemLinkedEstimate.BoQItemId))
										{
											int targetheaderId = sourceToTargetIdMapping[boqItemLinkedEstimate.BoQHeaderId];
											prcItemAssignment.BoqHeaderFk = targetheaderId;
											prcItemAssignment.BoqItemFk = sourceBoqId2TargetBoqId[boqItemLinkedEstimate.BoQItemId];
											prcItemsAssignmentToCreate.Add(prcItemAssignment);
										}
									}
								}
								else
								{
									var prcItemAssignment = new PrcItemAssignmentEntity();
									prcItemAssignment.EstHeaderFk = boqItemLinkedEstimate.EstHeaderFk;
									prcItemAssignment.EstLineItemFk = boqItemLinkedEstimate.Id;
									prcItemAssignment.PrcPackageFk = context.PackageId;

									if (sourceToTargetIdMapping.ContainsKey(boqItemLinkedEstimate.BoQHeaderId) &&
										sourceBoqId2TargetBoqId.ContainsKey(boqItemLinkedEstimate.BoQItemId))
									{
										int targetheaderId = sourceToTargetIdMapping[boqItemLinkedEstimate.BoQHeaderId];
										prcItemAssignment.BoqHeaderFk = targetheaderId;
										prcItemAssignment.BoqItemFk = sourceBoqId2TargetBoqId[boqItemLinkedEstimate.BoQItemId];
										prcItemsAssignmentToCreate.Add(prcItemAssignment);

									}
								}
							}
							var itemAssignIds = prcItemAssignmentLogic.GetNextIds(prcItemsAssignmentToCreate.Count).GetEnumerator();
							foreach (var item in prcItemsAssignmentToCreate)
							{
								itemAssignIds.MoveNext();
								item.Id = itemAssignIds.Current;
							}
							prcItemAssignmentLogic.Save(prcItemsAssignmentToCreate);
						}
					}
					else if (boqItemsForMerge.Count > 0)
					{
						//merge BoQ
						Dictionary<int, int> sourceToTargetIdMapping = new Dictionary<int, int>();
						List<int> targetHeaderIds = new List<int>();
						//IList<int> sourceBoQHeaderIds = boqItemsForMerge.Select(e => e.BoQHeaderId).Distinct().ToList();
						IList<int> sourceBoQHeaderIds = boqItemsForMerge.CollectIds(e => e.BoqHeaderFk).ToList();
						BoqHeaderLogic boqHeaderLogic = new BoqHeaderLogic();

						for (Int32 i = 0; i < sourceBoQHeaderIds.Count; i++)
						{
							Int32 sourceId = sourceBoQHeaderIds[i];

							IEnumerable<PrcBoqExtendedEntity> sourcePrcBoQExtendedList = targetExistBoQExtendedDic[sourceId];
							PrcBoqExtendedEntity targetPrcBoQExtended = sourcePrcBoQExtendedList.ElementAt(0);

							int targetHeaderId = targetPrcBoQExtended.BoqHeader.Id;
							targetHeaderIds.Add(targetHeaderId);
							if (!sourceToTargetIdMapping.ContainsKey(sourceId))
							{
								sourceToTargetIdMapping.Add(sourceId, targetHeaderId);
							}
							BoqHeaderEntity sourceBoQHeaderItem = boqHeaderLogic.GetEntityById(targetHeaderId);
							IList<IEstLineItemEnhanceEntity> linkedList = boqItemsForMerge.Where(e => e.BoqHeaderFk.Value == sourceId).ToList();

							if (targetPrcBoQExtended.BoqHeader.BoqStructureFk != sourceBoQHeaderItem.BoqStructureFk)
							{
								return -4;
							}

							SyncBoqItems(dbContext, boqItemLogic, package, targetPrcBoQExtended.BoqHeader, sourceId, targetPrcBoQExtended.BoqHeader.Id, linkedList/*, assignments4CalQuantity*/, linkedEstimateLines, ref sourceItem4CostGroupList, context, itemAssignmentBoqMap, itemAssignmentChildrenMap, itemAssignmentEstimateMap, nodeBoqItemsToSave);
						}

						#region Update prcItemAssignment
						var boqItems = new BoqItemLogic().GetBoqItems(e => targetHeaderIds.Contains(e.BoqHeaderFk)); // get boq items by headerids
						var boqMap = new Dictionary<string, List<BoqItemEntity>>();
						var boqWicMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();
						foreach (var boqItem in boqItems)
						{
							if (!string.IsNullOrEmpty(boqItem.Reference))
							{
								if (!boqMap.ContainsKey(boqItem.Reference))
								{
									boqMap.Add(boqItem.Reference, new List<BoqItemEntity>() { boqItem });
								}
								else
								{
									boqMap[boqItem.Reference].Add(boqItem);
								}
							}
							if (boqItem.BoqItemWicBoqFk.HasValue && boqItem.BoqItemWicItemFk.HasValue)
							{
								var wicId = new RVPC.IdentificationData() { Id = boqItem.BoqItemWicItemFk.Value, PKey1 = boqItem.BoqItemWicBoqFk.Value };
								List<BoqItemEntity> wics = null;
								if (!boqWicMap.TryGetValue(wicId, out wics))
								{
									boqWicMap.Add(wicId, new List<BoqItemEntity>() { boqItem });
								}
								else
								{
									wics.Add(boqItem);
								}
							}
						}

						// delete, create and modified prcItemAssignment
						List<PrcItemAssignmentEntity> deletedItems = new List<PrcItemAssignmentEntity>();
						List<PrcItemAssignmentEntity> modifiedItems = new List<PrcItemAssignmentEntity>();
						List<PrcItemAssignmentEntity> newItems = new List<PrcItemAssignmentEntity>();
						var newChildrenMap = new Dictionary<PrcItemAssignmentEntity, IEnumerable<PrcItemAssignmentEntity>>();

						// remove prcItemAssignment
						if (context.IsUpdateOperation)
						{
							var reMoveItems = assignmentItemsForUpdateOperation != null && assignmentItemsForUpdateOperation.Any() ? assignmentItemsForUpdateOperation.Where(e => e.BoqHeaderFk.HasValue && !targetHeaderIds.Contains(e.BoqHeaderFk.Value)).ToList() : new List<PrcItemAssignmentEntity>();
							deletedItems.AddRange(reMoveItems);
						}
						foreach (KeyValuePair<int, int> item in sourceToTargetIdMapping)
						{
							var boqLinkedEsts = linkedEstimateLines.Where(e => e.BoqHeaderFk.Value == item.Key).ToList();
							var boqLinkedEstIds = boqLinkedEsts.Select(e => e.Id).ToList();
							var tempAssignment = assignmentItemsForUpdateOperation != null && assignmentItemsForUpdateOperation.Any() ? assignmentItemsForUpdateOperation : itemAssignmentsExist;
							var prcAssignmentItemList = tempAssignment != null && tempAssignment.Any() ? tempAssignment.Where(e => e.BoqHeaderFk == item.Value ||
								(!e.BoqHeaderFk.HasValue && !e.BoqItemFk.HasValue && !e.PrcItemFk.HasValue)).ToList() : new List<PrcItemAssignmentEntity>();
							var itemAssignmentEstimateMapForUpdate = new Dictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>>();

							foreach (var prcAssignmentItem in prcAssignmentItemList)
							{
								var estimateId = new RVPC.IdentificationData() { Id = prcAssignmentItem.EstLineItemFk, PKey1 = prcAssignmentItem.EstHeaderFk };
								List<PrcItemAssignmentEntity> list = null;
								if (!itemAssignmentEstimateMapForUpdate.TryGetValue(estimateId, out list))
								{
									itemAssignmentEstimateMapForUpdate.Add(estimateId, new List<PrcItemAssignmentEntity>() { prcAssignmentItem });
								}
								else
								{
									list.Add(prcAssignmentItem);
								}

								if (context.IsUpdateOperation)
								{
									// delete items
									if (!boqLinkedEstIds.Contains(prcAssignmentItem.EstLineItemFk))
									{
										deletedItems.Add(prcAssignmentItem);
									}
								}
							}

							foreach (var boqLinkedEst in boqLinkedEsts)
							{
								var resources = lineItemId2ResourcesMap[new { EstHeaderFk = boqLinkedEst.EstHeaderFk, EstLineItemFk = boqLinkedEst.Id }];
								if (resources != null)
								{
									if (resources.Any())
									{
										foreach (var resource in resources)
										{
											var prcItemAssignmentEdit = GetValueFromDictionary(itemAssignmentEstimateMapForUpdate, resource.EstLineItemFk, resource.EstHeaderFk).FirstOrDefault(e => e.EstResourceFk == resource.Id && !e.PrcItemAssignmentFk.HasValue); // prcAssignmentItemList.FirstOrDefault(e => e.EstHeaderFk == resource.EstHeaderFk && e.EstLineItemFk == resource.EstLineItemFk && e.EstResourceFk == resource.Id && !e.PrcItemAssignmentFk.HasValue);
																																																																																				  // new items
											if (prcItemAssignmentEdit == null)
											{
												//PrcItemAssignmentEntity prcItemAssignment = prcItemAssignmentLogic.Create(boqLinkedEst.EstHeaderFk, boqLinkedEst.Id, context.PackageId);
												var prcItemAssignment = new PrcItemAssignmentEntity();
												prcItemAssignment.EstHeaderFk = boqLinkedEst.EstHeaderFk;
												prcItemAssignment.EstLineItemFk = boqLinkedEst.Id;
												prcItemAssignment.PrcPackageFk = context.PackageId;

												prcItemAssignment.BoqHeaderFk = targetExistBoQExtendedDic[item.Key].ElementAt(0).BoqHeader.Id;
												prcItemAssignment.EstResourceFk = resource.Id;
												UpdatePrcAssignmentItemWithBoqItem(boqLinkedEst.BoqHeaderFk.Value, boqLinkedEst.BoqItemFk.Value, boqMap, boqWicMap, sourceBoqItemIdData, isFromProject, ref prcItemAssignment, true);
												newItems.Add(prcItemAssignment);
												var newChildren = CreateChildrenItemAssignments(prcItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(prcItemAssignment))
													{
														newChildrenMap.Add(prcItemAssignment, newChildren);
													}
													newItems.AddRange(newChildren);
												}
											}
											else
											{
												var isNew = false;
												if (!prcItemAssignmentEdit.BoqHeaderFk.HasValue && !prcItemAssignmentEdit.BoqItemFk.HasValue)
												{
													prcItemAssignmentEdit.BoqHeaderFk = targetExistBoQExtendedDic[item.Key].ElementAt(0).BoqHeader.Id;
													isNew = true;
												}
												// modified items
												var isUpdated = UpdatePrcAssignmentItemWithBoqItem(boqLinkedEst.BoqHeaderFk.Value, boqLinkedEst.BoqItemFk.Value, boqMap, boqWicMap, sourceBoqItemIdData, isFromProject, ref prcItemAssignmentEdit, isNew);
												if (isUpdated)
												{
													modifiedItems.Add(prcItemAssignmentEdit);
													if (isNew)
													{
														var newChildren = CreateChildrenItemAssignments(prcItemAssignmentEdit, itemAssignmentBoqMap, itemAssignmentChildrenMap);
														if (newChildren != null && newChildren.Any())
														{
															if (!newChildrenMap.ContainsKey(prcItemAssignmentEdit))
															{
																newChildrenMap.Add(prcItemAssignmentEdit, newChildren);
															}
															newItems.AddRange(newChildren);
														}
													}
												}
											}
										}
									}
									else
									{
										var prcItemAssignmentEdit = GetValueFromDictionary(itemAssignmentEstimateMapForUpdate, boqLinkedEst.Id, boqLinkedEst.EstHeaderFk).FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue); // prcAssignmentItemList.FirstOrDefault(e => e.EstHeaderFk == boqLinkedEst.EstHeaderFk && e.EstLineItemFk == boqLinkedEst.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);
																																																																																	// new items
										if (prcItemAssignmentEdit == null)
										{
											//PrcItemAssignmentEntity prcItemAssignment = prcItemAssignmentLogic.Create(boqLinkedEst.EstHeaderFk, boqLinkedEst.Id, context.PackageId);
											var prcItemAssignment = new PrcItemAssignmentEntity();
											prcItemAssignment.EstHeaderFk = boqLinkedEst.EstHeaderFk;
											prcItemAssignment.EstLineItemFk = boqLinkedEst.Id;
											prcItemAssignment.PrcPackageFk = context.PackageId;

											prcItemAssignment.BoqHeaderFk = targetExistBoQExtendedDic[item.Key].ElementAt(0).BoqHeader.Id;
											UpdatePrcAssignmentItemWithBoqItem(boqLinkedEst.BoqHeaderFk.Value, boqLinkedEst.BoqItemFk.Value, boqMap, boqWicMap, sourceBoqItemIdData, isFromProject, ref prcItemAssignment, true);
											newItems.Add(prcItemAssignment);
											var newChildren = CreateChildrenItemAssignments(prcItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
											if (newChildren != null && newChildren.Any())
											{
												if (!newChildrenMap.ContainsKey(prcItemAssignment))
												{
													newChildrenMap.Add(prcItemAssignment, newChildren);
												}
												newItems.AddRange(newChildren);
											}
										}
										else
										{
											var isNew = false;
											if (!prcItemAssignmentEdit.BoqHeaderFk.HasValue && !prcItemAssignmentEdit.BoqItemFk.HasValue)
											{
												prcItemAssignmentEdit.BoqHeaderFk = targetExistBoQExtendedDic[item.Key].ElementAt(0).BoqHeader.Id;
												isNew = true;
											}
											// modified items
											var isUpdated = UpdatePrcAssignmentItemWithBoqItem(boqLinkedEst.BoqHeaderFk.Value, boqLinkedEst.BoqItemFk.Value, boqMap, boqWicMap, sourceBoqItemIdData, isFromProject, ref prcItemAssignmentEdit, isNew);
											if (isUpdated)
											{
												modifiedItems.Add(prcItemAssignmentEdit);
												if (isNew)
												{
													var newChildren = CreateChildrenItemAssignments(prcItemAssignmentEdit, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(prcItemAssignmentEdit))
														{
															newChildrenMap.Add(prcItemAssignmentEdit, newChildren);
														}
														newItems.AddRange(newChildren);
													}
												}
											}
										}
									}
								}
							}
						}
						var itemAssignIds = prcItemAssignmentLogic.GetNextIds(newItems.Count).GetEnumerator();
						foreach (var item in newItems)
						{
							itemAssignIds.MoveNext();
							item.Id = itemAssignIds.Current;
							if (newChildrenMap.ContainsKey(item))
							{
								newChildrenMap[item].ExtendEach(e => e.PrcItemAssignmentFk = item.Id);
							}
						}
						newItems.AddRange(modifiedItems);
						prcItemAssignmentLogic.Save(newItems);
						prcItemAssignmentLogic.Delete(deletedItems);
						#endregion
					}

					var prcItemsAssignmentForCreate = new List<PrcItemAssignmentEntity>();
					var prcItemAssignmentToUpdate = new List<PrcItemAssignmentEntity>();

					if (boqItemsForCreate.Count > 0 && !context.DoesUpdateBudgetOnly4AssignmentExist)
					{
						//create new baseBoQ
						Dictionary<int, int> sourceToTargetIdMapping = new Dictionary<int, int>();
						IList<int> sourceBoQHeaderIds = boqItemsForCreate.CollectIds(e => e.BoqHeaderFk).ToList();
						IList<BoqHeaderEntity> targetBoQHeaders = CopyBoQHeader(sourceBoQHeaderIds, ref sourceToTargetIdMapping, package.CurrencyFk);
						Dictionary<int, IEnumerable<BoqItemEntity>> BoqItemEntityMapping = new Dictionary<int, IEnumerable<BoqItemEntity>>();
						var boqMap = new Dictionary<string, List<BoqItemEntity>>();
						var boqWicMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();

						//save boq_item
						foreach (int sourceId in sourceBoQHeaderIds)
						{
							IList<IEstLineItemEnhanceEntity> linkedList = boqItemsForCreate.Where(e => e.BoqHeaderFk.Value == sourceId).ToList();

							int targetHeaderId = sourceToTargetIdMapping[sourceId];
							BoqHeaderEntity targetBoQHeader = targetBoQHeaders.Single(e => e.Id == targetHeaderId);

							BoQItemUpdate boqItemUpdate = CopyBoqItems(
																		boqItemLogic,
																		linkedList,
																		targetBoQHeader,
																		null,
																		isFromProject,
																		ref sourceItem4CostGroupList,
																		null,
																		nodeBoqItemsToSave);

							BoqItemEntity targetRootBoqitem = boqItemUpdate.TargetUpdateBoQItems.Where(e => e.BoqItemFk == null).ElementAt(0);
							SaveBoqs(
										dbContext,
										targetBoQHeader.Id,
										boqItemUpdate.TargetUpdateBoQItems,
										boqItemUpdate.UpdateBoqSurchargedItems,
										boqItemUpdate.UpdateBoqPriceConditions);

							var boqCalculateLogic = new BoqItemCalculateLogic(moduleName, package.Id, package.ExchangeRate, package.TaxCodeFk, package.BpdVatGroupFk);
							var calculationResult = boqCalculateLogic.CalculateBoqTreeAndPriceconditions(targetRootBoqitem);

							if (boqItemUpdate.TargetUpdateBoQItems != null && boqItemUpdate.TargetUpdateBoQItems.Any())
							{
								DbCompiledModel dbCompiledModel = RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder.DbModel;
								using (var boqDbContext = new DbContext(dbCompiledModel))
								{
									var helper = new BulkSaveHelper();
									var insertBoqItems = boqItemUpdate.TargetUpdateBoQItems.Where(e => e.Version == 0).ToList();
									var updateBoqItems = boqItemUpdate.TargetUpdateBoQItems.Where(e => e.Version > 0).ToList();
									helper.BulkUpdate(boqDbContext, updateBoqItems);
									helper.BulkInsert(boqDbContext, insertBoqItems);
								}
							}

							if (calculationResult != null && calculationResult.BoqPriceconditionChanged != null)
							{
								var logic = new BoqPriceconditionLogic();
								var insertConditionItems = calculationResult.BoqPriceconditionChanged.Where(e => e.Version == 0).ToList();
								var updateConditionItems = calculationResult.BoqPriceconditionChanged.Where(e => e.Version > 0).ToList();
								logic.BulkUpdateList(updateConditionItems);
								logic.BulkInsertList(insertConditionItems);
							}

							//BoqItemEntityMapping[targetBoQHeader.Id] = boqItemUpdate.TargetUpdateBoQItems;
							foreach (var boqItem in boqItemUpdate.TargetUpdateBoQItems)
							{
								if (!string.IsNullOrEmpty(boqItem.Reference))
								{
									if (!boqMap.ContainsKey(boqItem.Reference))
									{
										boqMap.Add(boqItem.Reference, new List<BoqItemEntity>() { boqItem });
									}
									else
									{
										boqMap[boqItem.Reference].Add(boqItem);
									}
								}
								if (boqItem.BoqItemWicBoqFk.HasValue && boqItem.BoqItemWicItemFk.HasValue)
								{
									var wicId = new RVPC.IdentificationData() { Id = boqItem.BoqItemWicItemFk.Value, PKey1 = boqItem.BoqItemWicBoqFk.Value };
									List<BoqItemEntity> wics = null;
									if (!boqWicMap.TryGetValue(wicId, out wics))
									{
										boqWicMap.Add(wicId, new List<BoqItemEntity>() { boqItem });
									}
									else
									{
										wics.Add(boqItem);
									}
								}
							}
						}

						IList<PrcBoqEntity> prcBoqs = new List<PrcBoqEntity>();

						//save prc_item
						foreach (BoqHeaderEntity boqHeader in targetBoQHeaders)
						{
							PrcBoqEntity prcBoqItem = prcBoqLogic.Create(context.PrcHeaderId, boqHeader.Id, context.PackageId);
							prcBoqs.Add(prcBoqItem);
						}
						prcBoqLogic.Save(prcBoqs);

						var newChildrenMap = new Dictionary<PrcItemAssignmentEntity, IEnumerable<PrcItemAssignmentEntity>>();
						foreach (IEstLineItemEnhanceEntity boqItemLinkedEstimate in linkedEstimateLines)
						{
							var resources = lineItemId2ResourcesMap[new { EstHeaderFk = boqItemLinkedEstimate.EstHeaderFk, EstLineItemFk = boqItemLinkedEstimate.Id }];
							if (resources == null)
							{
								continue;
							}

							if (resources.Any())
							{
								foreach (var resource in resources)
								{
									var isCreate = false;
									PrcItemAssignmentEntity prcItemAssignment = GetValueFromDictionary(itemAssignmentEstimateMap, boqItemLinkedEstimate.Id, boqItemLinkedEstimate.EstHeaderFk)
										.FirstOrDefault(e => e.EstResourceFk == resource.Id && !e.BoqHeaderFk.HasValue && !e.PrcItemFk.HasValue); // itemAssignmentsExist.FirstOrDefault(e =>
																																													 //e.EstHeaderFk == boqItemLinkedEstimate.EstHeaderFk &&
																																													 //e.EstLineItemFk == boqItemLinkedEstimate.Id &&
																																													 //e.EstResourceFk == resource.Id && !e.BoqHeaderFk.HasValue && !e.PrcItemFk.HasValue);
									if (prcItemAssignment == null)
									{
										prcItemAssignment = new PrcItemAssignmentEntity();
										prcItemAssignment.EstHeaderFk = boqItemLinkedEstimate.EstHeaderFk;
										prcItemAssignment.EstLineItemFk = boqItemLinkedEstimate.Id;
										prcItemAssignment.PrcPackageFk = context.PackageId;
										prcItemAssignment.EstResourceFk = resource.Id;
										isCreate = true;
									}
									if (sourceToTargetIdMapping.ContainsKey(boqItemLinkedEstimate.BoQHeaderId))
									{
										int targetheaderId = sourceToTargetIdMapping[boqItemLinkedEstimate.BoQHeaderId];
										prcItemAssignment.BoqHeaderFk = targetheaderId;
										UpdatePrcAssignmentItemWithBoqItem(boqItemLinkedEstimate.BoQHeaderId, boqItemLinkedEstimate.BoQItemId, boqMap, boqWicMap, sourceBoqItemIdData, isFromProject, ref prcItemAssignment, isCreate);
										if (isCreate)
										{
											prcItemsAssignmentForCreate.Add(prcItemAssignment);

										}
										else
										{
											prcItemAssignmentToUpdate.Add(prcItemAssignment);
										}
										var newChildren = CreateChildrenItemAssignments(prcItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
										if (newChildren != null && newChildren.Any())
										{
											if (!newChildrenMap.ContainsKey(prcItemAssignment))
											{
												newChildrenMap.Add(prcItemAssignment, newChildren);
											}
											prcItemsAssignmentForCreate.AddRange(newChildren);
										}
									}
								}
							}
							else
							{
								var isCreate = false;
								PrcItemAssignmentEntity prcItemAssignment = GetValueFromDictionary(itemAssignmentEstimateMap, boqItemLinkedEstimate.Id, boqItemLinkedEstimate.EstHeaderFk)
									.FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.BoqHeaderFk.HasValue && !e.PrcItemFk.HasValue); // itemAssignmentsExist.FirstOrDefault(e =>
																																										  //e.EstHeaderFk == boqItemLinkedEstimate.EstHeaderFk &&
																																										  //e.EstLineItemFk == boqItemLinkedEstimate.Id && !e.EstResourceFk.HasValue && !e.BoqHeaderFk.HasValue && !e.PrcItemFk.HasValue);
								if (prcItemAssignment == null)
								{
									prcItemAssignment = new PrcItemAssignmentEntity();
									prcItemAssignment.EstHeaderFk = boqItemLinkedEstimate.EstHeaderFk;
									prcItemAssignment.EstLineItemFk = boqItemLinkedEstimate.Id;
									prcItemAssignment.PrcPackageFk = context.PackageId;
									isCreate = true;
								}
								if (sourceToTargetIdMapping.ContainsKey(boqItemLinkedEstimate.BoQHeaderId))
								{
									int targetheaderId = sourceToTargetIdMapping[boqItemLinkedEstimate.BoQHeaderId];
									prcItemAssignment.BoqHeaderFk = targetheaderId;
									UpdatePrcAssignmentItemWithBoqItem(boqItemLinkedEstimate.BoQHeaderId, boqItemLinkedEstimate.BoQItemId, boqMap, boqWicMap, sourceBoqItemIdData, isFromProject, ref prcItemAssignment, isCreate);
									if (isCreate)
									{
										prcItemsAssignmentForCreate.Add(prcItemAssignment);
									}
									else
									{
										prcItemAssignmentToUpdate.Add(prcItemAssignment);
									}
									var newChildren = CreateChildrenItemAssignments(prcItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
									if (newChildren != null && newChildren.Any())
									{
										if (!newChildrenMap.ContainsKey(prcItemAssignment))
										{
											newChildrenMap.Add(prcItemAssignment, newChildren);
										}
										prcItemsAssignmentForCreate.AddRange(newChildren);
									}
								}
							}
						}
						var itemAssignIds = prcItemAssignmentLogic.GetNextIds(prcItemsAssignmentForCreate.Count).GetEnumerator();
						foreach (var item in prcItemsAssignmentForCreate)
						{
							itemAssignIds.MoveNext();
							item.Id = itemAssignIds.Current;
							if (newChildrenMap.ContainsKey(item))
							{
								newChildrenMap[item].ExtendEach(e => e.PrcItemAssignmentFk = item.Id);
							}
						}
						prcItemsAssignmentForCreate.AddRange(prcItemAssignmentToUpdate);
						prcItemAssignmentLogic.Save(prcItemsAssignmentForCreate);
					}

					if (sourceItem4CostGroupList.Any())
					{
						var sourceBoqItems = sourceItem4CostGroupList.Select(e => e.SourceBoqItem).ToList();
						var boqItem2CostGroups = new MainItem2CostGroupLogic("BOQ_ITEM2COSTGRP").GetByMainItems<IBoqItemEntity>(sourceBoqItems, e => e.Id, e => e.BoqHeaderFk);
						foreach (var item in boqItem2CostGroups)
						{
							var sourceItem4CostGrp = sourceItem4CostGroupList.FirstOrDefault(e => e.SourceBoqHeaderId == item.RootItemId && e.SourceBoqItemId == item.MainItemId);

							if (sourceItem4CostGrp != null && sourceItem4CostGrp.TargetKeys.Any())
							{
								var targets = sourceItem4CostGrp.TargetKeys;
								foreach (var target in targets)
								{
									var newItem2CostGroup = boqItem2CostGroupLogic.CreateEntity(target.Id, null);
									newItem2CostGroup.BoqHeaderFk = target.BoqHeaderId;
									newItem2CostGroup.BoqItemFk = target.Id;
									newItem2CostGroup.CostGroupCatFk = item.CostGroupCatFk;
									newItem2CostGroup.CostGroupFk = item.CostGroupFk;
									boqItem2CostGroupsToCreate.Add(newItem2CostGroup);
								}
							}
						}
					}

					CalculateTotals(package);
					boqItem2CostGroupLogic.SaveEntities(boqItem2CostGroupsToCreate);

					// package update option
					new PrcPackageUpdateOptionLogic().CreateOrSaveUpdateOption(context);

					transaction.Complete();
				}
			}
			return 1;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="boqItemLogic"></param>
		/// <param name="linkedList"></param>
		/// <param name="targetBoQHeader"></param>
		/// <param name="dragNDropInfo"></param>
		/// <returns>target boq root item</returns>
		private BoqItemEntity UpdatePackageBoqItems(
			BoqItemLogic boqItemLogic,
			IList<IEstLineItemEnhanceEntity> linkedList,
			BoqHeaderEntity targetBoQHeader,
			DragNDropInfo dragNDropInfo
		)
		{
			if (linkedList == null ||
				linkedList.CollectIds(e => e.BoqHeaderFk).Count() != 1 ||
				dragNDropInfo == null ||
				dragNDropInfo.SourceBoqItemId2NewBoqItemIdMap == null ||
				dragNDropInfo.SourceBoqItemId2NewBoqItemIdMap.IsNullOrEmpty() ||
				targetBoQHeader == null)
			{
				return null;
			}
			boqItemLogic = boqItemLogic ?? new BoqItemLogic();
			var targetBoQItems = boqItemLogic.GetBoqItemsByHeaderIds(new List<int>() { targetBoQHeader.Id }) as IList<BoqItemEntity>;
			var targetBoqItemIdData = targetBoQItems.ToDictionary(e => e.Id, e => e);

			foreach (var boqItemLinkedEstimate in linkedList)
			{
				if (boqItemLinkedEstimate == null || !dragNDropInfo.SourceBoqItemId2NewBoqItemIdMap.ContainsKey(boqItemLinkedEstimate.BoQItemId))
				{
					continue;
				}
				var updatePackageBoqItemId = dragNDropInfo.SourceBoqItemId2NewBoqItemIdMap[boqItemLinkedEstimate.BoQItemId];
				BoqItemEntity updatePackageBoqItem = targetBoqItemIdData.GetValueOrDefault(updatePackageBoqItemId);
				if (updatePackageBoqItem != null)
				{
					UpdateValuesFromLineItem(boqItemLinkedEstimate, ref updatePackageBoqItem);
				}
			}

			return targetBoQItems.FirstOrDefault(e => !e.BoqItemFk.HasValue);
		}

		private void UpdateValuesFromLineItem(IEstLineItemEnhanceEntity needCreateBoqItem, ref BoqItemEntity boqItemEntity)
		{
			if (needCreateBoqItem == null || boqItemEntity == null)
			{
				return;
			}
			var linkedItem = needCreateBoqItem;
			var costTotal = linkedItem.CostTotal;
			var budgetTotal = linkedItem.Budget;
			var isFixedBudget = linkedItem.IsFixedBudget;
			var isFixedBudgetUnit = linkedItem.IsFixedBudgetUnit;

			if (boqItemEntity.BoqLineTypeFk != (int)BoqLineType.SurchargeItem &&
				boqItemEntity.BoqLineTypeFk != (int)BoqLineType.SurchargeItem2 &&
				boqItemEntity.BoqLineTypeFk != (int)BoqLineType.SurchargeItem3 &&
				boqItemEntity.BoqLineTypeFk != (int)BoqLineType.SurchargeItem4)
			{
				boqItemEntity.Quantity = linkedItem.QuantityTarget;
				boqItemEntity.QuantityDetail = QuantityToString(boqItemEntity.Quantity);

				if (linkedItem.QuantityAdj.HasValue)
				{
					boqItemEntity.QuantityAdj = linkedItem.QuantityAdj.Value;
				}
				else
				{
					boqItemEntity.QuantityAdj = 0;
				}

				boqItemEntity.QuantityAdjDetail = QuantityToString(boqItemEntity.QuantityAdj);
				boqItemEntity.BudgetTotal = budgetTotal;
				boqItemEntity.BudgetFixedTotal = isFixedBudget;
				boqItemEntity.BudgetFixedUnit = isFixedBudgetUnit;

				if (linkedItem.QuantityTarget != 0)
				{
					boqItemEntity.PriceOc = linkedItem.BoqItemFactor != 0 ? costTotal / linkedItem.QuantityTarget / linkedItem.BoqItemFactor : costTotal / linkedItem.QuantityTarget;
					boqItemEntity.Price = boqItemEntity.PriceOc;
					boqItemEntity.CostOc = boqItemEntity.Cost = boqItemEntity.PriceOc;
					boqItemEntity.CorrectionOc = boqItemEntity.Correction = boqItemEntity.PriceOc - boqItemEntity.CostOc;
					boqItemEntity.BudgetPerUnit = budgetTotal / linkedItem.QuantityTarget;
				}
				else
				{
					boqItemEntity.PriceOc = linkedItem.BoqItemFactor != 0 ? costTotal / linkedItem.BoqItemFactor : costTotal;
					boqItemEntity.Price = costTotal;
					boqItemEntity.CostOc = boqItemEntity.Cost = costTotal;
					boqItemEntity.CorrectionOc = boqItemEntity.Correction = boqItemEntity.PriceOc - boqItemEntity.CostOc;
					boqItemEntity.BudgetPerUnit = budgetTotal;
				}
			}
			else
			{
				boqItemEntity.Cost = boqItemEntity.CostOc = boqItemEntity.CorrectionOc = boqItemEntity.Correction = 0;
				boqItemEntity.Quantity = 0;
				if (boqItemEntity.BoqLineTypeFk == (int)BoqLineType.SurchargeItem4)
				{
					boqItemEntity.Quantity = costTotal;
				}
				boqItemEntity.QuantityDetail = QuantityToString(boqItemEntity.Quantity);
			}

			if (!boqItemEntity.MdcTaxCodeFk.HasValue)
			{
				boqItemEntity.MdcTaxCodeFk = linkedItem.PackageTaxCodeFk;
			}
		}

		private BoQItemUpdate CopyBoqItems(
			BoqItemLogic boqItemLogic,
			IList<IEstLineItemEnhanceEntity> boqItemsForCreate,
			BoqHeaderEntity targetBoQHeader,
			IList<BoqItemEntity> targetBoQItemList,
			bool isFromProject,
			ref List<BoqItemInfo4CreatingBoqCostGroup> sourceItem4CostGroupList,
			IList<BoqItemEntity> sourceBoQItems = null,
			IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSave = null
			)
		{
			if (boqItemsForCreate.CollectIds(e => e.BoqHeaderFk).Count() != 1)
			{
				return null;
			}

			var blobLogic = new BlobLogic();
			var characContentLogic = new BoqCharacterContentLogic();
			var boqPriceConditionLogic = new BoqPriceconditionLogic();
			var boqSurchargedItemLogic = new BoqSurchargedItemLogic();

			int sourceBoQHeaderId = boqItemsForCreate[0].BoqHeaderFk.Value;
			int targetBoQHeaderId = targetBoQHeader.Id;
			if (sourceBoQItems == null)
			{
				sourceBoQItems = boqItemLogic.GetBoqItemsByHeaderIds(new List<int>() { sourceBoQHeaderId }) as IList<BoqItemEntity>;
			}
			var boqIdentifierInstance = IdentifierFactory.Create<BoqItemEntity>("Id", "BoqHeaderFk");
			BoqItemEntity sourceRootBoQItem = sourceBoQItems.Where(e => e.BoqItemFk == null).ToList()[0];
			var sourceBoqItemIdData = sourceBoQItems.ToDictionary(e => e.Id, e => e);
			//IList<BoqItemEntity> addBoQItems = new List<BoqItemEntity>();
			var addBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var blobIds = new List<int>();
			var characContentIds = new List<int>();
			var allAddBoqItemIdData = new Dictionary<int, BoqItemEntity>();
			var priceConditionBoqIdData = new List<RVPC.IdentificationData>();
			var surchargeItemBoqIdData = new List<RVPC.IdentificationData>();
			var allAddBoqParentIdData = new Dictionary<int, List<BoqItemEntity>>();
			Dictionary<string, List<BoqItemEntity>> targetBoqItemsRefMap = new Dictionary<string, List<BoqItemEntity>>();
			Dictionary<RVPC.IdentificationData, List<BoqItemEntity>> targetBoqItemsWicIdMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();
			var targetBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var boqMapForCreate = new Dictionary<RVPC.IdentificationData, IEstLineItemEnhanceEntity>();

			if (targetBoQItemList != null && targetBoQItemList.Any())
			{
				foreach (var boqItem in targetBoQItemList)
				{
					if (isFromProject)
					{
						if (!string.IsNullOrEmpty(boqItem.Reference))
						{
							if (!targetBoqItemsRefMap.ContainsKey(boqItem.Reference))
							{
								targetBoqItemsRefMap.Add(boqItem.Reference, new List<BoqItemEntity>() { boqItem });
							}
							else
							{
								targetBoqItemsRefMap[boqItem.Reference].Add(boqItem);
							}
						}
					}
					else
					{
						if (boqItem.BoqItemWicBoqFk.HasValue && boqItem.BoqItemWicItemFk.HasValue)
						{
							var wicId = new RVPC.IdentificationData() { Id = boqItem.BoqItemWicItemFk.Value, PKey1 = boqItem.BoqItemWicBoqFk.Value };
							List<BoqItemEntity> wics = null;
							if (!targetBoqItemsWicIdMap.TryGetValue(wicId, out wics))
							{
								targetBoqItemsWicIdMap.Add(wicId, new List<BoqItemEntity>() { boqItem });
							}
							else
							{
								wics.Add(boqItem);
							}
						}
					}

					var targetId = new RVPC.IdentificationData() { Id = boqItem.Id, PKey1 = boqItem.BoqHeaderFk };
					BoqItemEntity target = null;
					if (!targetBoqMap.TryGetValue(targetId, out target))
					{
						targetBoqMap.Add(targetId, boqItem);
					}

					//if (boqItem.BoqItemFk.HasValue)
					//{
					//	var parentId = new RVPC.IdentificationData() { Id = boqItem.BoqItemFk.Value, PKey1 = boqItem.BoqHeaderFk };
					//	List<BoqItemEntity> targetChildren = null;
					//	if (!targetBoqChildrenMap.TryGetValue(parentId, out targetChildren))
					//	{
					//		targetBoqChildrenMap.Add(parentId, new List<BoqItemEntity>() { boqItem });
					//	}
					//	else
					//	{
					//		targetChildren.Add(boqItem);
					//	}
					//}
				}
			}

			foreach (var boqItemLinkedEstimate in boqItemsForCreate)
			{
				if (!sourceBoqItemIdData.ContainsKey(boqItemLinkedEstimate.BoQItemId))
				{
					continue;
				}

				BoqItemEntity currentBoqItem = sourceBoqItemIdData[boqItemLinkedEstimate.BoQItemId];

				var boqId = new RVPC.IdentificationData() { Id = boqItemLinkedEstimate.BoQItemId, PKey1 = boqItemLinkedEstimate.BoQHeaderId };
				
				if (!boqMapForCreate.TryGetValue(boqId, out var boq))
				{
					boqMapForCreate.Add(boqId, boqItemLinkedEstimate);
				}

				while (currentBoqItem != null)
				{
					var curBoqId = new RVPC.IdentificationData() { Id = currentBoqItem.Id, PKey1 = currentBoqItem.BoqHeaderFk };
					BoqItemEntity curBoq = null;
					if (!addBoqMap.TryGetValue(curBoqId, out curBoq)) //  !addBoQItems.Contains(currentBoqItem))
					{
						//addBoQItems.Add(currentBoqItem);
						addBoqMap.Add(curBoqId, currentBoqItem);

						if (currentBoqItem.BasBlobsSpecificationFk.HasValue && !blobIds.Contains(currentBoqItem.BasBlobsSpecificationFk.Value))
						{
							blobIds.Add(currentBoqItem.BasBlobsSpecificationFk.Value);
						}
						if (currentBoqItem.BoqCharacterContentPrjFk.HasValue && !characContentIds.Contains(currentBoqItem.BoqCharacterContentPrjFk.Value))
						{
							characContentIds.Add(currentBoqItem.BoqCharacterContentPrjFk.Value);
						}
						if (currentBoqItem.BoqCharacterContentWorkFk.HasValue && !characContentIds.Contains(currentBoqItem.BoqCharacterContentWorkFk.Value))
						{
							characContentIds.Add(currentBoqItem.BoqCharacterContentWorkFk.Value);
						}
						var idData = boqIdentifierInstance.Value.GetEntityIdentification(currentBoqItem);
						if (!allAddBoqItemIdData.ContainsKey(currentBoqItem.Id))
						{
							allAddBoqItemIdData.Add(currentBoqItem.Id, currentBoqItem);

							if (currentBoqItem.PrcPriceConditionFk.HasValue)
							{
								priceConditionBoqIdData.Add(idData);
							}

							surchargeItemBoqIdData.Add(idData);
						}

						if (currentBoqItem.BoqItemFk.HasValue)
						{
							if (!allAddBoqParentIdData.ContainsKey(currentBoqItem.BoqItemFk.Value))
							{
								allAddBoqParentIdData.Add(currentBoqItem.BoqItemFk.Value, new List<BoqItemEntity>() { currentBoqItem });
							}
							else
							{
								allAddBoqParentIdData[currentBoqItem.BoqItemFk.Value].Add(currentBoqItem);
							}
						}
					}

					currentBoqItem = currentBoqItem.BoqItemParent;
				}
			}

			if (nodeBoqItemsToSave != null && nodeBoqItemsToSave.Any())
			{
				foreach (var nodeItem in nodeBoqItemsToSave)
				{
					if (!sourceBoqItemIdData.ContainsKey(nodeItem.Id))
					{
						continue;
					}

					var currentBoqItem = sourceBoqItemIdData[nodeItem.Id];

					while (currentBoqItem != null)
					{
						var curBoqId = new RVPC.IdentificationData() { Id = currentBoqItem.Id, PKey1 = currentBoqItem.BoqHeaderFk };
						BoqItemEntity curBoq = null;
						if (!addBoqMap.TryGetValue(curBoqId, out curBoq))  // if (!addBoQItems.Contains(currentBoqItem))
						{
							//addBoQItems.Add(currentBoqItem);
							addBoqMap.Add(curBoqId, currentBoqItem);

							if (currentBoqItem.BasBlobsSpecificationFk.HasValue && !blobIds.Contains(currentBoqItem.BasBlobsSpecificationFk.Value))
							{
								blobIds.Add(currentBoqItem.BasBlobsSpecificationFk.Value);
							}
							if (currentBoqItem.BoqCharacterContentPrjFk.HasValue && !characContentIds.Contains(currentBoqItem.BoqCharacterContentPrjFk.Value))
							{
								characContentIds.Add(currentBoqItem.BoqCharacterContentPrjFk.Value);
							}
							if (currentBoqItem.BoqCharacterContentWorkFk.HasValue && !characContentIds.Contains(currentBoqItem.BoqCharacterContentWorkFk.Value))
							{
								characContentIds.Add(currentBoqItem.BoqCharacterContentWorkFk.Value);
							}

							if (!allAddBoqItemIdData.ContainsKey(currentBoqItem.Id))
							{
								allAddBoqItemIdData.Add(currentBoqItem.Id, currentBoqItem);
							}

							if (currentBoqItem.BoqItemFk.HasValue)
							{
								if (!allAddBoqParentIdData.ContainsKey(currentBoqItem.BoqItemFk.Value))
								{
									allAddBoqParentIdData.Add(currentBoqItem.BoqItemFk.Value, new List<BoqItemEntity>() { currentBoqItem });
								}
								else
								{
									allAddBoqParentIdData[currentBoqItem.BoqItemFk.Value].Add(currentBoqItem);
								}
							}
						}

						currentBoqItem = currentBoqItem.BoqItemParent;
					}
				}
			}

			var blobsToCopy = blobLogic.GetBatchList(blobIds);
			var blobIdData = blobsToCopy.ToDictionary(e => e.Id, e => e);
			var blobIdEnumerator = blobLogic.GetNewBlobIds(blobsToCopy.Count()).GetEnumerator();
			foreach (var blob in blobsToCopy)
			{
				blobIdEnumerator.MoveNext();
				blob.Id = blobIdEnumerator.Current;
				blob.Version = 0;
			}

			var characContentToCopy = characContentLogic.GetBatchList(characContentIds);
			var characContentIdData = characContentToCopy.ToDictionary(e => e.Id, e => e);
			var contentIdEnumerator = characContentLogic.GetNextIds(characContentToCopy.Count()).GetEnumerator();
			foreach (var content in characContentToCopy)
			{
				contentIdEnumerator.MoveNext();
				content.Id = contentIdEnumerator.Current;
				content.Version = 0;
			}

			var priceConditionToCopy = boqPriceConditionLogic.GetBatchListByBoqIdData(priceConditionBoqIdData);

			var priceConditionIdData = new Dictionary<int, List<BoqPriceconditionEntity>>();

			if (priceConditionToCopy.Any())
			{
				var conditionIdEnumerator = boqPriceConditionLogic.GetNewIds(priceConditionToCopy.Count()).GetEnumerator();
				foreach (var condition in priceConditionToCopy)
				{
					conditionIdEnumerator.MoveNext();
					condition.Id = conditionIdEnumerator.Current;
					condition.Version = 0;

					if (!priceConditionIdData.ContainsKey(condition.BoqItemFk))
					{
						priceConditionIdData.Add(condition.BoqItemFk, new List<BoqPriceconditionEntity>() { condition });
					}
					else
					{
						priceConditionIdData[condition.BoqItemFk].Add(condition);
					}
				}
			}

			BoqItemEntity rootBoQItemEntity = addBoqMap.Values.Single(e => !e.BoqItemFk.HasValue); // addBoQItems.Single(e => e.BoqItemFk == null);
																																// List<BoqItemEntity> targetUpdateBoQItems = new List<BoqItemEntity>();
			var targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			Dictionary<int, int> targetBoQFks = new Dictionary<int, int>();
			List<List<BoqItemEntity>> boqItemsToHandle = new List<List<BoqItemEntity>>() { new List<BoqItemEntity>() { rootBoQItemEntity } };

			var boqItemIdEnumerator = boqItemLogic.GetNextIds(addBoqMap.Count).GetEnumerator();

			while (boqItemsToHandle.Count > 0)
			{
				var tempToHandle = new List<List<BoqItemEntity>>();
				foreach (var itemsToHandle in boqItemsToHandle)
				{
					BoqItemEntity lastItem = null;
					foreach (var currentBoQItem in itemsToHandle)
					{
						BoqItemEntity targetParentBoqItem = null;
						if (currentBoQItem.BoqItemParent != null && targetBoQFks.ContainsKey(currentBoQItem.BoqItemParent.Id))
						{
							var targetBoqItem = GetValueFromDictionary(targetUpdateBoqMap, targetBoQFks[currentBoQItem.BoqItemParent.Id], targetBoQHeaderId);
							if (targetBoqItem == null)
							{
								targetBoqItem = GetValueFromDictionary(targetBoqMap, targetBoQFks[currentBoQItem.BoqItemParent.Id], targetBoQHeaderId);
							}
							if (targetBoqItem != null)
							{
								targetParentBoqItem = targetBoqItem;
							}
						}

						IList<BoqItemEntity> existTargetBoqItem = null;
						if (targetBoQItemList != null)
						{
							if (isFromProject)
							{
								existTargetBoqItem = GetValueFromDictionary(targetBoqItemsRefMap, currentBoQItem.Reference).Where(e => e.BoqLineTypeFk == currentBoQItem.BoqLineTypeFk).ToList(); // targetBoQItemList.Where(e => !string.IsNullOrEmpty(e.Reference) && e.Reference.Equals(currentBoQItem.Reference, StringComparison.CurrentCultureIgnoreCase) && e.BoqLineTypeFk == currentBoQItem.BoqLineTypeFk).ToList();//targetBoQItemList.Where(e => e.BoqItemPrjItemFk == currentBoQItem.Id && e.BoqLineTypeFk == currentBoQItem.BoqLineTypeFk).ToList();
							}
							else
							{
								existTargetBoqItem = GetValueFromDictionary(targetBoqItemsWicIdMap, currentBoQItem.Id, currentBoQItem.BoqHeaderFk).Where(e => e.BoqLineTypeFk == currentBoQItem.BoqLineTypeFk).ToList(); //  targetBoQItemList.Where(e => e.BoqItemWicItemFk == currentBoQItem.Id && e.BoqLineTypeFk == currentBoQItem.BoqLineTypeFk).ToList();
							}
						}

						if (existTargetBoqItem == null || !existTargetBoqItem.Any())
						{
							BoqItemEntity newBoQItemEntity = CopyBoqItem2(currentBoQItem, targetBoQHeaderId, targetParentBoqItem, isFromProject);
							boqItemIdEnumerator.MoveNext();
							newBoQItemEntity.Id = boqItemIdEnumerator.Current;

							if (currentBoQItem.BasBlobsSpecificationFk.HasValue && blobIdData.ContainsKey(currentBoQItem.BasBlobsSpecificationFk.Value))
							{
								var blob = blobIdData[currentBoQItem.BasBlobsSpecificationFk.Value];
								newBoQItemEntity.BasBlobsSpecificationFk = blob.Id;
							}

							if (currentBoQItem.BoqCharacterContentPrjFk.HasValue && characContentIdData.ContainsKey(currentBoQItem.BoqCharacterContentPrjFk.Value))
							{
								var content = characContentIdData[currentBoQItem.BoqCharacterContentPrjFk.Value];
								newBoQItemEntity.BoqCharacterContentPrjFk = content.Id;
							}

							if (currentBoQItem.BoqCharacterContentWorkFk.HasValue && characContentIdData.ContainsKey(currentBoQItem.BoqCharacterContentWorkFk.Value))
							{
								var content = characContentIdData[currentBoQItem.BoqCharacterContentWorkFk.Value];
								newBoQItemEntity.BoqCharacterContentWorkFk = content.Id;
							}

							if (currentBoQItem.PrcPriceConditionFk.HasValue && priceConditionIdData.ContainsKey(currentBoQItem.Id))
							{
								var conditions = priceConditionIdData[currentBoQItem.Id];

								foreach (var condition in conditions)
								{
									condition.BoqHeaderFk = newBoQItemEntity.BoqHeaderFk;
									condition.BoqItemFk = newBoQItemEntity.Id;
								}
							}

							if (currentBoQItem.BoqLineTypeFk == (int)BoqLineType.Note ||
								currentBoQItem.BoqLineTypeFk == (int)BoqLineType.DesignDescription ||
								currentBoQItem.BoqLineTypeFk == (int)BoqLineType.TextElement ||
								currentBoQItem.BoqLineTypeFk == (int)BoqLineType.SubDescription)
							{
								newBoQItemEntity.BoqItemBasisFk = lastItem != null ? lastItem.Id : newBoQItemEntity.BoqItemFk;
							}

							lastItem = newBoQItemEntity;

							var found = sourceItem4CostGroupList.FirstOrDefault(e => e.SourceBoqHeaderId == currentBoQItem.BoqHeaderFk && e.SourceBoqItemId == currentBoQItem.Id);
							if (found == null)
							{
								var sourceItem4CostGrp = new BoqItemInfo4CreatingBoqCostGroup();
								sourceItem4CostGrp.SourceBoqItem = currentBoQItem;
								sourceItem4CostGrp.SourceBoqHeaderId = currentBoQItem.BoqHeaderFk;
								sourceItem4CostGrp.SourceBoqItemId = currentBoQItem.Id;
								sourceItem4CostGrp.TargetKeys = new List<BoqItemKey>() { new BoqItemKey() { Id = newBoQItemEntity.Id, BoqHeaderId = newBoQItemEntity.BoqHeaderFk } };
								sourceItem4CostGroupList.Add(sourceItem4CostGrp);
							}
							else
							{
								found.TargetKeys = found.TargetKeys.ConcatOne(new BoqItemKey() { Id = newBoQItemEntity.Id, BoqHeaderId = newBoQItemEntity.BoqHeaderFk });
							}

							IEstLineItemEnhanceEntity needCreateBoqItem = GetValueFromDictionary(boqMapForCreate, currentBoQItem.Id, currentBoQItem.BoqHeaderFk); // boqItemsForCreate.Where(e => e.BoqItemFk.Value == currentBoQItem.Id).ToList();
							UpdateValuesFromLineItem(needCreateBoqItem, ref newBoQItemEntity);

							targetBoQFks.Add(currentBoQItem.Id, newBoQItemEntity.Id);

							//targetUpdateBoQItems.Add(newBoQItemEntity);
							var updateId = new RVPC.IdentificationData() { Id = newBoQItemEntity.Id, PKey1 = newBoQItemEntity.BoqHeaderFk };
							BoqItemEntity update = null;
							if (!targetUpdateBoqMap.TryGetValue(updateId, out update))
							{
								targetUpdateBoqMap.Add(updateId, newBoQItemEntity);
							}
						}
						else
						{
							BoqItemEntity existBoqItem = existTargetBoqItem[0];
							var updateId = new RVPC.IdentificationData() { Id = existBoqItem.Id, PKey1 = existBoqItem.BoqHeaderFk };
							BoqItemEntity update = null;
							if (!targetUpdateBoqMap.TryGetValue(updateId, out update))
							{
								targetUpdateBoqMap.Add(updateId, existBoqItem);
								targetBoQFks.Add(currentBoQItem.Id, existBoqItem.Id);
							}
							//if (!targetUpdateBoQItems.Exists(p => p.Id == existBoqItem.Id))
							//{
							//	targetUpdateBoQItems.Add(existBoqItem);
							//	targetBoQFks.Add(currentBoQItem.Id, existBoqItem.Id);
							//}
						}

						if (allAddBoqParentIdData.ContainsKey(currentBoQItem.Id))
						{
							List<BoqItemEntity> tempBoqItem = allAddBoqParentIdData[currentBoQItem.Id];
							var tempBoqItemMap = tempBoqItem.ToDictionary(e => e.Id, e => e);
							var tempChildren = boqItemLogic.GetSortedChildren(currentBoQItem, null, null, true);
							var orderedBoqItems = new List<BoqItemEntity>();
							foreach (var child in tempChildren)
							{
								if (tempBoqItemMap.ContainsKey(child.Id))
								{
									orderedBoqItems.Add(child);
								}
							}
							allAddBoqParentIdData[currentBoQItem.Id] = orderedBoqItems;
							tempToHandle.Add(orderedBoqItems);
						}
					}
				}
				boqItemsToHandle = tempToHandle;
			}

			Func<int, int?> getTargetSurchargedItemFk = sourceSurchargedItemFk =>
			{
				if (sourceBoQItems == null || !sourceBoQItems.Any() || targetBoQItemList == null || !targetBoQItemList.Any() || !sourceBoqItemIdData.ContainsKey(sourceSurchargedItemFk))
				{
					return null;
				}

				var sourceBoqItem = sourceBoqItemIdData[sourceSurchargedItemFk];
				BoqItemEntity targetBoqItem = null;
				if (sourceBoqItem != null)
				{
					if (targetBoQItemList != null)
					{
						if (isFromProject)
						{
							targetBoqItem = GetValueFromDictionary(targetBoqItemsRefMap, sourceBoqItem.Reference).FirstOrDefault(e => e.BoqLineTypeFk == sourceBoqItem.BoqLineTypeFk); //targetBoQItemList.FirstOrDefault(e => !string.IsNullOrEmpty(e.Reference) && e.Reference.Equals(sourceBoqItem.Reference, StringComparison.CurrentCultureIgnoreCase) && e.BoqLineTypeFk == sourceBoqItem.BoqLineTypeFk);
						}
						else
						{
							targetBoqItem = GetValueFromDictionary(targetBoqItemsWicIdMap, sourceBoqItem.Id, sourceBoqItem.BoqHeaderFk).FirstOrDefault(e => e.BoqLineTypeFk == sourceBoqItem.BoqLineTypeFk); // targetBoQItemList.FirstOrDefault(e => e.BoqItemWicItemFk == sourceBoqItem.Id && e.BoqLineTypeFk == sourceBoqItem.BoqLineTypeFk);
						}
					}
				}
				return targetBoqItem != null ? targetBoqItem.Id : new Nullable<int>();
			};

			var targetUpdateBoQItems = targetUpdateBoqMap.Values.ToList();
			IList<BoqSurchargedItemEntity> updateBoqSurchargedItems = CopySurchargeItems(
																							sourceRootBoQItem.BoqHeaderFk,
																							targetBoQHeaderId,
																							targetBoQFks,
																							sourceBoQItems,
																							targetUpdateBoQItems,
																							isFromProject,
																							getTargetSurchargedItemFk
																						);

			// save blob and characteristic content
			blobLogic.SaveInsertedBlobs(blobsToCopy);
			characContentLogic.BulkInsertList(characContentToCopy);
			BoQItemUpdate boqItemUpdate = new BoQItemUpdate()
			{
				TargetUpdateBoQItems = targetUpdateBoQItems,
				UpdateBoqSurchargedItems = updateBoqSurchargedItems,
				UpdateBoqPriceConditions = priceConditionToCopy.ToList()
			};

			return boqItemUpdate;
		}

		/// <summary>
		/// Sync Boq Items
		/// </summary>
		/// <param name="dbContext"></param>
		/// <param name="boqItemLogic"></param>
		/// <param name="package"></param>
		/// <param name="targetBoqHeader"></param>
		/// <param name="sourceBoQHeaderId"></param>
		/// <param name="targetBoQHeaderId"></param>
		/// <param name="linkedList"></param>
		/// <param name="lineItems4CalQuantity"></param>
		/// <param name="sourceItem4CostGroupList"></param>
		/// <param name="context"></param>
		/// <param name="itemAssignmentBoqMap"></param>
		/// <param name="itemAssignmentChildrenMap"></param>
		/// <param name="estimate2ItemAssignmentMap"></param>
		/// <param name="nodeBoqItemsToSave"></param>
		/// <returns></returns>
		private IList<BoqItemEntity> SyncBoqItems(
			DbContext dbContext,
			BoqItemLogic boqItemLogic,
			PrcPackageEntity package,
			BoqHeaderEntity targetBoqHeader,
			Int32 sourceBoQHeaderId,
			Int32 targetBoQHeaderId,
			IList<IEstLineItemEnhanceEntity> linkedList,
			//IEnumerable<PrcItemAssignmentEntity> assignments4CalQuantity,
			IEnumerable<IEstLineItemEnhanceEntity> lineItems4CalQuantity,
			ref List<BoqItemInfo4CreatingBoqCostGroup> sourceItem4CostGroupList,
			CreateOrUpdatePackageBoqContext context,
			IDictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>> itemAssignmentBoqMap,
			IDictionary<int, List<PrcItemAssignmentEntity>> itemAssignmentChildrenMap,
			IDictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>> estimate2ItemAssignmentMap,
			IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSave = null
			)
		{
			CheckContext(context);

			var isFromProject = context.Source == UpdateOptionForBoq.Criteria.ProjectBoq;
			IList<BoqItemEntity> sourceBoQItems = boqItemLogic.GetBoqItemsByHeaderIds(new List<int>() { sourceBoQHeaderId }) as IList<BoqItemEntity>;
			IList<BoqItemEntity> targetBoQItems = boqItemLogic.GetBoqItemsByHeaderIds(new List<int>() { targetBoQHeaderId }) as IList<BoqItemEntity>;

			IDictionary<RVPC.IdentificationData, BoqItemEntity> updateBoqItemMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			IDictionary<RVPC.IdentificationData, List<BoqItemEntity>> updateBoqChildrenMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();
			// List<BoqItemEntity> updateBoQItems = new List<BoqItemEntity>();
			Stack<BoqItemEntity> sourceBoQItemsRecursivePath = new Stack<BoqItemEntity>();

			IList<IEstLineItemEnhanceEntity> needCreateBoQLinkedEstimate = new List<IEstLineItemEnhanceEntity>();
			Dictionary<RVPC.IdentificationData, BoqItemEntity> sourceBoqMap = sourceBoQItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }, e => e);
			Dictionary<string, List<BoqItemEntity>> targetBoqItemsRefMap = new Dictionary<string, List<BoqItemEntity>>();
			Dictionary<RVPC.IdentificationData, List<BoqItemEntity>> targetBoqItemsWicIdMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();
			Dictionary<RVPC.IdentificationData, List<BoqItemEntity>> estimate2TargetBoqItemsMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();
			var targetBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var targetBoqChildrenMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();

			foreach (var boqItem in targetBoQItems)
			{
				if (isFromProject)
				{
					if (!string.IsNullOrEmpty(boqItem.Reference))
					{
						if (!targetBoqItemsRefMap.ContainsKey(boqItem.Reference))
						{
							targetBoqItemsRefMap.Add(boqItem.Reference, new List<BoqItemEntity>() { boqItem });
						}
						else
						{
							targetBoqItemsRefMap[boqItem.Reference].Add(boqItem);
						}
					}
				}
				else
				{
					if (boqItem.BoqItemWicBoqFk.HasValue && boqItem.BoqItemWicItemFk.HasValue)
					{
						var wicId = new RVPC.IdentificationData() { Id = boqItem.BoqItemWicItemFk.Value, PKey1 = boqItem.BoqItemWicBoqFk.Value };
						List<BoqItemEntity> wics = null;
						if (!targetBoqItemsWicIdMap.TryGetValue(wicId, out wics))
						{
							targetBoqItemsWicIdMap.Add(wicId, new List<BoqItemEntity>() { boqItem });
						}
						else
						{
							wics.Add(boqItem);
						}
					}
				}

				var targetId = new RVPC.IdentificationData() { Id = boqItem.Id, PKey1 = boqItem.BoqHeaderFk };
				BoqItemEntity target = null;
				if (!targetBoqMap.TryGetValue(targetId, out target))
				{
					targetBoqMap.Add(targetId, boqItem);
				}

				if (boqItem.BoqItemFk.HasValue)
				{
					var parentId = new RVPC.IdentificationData() { Id = boqItem.BoqItemFk.Value, PKey1 = boqItem.BoqHeaderFk };
					List<BoqItemEntity> targetChildren = null;
					if (!targetBoqChildrenMap.TryGetValue(parentId, out targetChildren))
					{
						targetBoqChildrenMap.Add(parentId, new List<BoqItemEntity>() { boqItem });
					}
					else
					{
						targetChildren.Add(boqItem);
					}
				}
			}

			var itemAssignmentList = estimate2ItemAssignmentMap != null && estimate2ItemAssignmentMap.Any() ? estimate2ItemAssignmentMap.SelectMany(e => e.Value) : new List<PrcItemAssignmentEntity>();
			foreach (var itemAssign in itemAssignmentList)
			{
				if (!itemAssign.BoqHeaderFk.HasValue || !itemAssign.BoqItemFk.HasValue || itemAssign.PrcItemAssignmentFk.HasValue)
				{
					continue;
				}

				var tempBoq = new RVPC.IdentificationData()
				{
					Id = itemAssign.BoqItemFk.Value,
					PKey1 = itemAssign.BoqHeaderFk.Value
				};

				if (!targetBoqMap.TryGetValue(tempBoq, out var boq))
				{
					continue;
				}

				var tempEstimate = new RVPC.IdentificationData()
				{
					Id = itemAssign.EstLineItemFk,
					PKey1 = itemAssign.EstHeaderFk
				};

				if (estimate2TargetBoqItemsMap.TryGetValue(tempEstimate, out var boqItems))
				{
					boqItems.Add(boq);
				}
				else
				{
					estimate2TargetBoqItemsMap.Add(tempEstimate, new List<BoqItemEntity>() { boq });
				}
			}


			//if (assignments4CalQuantity != null && assignments4CalQuantity.Any())
			//{
			//	foreach (var itemAssign in assignments4CalQuantity)
			//	{
			//		if (!itemAssign.BoqHeaderFk.HasValue || !itemAssign.BoqItemFk.HasValue)
			//		{
			//			continue;
			//		}
			//		var boqId = new RVPC.IdentificationData() { Id = itemAssign.BoqItemFk.Value, PKey1 = itemAssign.BoqHeaderFk.Value };
			//		List<PrcItemAssignmentEntity> list = null;
			//		if (!itemAssignment4CalQtyBoqMap.TryGetValue(boqId, out list))
			//		{
			//			itemAssignment4CalQtyBoqMap.Add(boqId, new List<PrcItemAssignmentEntity>() { itemAssign });
			//		}
			//		else
			//		{
			//			list.Add(itemAssign);
			//		}
			//	}
			//}

			foreach (IEstLineItemEnhanceEntity boqItemlinked in linkedList)
			{
				bool needToUpdate = false;
				BoqItemEntity sourceBoQItem = null;
				if (!boqItemlinked.BoqItemFk.HasValue || !boqItemlinked.BoqHeaderFk.HasValue)
				{
					continue;
				}

				sourceBoQItem = GetValueFromDictionary(sourceBoqMap, boqItemlinked.BoqItemFk.Value, boqItemlinked.BoqHeaderFk.Value);
				if (sourceBoQItem != null)
				{
					BoqItemEntity targetBoQItem = null; //new BoqItemEntity();

					if (isFromProject)
					{
						var tempEstiamte = new RVPC.IdentificationData()
						{
							Id = boqItemlinked.Id,
							PKey1 = boqItemlinked.EstHeaderFk
						};

						if (context.IsUpdateOperation && estimate2TargetBoqItemsMap.TryGetValue(tempEstiamte, out var targetBoQItemList))
						{
							targetBoQItem = targetBoQItemList.FirstOrDefault(boqItem => boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position ||
								boqItem.BoqLineTypeFk == (int)BoqLineType.SurchargeItem4);
						}

						if (!context.IsUpdateOperation)
						{
							targetBoQItem = GetValueFromDictionary(targetBoqItemsRefMap, sourceBoQItem.Reference)
							.FirstOrDefault(boqItem => boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position ||
							boqItem.BoqLineTypeFk == (int)BoqLineType.SurchargeItem4);
						}
					}
					else
					{
						targetBoQItem = GetValueFromDictionary(targetBoqItemsWicIdMap, sourceBoQItem.Id, sourceBoQItem.BoqHeaderFk)
								.FirstOrDefault(boqItem => boqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position ||
								boqItem.BoqLineTypeFk == (int)BoqLineType.SurchargeItem4);
					}
					if (targetBoQItem != null)
					{
						decimal lineItemPrice = 0;
						decimal budgetUnit = 0;
						decimal quantity = context.DoesUpdateBudgetOnly4AssignmentExist ? targetBoQItem.Quantity : boqItemlinked.QuantityTarget;
						decimal linkedCostTotal = boqItemlinked.CostTotal;
						decimal costTotal = linkedCostTotal;
						decimal linkedBudgetTotal = boqItemlinked.Budget;
						bool isFixedBudget = boqItemlinked.IsFixedBudget;
						bool isFixedBudgetUnit = boqItemlinked.IsFixedBudgetUnit;
						decimal budgetTotal = boqItemlinked.Budget;
						decimal factor = boqItemlinked.BoqItemFactor;
						var itemAssigns = GetValueFromDictionary(itemAssignmentBoqMap, targetBoQItem.Id, targetBoQItem.BoqHeaderFk);
						var itemAssign = itemAssigns.FirstOrDefault();
						var subItemAssignments = itemAssign != null ? GetValueFromDictionary(itemAssignmentChildrenMap, itemAssign.Id) : new List<PrcItemAssignmentEntity>();
						var boqsReplacement = new List<BoqItemEntity>();

						if (!context.IsUpdateOperation && !context.DoesUpdateBudgetOnly4AssignmentExist)
						{
							costTotal = targetBoQItem.FinalpriceOc + linkedCostTotal;
							budgetTotal = targetBoQItem.BudgetTotal + linkedBudgetTotal;
							var est2LineItemMap = new Dictionary<RVPC.IdentificationData, bool>();
							foreach (var item in itemAssigns)
							{
								var temp = new RVPC.IdentificationData() { Id = item.EstLineItemFk, PKey1 = item.EstHeaderFk };
								if (!est2LineItemMap.TryGetValue(temp, out var result))
								{
									est2LineItemMap.Add(temp, true);
								}
							}
							if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemAQ ||
								context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ ||
								context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemQuantityTotal)
							{
								quantity = targetBoQItem.Quantity;
								foreach (var lineItem in lineItems4CalQuantity)
								{
									if (!est2LineItemMap.TryGetValue(new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk }, out var result))
									{
										quantity += lineItem.QuantityTarget;
									}
								}
							}
						}

						if (targetBoQItem.BoqLineTypeFk != (int)BoqLineType.SurchargeItem &&
							targetBoQItem.BoqLineTypeFk != (int)BoqLineType.SurchargeItem2 &&
							targetBoQItem.BoqLineTypeFk != (int)BoqLineType.SurchargeItem3 &&
							targetBoQItem.BoqLineTypeFk != (int)BoqLineType.SurchargeItem4)
						{
							if (quantity != 0)
							{
								lineItemPrice = factor != 0 ? costTotal / quantity / factor : costTotal / quantity;
								budgetUnit = budgetTotal / quantity;
							}
							else
							{
								lineItemPrice = factor != 0 ? costTotal / factor : costTotal;
								budgetUnit = budgetTotal;
							}

							if (!context.DoesUpdateBudgetOnly4AssignmentExist)
							{
								if (targetBoQItem.Quantity != quantity)
								{
									var proportion = targetBoQItem.Quantity != 0 ? quantity / targetBoQItem.Quantity : quantity;
									targetBoQItem.Quantity = quantity;
									targetBoQItem.QuantityDetail = QuantityToString(quantity);
									needToUpdate = true;
									foreach (var assign in subItemAssignments)
									{
										if (!assign.BoqHeaderFk.HasValue || !assign.BoqItemFk.HasValue)
										{
											continue;
										}
										BoqItemEntity replacement = GetValueFromDictionary(targetBoqMap, assign.BoqItemFk.Value, assign.BoqHeaderFk.Value);
										if (replacement == null)
										{
											continue;
										}
										replacement.Quantity *= proportion;
										replacement.QuantityDetail = QuantityToString(replacement.Quantity);
										boqsReplacement.Add(replacement);
									}
								}

								if (boqItemlinked.QuantityAdj.HasValue && targetBoQItem.QuantityAdj != boqItemlinked.QuantityAdj.Value)
								{
									targetBoQItem.QuantityAdj = boqItemlinked.QuantityAdj.Value;
									targetBoQItem.QuantityAdjDetail = QuantityToString(targetBoQItem.QuantityAdj);
									needToUpdate = true;
								}

								if (targetBoQItem.PriceOc != lineItemPrice)
								{
									targetBoQItem.PriceOc = lineItemPrice;
									targetBoQItem.Price = lineItemPrice;
									needToUpdate = true;
								}

								if (targetBoQItem.CostOc != lineItemPrice)
								{
									targetBoQItem.CostOc = targetBoQItem.Cost = lineItemPrice;
									needToUpdate = true;
								}

								if (targetBoQItem.PriceOc != lineItemPrice || targetBoQItem.CostOc != lineItemPrice)
								{
									targetBoQItem.CorrectionOc = targetBoQItem.Correction = targetBoQItem.PriceOc - targetBoQItem.CostOc;
									needToUpdate = true;
								}
							}

							if (targetBoQItem.BudgetTotal != budgetTotal)
							{
								targetBoQItem.BudgetTotal = budgetTotal;
								needToUpdate = true;
							}

							if (targetBoQItem.BudgetPerUnit != budgetUnit)
							{
								targetBoQItem.BudgetPerUnit = budgetUnit;
								needToUpdate = true;
							}
							targetBoQItem.BudgetFixedTotal = isFixedBudget;
							targetBoQItem.BudgetFixedUnit = isFixedBudgetUnit;
						}
						else
						{
							if (targetBoQItem.BoqLineTypeFk == (int)BoqLineType.SurchargeItem4)
							{
								if (!context.DoesUpdateBudgetOnly4AssignmentExist && !context.IsUpdateOperation)
								{
									costTotal = targetBoQItem.Quantity + linkedCostTotal;

									if (targetBoQItem.Quantity != costTotal)
									{
										targetBoQItem.Quantity = costTotal;
										targetBoQItem.QuantityDetail = QuantityToString(costTotal);
										needToUpdate = true;
									}
								}
							}
						}

						if (needToUpdate)
						{
							//updateBoQItems.Add(targetBoQItem);
							var itemsToUpdate = new List<BoqItemEntity>() { targetBoQItem };
							itemsToUpdate.AddRange(boqsReplacement);
							foreach (var toUpdate in itemsToUpdate)
							{
								var updateId = new RVPC.IdentificationData() { Id = toUpdate.Id, PKey1 = toUpdate.BoqHeaderFk };
								BoqItemEntity update = null;
								if (!updateBoqItemMap.TryGetValue(updateId, out update))
								{
									updateBoqItemMap.Add(updateId, toUpdate);
									if (toUpdate.BoqItemFk.HasValue)
									{
										List<BoqItemEntity> updates = null;
										var parentId = new RVPC.IdentificationData() { Id = toUpdate.BoqItemFk.Value, PKey1 = toUpdate.BoqHeaderFk };
										if (!updateBoqChildrenMap.TryGetValue(parentId, out updates))
										{
											updateBoqChildrenMap.Add(parentId, new List<BoqItemEntity>() { toUpdate });
										}
										else
										{
											updates.Add(toUpdate);
										}
									}
								}

								//need add all parent node
								BoqItemEntity currentBoQItem = toUpdate;
								while (currentBoQItem != null)
								{
									//if (!updateBoQItems.Exists(e => e.Id == currentBoQItem.Id))
									//{
									//	updateBoQItems.Add(currentBoQItem);
									//}

									var updateId2 = new RVPC.IdentificationData() { Id = currentBoQItem.Id, PKey1 = toUpdate.BoqHeaderFk };
									BoqItemEntity update2 = null;
									if (!updateBoqItemMap.TryGetValue(updateId2, out update2))
									{
										updateBoqItemMap.Add(updateId2, currentBoQItem);
										if (currentBoQItem.BoqItemFk.HasValue)
										{
											List<BoqItemEntity> updates = null;
											var parentId = new RVPC.IdentificationData() { Id = currentBoQItem.BoqItemFk.Value, PKey1 = currentBoQItem.BoqHeaderFk };
											if (!updateBoqChildrenMap.TryGetValue(parentId, out updates))
											{
												updateBoqChildrenMap.Add(parentId, new List<BoqItemEntity>() { currentBoQItem });
											}
											else
											{
												updates.Add(currentBoQItem);
											}
										}
									}

									currentBoQItem = currentBoQItem.BoqItemParent;
								}
							}
						}
					}
					else
					{
						needCreateBoQLinkedEstimate.Add(boqItemlinked);
					}
				}
			}

			Dictionary<int, int> sourceBoqItemId2TargetBoqItemId = new Dictionary<int, int>();
			IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSaveForCreate = null;

			// save the boq item with node type
			if (nodeBoqItemsToSave != null && nodeBoqItemsToSave.Any(e => e.BoqHeaderFk == sourceBoQHeaderId))
			{
				var tempNodeBoqItems = nodeBoqItemsToSave.Where(e => e.BoqHeaderFk == sourceBoQHeaderId).ToList();
				var groupBoqLinkedEst = needCreateBoQLinkedEstimate.GroupBy(e => e.BoqHeaderFk);
				var sourceLinkedBoqItems = new Dictionary<RVPC.IdentificationData, BoqItemEntity>(); // new List<BoqItemEntity>();
																																 // get the source boq item for the linked estimate
				foreach (var linkedItem in needCreateBoQLinkedEstimate)
				{
					if (!linkedItem.BoqHeaderFk.HasValue || !linkedItem.BoqItemFk.HasValue)
					{
						continue;
					}

					var sourceLinkedBoqItem = GetValueFromDictionary(sourceBoqMap, linkedItem.BoqItemFk.Value, linkedItem.BoqHeaderFk.Value); // sourceBoQItems.FirstOrDefault(e => e.BoqHeaderFk == linkedItem.BoqHeaderFk && e.Id == linkedItem.BoqItemFk);
					if (sourceLinkedBoqItem != null)
					{
						var linkedId = new RVPC.IdentificationData() { Id = sourceLinkedBoqItem.Id, PKey1 = sourceLinkedBoqItem.BoqHeaderFk };
						if (!sourceLinkedBoqItems.ContainsKey(linkedId))
						{
							sourceLinkedBoqItems.Add(linkedId, sourceLinkedBoqItem);
						}
					}
				}

				// get the source parent boq items for the node items
				var sourceParentBoqItems = new List<BoqItemEntity>();
				foreach (var nodeItem in tempNodeBoqItems)
				{
					var sourceNodeItem = GetValueFromDictionary(sourceBoqMap, nodeItem.Id, nodeItem.BoqHeaderFk); // sourceBoQItems.FirstOrDefault(e => e.Id == nodeItem.Id);
					if (sourceNodeItem == null)
					{
						continue;
					}
					var sourceParentBoqItem = sourceNodeItem.BoqItemParent;
					BoqItemEntity sourceBoqItemBasisItem = null;

					if (sourceNodeItem.BoqItemBasisFk.HasValue)
					{
						sourceBoqItemBasisItem = GetValueFromDictionary(sourceBoqMap, sourceNodeItem.BoqItemBasisFk.Value, sourceNodeItem.BoqHeaderFk);
					}
					//sourceBoQItems.FirstOrDefault(e => e.Id == sourceNodeItem.BoqItemBasisFk);

					// if the node item is not under the same division with the new boq item, create it in sync function
					if (sourceNodeItem.BoqItemFk.HasValue)
					{
						var nodeId = new RVPC.IdentificationData() { Id = sourceNodeItem.BoqItemFk.Value, PKey1 = sourceNodeItem.BoqHeaderFk };
						if (!sourceLinkedBoqItems.ContainsKey(nodeId))
						{
							while (sourceParentBoqItem != null)
							{
								if (!sourceParentBoqItems.Any(e => e.Id == sourceParentBoqItem.Id))
								{
									sourceParentBoqItems.Add(sourceParentBoqItem);
								}
								sourceParentBoqItem = sourceParentBoqItem.BoqItemParent;
							}
						}
					}

					if (sourceBoqItemBasisItem != null)
					{
						BoqItemEntity foundTargetBoqItem = null;
						if (isFromProject)
						{
							foundTargetBoqItem = GetValueFromDictionary(targetBoqItemsRefMap, sourceBoqItemBasisItem.Reference)
								.FirstOrDefault(e => e.BoqLineTypeFk == sourceBoqItemBasisItem.BoqLineTypeFk);
						}
						else
						{
							foundTargetBoqItem = GetValueFromDictionary(targetBoqItemsWicIdMap, sourceBoqItemBasisItem.Id, sourceBoqItemBasisItem.BoqHeaderFk)
								.FirstOrDefault(e => e.BoqLineTypeFk != (int)BoqLineType.Note &&
								e.BoqLineTypeFk != (int)BoqLineType.DesignDescription &&
								e.BoqLineTypeFk != (int)BoqLineType.TextElement &&
								e.BoqLineTypeFk != (int)BoqLineType.SubDescription &&
								e.BoqLineTypeFk == sourceBoqItemBasisItem.BoqLineTypeFk);
						}

						if (foundTargetBoqItem == null)
						{
							continue;
						}

						if (!sourceBoqItemId2TargetBoqItemId.ContainsKey(sourceBoqItemBasisItem.Id))
						{
							sourceBoqItemId2TargetBoqItemId.Add(sourceBoqItemBasisItem.Id, foundTargetBoqItem.Id);
						}
					}
				}

				// prepare the source parenet boq item and target parent boq item id map
				foreach (var sourceParentItem in sourceParentBoqItems)
				{
					BoqItemEntity foundTargetBoqItem = null;
					if (isFromProject)
					{
						foundTargetBoqItem = GetValueFromDictionary(targetBoqItemsRefMap, sourceParentItem.Reference)
							.FirstOrDefault(e => e.BoqLineTypeFk == sourceParentItem.BoqLineTypeFk);
					}
					else
					{
						foundTargetBoqItem = GetValueFromDictionary(targetBoqItemsWicIdMap, sourceParentItem.Id, sourceParentItem.BoqHeaderFk)
							.FirstOrDefault(e => e.BoqLineTypeFk == sourceParentItem.BoqLineTypeFk);
					}

					if (foundTargetBoqItem == null)
					{
						continue;
					}

					if (!sourceBoqItemId2TargetBoqItemId.ContainsKey(sourceParentItem.Id))
					{
						sourceBoqItemId2TargetBoqItemId.Add(sourceParentItem.Id, foundTargetBoqItem.Id);
					}
				}

				CreateNodeBoqItems(sourceBoQHeaderId, targetBoQHeaderId, isFromProject, boqItemLogic, nodeBoqItemsToSave,
					sourceBoqMap, targetBoqMap, targetBoqChildrenMap, ref updateBoqItemMap, ref updateBoqChildrenMap, ref sourceBoqItemId2TargetBoqItemId);

				var copiedNodeItemIds = sourceBoqItemId2TargetBoqItemId.Keys;
				nodeBoqItemsToSaveForCreate = nodeBoqItemsToSave.Where(e => !copiedNodeItemIds.Contains(e.Id)).ToList(); // if the node items are created in sync function, don't save them again in copy function
			}

			BoQItemUpdate boqItemUpdate = null;
			IList<BoqSurchargedItemEntity> UpdateBoqSurchargedItems = null;
			IList<BoqPriceconditionEntity> updateBoqPriceConditions = null;

			if (needCreateBoQLinkedEstimate.Count > 0 && !context.DoesUpdateBudgetOnly4AssignmentExist)
			{
				boqItemUpdate = CopyBoqItems(
												boqItemLogic,
												needCreateBoQLinkedEstimate,
												targetBoqHeader,
												targetBoQItems,
												isFromProject,
												ref sourceItem4CostGroupList,
												sourceBoQItems,
												nodeBoqItemsToSaveForCreate
											);

				foreach (BoqItemEntity boqItem in boqItemUpdate.TargetUpdateBoQItems)
				{
					var updateId = new RVPC.IdentificationData() { Id = boqItem.Id, PKey1 = boqItem.BoqHeaderFk };
					BoqItemEntity update = null;
					if (!updateBoqItemMap.TryGetValue(updateId, out update))
					{
						updateBoqItemMap.Add(updateId, boqItem);
					}
					//if (!updateBoQItems.Exists(e => e.Id == boqItem.Id))
					//{
					//	updateBoQItems.Add(boqItem);
					//}
				}
			}

			BoqItemEntity targetRootBoqitem = targetBoQItems.Where(e => e.BoqItemFk == null).ElementAt(0);
			if (boqItemUpdate != null)
			{
				UpdateBoqSurchargedItems = boqItemUpdate.UpdateBoqSurchargedItems;
				updateBoqPriceConditions = boqItemUpdate.UpdateBoqPriceConditions;
			}
			var updateBoQItems = updateBoqItemMap.Values.ToList();
			SaveBoqs(dbContext, targetBoQHeaderId, updateBoQItems, UpdateBoqSurchargedItems, updateBoqPriceConditions);
			var boqCalculateLogic = new BoqItemCalculateLogic(moduleName, package.Id, package.ExchangeRate, package.TaxCodeFk, package.BpdVatGroupFk);
			var calculationResult = boqCalculateLogic.CalculateBoqTreeAndPriceconditions(targetRootBoqitem);

			if (updateBoQItems != null && updateBoQItems.Any())
			{
				DbCompiledModel dbCompiledModel = RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder.DbModel;
				using (var boqDbContext = new DbContext(dbCompiledModel))
				{
					var helper = new BulkSaveHelper();
					var insertBoqItems = updateBoQItems.Where(e => e.Version == 0).ToList();
					var updateBoqItems = updateBoQItems.Where(e => e.Version > 0).ToList();
					helper.BulkUpdate(boqDbContext, updateBoqItems);
					helper.BulkInsert(boqDbContext, insertBoqItems);
				}
			}

			if (calculationResult != null && calculationResult.BoqPriceconditionChanged != null)
			{
				var logic = new BoqPriceconditionLogic();
				var insertConditionItems = calculationResult.BoqPriceconditionChanged.Where(e => e.Version == 0).ToList();
				var updateConditionItems = calculationResult.BoqPriceconditionChanged.Where(e => e.Version > 0).ToList();
				logic.BulkUpdateList(updateConditionItems);
				logic.BulkInsertList(insertConditionItems);
			}
			return updateBoQItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sourceBoQHeaderIds"></param>
		/// <param name="sourceToTargetIdMapping"></param>
		/// <param name="packageCurrencyFk"></param>
		/// <returns></returns>
		private IList<BoqHeaderEntity> CopyBoQHeader(IList<int> sourceBoQHeaderIds, ref Dictionary<int, int> sourceToTargetIdMapping, int packageCurrencyFk)
		{
			BoqHeaderLogic boqHeaderLogic = new BoqHeaderLogic();
			BoqItemLogic boqItemLogic = new BoqItemLogic();
			var boqItemCopyInfo = new BoqItemCopyInfo();
			boqItemCopyInfo.MaintainBaseBoqLink = false;

			IList<BoqHeaderEntity> sourceHeaderList = new BoqHeaderLogic().GetSearchList(e => sourceBoQHeaderIds.Contains(e.Id));
			IList<BoqHeaderEntity> targetBoQHeaderList = new List<BoqHeaderEntity>();
			IList<IBoqCopyData> boqCopyDatas = new List<IBoqCopyData>();

			for (int i = 0; i < sourceHeaderList.Count; i++)
			{
				BoqHeaderEntity boqHeader = sourceHeaderList[i];
				BoqItemEntity sourceRootBoqItem = boqItemLogic.GetBoqRootItemByHeaderId(boqHeader.Id);

				var boqCopyData = boqItemLogic.CopyBoqHeader(boqHeader, sourceRootBoqItem, boqItemCopyInfo);

				if (boqCopyData != null && boqCopyData.BoqHeader != null)
				{
					var targetBoqHeader = boqCopyData.BoqHeader as BoqHeaderEntity;
					targetBoqHeader.BasCurrencyFk = packageCurrencyFk;
					targetBoQHeaderList.Add(targetBoqHeader);
					sourceToTargetIdMapping.Add(sourceHeaderList[i].Id, targetBoqHeader.Id);
					boqCopyDatas.Add(boqCopyData);
				}
			}

			boqItemLogic.SaveBoqs(boqCopyDatas); // For boqCopyDatas currently only carries the copied boq header and corresponding sub entities this SaveBoqs only saves them !!

			return targetBoQHeaderList;
		}

		/// <summary>
		/// CopyBoqItem
		/// </summary>
		/// <param name="sourceBoqItem"></param>
		/// <param name="targetBoqHeaderId"></param>
		/// <param name="boqItemLogic"></param>
		/// <param name="targetParentBoqItem"></param>
		/// <param name="isFromProject"></param>
		/// <returns></returns>
		private BoqItemEntity CopyBoqItem(BoqItemEntity sourceBoqItem, int targetBoqHeaderId, BoqItemLogic boqItemLogic, BoqItemEntity targetParentBoqItem, bool isFromProject)
		{
			//copy blob
			int? targetBlobId = null;
			if (sourceBoqItem.BasBlobsSpecificationFk != null)
			{
				targetBlobId = new BlobLogic().CopyBlob((int)sourceBoqItem.BasBlobsSpecificationFk);
			}

			//copy item
			BoqItemEntity targetItem = sourceBoqItem.Clone(isFromProject);
			targetItem.BoqHeaderFk = targetBoqHeaderId;
			targetItem.Id = boqItemLogic.GetNextBoqId();
			targetItem.Version = 0;
			targetItem.BriefInfo = new DescriptionTranslateType();
			if (sourceBoqItem.BriefInfo != null)
			{
				targetItem.BriefInfo = new Platform.Common.DescriptionTranslateType(sourceBoqItem.BriefInfo.Description);

				if (sourceBoqItem.BriefInfo.VersionTr > 0)
				{
					targetItem.BriefInfo.Translated = sourceBoqItem.BriefInfo.Translated;
					targetItem.BriefInfo.Modified = true;
				}
			}

			//copy character content
			var boqCharacterContentLogic = new BoqCharacterContentLogic();
			if (targetItem.BoqCharacterContentPrjFk != null)
			{
				var newContent = boqCharacterContentLogic.Create();
				newContent.Content = boqCharacterContentLogic.GetItembyKey((int)targetItem.BoqCharacterContentPrjFk).Content;
				targetItem.BoqCharacterContentPrjFk = newContent.Id;
				boqCharacterContentLogic.Save(newContent);
			}

			if (targetItem.BoqCharacterContentWorkFk != null)
			{
				var newContent = boqCharacterContentLogic.Create();
				newContent.Content = boqCharacterContentLogic.GetItembyKey((int)targetItem.BoqCharacterContentWorkFk).Content;
				targetItem.BoqCharacterContentWorkFk = newContent.Id;
				boqCharacterContentLogic.Save(newContent);
			}

			targetItem.BasBlobsSpecificationFk = targetBlobId;

			if (!isFromProject)
			{
				targetItem.BoqItemWicBoqFk  = sourceBoqItem.BoqHeaderFk;
				targetItem.BoqItemWicItemFk = sourceBoqItem.Id;
			}
			targetItem.BoqItemReferenceFk = null;

			if (targetParentBoqItem != null)
			{
				targetItem.BoqItemFk = targetParentBoqItem.Id;

				targetItem.BoqItemParent = targetParentBoqItem;
				targetParentBoqItem.BoqItemChildren.Add(targetItem);
			}

			return targetItem;
		}

		/// <summary>
		/// CopyBoqItem
		/// </summary>
		/// <param name="sourceBoqItem"></param>
		/// <param name="targetBoqHeaderId"></param>
		/// <param name="targetParentBoqItem"></param>
		/// <param name="isFromProject"></param>
		/// <returns></returns>
		private BoqItemEntity CopyBoqItem2(BoqItemEntity sourceBoqItem, int targetBoqHeaderId, BoqItemEntity targetParentBoqItem, bool isFromProject)
		{
			//copy item
			BoqItemEntity targetItem = sourceBoqItem.Clone(isFromProject);
			targetItem.BoqHeaderFk = targetBoqHeaderId;
			targetItem.Version = 0;
			targetItem.BriefInfo = new DescriptionTranslateType();
			if (sourceBoqItem.BriefInfo != null)
			{
				targetItem.BriefInfo = new Platform.Common.DescriptionTranslateType(sourceBoqItem.BriefInfo.Description);

				if (sourceBoqItem.BriefInfo.VersionTr > 0)
				{
					targetItem.BriefInfo.Translated = sourceBoqItem.BriefInfo.Translated;
					targetItem.BriefInfo.Modified = true;
				}
			}

			if (!isFromProject)
			{
				targetItem.BoqItemWicBoqFk  = sourceBoqItem.BoqHeaderFk;
				targetItem.BoqItemWicItemFk = sourceBoqItem.Id;
			}
			targetItem.BoqItemReferenceFk = null;

			if (targetParentBoqItem != null)
			{
				targetItem.BoqItemFk = targetParentBoqItem.Id;

				targetItem.BoqItemParent = targetParentBoqItem;
				targetParentBoqItem.BoqItemChildren.Add(targetItem);
			}

			return targetItem;
		}

		/// <summary>
		/// CopySurchargeItems
		/// </summary>
		/// <param name="sourceBoqHeaderId"></param>
		/// <param name="targetHeaderId"></param>
		/// <param name="targetBoQFks"></param>
		/// <param name="sourceBoQItems"></param>
		/// <param name="updateBoQItems"></param>
		/// <param name="isFromProject"></param>
		/// <param name="getTargetSurchargedItemFk"></param>
		/// <returns></returns>
		private IList<BoqSurchargedItemEntity> CopySurchargeItems(
			int sourceBoqHeaderId,
			int targetHeaderId,
			Dictionary<int, int> targetBoQFks,
			IList<BoqItemEntity> sourceBoQItems,
			IList<BoqItemEntity> updateBoQItems,
			bool isFromProject,
			Func<int, int?> getTargetSurchargedItemFk
			)
		{
			if (targetBoQFks.Count == 0)
			{
				return new List<BoqSurchargedItemEntity>();
			}

			IEnumerable<int> targetIds;
			if (isFromProject)
			{
				IEnumerable<String> addReferences = updateBoQItems.Where(g => g.Version == 0).Select(g => g.Reference).ToList();
				targetIds = sourceBoQItems.Where(e => addReferences.Contains(e.Reference)).CollectIds(e => e.Id);
			}
			else
			{
				targetIds = updateBoQItems.Where(e => e.Version == 0).CollectIds(e => e.BoqItemWicItemFk);
			}

			var boqIdData = new List<RVPC.IdentificationData>();
			foreach (var id in targetIds)
			{
				boqIdData.Add(new RVPC.IdentificationData()
				{
					Id = id,
					PKey1 = sourceBoqHeaderId
				});
			}

			var surchargeItems = new BoqSurchargedItemLogic().GetBatchListByBoqIdData(boqIdData);

			using (DbContext dbContext = new DbContext(RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder.DbModel))
			{
				IList<BoqSurchargedItemEntity> targetSurchargedItems = new List<BoqSurchargedItemEntity>();

				foreach (var sourceSurchargeItem in surchargeItems)
				{
					if (sourceSurchargeItem.BoqSurcharedItemFk.HasValue && targetBoQFks.ContainsKey(sourceSurchargeItem.BoqItemFk))
					{
						if (!targetBoQFks.ContainsKey(sourceSurchargeItem.BoqSurcharedItemFk.Value))
						{
							if (getTargetSurchargedItemFk == null)
							{
								continue;
							}
							var targetValue = getTargetSurchargedItemFk(sourceSurchargeItem.BoqSurcharedItemFk.Value);
							if (!targetValue.HasValue)
							{
								continue;
							}
							targetBoQFks[sourceSurchargeItem.BoqSurcharedItemFk.Value] = targetValue.Value;
						}
						BoqSurchargedItemEntity targetSurchargedItem = sourceSurchargeItem.Clone() as BoqSurchargedItemEntity;
						targetSurchargedItem.BoqHeaderFk = targetHeaderId;
						targetSurchargedItem.Version = 0;
						targetSurchargedItem.BoqItemFk = targetBoQFks[targetSurchargedItem.BoqItemFk];
						targetSurchargedItem.BoqSurcharedItemFk = targetBoQFks[targetSurchargedItem.BoqSurcharedItemFk.Value];
						targetSurchargedItems.Add(targetSurchargedItem);
					}
				}

				var surchargedItemIdEnumerator = new BoqSurchargedItemLogic().GetNextIds(targetSurchargedItems.Count()).GetEnumerator();

				foreach (var surchargeditem in targetSurchargedItems)
				{
					surchargedItemIdEnumerator.MoveNext();
					surchargeditem.Id = surchargedItemIdEnumerator.Current;
				}

				return targetSurchargedItems;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dbContext"></param>
		/// <param name="boqHeaderId"></param>
		/// <param name="updateBoqItems"></param>
		/// <param name="updateBoqSurchargedItems"></param>
		/// <param name="updatePriceConditions"></param>
		private void SaveBoqs(
			DbContext dbContext,
			int boqHeaderId,
			IEnumerable<BoqItemEntity> updateBoqItems,
			IEnumerable<BoqSurchargedItemEntity> updateBoqSurchargedItems,
			IEnumerable<BoqPriceconditionEntity> updatePriceConditions
			)
		{
			if (updateBoqItems.Count() == 0)
			{
				return;
			}

			DbCompiledModel dbCompiledModel = RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder.DbModel;
			var insertBoqItems = updateBoqItems.Where(e => e.Version == 0).ToList();
			var updateBoqItems2 = updateBoqItems.Where(e => e.Version > 0).ToList();
			var entitiesToSaveTranslation = insertBoqItems.Where(e => e.BriefInfo != null && e.BriefInfo.Modified).ToList();
			if (entitiesToSaveTranslation != null && entitiesToSaveTranslation.Any())
			{
				entitiesToSaveTranslation.SaveTranslate(this.UserLanguageId, new Func<BoqItemEntity, DescriptionTranslateType>[] { e => e.BriefInfo });
			}

			using (var boqDbContext = new DbContext(dbCompiledModel))
			{
				var helper = new BulkSaveHelper();
				helper.BulkUpdate(boqDbContext, updateBoqItems2);
				helper.BulkInsert(boqDbContext, insertBoqItems);
			}

			if (updateBoqSurchargedItems != null && updateBoqSurchargedItems.Count() > 0)
			{
				var logic = new BoqSurchargedItemLogic();
				var insertItems = updateBoqSurchargedItems.Where(e => e.Version == 0).ToList();
				var updateItems = updateBoqSurchargedItems.Where(e => e.Version > 0).ToList();
				logic.BulkInsertList(insertItems);
				logic.BulkUpdateList(updateItems);
			}

			if (updatePriceConditions != null && updatePriceConditions.Any())
			{
				var logic = new BoqPriceconditionLogic();
				var insertItems = updatePriceConditions.Where(e => e.Version == 0).ToList();
				var updateItems = updatePriceConditions.Where(e => e.Version > 0).ToList();
				logic.BulkInsertList(insertItems);
				logic.BulkUpdateList(updateItems);
			}

			dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", boqHeaderId);
		}


		#region create Boq item from estimate line item

		/// <summary>
		///
		/// </summary>
		/// <param name="context"></param>
		private void CreatePackageBoqFromLineItem(CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);

			var lineItem2CostGroupLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstLineItem2CostGroupLogic>();
			var costGroupCatalogLogic = new CostGroupCatLogic();
			PrcItemAssignmentLogic prcItemAssignmentLogic = new PrcItemAssignmentLogic();

			var package = context.Package ?? new PrcPackageLogic().GetItemByKey(context.PackageId);
			var projectId = package.ProjectFk;
			IDictionary<object, List<IEstLineItemEntity>> mergeMatch = null;
			var allItemAssignments = prcItemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue && e.EstHeaderFk == context.EstHeaderId).ToList();
			var lineItems = GetLineItemsByItemAssignment(allValidItemAssignments).OrderBy(e => e.Code).ToList();
			var tempLineItems = lineItems.ToEntities(e => new BoQItemLinkedEstimateLineItem(e, BoqSource.LineItem));
			GetLookupData(lineItems);

			IEnumerable<PrcItemAssignmentEntity> validAssignments = new List<PrcItemAssignmentEntity>();
			IEnumerable<IScriptEstResource> validResources4Cost = new List<IScriptEstResource>();
			IEnumerable<IEstLineItemEnhanceEntity> validLineItems = new List<IEstLineItemEnhanceEntity>();
			IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLinesWithoutResources = new List<IEstLineItemEnhanceEntity>();

			FetchValidData(tempLineItems, ref validAssignments, ref validResources4Cost, ref validLineItems, ref validLinkedEstimateLinesWithoutResources, context.DoesUpdateBudgetOnly4AssignmentExist, allItemAssignments);

			if ((validResources4Cost == null || !validResources4Cost.Any()) && (validLineItems == null || !validLineItems.Any()))
			{
				return;
			}

			#region recalculate line items and resources when line item is option is true and is option IT is false
			var validLineItemsIsOptional = validLineItems.Where(e => e.IsOptional && !e.IsOptionalIT).ToList();
			var validLineItemsIsOptionalMap = validLineItemsIsOptional.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.EstHeaderFk }, e => true);
			var validResourcesIsOptional = new List<IScriptEstResource>();
			if (validResources4Cost != null && validResources4Cost.Any()) {
				foreach (var res in validResources4Cost)
				{
					var temp = new RVPC.IdentificationData()
					{
						Id = res.EstLineItemFk,
						PKey1 = res.EstHeaderFk
					};

					if (validLineItemsIsOptionalMap.TryGetValue(temp, out var hasValue))
					{
						validResourcesIsOptional.Add(res);
					}
				}
			}
			RecalculateCostTotalIfLineItemIsOptional(validLinkedEstimateLinesWithoutResources, validResourcesIsOptional);
			#endregion recalculate line items and resources when line item is optional is true and is optional IT is false

			var groupedValidAssigns = validAssignments.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue && !e.PrcItemAssignmentFk.HasValue).GroupBy(e => new { EstHeaderFk = e.EstHeaderFk, EstLineItemFk = e.EstLineItemFk, BoqHeaderFk = e.BoqHeaderFk.Value, BoqItemFk = e.BoqItemFk.Value }).Select(e => e.FirstOrDefault()).ToList();

			Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap;
			Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap = null;
			BuildLineItemMaps(validLinkedEstimateLinesWithoutResources, validResources4Cost, out lineItemId2ResourcesMap, false, out lineItemId2MergedPropMap);
			var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByMainItems<IEstLineItemEntity>(validLineItems, e => e.Id, e => e.EstHeaderFk);
			var costGroupComplete = new CostGroupCatLogic().GetCostGroupCatsByModule(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, projectId);

			var allCostGroupCats = new List<CostGroupCatEntity>();
			if (costGroupComplete != null && costGroupComplete.PrjCostGroupCats != null && costGroupComplete.PrjCostGroupCats.Any())
			{
				allCostGroupCats.AddRange(costGroupComplete.PrjCostGroupCats);
			}
			if (costGroupComplete != null && costGroupComplete.LicCostGroupCats != null && costGroupComplete.LicCostGroupCats.Any())
			{
				allCostGroupCats.AddRange(costGroupComplete.LicCostGroupCats);
			}
			List<MainItem2CostGroupEntity> mergedCostGroups = null;
			IEnumerable<IEstLineItemEntity> merged = MergeLineItems(validLineItems, package, out mergeMatch, groupedValidAssigns, allCostGroupCats, lineItem2CostGroups, out mergedCostGroups, context, lineItemId2MergedPropMap);
			DoCreatePackageBoqFromLineItem(package, merged, validAssignments, lineItemId2ResourcesMap, mergeMatch, mergedCostGroups, context);
		}

		private IEnumerable<IEstLineItemEntity> GetLineItemsByResources(IEnumerable<IScriptEstResource> resources)
		{
			if (resources == null || !resources.Any())
			{
				return new List<IEstLineItemEntity>();
			}

			var estHeaderId2LineItemIdsMap = new Dictionary<int, List<int>>();
			foreach (var resource in resources)
			{
				if (!estHeaderId2LineItemIdsMap.ContainsKey(resource.EstHeaderFk))
				{
					estHeaderId2LineItemIdsMap.Add(resource.EstHeaderFk, new List<int>() { resource.EstLineItemFk });
				}
				else if (!estHeaderId2LineItemIdsMap[resource.EstHeaderFk].Contains(resource.EstLineItemFk))
				{
					estHeaderId2LineItemIdsMap[resource.EstHeaderFk].Add(resource.EstLineItemFk);
				}
			}
			return GetLineItemsByEstHeader2LineItemIdsMap(estHeaderId2LineItemIdsMap);
		}

		private IEnumerable<IScriptEstLineItem> GetScriptLineItemsByResources(IEnumerable<IScriptEstResource> resources)
		{
			if (resources == null || !resources.Any())
			{
				return new List<IScriptEstLineItem>();
			}

			var estHeaderId2LineItemIdsMap = new Dictionary<int, List<int>>();
			foreach (var resource in resources)
			{
				if (!estHeaderId2LineItemIdsMap.ContainsKey(resource.EstHeaderFk))
				{
					estHeaderId2LineItemIdsMap.Add(resource.EstHeaderFk, new List<int>() { resource.EstLineItemFk });
				}
				else if (!estHeaderId2LineItemIdsMap[resource.EstHeaderFk].Contains(resource.EstLineItemFk))
				{
					estHeaderId2LineItemIdsMap[resource.EstHeaderFk].Add(resource.EstLineItemFk);
				}
			}
			return GetScriptLineItemsByEstHeader2LineItemIdsMap(estHeaderId2LineItemIdsMap);
		}

		private IEnumerable<IEstLineItemEntity> GetLineItemsByItemAssignment(IEnumerable<PrcItemAssignmentEntity> itemAssignments)
		{
			var estHeaderId2LineItemIdsMap = new Dictionary<int, List<int>>();

			foreach (var itemAssignment in itemAssignments)
			{
				if (!estHeaderId2LineItemIdsMap.ContainsKey(itemAssignment.EstHeaderFk))
				{
					estHeaderId2LineItemIdsMap.Add(itemAssignment.EstHeaderFk, new List<int>() { itemAssignment.EstLineItemFk });
				}
				else if (!estHeaderId2LineItemIdsMap[itemAssignment.EstHeaderFk].Contains(itemAssignment.EstLineItemFk))
				{
					estHeaderId2LineItemIdsMap[itemAssignment.EstHeaderFk].Add(itemAssignment.EstLineItemFk);
				}
			}

			return GetLineItemsByEstHeader2LineItemIdsMap(estHeaderId2LineItemIdsMap);
		}

		private IEnumerable<IScriptEstLineItem> GetScriptLineItemsByEstHeader2LineItemIdsMap(IDictionary<int, List<int>> map)
		{
			var logic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IScriptEstLineItemLogic>();
			var lineItems = new List<IScriptEstLineItem>();
			foreach (var keyValue in map)
			{
				var estHeaderFk = keyValue.Key;
				var lineItemIds = keyValue.Value;

				var list = logic.GetListByIds(estHeaderFk, lineItemIds);
				lineItems.AddRange(list);
			}

			return lineItems;
		}

		private IEnumerable<IEstLineItemEntity> GetLineItemsByEstHeader2LineItemIdsMap(IDictionary<int, List<int>> map)
		{
			var logic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
			var lineItems = new List<IEstLineItemEntity>();
			foreach (var keyValue in map)
			{
				var estHeaderFk = keyValue.Key;
				var lineItemIds = keyValue.Value;

				var list = logic.GetLineItemByIds(lineItemIds, estHeaderFk);
				lineItems.AddRange(list);
			}

			return lineItems;
		}

		/// <summary>
		/// Get lookup data of uom and controllingunit
		/// </summary>
		/// <param name="lineItems"></param>
		private void GetLookupData(IEnumerable<IEstLineItemEntity> lineItems)
		{
			var controllingUnitIds = lineItems.CollectIds(e => e.MdcControllingUnitFk);

			var controllingUnitLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IControllingUnitLogic>();
			foreach (var id in controllingUnitIds)
			{
				controllingUnits.Add(controllingUnitLogic.GetCtuItemById(id));
			}
		}

		/// <summary>
		/// Merge the line item with the same description, uomfk and controllingunitfk
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="package"></param>
		/// <param name="mergeMatch"></param>
		/// <param name="itemAssignments"></param>
		/// <param name="allCostGroupCats"></param>
		/// <param name="lineItem2CostGroups"></param>
		/// <param name="mergedCostGroups"></param>
		/// <param name="context"></param>
		/// <param name="lineItemId2MergedPropMap"></param>
		/// <param name="lineItemsToAdd"></param>
		/// <returns></returns>
		private IEnumerable<IEstLineItemEntity> MergeLineItems(
			IEnumerable<IEstLineItemEntity> lineItems,
			PrcPackageEntity package,
			out IDictionary<object, List<IEstLineItemEntity>> mergeMatch,
			List<PrcItemAssignmentEntity> itemAssignments,
			IEnumerable<CostGroupCatEntity> allCostGroupCats,
			IEnumerable<MainItem2CostGroupEntity> lineItem2CostGroups,
			out List<MainItem2CostGroupEntity> mergedCostGroups,
			CreateOrUpdatePackageBoqContext context,
			IDictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap,
			IEnumerable<IEstLineItemEntity> lineItemsToAdd = null)
		{
			CheckContext(context);
			IDictionary<string, IEstLineItemEntity> lineItemsMerged = new Dictionary<string, IEstLineItemEntity>();
			IDictionary<string, List<IEstLineItemEntity>> uniqueKey2LineItemsMap = new Dictionary<string, List<IEstLineItemEntity>>();
			mergeMatch = new Dictionary<object, List<IEstLineItemEntity>>();
			Dictionary<object, List<IEstLineItemEntity>> boq2LineItemsMap = new Dictionary<object, List<IEstLineItemEntity>>();
			Dictionary<object, List<IEstLineItemEntity>> validBoq2LineItemsMap = new Dictionary<object, List<IEstLineItemEntity>>();

			foreach (var assign in itemAssignments)
			{
				var line = lineItems.FirstOrDefault(e => e.Id == assign.EstLineItemFk && e.EstHeaderFk == assign.EstHeaderFk);
				if (line == null)
				{
					continue;
				}

				if (!context.IsUpdateOperation) // if create / update line item from estimate, set the original CostTotal to 0.
				{
					line.CostTotal = 0;
					line.Budget = 0;
				}
				if (assign.BoqHeaderFk.HasValue && assign.BoqItemFk.HasValue && boq2LineItemsMap.ContainsKey(new { BoqHeaderFk = assign.BoqHeaderFk.Value, BoqItemFk = assign.BoqItemFk.Value }))
				{
					boq2LineItemsMap[new { BoqHeaderFk = assign.BoqHeaderFk.Value, BoqItemFk = assign.BoqItemFk.Value }].Add(line);
				}
				else
				{
					boq2LineItemsMap[new { BoqHeaderFk = assign.BoqHeaderFk.Value, BoqItemFk = assign.BoqItemFk.Value }] = new List<IEstLineItemEntity>() { line };
				}
			}

			var newBoqHeaderIndex = -1;
			var newBoqItemIndex = -1;

			foreach (var lineItem in lineItems)
			{
				var existAssign = itemAssignments.FirstOrDefault(e => e.EstLineItemFk == lineItem.Id && e.EstHeaderFk == lineItem.EstHeaderFk);

				if (existAssign == null && !lineItemId2MergedPropMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
				{
					continue;
				}

				var uniqueKey = string.Empty;
				var index = 0;
				var type = typeof(IEstLineItemEntity);
				var costGroups = lineItem2CostGroups.Where(e => e.RootItemId == lineItem.EstHeaderFk && e.MainItemId == lineItem.Id).ToList();
				if (context.UniqueFields != null && context.UniqueFields.Any())
				{
					foreach (var field in context.UniqueFields)
					{
						var tempField = field;
						var tempFieldCode = tempField.Code;
						object fieldValue = null;
						if (!tempField.Id.HasValue)
						{
							var propertyInfo = type.GetProperty(tempFieldCode);
							if (propertyInfo == null)
							{
								var baseInterface = type.GetInterface("IEstimateLeadingStructureContext");
								if(baseInterface != null)
								{
									propertyInfo = baseInterface.GetProperty(tempFieldCode);
								}
							}
							if (propertyInfo != null)
							{
								fieldValue = GetPropertyValue(propertyInfo, lineItem);
							}
							else
							{
								throw new BusinessLayerException(string.Format("This field \"{0}\" is not the property of IEstLineItemEntity.", tempFieldCode));
							}
						}
						else
						{
							var tempCostGroup = costGroups.FirstOrDefault(e => e.CostGroupCatFk == tempField.Id.Value);
							fieldValue = tempCostGroup != null ? tempCostGroup.CostGroupFk : new Nullable<int>();
						}

						var uniqueKeyTemp = string.Empty;
						if (index == 0)
						{
							uniqueKeyTemp = "{0}({1})";
						}
						else
						{
							uniqueKeyTemp = "-{0}({1})";
						}

						uniqueKey += string.Format(uniqueKeyTemp, tempFieldCode, fieldValue != null ? fieldValue : string.Empty);
						++index;
					}
				}
				else
				{
					uniqueKey = lineItem.Id.ToString();
				}

				if (lineItemsMerged.ContainsKey(uniqueKey))
				{
					uniqueKey2LineItemsMap[uniqueKey].Add(lineItem);
				}
				else
				{
					lineItemsMerged[uniqueKey] = lineItem;
					uniqueKey2LineItemsMap[uniqueKey] = new List<IEstLineItemEntity> { lineItem };
				}
			}

			foreach (var mergedKeyValue in uniqueKey2LineItemsMap)
			{
				var uniqueKey = mergedKeyValue.Key;
				var lineItemList = mergedKeyValue.Value;
				PrcItemAssignmentEntity existAssign = null;
				if (!lineItemsMerged.ContainsKey(uniqueKey))
				{
					continue;
				}
				var mergedLineItem = lineItemsMerged[uniqueKey];

				for (var i = 0; i < lineItemList.Count; ++i)
				{
					var lineItem = lineItemList[i];
					existAssign = itemAssignments.FirstOrDefault(e => e.EstLineItemFk == lineItem.Id && e.EstHeaderFk == lineItem.EstHeaderFk);
					if (existAssign != null)
					{
						break;
					}
				}

				if (existAssign == null)
				{
					boq2LineItemsMap[new { BoqHeaderFk = newBoqHeaderIndex, BoqItemFk = newBoqItemIndex }] = new List<IEstLineItemEntity>() { mergedLineItem };
					existAssign = new PrcItemAssignmentEntity()
					{
						EstHeaderFk = mergedLineItem.EstHeaderFk,
						EstLineItemFk = mergedLineItem.Id,
						BoqHeaderFk = newBoqHeaderIndex,
						BoqItemFk = newBoqItemIndex
					};

					itemAssignments.Add(existAssign);
					--newBoqHeaderIndex;
					--newBoqItemIndex;
				}

				if (existAssign != null && existAssign.BoqHeaderFk.HasValue && existAssign.BoqItemFk.HasValue && boq2LineItemsMap.ContainsKey(new { BoqHeaderFk = existAssign.BoqHeaderFk.Value, BoqItemFk = existAssign.BoqItemFk.Value }))
				{
					var group = boq2LineItemsMap[new { BoqHeaderFk = existAssign.BoqHeaderFk.Value, BoqItemFk = existAssign.BoqItemFk.Value }];
					if (group != null && group.Any())
					{
						foreach (var lineItem in lineItemList)
						{
							if (!group.Any(e => e.Id == lineItem.Id && e.EstHeaderFk == lineItem.EstHeaderFk))
							{
								group.Add(lineItem);
							}
						}
					}
				}
			}

			if (!context.IsUpdateOperation)
			{
				foreach (var boqKeyValue in boq2LineItemsMap)
				{
					var lineItemList = boqKeyValue.Value;
					bool hasLineItem2Update = false;
					foreach (var lineItem in lineItemList)
					{
						if (lineItemId2MergedPropMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
						{
							hasLineItem2Update = true;
							break;
						}
					}
					if (hasLineItem2Update)
					{
						validBoq2LineItemsMap.Add(boqKeyValue.Key, lineItemList);
					}
				}
			}
			else
			{
				validBoq2LineItemsMap = boq2LineItemsMap;
			}

			return DoMergeLineItems(package, validBoq2LineItemsMap, mergeMatch, allCostGroupCats, lineItem2CostGroups, out mergedCostGroups, context, lineItemId2MergedPropMap, lineItemsToAdd);
		}

		private IEnumerable<IEstLineItemEntity> DoMergeLineItems(
			PrcPackageEntity package,
			IDictionary<object, List<IEstLineItemEntity>> boq2LineItemsMap,
			IDictionary<object, List<IEstLineItemEntity>> mergeMatch,
			IEnumerable<CostGroupCatEntity> allCostGroupCats,
			IEnumerable<MainItem2CostGroupEntity> lineItem2CostGroups,
			out List<MainItem2CostGroupEntity> mergedCostGroups,
			CreateOrUpdatePackageBoqContext context,
			IDictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap,
			IEnumerable<IEstLineItemEntity> lineItemsToAdd = null)
		{
			CheckContext(context);
			var projectLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var projectInfo = projectLogic.GetProjectById(package.ProjectFk);
			var exchangerate = new ProcurementCommonExchangeRateLogic().GetExchangeRateOc(package.CurrencyFk, projectInfo.CurrencyFk, projectInfo.Id, true);
			Dictionary<object, IEstLineItemEntity> lineItemsMergedMap = new Dictionary<object, IEstLineItemEntity>();
			mergedCostGroups = new List<MainItem2CostGroupEntity>();

			var isConsideredQtyRel = GetBooleanValue(context.UpdateOption.IsConsideredQtyRel);
			var uniqueFields = context.UniqueFields;
			foreach (var pair in boq2LineItemsMap)
			{
				foreach (var lineItem in pair.Value)
				{
					var needCalQuantity = lineItemsToAdd != null ? lineItemsToAdd.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.Id == lineItem.Id) : true;
					// cost total from resource
					if (lineItemId2MergedPropMap != null && lineItemId2MergedPropMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
					{
						var mergedProp = lineItemId2MergedPropMap[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }];
						lineItem.CostTotal = mergedProp.CostTotal;
						lineItem.Budget = mergedProp.BudgetTotal;
					}

					lineItem.CostTotal = lineItem.CostTotal / exchangerate.Value;

					if (lineItemsMergedMap.ContainsKey(pair.Key))
					{
						var merged = lineItemsMergedMap[pair.Key];
						if (!IsIgnoreLineItemQuantity(isConsideredQtyRel, lineItem))
						{
							merged.QuantityTotal += needCalQuantity ? lineItem.QuantityTotal : 0;
						}

						merged.CostTotal += lineItem.CostTotal;
						merged.Budget += lineItem.Budget;
						if (merged.QuantityTotal != 0)
						{
							merged.CostUnitTarget = merged.CostTotal / merged.QuantityTotal;
							merged.BudgetUnit = merged.Budget / merged.QuantityTotal;
						}
						else
						{
							merged.CostUnitTarget = merged.CostTotal;
							merged.BudgetUnit = merged.Budget;
						}

						if (IsNotUniqueField(uniqueFields, "DescriptionInfo") &&
							((!string.IsNullOrEmpty(merged.DescriptionInfo.Translated) && !merged.DescriptionInfo.Translated.Equals(lineItem.DescriptionInfo.Translated, StringComparison.CurrentCultureIgnoreCase)) ||
							(!string.IsNullOrEmpty(lineItem.DescriptionInfo.Translated) && string.IsNullOrEmpty(merged.DescriptionInfo.Translated))))
						{
							const string desc = "Summary of Line Items";
							merged.DescriptionInfo = new DescriptionTranslateType(desc);
							merged.DescriptionInfo.Translated = desc;
						}

						if (IsNotUniqueField(uniqueFields, "BasUomTargetFk") && merged.BasUomTargetFk != lineItem.BasUomTargetFk)
						{
							merged.BasUomTargetFk = 0;
						}

						if (IsNotUniqueField(uniqueFields, "MdcControllingUnitFk") && merged.MdcControllingUnitFk != lineItem.MdcControllingUnitFk)
						{
							merged.MdcControllingUnitFk = null;
						}

						if (IsNotUniqueField(uniqueFields, "BasUomFk") && merged.BasUomFk != lineItem.BasUomFk)
						{
							merged.BasUomFk = 0;
						}

						if (IsNotUniqueField(uniqueFields, "MdcCostCodeFk") && merged.MdcCostCodeFk != lineItem.MdcCostCodeFk)
						{
							merged.MdcCostCodeFk = null;
						}

						if (IsNotUniqueField(uniqueFields, "MdcAssetMasterFk") && merged.MdcAssetMasterFk != lineItem.MdcAssetMasterFk)
						{
							merged.MdcAssetMasterFk = null;
						}

						if (IsNotUniqueField(uniqueFields, "PrcStructureFk") && merged.PrcStructureFk != lineItem.PrcStructureFk)
						{
							merged.PrcStructureFk = null;
						}

						if (IsNotUniqueField(uniqueFields, "UserDefined1") && merged.UserDefined1 != lineItem.UserDefined1)
						{
							merged.UserDefined1 = null;
						}

						if (IsNotUniqueField(uniqueFields, "UserDefined2") && merged.UserDefined2 != lineItem.UserDefined2)
						{
							merged.UserDefined2 = null;
						}

						if (IsNotUniqueField(uniqueFields, "UserDefined3") && merged.UserDefined3 != lineItem.UserDefined3)
						{
							merged.UserDefined3 = null;
						}

						if (IsNotUniqueField(uniqueFields, "UserDefined4") && merged.UserDefined4 != lineItem.UserDefined4)
						{
							merged.UserDefined4 = null;
						}

						if (IsNotUniqueField(uniqueFields, "UserDefined5") && merged.UserDefined5 != lineItem.UserDefined5)
						{
							merged.UserDefined5 = null;
						}

						if ((IsNotUniqueField(uniqueFields, "LicCostGroup1Fk")) && merged.LicCostGroup1Fk != lineItem.LicCostGroup1Fk)
						{
							merged.LicCostGroup1Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "LicCostGroup2Fk")) && merged.LicCostGroup2Fk != lineItem.LicCostGroup2Fk)
						{
							merged.LicCostGroup2Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "LicCostGroup3Fk")) && merged.LicCostGroup3Fk != lineItem.LicCostGroup3Fk)
						{
							merged.LicCostGroup3Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "LicCostGroup4Fk")) && merged.LicCostGroup4Fk != lineItem.LicCostGroup4Fk)
						{
							merged.LicCostGroup4Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "LicCostGroup5Fk")) && merged.LicCostGroup5Fk != lineItem.LicCostGroup5Fk)
						{
							merged.LicCostGroup5Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "PrjCostGroup1Fk")) && merged.PrjCostGroup1Fk != lineItem.PrjCostGroup1Fk)
						{
							merged.PrjCostGroup1Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "PrjCostGroup2Fk")) && merged.PrjCostGroup2Fk != lineItem.PrjCostGroup2Fk)
						{
							merged.PrjCostGroup2Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "PrjCostGroup3Fk")) && merged.PrjCostGroup3Fk != lineItem.PrjCostGroup3Fk)
						{
							merged.PrjCostGroup3Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "PrjCostGroup4Fk")) && merged.PrjCostGroup4Fk != lineItem.PrjCostGroup4Fk)
						{
							merged.PrjCostGroup4Fk = null;
						}

						if ((IsNotUniqueField(uniqueFields, "PrjCostGroup5Fk")) && merged.PrjCostGroup5Fk != lineItem.PrjCostGroup5Fk)
						{
							merged.PrjCostGroup5Fk = null;
						}

						if (IsNotUniqueField(uniqueFields, "PrjLocationFk") && merged.PrjLocationFk != lineItem.PrjLocationFk)
						{
							merged.PrjLocationFk = null;
						}

						if (IsNotUniqueField(uniqueFields, "IsLumpsum") && merged.IsLumpsum != lineItem.IsLumpsum)
						{
							merged.IsLumpsum = false;
						}

						if (IsNotUniqueField(uniqueFields, "MdcMaterialFk") && merged.MdcMaterialFk != lineItem.MdcMaterialFk)
						{
							merged.MdcMaterialFk = null;
						}
						#region SortCode
						if (IsNotUniqueField(uniqueFields, "SortCode01Fk") && merged.SortCode01Fk != lineItem.SortCode01Fk)
						{
							merged.SortCode01Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode02Fk") && merged.SortCode02Fk != lineItem.SortCode02Fk)
						{
							merged.SortCode02Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode03Fk") && merged.SortCode03Fk != lineItem.SortCode03Fk)
						{
							merged.SortCode03Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode04Fk") && merged.SortCode04Fk != lineItem.SortCode04Fk)
						{
							merged.SortCode04Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode05Fk") && merged.SortCode05Fk != lineItem.SortCode05Fk)
						{
							merged.SortCode05Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode06Fk") && merged.SortCode06Fk != lineItem.SortCode06Fk)
						{
							merged.SortCode06Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode07Fk") && merged.SortCode07Fk != lineItem.SortCode07Fk)
						{
							merged.SortCode07Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode08Fk") && merged.SortCode08Fk != lineItem.SortCode08Fk)
						{
							merged.SortCode08Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode09Fk") && merged.SortCode09Fk != lineItem.SortCode09Fk)
						{
							merged.SortCode09Fk = null;
						}
						if (IsNotUniqueField(uniqueFields, "SortCode10Fk") && merged.SortCode10Fk != lineItem.SortCode10Fk)
						{
							merged.SortCode10Fk = null;
						}
						#endregion

						foreach (var cat in allCostGroupCats)
						{
							var unique = uniqueFields.FirstOrDefault(e => e.Id.HasValue && e.Id.Value == cat.Id);
							if (unique == null)
							{
								var tempCostGrp = lineItem2CostGroups.FirstOrDefault(e => e.RootItemId == lineItem.EstHeaderFk && e.MainItemId == lineItem.Id && e.CostGroupCatFk == cat.Id);
								var tempMergedCostGrp = mergedCostGroups.FirstOrDefault(e => e.RootItemId == merged.EstHeaderFk && e.MainItemId == merged.Id && e.CostGroupCatFk == cat.Id);

								if ((tempCostGrp != null && tempMergedCostGrp != null && tempCostGrp.CostGroupFk != tempMergedCostGrp.CostGroupFk) ||
									(tempCostGrp == null && tempMergedCostGrp != null))
								{
									mergedCostGroups.Remove(tempMergedCostGrp);
								}
							}
						}

						if (mergeMatch.ContainsKey(new { EstHeaderFk = merged.EstHeaderFk, EstLineItemFk = merged.Id }))
						{
							mergeMatch[new { EstHeaderFk = merged.EstHeaderFk, EstLineItemFk = merged.Id }].Add(lineItem);
						}
						else
						{
							mergeMatch[new { EstHeaderFk = merged.EstHeaderFk, EstLineItemFk = merged.Id }] = new List<IEstLineItemEntity>() { lineItem };
						}
					}
					else
					{
						if (!needCalQuantity)
						{
							lineItem.QuantityTotal = 0;
						}
						if (IsIgnoreLineItemQuantity(isConsideredQtyRel, lineItem))
						{
							lineItem.CostUnitTarget = lineItem.CostTotal;
							lineItem.QuantityTotal = 0;
						}
						else
						{
							if (lineItem.QuantityTotal != 0)
							//if (lineItem.QuantityTarget != 0)
							{
								lineItem.CostUnitTarget = lineItem.CostTotal / lineItem.QuantityTotal;
								//lineItem.CostUnitTarget = lineItem.CostTotal / lineItem.QuantityTarget;
								lineItem.BudgetUnit = lineItem.Budget / lineItem.QuantityTotal;
							}
							else
							{
								lineItem.CostUnitTarget = lineItem.CostTotal;
								lineItem.BudgetUnit = lineItem.Budget;
							}
						}

						lineItemsMergedMap[pair.Key] = lineItem;
						var temp = lineItem2CostGroups.Where(e => e.RootItemId == lineItem.EstHeaderFk && e.MainItemId == lineItem.Id).ToList();
						mergedCostGroups.AddRange(temp);
					}
				}
			}
			var dataLineItems = lineItemsMergedMap.Values.ToList();
			var listUniqueFields = context.UniqueFields.ToList();
			var prcStructureLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcStructureLogic>();
			var controllingUnitLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IControllingUnitLogic>();
			var dataUseAsBoQDescription = listUniqueFields.FirstOrDefault(e => e.IsUseAsBoQDescription == true);
			if (dataUseAsBoQDescription != null)
			{
				for (int i = 0; i < dataLineItems.Count; i++)
				{
					DescriptionTranslateType finalDescription = null;
					switch (dataUseAsBoQDescription.Code)
					{
						case "PrcStructureFk":
							if (dataLineItems[i].PrcStructureFk.HasValue)
							{
								var dataPrcStructure = prcStructureLogic.GetPrcStructureById (dataLineItems[i].PrcStructureFk.Value); ;
								finalDescription = new DescriptionTranslateType(dataPrcStructure.DescriptionInfo.Description, dataPrcStructure.DescriptionInfo.Translated);
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "MdcControllingUnitFk":
							if (dataLineItems[i].MdcControllingUnitFk.HasValue)
							{
								var dataControllingUnit = controllingUnitLogic.GetCtuItemById(dataLineItems[i].MdcControllingUnitFk.Value);
								finalDescription = new DescriptionTranslateType(dataControllingUnit.DescriptionInfo.Description, dataControllingUnit.DescriptionInfo.Translated);
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "UserDefined1":
							finalDescription = new DescriptionTranslateType(dataLineItems[i].UserDefined1, dataLineItems[i].UserDefined1);
							break;
						case "UserDefined2":
							finalDescription = new DescriptionTranslateType(dataLineItems[i].UserDefined2, dataLineItems[i].UserDefined2);
							break;
						case "UserDefined3":
							finalDescription = new DescriptionTranslateType(dataLineItems[i].UserDefined3, dataLineItems[i].UserDefined3);
							break;
						case "UserDefined4":
							finalDescription = new DescriptionTranslateType(dataLineItems[i].UserDefined4, dataLineItems[i].UserDefined4);
							break;
						case "UserDefined5":
							finalDescription = new DescriptionTranslateType(dataLineItems[i].UserDefined5, dataLineItems[i].UserDefined5);
							break;
						case "SortCode01Fk":
							if (dataLineItems[i].SortCode01Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode1").FirstOrDefault();
								if (logic != null)
								{
									var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode01Fk.Value);
									finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
								}
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode02Fk":
							if (dataLineItems[i].SortCode02Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode2").FirstOrDefault();
								if (logic != null)
								{
									var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode02Fk.Value);
									finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
								}
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode03Fk":
							if (dataLineItems[i].SortCode03Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode3").FirstOrDefault();
								if (logic != null)
								{
									var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode03Fk.Value);
									finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
								}
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode04Fk":
							if (dataLineItems[i].SortCode04Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode4").FirstOrDefault();
								if (logic != null) {

									var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode04Fk.Value);
									finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
								}

							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode05Fk":
							if (dataLineItems[i].SortCode05Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode5").FirstOrDefault();
								if (logic != null)
								{
									var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode05Fk.Value);
									finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
								}
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode06Fk":
							if (dataLineItems[i].SortCode06Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode6").FirstOrDefault();
								if (logic != null)
								{
									var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode06Fk.Value);
									finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
								}
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode07Fk":
							if (dataLineItems[i].SortCode07Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode7").FirstOrDefault();
								var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode07Fk.Value);
								finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode08Fk":
							if (dataLineItems[i].SortCode08Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode8").FirstOrDefault();
								var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode08Fk.Value);
								finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode09Fk":
							if (dataLineItems[i].SortCode09Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode9").FirstOrDefault();
								var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode09Fk.Value);
								finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						case "SortCode10Fk":
							if (dataLineItems[i].SortCode10Fk.HasValue)
							{
								var logic = RVPARB.BusinessEnvironment.GetExportedValues<IProjectSortCodeLogic>("project.structure.sortcode10").FirstOrDefault();
								var dataSortcode = logic.GetSortCodeById(dataLineItems[i].SortCode10Fk.Value);
								finalDescription = new DescriptionTranslateType(dataSortcode.DescriptionInfo.Description, dataSortcode.DescriptionInfo.Translated);
							}
							else
							{
								finalDescription = new DescriptionTranslateType("", "");
							}
							break;
						default:
							break;
					}
					if (finalDescription!=null)
					{
						dataLineItems[i].DescriptionInfo = finalDescription;
					}
				}
			}
			return dataLineItems;
		}

		private bool IsNotUniqueField(IEnumerable<IUniqueFieldProfileEntity> uniqueFields, string field)
		{
			return !uniqueFields.Any(e => !string.IsNullOrEmpty(e.Code) && e.Code.Equals(field, StringComparison.CurrentCultureIgnoreCase));
		}

		private IEnumerable<PrcBoqExtendedEntity> GetBoqExtended4LineItem(int prcHeaderId, string rootReferencNo, out IEnumerable<BOQMAIN.BoqStructureEntity> boqStructures, out int? maxIndex)
		{
			string pattern = rootReferencNo.ToLower() + @"(\((\d+)\))?$";
			List<PrcBoqExtendedEntity> prcBoqExtended4LineItem = new List<PrcBoqExtendedEntity>();
			List<int> boqStructureIds = new List<int>();
			boqStructures = new List<BOQMAIN.BoqStructureEntity>();
			maxIndex = null;

			BOQMAIN.BoqTypeLogic boqTypeLogic = new BOQMAIN.BoqTypeLogic();
			PrcBoqLogic prcBoqLogic = new PrcBoqLogic();
			IEnumerable<PrcBoqEntity> prcBoqList = prcBoqLogic.GetList(prcHeaderId);
			List<PrcBoqExtendedEntity> prcBoQExtendedList = new List<PrcBoqExtendedEntity>();
			IDictionary<int, List<PrcBoqExtendedEntity>> boqStructureId2Boqs = new Dictionary<int, List<PrcBoqExtendedEntity>>();

			if (prcBoqList == null || !prcBoqList.Any())
			{
				return prcBoqExtended4LineItem;
			}

			foreach (var prcBoq in prcBoqList)
			{
				var prcBoqExtended = prcBoqLogic.GetPrcBoqExtended(prcBoq);
				prcBoQExtendedList.Add(prcBoqExtended);
			}

			// get prc boq extended with reference no "LINE ITEM". And get the max index.
			foreach (var boqExtended in prcBoQExtendedList)
			{
				var match = Regex.Match(boqExtended.BoqRootItem.Reference.ToLower(), pattern);
				if (!String.IsNullOrEmpty(match.Value))
				{
					if (!string.IsNullOrEmpty(match.Groups[1].Value))
					{
						var tmpIndex = int.Parse(match.Groups[1].Value);
						maxIndex = !maxIndex.HasValue || maxIndex < tmpIndex ? tmpIndex : maxIndex;
					}
					prcBoqExtended4LineItem.Add(boqExtended);
					if (boqExtended.BoqHeader.BoqStructureFk.HasValue)
					{
						var struId = boqExtended.BoqHeader.BoqStructureFk.Value;
						boqStructureIds.Add(struId);
						if (boqStructureId2Boqs.ContainsKey(struId))
						{
							boqStructureId2Boqs[struId].Add(boqExtended);
						}
						else
						{
							boqStructureId2Boqs[struId] = new List<PrcBoqExtendedEntity>() { boqExtended };
						}
					}
				}
			}

			if (!boqStructureIds.Any())
			{
				return prcBoqExtended4LineItem;
			}

			boqStructures = boqTypeLogic.GetBoqStructureList(boqStructureIds);
			foreach (var structure in boqStructures)
			{
				var details = boqTypeLogic.GetBoqStructureDetails(structure.Id);
				structure.BoqStructureDetailEntities = details.ToList();
			}

			// Check whether the boq structure is right or not. if not right, remove it from the prc boq extended list
			// the boq structure should be:
			// Level1
			//		Position
			foreach (var structure in boqStructures)
			{
				var valid = true;

				var structureDetails = structure.BoqStructureDetailEntities;
				structureDetails = structureDetails.OrderBy(e => e.BoqLineTypeFk).ToList();
				for (var i = 0; i < structureDetails.Count; ++i)
				{
					var tempValid = false;
					var detail = structureDetails.ElementAt(i);
					if (i == 0)
					{
						tempValid = detail.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position;
					}
					else if (i < 2)
					{
						tempValid = detail.BoqLineTypeFk == i;
					}
					else
					{
						tempValid = true;
					}

					valid &= tempValid;
				}

				if (!valid)
				{
					var toRemove = boqStructureId2Boqs[structure.Id];
					foreach (var remove in toRemove)
					{
						prcBoqExtended4LineItem.Remove(remove);
					}
					boqStructureId2Boqs.Remove(structure.Id);
				}
			}

			return prcBoqExtended4LineItem;
		}

		private void SaveAndRecalculateBoqItems(PrcPackageEntity package, IEnumerable<int> boqHeaderIds, IEnumerable<BoqItemEntity> toSave, decimal? exchangeRate)
		{
			BoqItemLogic boqItemLogic = new BoqItemLogic();
			boqItemLogic.SetCurrentExchangeRateHc2Oc(exchangeRate);
			DbCompiledModel dbCompiledModel = RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder.DbModel;
			using (var boqDbContext = new DbContext(dbCompiledModel))
			{
				var helper = new BulkSaveHelper();
				var insertBoqItems = toSave.Where(e => e.Version == 0).ToList();
				var updateBoqItems = toSave.Where(e => e.Version > 0).ToList();
				helper.BulkUpdate(boqDbContext, updateBoqItems);
				helper.BulkInsert(boqDbContext, insertBoqItems);
			}

			//save modified boq items
			var modifiedBoqItems = new List<BoqItemEntity>();
			var modifiedBoqPriceConditions = new List<BoqPriceconditionEntity>();
			foreach (var id in boqHeaderIds)
			{
				var boqItemsTree = boqItemLogic.GetBoqItems(id);
				if (boqItemsTree != null && boqItemsTree.Any())
				{
					var tree = boqItemsTree.FirstOrDefault(e => !e.BoqItemFk.HasValue);
					var boqCalculateLogic = new BoqItemCalculateLogic(moduleName, package.Id, package.ExchangeRate, package.TaxCodeFk, package.BpdVatGroupFk);
					var calculationResult = boqCalculateLogic.CalculateBoqTreeAndPriceconditions(tree);
					if (calculationResult != null && calculationResult.BoqPriceconditionChanged != null)
					{
						modifiedBoqPriceConditions.AddRange(calculationResult.BoqPriceconditionChanged);
					}
					modifiedBoqItems.AddRange(boqItemsTree);
				}
			}

			var boqItemFlatList = modifiedBoqItems.Flatten(e => e.BoqItemChildren);
			using (var boqDbContext = new DbContext(BOQMAIN.ModelBuilder.DbModel))
			{
				var helper = new BulkSaveHelper();
				var insertBoqItems = boqItemFlatList.Where(e => e.Version == 0).ToList();
				var updateBoqItems = boqItemFlatList.Where(e => e.Version > 0).ToList();
				helper.BulkUpdate(boqDbContext, updateBoqItems);
				helper.BulkInsert(boqDbContext, insertBoqItems);
			}
			var logic = new BoqPriceconditionLogic();
			var insertConditionItems = modifiedBoqPriceConditions.Where(e => e.Version == 0).ToList();
			var updateConditionItems = modifiedBoqPriceConditions.Where(e => e.Version > 0).ToList();
			logic.BulkUpdateList(updateConditionItems);
			logic.BulkInsertList(insertConditionItems);
			using (var dbContext = new DbContext(BOQMAIN.ModelBuilder.DbModel))
			{
				foreach (var id in boqHeaderIds)
				{
					dbContext.ExecuteStoredProcedure("BOQ_ITEM_LEVEL8_PROC", id);
				}
			}
		}

		private BoqItemEntity CreateBoqItem(
			int level,
			string referenceNo,
			string breifInfo,
			int boqHeaderId,
			BoqItemEntity parent,
			IEstLineItemEntity lineItem
		)
		{
			var creationData = new BoqItemCreationData();
			creationData.BoqHeaderFk = boqHeaderId;
			creationData.LineType = (0 <= level && level < 2) ? (level == 0 ? (int)BoqConstants.EBoqLineType.Root : level) : (int)BoqConstants.EBoqLineType.Position;
			creationData.ParentItemId = parent.Id;
			creationData.RefCode = referenceNo;
			creationData.DoSave = false;
			var newBoqItem = new BoqItemLogic().Create(creationData);

			if (!string.IsNullOrEmpty(breifInfo))
			{
				newBoqItem.BriefInfo = new DescriptionTranslateType(breifInfo);
			}

			newBoqItem.BoqItemChildren = new List<BoqItemEntity>();
			if (parent != null)
			{
				parent.BoqItemChildren.Add(newBoqItem);
			}

			if (lineItem != null)
			{
				if (string.IsNullOrEmpty(breifInfo) && lineItem.DescriptionInfo != null)
				{
					newBoqItem.BriefInfo = new DescriptionTranslateType(lineItem.DescriptionInfo.Description);
					if (lineItem.DescriptionInfo.VersionTr > 0)
					{
						newBoqItem.BriefInfo.Translated = lineItem.DescriptionInfo.Translated;
						newBoqItem.BriefInfo.Modified = true;
					}
				}

				newBoqItem.Quantity = lineItem.QuantityTotal;
				newBoqItem.QuantityDetail = QuantityToString(newBoqItem.Quantity);
				newBoqItem.PriceOc = lineItem.CostUnitTarget; // price;
				newBoqItem.Price = lineItem.CostUnitTarget;
				newBoqItem.CostOc = newBoqItem.Cost = lineItem.CostUnitTarget;
				newBoqItem.CorrectionOc = newBoqItem.Correction = newBoqItem.PriceOc - newBoqItem.CostOc;
				newBoqItem.BudgetTotal = lineItem.Budget;
				newBoqItem.BudgetPerUnit = lineItem.BudgetUnit;
				newBoqItem.BudgetFixedTotal = lineItem.IsFixedBudget;
				newBoqItem.BudgetFixedUnit = lineItem.IsFixedBudgetUnit;
				newBoqItem.IsLumpsum = lineItem.IsLumpsum;
				newBoqItem.MdcMaterialFk = lineItem.MdcMaterialFk;
				newBoqItem.BasUomFk = lineItem.BasUomFk;
				newBoqItem.MdcControllingUnitFk = lineItem.MdcControllingUnitFk;
				newBoqItem.MdcCostCodeFk = lineItem.MdcCostCodeFk;
				newBoqItem.MdcAssetMasterFk = lineItem.MdcAssetMasterFk;
				newBoqItem.PrcStructureFk = lineItem.PrcStructureFk;

				newBoqItem.Userdefined1 = lineItem.UserDefined1;
				newBoqItem.Userdefined2 = lineItem.UserDefined2;
				newBoqItem.Userdefined3 = lineItem.UserDefined3;
				newBoqItem.Userdefined4 = lineItem.UserDefined4;
				newBoqItem.Userdefined5 = lineItem.UserDefined5;
				newBoqItem.PrjLocationFk = lineItem.PrjLocationFk;
				if (_packageTaxCodeFk.HasValue)
				{
					newBoqItem.MdcTaxCodeFk = _packageTaxCodeFk;
				}
			}

			return newBoqItem;
		}

		private void CreateBoqItems(
			int level,
			int boqHeaderId,
			IEstLineItemEntity item,
			BoqItemEntity parent,
			BoqItemEntity previous,
			BOQMAIN.BoqStructureEntity boqStructure,
			IEnumerable<MainItem2CostGroupEntity> mergedCostGrps,
			ref List<BoqItemEntity> toCreate,
			ref List<PrcItemAssignmentEntity> itemAssigns,
			ref List<BoqItem2CostGroupEntity> boqItem2CostGrps,
			bool isControllingUnitAsTitle = true)
		{
			if (level > 2)
			{
				return;
			}

			BoqItemEntity tempParent = parent;
			var tempPrevious = previous;
			// create boq item with structure as:
			// Level1
			//		Position
			for (int levelIndex = level; levelIndex <= 2; ++levelIndex)
			{
				string code = null;
				string briefInfo = string.Empty;
				IControllingUnitEntity controllingUnit = null;
				switch (levelIndex)
				{
					case 1:
						code = GenerateBoqReference(1, tempParent, tempParent, tempPrevious, boqStructure, levelIndex, true);
						if (isControllingUnitAsTitle)
						{
							if (item.MdcControllingUnitFk.HasValue)
							{
								controllingUnit = controllingUnits.FirstOrDefault(e => e.Id == item.MdcControllingUnitFk.Value);
								briefInfo = controllingUnit != null ? controllingUnit.Code : string.Empty;
							}
						}
						break;
					case 2:
						code = GenerateBoqReference((int)BoqConstants.EBoqLineType.Position, tempParent, tempParent, tempPrevious, boqStructure, levelIndex, true);
						break;
					default:
						break;
				}

				// create boq
				var newBoqItem = CreateBoqItem(
					levelIndex,
					code,
					levelIndex == 2 ? null : briefInfo,
					boqHeaderId,
					tempParent,
					levelIndex == 2 ? item : null);

				// creat boq item to cost group
				if (levelIndex == 2)
				{
					if (boqItem2CostGrps == null)
					{
						boqItem2CostGrps = new List<BoqItem2CostGroupEntity>();
					}
					var tempCostGrps = mergedCostGrps.Where(e => e.RootItemId == item.EstHeaderFk && e.MainItemId == item.Id).ToList();
					foreach (var costGrp in tempCostGrps)
					{
						var newBoq2CostGrp = new BoqItem2CostGroupLogic().CreateEntity(newBoqItem.Id, null);
						newBoq2CostGrp.BoqHeaderFk = newBoqItem.BoqHeaderFk;
						newBoq2CostGrp.BoqItemFk = newBoqItem.Id;
						newBoq2CostGrp.CostGroupCatFk = costGrp.CostGroupCatFk;
						newBoq2CostGrp.CostGroupFk = costGrp.CostGroupFk;
						boqItem2CostGrps.Add(newBoq2CostGrp);
					}
				}

				// create item assignment
				var newItemAssign = new PrcItemAssignmentEntity()
				{
					EstLineItemFk = item.Id,
					BoqItemFk = newBoqItem.Id,
					BoqHeaderFk = newBoqItem.BoqHeaderFk
				};

				itemAssigns.Add(newItemAssign);

				toCreate.Add(newBoqItem);
				tempParent = newBoqItem;
				tempPrevious = null;
			}
		}

		private void UpdateOrCreateBoqItems(
			int level,
			int boqHeaderId,
			BoqItemEntity parent,
			IEstLineItemEntity lineItem,
			BOQMAIN.BoqStructureEntity boqStructure,
			IEnumerable<MainItem2CostGroupEntity> mergedCostGrps,
			bool isControllingUnitAsTitle,
			ref List<BoqItemEntity> toUpdate,
			ref List<BoqItemEntity> toCreate,
			ref List<PrcItemAssignmentEntity> itemAssignments,
			ref List<BoqItem2CostGroupEntity> boqItem2CostGrps,
			bool isUpdateOperation,
			bool doesUpdateBudgetOnly,
			IDictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>> itemAssignmentBoqMap,
			IDictionary<int, List<PrcItemAssignmentEntity>> itemAssignmentChildrenMap,
			IDictionary<RVPC.IdentificationData, BoqItemEntity> targetBoqMap,
			ref List<BoqItemEntity> replacementToUpdate,
			IDictionary<object, List<IEstLineItemEntity>> mergeMatch)
		{
			bool isExist = false;
			BoqItemEntity previous = null;
			IControllingUnitEntity controllingUnit = null;
			IEnumerable<BoqItemEntity> children = parent.BoqItemChildren;
			var isAssigned = itemAssignments.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && e.BoqItemFk.HasValue && e.BoqHeaderFk.HasValue && !e.PrcItemAssignmentFk.HasValue);

			foreach (BoqItemEntity boq in children)
			{
				if (isExist)
				{
					break;
				}

				bool isLevel2 = false;
				previous = boq;
				switch (level)
				{
					case 1:
						if (isControllingUnitAsTitle)
						{
							if (lineItem.MdcControllingUnitFk.HasValue)
							{
								controllingUnit = controllingUnits.FirstOrDefault(e => e.Id == lineItem.MdcControllingUnitFk.Value);
							}
							if ((controllingUnit != null && controllingUnit.Code.Equals(boq.BriefInfo.Description, StringComparison.CurrentCultureIgnoreCase)) ||
								(controllingUnit == null && string.IsNullOrEmpty(boq.BriefInfo.Description)))
							{
								isExist = true;
							}
						}
						else
						{
							isExist = true;
						}
						break;
					case 2:
						isLevel2 = true;
						isExist = IsSameBetweenLineItemNBoqItem(lineItem, boq, itemAssignments);
						if (isExist)
						{
							decimal lineItemPrice = 0;
							decimal budgetUnit = 0;
							decimal costTotal = lineItem.CostTotal;
							decimal budgetTotal = lineItem.Budget;
							bool isFixedBudget = lineItem.IsFixedBudget;
							bool isFixedBudgetUnit = lineItem.IsFixedBudgetUnit;
							var itemAssigns = GetValueFromDictionary(itemAssignmentBoqMap, boq.Id, boq.BoqHeaderFk);
							decimal quantity = 0;
							if (doesUpdateBudgetOnly)
							{
								quantity = boq.Quantity;
							}
							else if (!isUpdateOperation)
							{
								var est2LineItemMap = new Dictionary<RVPC.IdentificationData, bool>();
								quantity = boq.Quantity;
								List<IEstLineItemEntity> matchedLineItems = new List<IEstLineItemEntity>();
								if (mergeMatch != null && mergeMatch.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id })) // mergeMatch -> if empty, no combine line items;
								{
									matchedLineItems = mergeMatch[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }];
								}

								matchedLineItems.Add(lineItem);

								foreach (var item in itemAssigns)
								{
									var temp = new RVPC.IdentificationData() { Id = item.EstLineItemFk, PKey1 = item.EstHeaderFk };
									if (!est2LineItemMap.TryGetValue(temp, out var result))
									{
										est2LineItemMap.Add(temp, true);
									}
								}
								foreach (var match in matchedLineItems)
								{
									if (!est2LineItemMap.TryGetValue(new RVPC.IdentificationData() { Id = match.Id, PKey1 = match.EstHeaderFk }, out var hasValue))
									{
										quantity += match.QuantityTotal;
									}
								}
							}
							else
							{
								quantity = lineItem.QuantityTotal;
							}

							var itemAssignment = itemAssigns.FirstOrDefault();
							var subItemAssignments = itemAssignment != null ? GetValueFromDictionary(itemAssignmentChildrenMap, itemAssignment.Id) : new List<PrcItemAssignmentEntity>();

							if (!isUpdateOperation)
							{
								costTotal = boq.FinalpriceOc + lineItem.CostTotal;
								budgetTotal = boq.BudgetTotal + lineItem.Budget;
							}

							if (quantity != 0)
							{
								lineItemPrice = costTotal / quantity;
								budgetUnit = budgetTotal / quantity;
							}
							else
							{
								lineItemPrice = costTotal;
								budgetUnit = budgetTotal;
							}

							if (!doesUpdateBudgetOnly)
							{
								if (quantity != boq.Quantity)
								{
									var proportion = boq.Quantity != 0 ? quantity / boq.Quantity : quantity;
									boq.Quantity = quantity;
									boq.QuantityDetail = QuantityToString(boq.Quantity);
									foreach (var assign in subItemAssignments)
									{
										if (!assign.BoqHeaderFk.HasValue || !assign.BoqItemFk.HasValue)
										{
											continue;
										}
										BoqItemEntity replacement = GetValueFromDictionary(targetBoqMap, assign.BoqItemFk.Value, assign.BoqHeaderFk.Value);
										if (replacement == null)
										{
											continue;
										}
										replacement.Quantity *= proportion;
										replacement.QuantityDetail = QuantityToString(replacement.Quantity);
										replacementToUpdate.Add(replacement);
									}
								}
								boq.PriceOc = lineItemPrice;
								boq.Price = lineItemPrice;
							}
							boq.BudgetTotal = budgetTotal;
							boq.BudgetPerUnit = budgetUnit;
							boq.BudgetFixedTotal = isFixedBudget;
							boq.BudgetFixedUnit = isFixedBudgetUnit;
							toUpdate.Add(boq);
						}
						break;
					default:
						break;
				}

				if ((isExist || isAssigned) && !isLevel2)
				{
					UpdateOrCreateBoqItems(
						level + 1,
						boqHeaderId,
						boq,
						lineItem,
						boqStructure,
						mergedCostGrps,
						isControllingUnitAsTitle,
						ref toUpdate,
						ref toCreate,
						ref itemAssignments,
						ref boqItem2CostGrps,
						isUpdateOperation,
						doesUpdateBudgetOnly,
						itemAssignmentBoqMap,
						itemAssignmentChildrenMap,
						targetBoqMap,
						ref replacementToUpdate,
						mergeMatch);
				}
			}

			if (!isExist && !isAssigned && !doesUpdateBudgetOnly)
			{
				CreateBoqItems(
					level,
					boqHeaderId,
					lineItem,
					parent,
					previous,
					boqStructure,
					mergedCostGrps,
					ref toCreate,
					ref itemAssignments,
					ref boqItem2CostGrps,
					isControllingUnitAsTitle);
			}
		}

		private bool IsSameBetweenLineItemNBoqItem(IEstLineItemEntity lineItem, BoqItemEntity boq, IEnumerable<PrcItemAssignmentEntity> itemAssigns)
		{
			if (itemAssigns == null || !itemAssigns.Any())
			{
				return false;
			}
			return itemAssigns.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && e.BoqHeaderFk == boq.BoqHeaderFk && e.BoqItemFk == boq.Id);
		}

		private object GetPropertyValue(PropertyInfo propertyInfo, object obj)
		{
			if (propertyInfo == null)
			{
				return null;
			}
			object value = null;
			var propertyType = propertyInfo.PropertyType;
			if (propertyType == typeof(DescriptionTranslateType))
			{
				value = propertyInfo.GetValue(obj);
				propertyInfo = propertyType.GetProperty("Translated");
				value = propertyInfo.GetValue(value);
			}
			else
			{
				value = propertyInfo.GetValue(obj);
			}
			return value;
		}

		private string GenerateBoqReference(int lineType, BoqItemEntity parentItem,
			BoqItemEntity selectedItem, BoqItemEntity selectedItemPreviousItem, BOQMAIN.BoqStructureEntity boqStructure, int level,
			bool doAppend)
		{
			String reference = String.Empty;
			string error = string.Empty;
			BoQStructureInfo.StructureDetailInfo structureDetailInfo = null;
			BoQStructureInfo boqStructureInfo = GetBoqStructureInformations(boqStructure, ref error);
			structureDetailInfo = boqStructureInfo.GetStructureDetailInfoByLineType((BoqConstants.EBoqLineType)lineType);

			if (structureDetailInfo == null)
			{
				return reference; // Without a valid structure info we can't create a reference
			}

			var delimiter = boqStructureInfo.IsFreeStructure() ? "" : ".";

			String refPart = String.Empty;
			String leadingZerosPart = String.Empty;
			String leadingZerosChar = boqStructure.LeadingZeros ? "0" : " ";
			ICollection<BoqItemEntity> siblings;

			// Remove dot because there might still be references with final dot.
			// By doing so the following functionality can handle both cases, the one with dot and the one without.
			// This is a historical issue that will disappear in the future.
			var parentReference = RemoveDotAtEnd(parentItem.Reference);

			siblings = parentItem.BoqItemChildren.ToList();
			if (siblings.Count == 0)
			{
				if (structureDetailInfo.LengthReference - structureDetailInfo.StartValue.ToString().Length > 0)
				{
					for (int i = 1; i <= (structureDetailInfo.LengthReference - structureDetailInfo.StartValue.ToString().Length); i++)
					{
						leadingZerosPart += leadingZerosChar;
					}
				}

				reference = BOQMAIN.BoqLineTypeHelper.IsRoot(parentItem) ? leadingZerosPart + structureDetailInfo.StartValue.ToString() : String.IsNullOrEmpty(parentReference) ? String.Empty : parentReference + delimiter + leadingZerosPart + structureDetailInfo.StartValue;
			}
			else
			{
				if (parentItem.Id == selectedItem.Id)
				{
					// append child element at first pos same as first pos is current and insert element
					selectedItem = siblings.OrderBy(e => e.Reference, new BoqItemReferenceComparer()).LastOrDefault(); // I want to append it at the end
				}

				var isReferenceFound = FindReference(selectedItem, selectedItemPreviousItem, doAppend, out refPart, structureDetailInfo, level);
				if (!isReferenceFound && !doAppend)
				{
					// We tried to append the new item before the selected item and didn't succeed
					// -> try to add it at the end
					selectedItem = siblings.OrderBy(e => e.Reference, new BoqItemReferenceComparer()).AsEnumerable().LastOrDefault(); // I want to append it at the end
					isReferenceFound = FindReference(selectedItem, null, true, out refPart, structureDetailInfo, level);
				}

				if (isReferenceFound)
				{
					if (structureDetailInfo.LengthReference - refPart.Length > 0)
					{
						for (int i = 1; i <= (structureDetailInfo.LengthReference - refPart.Length); i++)
						{
							leadingZerosPart += leadingZerosChar;
						}
					}

					if (!String.IsNullOrEmpty(refPart))
					{
						reference = BOQMAIN.BoqLineTypeHelper.IsRoot(parentItem) ? leadingZerosPart + refPart : parentReference + (delimiter) + leadingZerosPart + refPart;
					}
				}
			}

			if (!String.IsNullOrEmpty(reference) && reference.LastIndexOf('.') != reference.Length - 1)
			{
				reference += (delimiter); // Add final dot
			}

			return reference;
		}

		/// <summary>
		/// Remove the last dot of the given reference number if it is at the end
		/// </summary>
		/// <param name="reference">reference number whose last dot is to be removed</param>
		private string RemoveDotAtEnd(string reference)
		{
			var referenceWithoutFinalDot = reference;

			if (!String.IsNullOrEmpty(reference))
			{
				var dotIndex = reference.IndexOf('.');

				if (dotIndex != -1)
				{

					// UpdateOrCreateBoqItems if dot is at end of string
					dotIndex = reference.LastIndexOf('.');
					if (dotIndex == reference.Length - 1)
					{
						// Remove last dot to better be able to extract last reference part
						referenceWithoutFinalDot = reference.Substring(0, reference.Length - 1);
					}
				}
			}

			return referenceWithoutFinalDot;
		}

		private BoQStructureInfo GetBoqStructureInformations(BOQMAIN.BoqStructureEntity structureEntity, ref string error)
		{
			if (structureEntity == null) { return null; }

			// Access the boqMask to have initial informations on the boq structure.
			// The boqMask is a specially formatted string representing type and hierarchy information of the items in this boq.
			// Furthermore it gives information about the key length on a given hierarchy level.
			// Example boqMask: 112233PPPP
			// This indicates we have a folder hierarchy down to level 3 with a key length of 2 for those items.
			// Following the folders we have the position types on level 4 with a key length of 4.
			var structureInfo = new BoQStructureInfo(structureEntity);

			bool boqMaskIsValid = new BoqItemLogic().ValidateBoqMask(structureEntity.Boqmask, structureInfo, ref error); // Although we hand over the error string we don't use it at the moment.

			// Next we merge the informations of the existing BoqStructureDetailEntities to the BoQStructureInfo.
			// The BoQStructureInfo has logic to decide which informations to merge and also handles the case if
			// the boqMaks is not valid. In this case the BoqStructureDetailEntities have the leading role.
			// To have a single interface to access the structure information we map the BoqStructureDetailEntities
			// to BoQStructureInfo objects.
			var details = structureEntity.BoqStructureDetailEntities.ToList();

			structureInfo.MergeWithBoqStructureDetailEntites(details);

			return structureInfo;
		}

		private bool FindReference(BoqItemEntity currentItem, BoqItemEntity previousItem, bool doAppend, out string newReference, BoQStructureInfo.StructureDetailInfo structureDetailInfo, int level)
		{
			bool result = false;
			bool canCreateAlphanumerical = true;
			BoqItemEntity neighborItem = null;
			string s1 = string.Empty;
			string s2 = string.Empty;
			bool createNewAtEnd = false;
			newReference = String.Empty;

			if (BOQMAIN.BoqLineTypeHelper.IsRoot(currentItem))
			{
				if (currentItem.BoqItemParent != null && currentItem.BoqItemParent.BoqItemChildren.ToList().Count > 0)
				{
					s1 = "0";
					BoqItemEntity currentchild = currentItem.BoqItemParent.BoqItemChildren.ToList()[0];
					if (currentchild != null) /*Workaround for bad data. Sometimes first child is of type BoqDetailVM*/
					{
						s2 = currentchild.Reference;
					}
				}
				else
				{
					if (doAppend)
					{
						newReference = String.Empty;
						result = true;
					}
				}
			}
			else
			{
				if (structureDetailInfo != null)
				{
					if (!String.IsNullOrEmpty(currentItem.Reference))
					{
						var myReference = RemoveDotAtEnd(currentItem.Reference);
						if (myReference.Contains("."))
						{
							// level is not first level
							s1 = ExtractReferencePartBasedOnLineType(currentItem, level);
						}
						else
						{
							// first level
							s1 = myReference;
						}
					}
					else
					{
						if (structureDetailInfo.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric)
						{
							s1 = structureDetailInfo.StartValue.ToString();
						}
						else
						{
							//s1 = "A";
							//s1 = Convert.ToString((char)(((int)s1.ToCharArray()[s1.Length - 1]) - 1));
							canCreateAlphanumerical = false;
						}
					}
					if (doAppend)
					{
						createNewAtEnd = true;
						if (structureDetailInfo.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric)
						{
							s2 = "0";
						}
						else
						{
							switch (s1.Length)
							{
								case 0:
									canCreateAlphanumerical = false;
									break;
								case 1:
									s2 = System.Convert.ToString((char)(((int)s1.ToCharArray()[s1.Length - 1]) + 2));
									break;
								default:
									s2 = s1.Substring(0, s1.Length - 1) + System.Convert.ToString((char)(((int)s1.ToCharArray()[s1.Length - 1]) + 2));
									break;
							}
						}
					}
					else
					{
						if (previousItem == null)
						{
							if (structureDetailInfo.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric)
							{
								// nuemrical
								s2 = "0";
							}
							else
							{
								canCreateAlphanumerical = false;
							}
						}
						else
						{
							neighborItem = previousItem;
						}
					}

					if (neighborItem != null)
					{
						if (!String.IsNullOrEmpty(neighborItem.Reference))
						{
							var myReference = RemoveDotAtEnd(neighborItem.Reference);
							if (myReference.Contains("."))
							{
								s2 = ExtractReferencePartBasedOnLineType(neighborItem,level);
							}
							else
							{
								s2 = myReference;
							}
						}
						else
						{
							if (structureDetailInfo.DataType == (int)BoqConstants.EBoqStructureDetailDataType.Numeric)
							{
								// nuemrical
								s2 = "0";
							}
							else
							{
								canCreateAlphanumerical = false;
							}
						}

					}
				}
			}
			if (structureDetailInfo != null)
			{
				int tmpNumberOfReferences = 0; //...not in use
				result = BoqHelper.GenerateReference(s1, s2, doAppend, canCreateAlphanumerical, createNewAtEnd, structureDetailInfo.DataType, structureDetailInfo.StepIncrement, structureDetailInfo.LengthReference, false, false, out newReference, out tmpNumberOfReferences);
			}

			return result;
		}

		/// <summary>
		/// Extract reference part of the reference on the given lineType
		/// </summary>
		/// <param name="boqItem">boq item whose reference part is to be extracted</param>
		/// <param name="level"></param>
		/// <returns></returns>
		private string ExtractReferencePartBasedOnLineType(BoqItemEntity boqItem, int level)
		{
			string referencePart = "";
			var reference = boqItem != null ? boqItem.Reference : "";
			if (!String.IsNullOrEmpty(reference))
			{
				// Extract all reference parts
				var refParts = reference.Split('.');

				if (level > 0 && level <= refParts.Length)
				{
					referencePart = refParts[level - 1].Trim();
				}
			}

			return referencePart;
		}

		#endregion create Boq item from estimate line item

		#region Implement IPackageGenerateBoQWizardLogic
		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		int IPackageGenerateBoQWizardLogic.GeneratePackageBoQFromProjectBoQ(GeneratePackageBoQFromBoQParam param)
		{
			int packageId = param.PackageId;
			int prcHeaderId = param.PrcHeaderId;
			string quantityTransferFrom = param.QuantityTransferFrom;
			bool considerBoqQtyRelation = param.ConsiderBoqQtyRelation;
			IEnumerable<IEstLineItemEntity> lineItemsWithoutResource = param.LineItemsWithoutResource;
			IEnumerable<IScriptEstResource> newResources = param.NewResources;
			bool isMarkupCostCode = param.IsMarkupCostCode;
			IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSave = param.NodeBoqItemsToSave;
			DragNDropInfo dragNDropInfo = param.DragNDropInfo;

			if ((newResources == null || !newResources.Any()) && (lineItemsWithoutResource == null || !lineItemsWithoutResource.Any()))
			{
				return -6;
			}

			var parameter = new GeneratePackageBoQParameter()
			{
				PackageId = packageId,
				PrcHeaderId = prcHeaderId,
				ConsiderBoqQtyRelation = considerBoqQtyRelation,
				Source = UpdateOptionForBoq.Criteria.ProjectBoq,
				QuantityTransferFrom = quantityTransferFrom,
				WicCatGroupId = 0,
				IsControllingUnitAsTitle = false,
				UniqueFields = null
			};

			var context = new CreateOrUpdatePackageBoqContext(parameter, false);

			var itemAssignmentLogic = new PrcItemAssignmentLogic();
			Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap = null;
			Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap = null;
			var allItemAssignments = itemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();
			var invalidItemAssigns = allItemAssignments.Where(e => e.PrcItemFk.HasValue).ToList();
			var validResources = FetchValidReasources(invalidItemAssigns, newResources);
			var validLineItemsWithoutResources = FetchValidLineItems(invalidItemAssigns, lineItemsWithoutResource);
			if (dragNDropInfo != null && dragNDropInfo.IsForcedToCreateBoq)
			{
				validResources = GetResourcesNotLinkToPackage(packageId, allValidItemAssignments, validResources);
				validLineItemsWithoutResources = GetLineItemsNotLinkToPackage(packageId, allItemAssignments, validLineItemsWithoutResources);
			}
			if ((validResources == null || !validResources.Any()) && (validLineItemsWithoutResources == null || !validLineItemsWithoutResources.Any()))
			{
				return -6;
			}

			RecalculateCostTotalIfLineItemIsOptional(validLineItemsWithoutResources, validResources);

			var lineItemHeaderFkIdsMap = BuildLineItemMaps(validLineItemsWithoutResources, validResources, out lineItemId2ResourcesMap, isMarkupCostCode, out lineItemId2MergedPropMap);
			var validLineItems = GetValidLineItems(lineItemHeaderFkIdsMap, lineItemId2ResourcesMap, BoqSource.Project);
			//var lineItemIds = validLineItems.CollectIds(e => e.Id);
			//var itemAssigns4CalQty = allItemAssignments.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue && lineItemIds.Contains(e.EstLineItemFk));

			IEnumerable<BoQItemLinkedEstimateLineItem> boqItemsLinkedEstimateLine = null;
			IEnumerable<BoQItemLinkedEstimateLineItem> linkedEstimateLines = boqItemsLinkedEstimateLine = validLineItems as IEnumerable<BoQItemLinkedEstimateLineItem>;

			IEnumerable<IBoqItemEntity> boqItems = GetBoqItemsFromLineItems(linkedEstimateLines, (lineItem, boqItem) =>
			{
				lineItem.BoqItemUomId = boqItem.BasUomFk;
				lineItem.BoqItemFactor = boqItem.Factor;
			});

			if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
			{
				foreach (var item in validLineItems)
				{
					item.QuantityTarget = item.WqQuantityTarget;
				}
			}
			else if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemQuantityTotal)
			{
				foreach (var item in validLineItems)
				{
					item.QuantityTarget = item.QuantityTotal;
				}
			}

			var mergedLineItems = MergeLineItem4ProjectBoq(boqItemsLinkedEstimateLine, boqItems, context, lineItemId2MergedPropMap);
			ResetQuantityForAssignLineItems(linkedEstimateLines, context);
			return GeneratePackageBoQ(mergedLineItems, linkedEstimateLines, lineItemId2ResourcesMap, null, context, allValidItemAssignments/*, itemAssigns4CalQty*/, nodeBoqItemsToSave,
				dragNDropInfo);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		int IPackageGenerateBoQWizardLogic.GeneratePackageBoQFromWicBoQ(GeneratePackageBoQFromBoQParam param)
		{
			int packageId = param.PackageId;
			int prcHeaderId = param.PrcHeaderId;
			string quantityTransferFrom = param.QuantityTransferFrom;
			bool considerBoqQtyRelation = param.ConsiderBoqQtyRelation;
			IEnumerable<IEstLineItemEntity> lineItemsWithoutResources = param.LineItemsWithoutResource;
			IEnumerable<IScriptEstResource> newResources = param.NewResources;
			bool isMarkupCostCode = param.IsMarkupCostCode;
			IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSave = param.NodeBoqItemsToSave;
			DragNDropInfo dragNDropInfo = param.DragNDropInfo;

			if ((newResources == null || !newResources.Any()) && (lineItemsWithoutResources == null || !lineItemsWithoutResources.Any()))
			{
				return -6;
			}

			var parameter = new GeneratePackageBoQParameter()
			{
				PackageId = packageId,
				PrcHeaderId = prcHeaderId,
				ConsiderBoqQtyRelation = considerBoqQtyRelation,
				Source = UpdateOptionForBoq.Criteria.WicBoq,
				QuantityTransferFrom = quantityTransferFrom,
				WicCatGroupId = 0,
				IsControllingUnitAsTitle = false,
				UniqueFields = null
			};
			var context = new CreateOrUpdatePackageBoqContext(parameter, false);

			var itemAssignmentLogic = new PrcItemAssignmentLogic();
			var allItemAssignments = itemAssignmentLogic.GetEntitiesByPackageId(packageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();
			var invalidItemAssigns = allItemAssignments.Where(e => e.PrcItemFk.HasValue).ToList();
			var validResources = FetchValidReasources(invalidItemAssigns, newResources);
			var validLineItemsWithoutResources = FetchValidLineItems(invalidItemAssigns, lineItemsWithoutResources);
			if (dragNDropInfo != null && dragNDropInfo.IsForcedToCreateBoq)
			{
				validResources = GetResourcesNotLinkToPackage(packageId, allValidItemAssignments, validResources);
				validLineItemsWithoutResources = GetLineItemsNotLinkToPackage(packageId, allItemAssignments, validLineItemsWithoutResources);
			}
			if ((validResources == null || !validResources.Any()) && (validLineItemsWithoutResources == null || !validLineItemsWithoutResources.Any()))
			{
				return -6;
			}

			RecalculateCostTotalIfLineItemIsOptional(validLineItemsWithoutResources, validResources);

			Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap = null;
			Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap = null;
			var lineItemHeaderFkIdsMap = BuildLineItemMaps(validLineItemsWithoutResources, validResources, out lineItemId2ResourcesMap, isMarkupCostCode, out lineItemId2MergedPropMap);
			var validLineItems = GetValidLineItems(lineItemHeaderFkIdsMap, lineItemId2ResourcesMap, BoqSource.Wic);
			//var lineItemIds = validLineItems.CollectIds(e => e.Id);
			//var itemAssigns4CalQty = allItemAssignments.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue && lineItemIds.Contains(e.EstLineItemFk));

			IEnumerable<BoQItemLinkedEstimateLineItem> boqItemsLinkedEstimateLine = null;
			IEnumerable<BoQItemLinkedEstimateLineItem> linkedEstimateLines = boqItemsLinkedEstimateLine = validLineItems as IEnumerable<BoQItemLinkedEstimateLineItem>;

			IEnumerable<IBoqItemEntity> boqItems = GetBoqItemsFromLineItems(linkedEstimateLines, (lineItem, boqItem) =>
			{
				lineItem.BoqItemUomId = boqItem.BasUomFk;
				lineItem.BoqItemFactor = boqItem.Factor;
			});

			if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
			{
				foreach (var item in validLineItems)
				{
					item.QuantityTarget = item.WqQuantityTarget;
				}
			}

			var mergedLineItems = MergeLineItem4WicBoq(boqItemsLinkedEstimateLine, context, lineItemId2MergedPropMap);
			ResetQuantityForAssignLineItems(linkedEstimateLines, context);
			return GeneratePackageBoQ(mergedLineItems, linkedEstimateLines, lineItemId2ResourcesMap, null, context, allValidItemAssignments/*, itemAssigns4CalQty*/, nodeBoqItemsToSave,
				dragNDropInfo);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="packageId"></param>
		/// <param name="prcHeaderId"></param>
		/// <param name="isControllingUnitAsTitle"></param>
		/// <param name="considerBoqQtyRelation"></param>
		/// <param name="uniqueFields"></param>
		/// <param name="lineItemsWithoutResources"></param>
		/// <param name="newResources"></param>
		/// <param name="isMarkupCostCode">
		/// true - when summing up the cost total, count in the cost total of resources of which COSTCODE.IsCost is false if there are such kind of resuorces.
		/// false - ignore such kind of resources.
		/// </param>
		int IPackageGenerateBoQWizardLogic.CreatePackageBoqFromLineItem(
			int packageId,
			int prcHeaderId,
			bool isControllingUnitAsTitle,
			bool considerBoqQtyRelation,
			IEnumerable<IUniqueFieldProfileEntity> uniqueFields,
			IEnumerable<IEstLineItemEntity> lineItemsWithoutResources,
			IEnumerable<IScriptEstResource> newResources,
			bool isMarkupCostCode)
		{
			if ((newResources == null || !newResources.Any()) && (lineItemsWithoutResources == null || !lineItemsWithoutResources.Any()))
			{
				return -1;
			}

			lineItemsWithoutResources = lineItemsWithoutResources.OrderBy(e => e.Code).ToList();

			var parameter = new GeneratePackageBoQParameter()
			{
				PackageId = packageId,
				PrcHeaderId = prcHeaderId,
				ConsiderBoqQtyRelation = considerBoqQtyRelation,
				Source = UpdateOptionForBoq.Criteria.LineItem,
				QuantityTransferFrom = null,
				WicCatGroupId = 0,
				IsControllingUnitAsTitle = isControllingUnitAsTitle,
				UniqueFields = uniqueFields != null && uniqueFields.Any() ? uniqueFields.Select(e => new UniqueFieldEntity(e)).ToList() : null
			};
			var context = new CreateOrUpdatePackageBoqContext(parameter, false);

			var prcItemAssignmentLogic = new PrcItemAssignmentLogic();
			var estimateWizardLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainWizardLogic>();
			var lineItem2CostGroupLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstLineItem2CostGroupLogic>();
			var costGroupCatalogLogic = new CostGroupCatLogic();

			var package = context.Package ?? new PrcPackageLogic().GetItemByKey(context.PackageId);
			var projectId = package.ProjectFk;
			IDictionary<object, List<IEstLineItemEntity>> mergeMatch = null;
			var allItemAssignments = prcItemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();
			var invalidItemAssigns = allItemAssignments.Where(e => e.PrcPackageFk == context.PackageId && e.PrcItemFk.HasValue).ToList();
			var validResources = FetchValidReasources(invalidItemAssigns, newResources);
			var validLineItemsWithoutResources = FetchValidLineItems(invalidItemAssigns, lineItemsWithoutResources);

			if ((validResources == null || !validResources.Any()) && (validLineItemsWithoutResources == null || !validLineItemsWithoutResources.Any()))
			{
				return -1;
			}

			var allValidItemAssignmentLinkedToPackageBoq = allValidItemAssignments.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue).ToList();
			var allLineItemsLinkedToPackageBoq = GetLineItemsByItemAssignment(allValidItemAssignmentLinkedToPackageBoq).ToList();
			var lineItems = GetLineItemsByResources(validResources).OrderBy(e => e.Code).ToList();
			//var estHeaderIds = resourceList.CollectIds(e => e.EstHeaderFk);

			RecalculateCostTotalIfLineItemIsOptional(validLineItemsWithoutResources, validResources);

			if (validLineItemsWithoutResources != null && validLineItemsWithoutResources.Any())
			{
				lineItems = lineItems.ConcatNoRepeat((a, b) => a.Id == b.Id, validLineItemsWithoutResources).ToList();
			}
			var lineItemIds = lineItems.CollectIds(e => e.Id);
			allLineItemsLinkedToPackageBoq.AddRange(lineItems);
			GetLookupData(allLineItemsLinkedToPackageBoq);

			//var itemAssignments = allItemAssignments.Where(e => e.PrcPackageFk == context.PackageId && lineItemIds.Contains(e.EstLineItemFk) && estHeaderIds.Contains(e.EstHeaderFk) && !e.PrcItemFk.HasValue).ToList();
			//IDictionary<int, IEnumerable<int>> estHeaderId2LineItemIds = GetEstHeaderId2LineItemIdsMap(lineItems.ToEntities(e => new BoQItemLinkedEstimateLineItem(e, BoqSource.LineItem)));
			//itemAssignments = FilterItemAssignsByEstHeaderId2LineItemIdsMap(itemAssignments, estHeaderId2LineItemIds).ToList();
			List<PrcItemAssignmentEntity> validAssignments = new List<PrcItemAssignmentEntity>();
			var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByMainItems<IEstLineItemEntity>(lineItems, e => e.Id, e => e.EstHeaderFk);

			//if (itemAssignments != null && itemAssignments.Any())
			//{
			//	itemAssignments.ToList().ForEach(assign =>
			//	{
			//		var found = lineItems.Where(f => f.EstHeaderFk == assign.EstHeaderFk && f.Id == assign.EstLineItemFk);
			//		if (found.Any())
			//		{
			//			//if (!assign.PrcItemFk.HasValue)
			//			//{
			//				validAssignments.Add(assign);
			//			allValidItemAssignmentLinkedToPackageBoq.Add(assign);
			//			//}
			//		}
			//	});
			//}

			var lineItemMap = new Dictionary<string, List<IEstLineItemEntity>>();
			var lineItemsCombined = lineItems.ToList();
			foreach (var lineItem in allLineItemsLinkedToPackageBoq)
			{
				//var existAssign = allValidItemAssignmentLinkedToPackageBoq.FirstOrDefault(e => e.EstLineItemFk == lineItem.Id && e.EstHeaderFk == lineItem.EstHeaderFk && !e.PrcItemAssignmentFk.HasValue);
				//if (existAssign == null)
				//{
				//	continue;
				//}

				var uniqueKey = string.Empty;
				var index = 0;
				var type = typeof(IEstLineItemEntity);
				var costGroups = lineItem2CostGroups.Where(e => e.RootItemId == lineItem.EstHeaderFk && e.MainItemId == lineItem.Id).ToList();
				if (context.UniqueFields != null && context.UniqueFields.Any())
				{
					foreach (var field in context.UniqueFields)
					{
						var tempField = field;
						var tempFieldCode = tempField.Code;
						object fieldValue = null;
						if (!tempField.Id.HasValue)
						{


							var propertyInfo = type.GetProperty(tempFieldCode);
							if (propertyInfo == null)
							{
								var baseInterface = type.GetInterface("IEstimateLeadingStructureContext");
								if (baseInterface != null)
								{
									propertyInfo = baseInterface.GetProperty(tempFieldCode);
								}
							}
						    if (propertyInfo != null)
							{
								fieldValue = GetPropertyValue(propertyInfo, lineItem);
							}
							else
							{
								throw new BusinessLayerException(string.Format("This field \"{0}\" is not the property of IEstLineItemEntity.", tempFieldCode));
							}
						}
						else
						{
							var tempCostGroup = costGroups.FirstOrDefault(e => e.CostGroupCatFk == tempField.Id.Value);
							fieldValue = tempCostGroup != null ? tempCostGroup.CostGroupFk : new Nullable<int>();
						}

						var uniqueKeyTemp = string.Empty;
						if (index == 0)
						{
							uniqueKeyTemp = "{0}({1})";
						}
						else
						{
							uniqueKeyTemp = "-{0}({1})";
						}

						uniqueKey += string.Format(uniqueKeyTemp, tempFieldCode, fieldValue != null ? fieldValue : string.Empty);
						++index;
					}
				}
				else
				{
					uniqueKey = lineItem.Id.ToString();
				}

				if (!lineItemMap.ContainsKey(uniqueKey))
				{
					lineItemMap.Add(uniqueKey, new List<IEstLineItemEntity>() { lineItem });
				}
				else
				{
					lineItemMap[uniqueKey].Add(lineItem);
				}
			}

			foreach (var keyValue in lineItemMap)
			{
				var list = keyValue.Value;
				var isValid = false;
				foreach (var item in list)
				{
					if (lineItemIds.Contains(item.Id))
					{
						isValid = true;
						break;
					}
				}
				if (isValid)
				{
					lineItemsCombined = lineItemsCombined.ConcatNoRepeat((a, b) => a.Id == b.Id && a.EstHeaderFk == b.EstHeaderFk, list).ToList();
				}
			}

			lineItemsCombined = lineItemsCombined.OrderBy(e => e.Code).ToList();
			IEnumerable<PrcItemAssignmentEntity> validAssignments2 = new List<PrcItemAssignmentEntity>();
			IEnumerable<IScriptEstResource> validResources4Cost = new List<IScriptEstResource>();
			IEnumerable<IEstLineItemEnhanceEntity> validLineItems = new List<IEstLineItemEnhanceEntity>();
			IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLinesWithoutResources = new List<IEstLineItemEnhanceEntity>();

			var tempLineItems = lineItemsCombined.ToEntities(e => new BoQItemLinkedEstimateLineItem(e, BoqSource.LineItem));
			FetchValidData(tempLineItems, ref validAssignments2, ref validResources4Cost, ref validLineItems, ref validLinkedEstimateLinesWithoutResources, context.DoesUpdateBudgetOnly4AssignmentExist, allItemAssignments);
			var groupedValidAssigns = allValidItemAssignments.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue && !e.PrcItemAssignmentFk.HasValue).GroupBy(e => new { EstHeaderFk = e.EstHeaderFk, EstLineItemFk = e.EstLineItemFk, BoqHeaderFk = e.BoqHeaderFk.Value, BoqItemFk = e.BoqItemFk.Value }).Select(e => e.FirstOrDefault()).ToList();

			Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap = null;
			Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap = null;
			var validLineItemsWithoutResources2 = validLineItemsWithoutResources.ConcatNoRepeat((a, b) => a.Id == b.Id && a.EstHeaderFk == b.EstHeaderFk, validLinkedEstimateLinesWithoutResources).ToList();
			var validResources2 = validResources.ConcatNoRepeat((a, b) => a.Id == b.Id && a.EstHeaderFk == b.EstHeaderFk && a.EstLineItemFk == b.EstLineItemFk, validResources4Cost).ToList();
			BuildLineItemMaps(validLineItemsWithoutResources2, validResources2, out lineItemId2ResourcesMap, isMarkupCostCode, out lineItemId2MergedPropMap, validLineItemsWithoutResources, validResources);
			var costGroupComplete = new CostGroupCatLogic().GetCostGroupCatsByModule(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, projectId);

			var allCostGroupCats = new List<CostGroupCatEntity>();
			if (costGroupComplete != null && costGroupComplete.PrjCostGroupCats != null && costGroupComplete.PrjCostGroupCats.Any())
			{
				allCostGroupCats.AddRange(costGroupComplete.PrjCostGroupCats);
			}
			if (costGroupComplete != null && costGroupComplete.LicCostGroupCats != null && costGroupComplete.LicCostGroupCats.Any())
			{
				allCostGroupCats.AddRange(costGroupComplete.LicCostGroupCats);
			}
			List<MainItem2CostGroupEntity> mergedCostGroups = null;
			IEnumerable<IEstLineItemEntity> merged = MergeLineItems(lineItemsCombined, package, out mergeMatch, groupedValidAssigns, allCostGroupCats, lineItem2CostGroups, out mergedCostGroups, context, lineItemId2MergedPropMap, lineItems);
			DoCreatePackageBoqFromLineItem(package, merged, allValidItemAssignments, lineItemId2ResourcesMap, mergeMatch, mergedCostGroups, context);
			return 1;
		}

		#endregion Implement IPackageGenerateBoQWizardLogic

		private void ForEachBoqItem(BoqItemEntity boqItem, Action<BoqItemEntity> actionAfterLoop)
		{
			if (boqItem == null)
			{
				return;
			}

			ForEachBoqItem(boqItem.BoqItemParent, actionAfterLoop);

			if (actionAfterLoop != null)
			{
				actionAfterLoop(boqItem);
			}
		}

		private void CalculateTotals(PrcPackageEntity package)
		{
			var pkgTotalLogic = new PrcPackageTotalLogic();
			var totals = pkgTotalLogic.GetSearchList(e => e.HeaderFk == package.Id);

			if (!totals.Any())
			{
				totals = pkgTotalLogic.CopyFromConfiguration(package);
			}

			if (totals != null && totals.Any())
			{
				pkgTotalLogic.RecalculateTotalsFromHeader(package, totals);

				pkgTotalLogic.Save(totals);
			}
		}

		private Dictionary<int, List<int>> BuildLineItemMaps(IEnumerable<IEstLineItemEntity> lineItems, IEnumerable<IScriptEstResource> resources,
			out Dictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap,
			bool isMarkupCostCode,
			out Dictionary<object, LineItemMergedProperty> lineItemId2MergedPropMap,
			IEnumerable<IEstLineItemEntity> lineItemsToAdd = null,
			IEnumerable<IScriptEstResource> resourceToAdd = null)
		{
			lineItemId2ResourcesMap = new Dictionary<object, IEnumerable<IScriptEstResource>>();
			lineItemId2MergedPropMap = new Dictionary<object, LineItemMergedProperty>();
			var lineItemHeaderFkIdsMap = new Dictionary<int, List<int>>();

			if (lineItems != null && lineItems.Any())
			{
				foreach (var lineItem in lineItems)
				{
					var needCalPrice = lineItemsToAdd != null ? lineItemsToAdd.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.Id == lineItem.Id) : true;
					if (!lineItemId2ResourcesMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
					{
						lineItemId2ResourcesMap.Add(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }, new List<IScriptEstResource>());
						lineItemId2MergedPropMap.Add(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }, new LineItemMergedProperty()
						{
							CostTotal = needCalPrice ? lineItem.CostTotal : 0,
							BudgetTotal = needCalPrice ? lineItem.Budget : 0
						});
					}
					else
					{
						lineItemId2MergedPropMap[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }].CostTotal += needCalPrice ? lineItem.CostTotal : 0;
					}

					if (!lineItemHeaderFkIdsMap.ContainsKey(lineItem.EstHeaderFk))
					{
						lineItemHeaderFkIdsMap.Add(lineItem.EstHeaderFk, new List<int>() { lineItem.Id });
					}
					else if (!lineItemHeaderFkIdsMap[lineItem.EstHeaderFk].Contains(lineItem.Id))
					{
						lineItemHeaderFkIdsMap[lineItem.EstHeaderFk].Add(lineItem.Id);
					}
				}
			}

			if (resources != null && resources.Any())
			{
				foreach (var resource in resources)
				{
					var needCalPrice = resourceToAdd != null ? resourceToAdd.Any(e => e.EstHeaderFk == resource.EstHeaderFk &&
						e.EstLineItemFk == resource.EstLineItemFk && e.Id == resource.Id) : true;
					if (!lineItemId2ResourcesMap.ContainsKey(new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }))
					{
						lineItemId2ResourcesMap.Add(new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }, new List<IScriptEstResource>() { resource });
						lineItemId2MergedPropMap.Add(new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }, new LineItemMergedProperty()
						{
							CostTotal = 0,
							BudgetTotal = 0
						});
						if (resource.IsCost || isMarkupCostCode)
						{
							lineItemId2MergedPropMap[new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }].CostTotal = needCalPrice ? resource.CostTotal : 0;
						}

						if (resource.IsBudget && !resource.IsDisabled && !resource.IsDisabledPrc)
						{
							lineItemId2MergedPropMap[new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }].BudgetTotal = needCalPrice ? resource.Budget : 0;
						}
					}
					else
					{
						var temp = lineItemId2ResourcesMap[new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }];
						if (temp.Any())
						{
							lineItemId2ResourcesMap[new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }] = temp.ConcatNoRepeat((a, b) => a.Id == b.Id, new List<IScriptEstResource>() { resource });
							if (resource.IsCost || isMarkupCostCode)
							{
								lineItemId2MergedPropMap[new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }].CostTotal += needCalPrice ? resource.CostTotal : 0;
							}

							if (resource.IsBudget && !resource.IsDisabled && !resource.IsDisabledPrc)
							{
								lineItemId2MergedPropMap[new { EstHeaderFk = resource.EstHeaderFk, EstLineItemFk = resource.EstLineItemFk }].BudgetTotal += needCalPrice ? resource.Budget : 0;
							}
						}
					}

					if (!lineItemHeaderFkIdsMap.ContainsKey(resource.EstHeaderFk))
					{
						lineItemHeaderFkIdsMap.Add(resource.EstHeaderFk, new List<int>() { resource.EstLineItemFk });
					}
					else if (!lineItemHeaderFkIdsMap[resource.EstHeaderFk].Contains(resource.EstLineItemFk))
					{
						lineItemHeaderFkIdsMap[resource.EstHeaderFk].Add(resource.EstLineItemFk);
					}
				}
			}

			return lineItemHeaderFkIdsMap;
		}

		private bool UpdatePrcAssignmentItemWithBoqItem(int boqHeaderId, int boqItemId,
			//IEnumerable<IBoqItemEntity> boqItems,
			IDictionary<string, List<BoqItemEntity>> boqMap,
			IDictionary<RVPC.IdentificationData, List<BoqItemEntity>> boqWicMap,
			Dictionary<RVPC.IdentificationData, BoqItemEntity> sourceBoQItems,
			bool isFromProject, ref PrcItemAssignmentEntity prcItemAssignment,
			bool isNew)
		{
			var isUpdated = false;
			IBoqItemEntity boqItem = null;
			var assignmentBoqHeaderId = prcItemAssignment.BoqHeaderFk;
			if (isFromProject)
			{
				if (!isNew)
				{
					return false;
				}
				BoqItemEntity sourceBoq = GetValueFromDictionary(sourceBoQItems, boqItemId, boqHeaderId);
				if (sourceBoq == null)
				{
					return false;
				}

				boqItem = GetValueFromDictionary(boqMap, sourceBoq.Reference).FirstOrDefault(e => e.BoqHeaderFk == assignmentBoqHeaderId); // boqItems.FirstOrDefault(e => e.BoqHeaderFk == assignmentBoqHeaderId && !string.IsNullOrEmpty(e.Reference) && e.Reference.Equals(sourceBoq.Reference, StringComparison.CurrentCultureIgnoreCase));
			}
			else
			{
				boqItem = GetValueFromDictionary(boqWicMap, boqItemId, boqHeaderId).FirstOrDefault(e => e.BoqHeaderFk == assignmentBoqHeaderId); // boqItems.FirstOrDefault(e => e.BoqItemWicItemFk == boqItemId && e.BoqItemWicBoqFk == boqHeaderId);
			}
			if (boqItem != null && prcItemAssignment.BoqItemFk != boqItem.Id)
			{
				prcItemAssignment.BoqItemFk = boqItem.Id;
				isUpdated = true;
			}
			return isUpdated;
		}

		private void GetValidData(
			IEnumerable<PrcItemAssignmentEntity> itemAssignments,
			IEnumerable<IEstLineItemEnhanceEntity> linkedEstimateLines,
			ref IEnumerable<PrcItemAssignmentEntity> validAssignments,
			ref IEnumerable<IScriptEstResource> validResources,
			ref IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLines,
			ref IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLinesWithoutResources,
			bool doesUpdateBudgetOnly4AssignmentExist)
		{
			validLinkedEstimateLines = new List<IEstLineItemEnhanceEntity>();
			validLinkedEstimateLinesWithoutResources = new List<IEstLineItemEnhanceEntity>();
			IEnumerable<PrcItemAssignmentEntity> invalidItemAssignments = null;

			if (itemAssignments != null && itemAssignments.Any())
			{
				invalidItemAssignments = itemAssignments.Where(e => e.PrcItemFk.HasValue).ToList();
				validAssignments = itemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();// && e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue).ToList();
			}

			if (invalidItemAssignments != null && invalidItemAssignments.Any())
			{
				foreach (var invalid in invalidItemAssignments)
				{
					if (validResources != null && validResources.Any())
					{
						validResources = validResources.Where(e => e.EstHeaderFk != invalid.EstHeaderFk && e.EstLineItemFk != invalid.EstLineItemFk && e.Id != invalid.EstResourceFk);
					}

					if (!invalid.EstResourceFk.HasValue && linkedEstimateLines != null && linkedEstimateLines.Any())
					{
						linkedEstimateLines = linkedEstimateLines.Where(e => e.EstHeaderFk != invalid.EstHeaderFk && e.EstLineItemFk != invalid.EstLineItemFk);
					}
				}
			}

			if (linkedEstimateLines != null && linkedEstimateLines.Any())
			{
				foreach (var lineItem in linkedEstimateLines)
				{
					var isLineItemWithoutResources = validAssignments.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);
					if (validResources != null && validResources.Any())
					{
						var foundResources = validResources.Where(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id).ToList();
						if (foundResources.Any())
						{
							if (isLineItemWithoutResources)
							{
								validResources = validResources.Where(e => !foundResources.Any(f => f.Id == e.Id && f.EstHeaderFk == e.EstHeaderFk && f.EstLineItemFk == e.EstLineItemFk)).ToList();
								validLinkedEstimateLines = validLinkedEstimateLines.ConcatOne(lineItem);
								validLinkedEstimateLinesWithoutResources = validLinkedEstimateLinesWithoutResources.ConcatOne(lineItem);
							}
							else
							{
								validLinkedEstimateLines = validLinkedEstimateLines.ConcatOne(lineItem);
							}
						}
						else if (isLineItemWithoutResources)
						{
							validLinkedEstimateLines = validLinkedEstimateLines.ConcatOne(lineItem);
							validLinkedEstimateLinesWithoutResources = validLinkedEstimateLinesWithoutResources.ConcatOne(lineItem);
						}
					}
					else if (isLineItemWithoutResources)
					{
						validLinkedEstimateLines = validLinkedEstimateLines.ConcatOne(lineItem);
						validLinkedEstimateLinesWithoutResources = validLinkedEstimateLinesWithoutResources.ConcatOne(lineItem);
					}
				}
			}

			//validAssignments = itemAssignments.Where(e => !e.PrcItemFk.HasValue && e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue).ToList();
			validResources = validResources.ToList();

			if (doesUpdateBudgetOnly4AssignmentExist)
			{
				var invalidLineItems = new List<IEstLineItemEnhanceEntity>();
				var invalidResources = new List<IScriptEstResource>();

				foreach (var lineItem in validLinkedEstimateLines)
				{
					if (!validAssignments.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id))
					{
						invalidLineItems.Add(lineItem);
					}
				}

				foreach (var resource in validResources)
				{
					if (!validAssignments.Any(e => e.EstHeaderFk == resource.EstHeaderFk && e.EstLineItemFk == resource.EstLineItemFk && e.EstResourceFk == resource.Id))
					{
						invalidResources.Add(resource);
					}
				}

				if (invalidLineItems.Any())
				{
					validLinkedEstimateLines = validLinkedEstimateLines.Where(e => !invalidLineItems.Any(f => f.Id == e.Id)).ToList();
					validLinkedEstimateLinesWithoutResources = validLinkedEstimateLinesWithoutResources.Where(e => !invalidLineItems.Any(f => f.Id == e.Id)).ToList();
				}

				if (invalidResources.Any())
				{
					validResources = validResources.Where(e => !invalidResources.Any(f => f.Id == e.Id)).ToList();
				}
			}
		}

		private IDictionary<int, IEnumerable<int>> GetEstHeaderId2LineItemIdsMap(IEnumerable<IEstLineItemEnhanceEntity> linkedEstimateLines)
		{
			IDictionary<int, IEnumerable<int>> estHeaderId2LineItemIds = new Dictionary<int, IEnumerable<int>>();
			foreach (var lineItem in linkedEstimateLines)
			{
				if (!estHeaderId2LineItemIds.ContainsKey(lineItem.EstHeaderFk))
				{
					estHeaderId2LineItemIds.Add(lineItem.EstHeaderFk, new List<int>() { lineItem.Id });
				}
				else
				{
					estHeaderId2LineItemIds[lineItem.EstHeaderFk] = estHeaderId2LineItemIds[lineItem.EstHeaderFk].Concat(new List<int>() { lineItem.Id });
				}
			}

			return estHeaderId2LineItemIds;
		}

		private IEnumerable<PrcItemAssignmentEntity> FilterItemAssignsByEstHeaderId2LineItemIdsMap(IEnumerable<PrcItemAssignmentEntity> itemAssignments, IDictionary<int, IEnumerable<int>> estHeaderId2LineItemIds)
		{
			return itemAssignments.Where(e =>
			{
				if (estHeaderId2LineItemIds.ContainsKey(e.EstHeaderFk))
				{
					return estHeaderId2LineItemIds[e.EstHeaderFk].Contains(e.EstLineItemFk);
				}
				return false;
			});
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="linkedEstimateLines"></param>
		/// <param name="validAssignments"></param>
		/// <param name="validResources4Cost"></param>
		/// <param name="validLinkedEstimateLines"></param>
		/// <param name="validLinkedEstimateLinesWithoutResources"></param>
		/// <param name="doesUpdateBudgetOnly4AssignmentExist"></param>
		/// <param name="allItemAssignments"></param>
		private void FetchValidData(
			IEnumerable<IEstLineItemEnhanceEntity> linkedEstimateLines,
			ref IEnumerable<PrcItemAssignmentEntity> validAssignments,
			ref IEnumerable<IScriptEstResource> validResources4Cost,
			ref IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLines,
			ref IEnumerable<IEstLineItemEnhanceEntity> validLinkedEstimateLinesWithoutResources,
			bool doesUpdateBudgetOnly4AssignmentExist,
			IEnumerable<PrcItemAssignmentEntity> allItemAssignments)
		{
			var resourceLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainResourceLogic>();

			var lineItemIds = linkedEstimateLines.CollectIds(e => e.Id);
			var estHeaderIds = linkedEstimateLines.CollectIds(e => e.EstHeaderFk);
			var itemAssignments = allItemAssignments.Where(e => lineItemIds.Contains(e.EstLineItemFk) && estHeaderIds.Contains(e.EstHeaderFk)).ToList();
			IDictionary<int, IEnumerable<int>> estHeaderId2LineItemIds = GetEstHeaderId2LineItemIdsMap(linkedEstimateLines);
			itemAssignments = FilterItemAssignsByEstHeaderId2LineItemIdsMap(itemAssignments, estHeaderId2LineItemIds).ToList();
			var resourceIds = itemAssignments.Where(e => e.EstResourceFk.HasValue).ToDictionary(e => new RVPC.IdentificationData() { Id = e.EstResourceFk.Value, PKey1 = e.EstHeaderFk, PKey2 = e.EstLineItemFk }, e => true);

			List<IScriptEstResource> resources = new List<IScriptEstResource>();
			foreach (var keyValue in estHeaderId2LineItemIds)
			{
				var headerId = keyValue.Key;
				var lineItemIdList = keyValue.Value;
				var tempReses = resourceLogic.GetResourceListByHeaderIdAndLineItemIds(headerId, lineItemIdList);
				bool resourceValue = false;
				resources.AddRange(tempReses.Where(e => resourceIds.TryGetValue(new RVPC.IdentificationData()
				{
					Id = e.Id,
					PKey1 = e.EstHeaderFk,
					PKey2 = e.EstLineItemFk
				}, out resourceValue)).ToList());
			}

			validResources4Cost = resources;
			GetValidData(itemAssignments, linkedEstimateLines, ref validAssignments, ref validResources4Cost, ref validLinkedEstimateLines, ref validLinkedEstimateLinesWithoutResources, doesUpdateBudgetOnly4AssignmentExist);
		}

		private IEnumerable<IEstLineItemEnhanceEntity> GetValidLineItems(IDictionary<int, List<int>> lineItemHeaderFkIdsMap,
			IDictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap, BoqSource source)
		{
			var validLineItems = new List<BoQItemLinkedEstimateLineItem>();
			if (lineItemId2ResourcesMap == null)
			{
				return validLineItems;
			}

			var lineItems = GetLineItemsByEstHeader2LineItemIdsMap(lineItemHeaderFkIdsMap);

			foreach (var lineItem in lineItems)
			{
				if (lineItemId2ResourcesMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
				{
					var linkedItem = new BoQItemLinkedEstimateLineItem(lineItem, source);
					if (linkedItem.BoQHeaderId > 0 && linkedItem.BoQItemId > 0)
					{
						validLineItems.Add(linkedItem);
					}
				}
			}
			return validLineItems;
		}

		private void DoCreatePackageBoqFromLineItem(
			PrcPackageEntity package,
			IEnumerable<IEstLineItemEntity> merged,
			IEnumerable<PrcItemAssignmentEntity> validAssignments,
			IDictionary<object, IEnumerable<IScriptEstResource>> lineItemId2ResourcesMap,
			IDictionary<object, List<IEstLineItemEntity>> mergeMatch,
			IEnumerable<MainItem2CostGroupEntity> mergedCostGroups,
			CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);
			var boqItemLogic = new BoqItemLogic();
			BOQMAIN.BoqTypeLogic boqTypeLogic = new BOQMAIN.BoqTypeLogic();
			PrcBoqLogic prcBoqLogic = new PrcBoqLogic();
			var itemAssignmentLogic = new PrcItemAssignmentLogic();
			var boqItem2CostGrpLogic = new BoqItem2CostGroupLogic();

			string rootReferencNo = Resources.BoqRootItemReferenceNoFromLineItem;
			IEnumerable<BOQMAIN.BoqStructureEntity> boqStructures = null;
			int? maxIndex = null;
			int packageId = package.Id;
			_packageTaxCodeFk = package.TaxCodeFk;
			var isControllingUnitAsTitle = context.IsControllingUnitAsTitle;

			var prcBoqExtended4LineItem = GetBoqExtended4LineItem(context.PrcHeaderId, rootReferencNo, out boqStructures, out maxIndex);

			List<BoqItemEntity> toUpdate = new List<BoqItemEntity>();
			List<BoqItemEntity> toCreate = new List<BoqItemEntity>();
			IDictionary<int, int> lineItemId2BoqItemId = new Dictionary<int, int>();
			List<PrcItemAssignmentEntity> itemAssignmentToSave = new List<PrcItemAssignmentEntity>();
			List<BoqItem2CostGroupEntity> boqItem2CostGrpToSave = new List<BoqItem2CostGroupEntity>();
			var itemAssignmentBoqMap = new Dictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>>();
			var itemAssignmentChildrenMap = new Dictionary<int, List<PrcItemAssignmentEntity>>();
			var itemAssignmentEstimateMap = new Dictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>>();
			var newChildrenMap = new Dictionary<PrcItemAssignmentEntity, IEnumerable<PrcItemAssignmentEntity>>();

			if (validAssignments != null && validAssignments.Any())
			{
				foreach (var itemAssign in validAssignments)
				{
					if (itemAssign.BoqHeaderFk.HasValue && itemAssign.BoqItemFk.HasValue)
					{
						if (!itemAssign.PrcItemAssignmentFk.HasValue)
						{
							var boqId = new RVPC.IdentificationData() { Id = itemAssign.BoqItemFk.Value, PKey1 = itemAssign.BoqHeaderFk.Value };
							List<PrcItemAssignmentEntity> list = null;
							if (!itemAssignmentBoqMap.TryGetValue(boqId, out list))
							{
								itemAssignmentBoqMap.Add(boqId, new List<PrcItemAssignmentEntity>() { itemAssign });
							}
							else
							{
								list.Add(itemAssign);
							}
						}
						else
						{
							if (!itemAssignmentChildrenMap.ContainsKey(itemAssign.PrcItemAssignmentFk.Value))
							{
								itemAssignmentChildrenMap.Add(itemAssign.PrcItemAssignmentFk.Value, new List<PrcItemAssignmentEntity>() { itemAssign });
							}
							else
							{
								itemAssignmentChildrenMap[itemAssign.PrcItemAssignmentFk.Value].Add(itemAssign);
							}
						}
					}

					var estimateId = new RVPC.IdentificationData() { Id = itemAssign.EstLineItemFk, PKey1 = itemAssign.EstHeaderFk };
					List<PrcItemAssignmentEntity> list2 = null;
					if (!itemAssignmentEstimateMap.TryGetValue(estimateId, out list2))
					{
						itemAssignmentEstimateMap.Add(estimateId, new List<PrcItemAssignmentEntity>() { itemAssign });
					}
					else
					{
						list2.Add(itemAssign);
					}
				}
			}

			// if the prc boq exist in the database, update the source and create new boq items under this source
			if (prcBoqExtended4LineItem.Any())
			{
				var boqHeaderIds = new List<int>();
				foreach (var boqExtended in prcBoqExtended4LineItem)
				{
					var rootItems = boqItemLogic.GetBoqItemList(boqExtended.BoqHeader.Id, 0, 99);
					if (!rootItems.Any())
					{
						continue;
					}

					var boqHeaderId = boqExtended.BoqHeader.Id;

					if (!boqExtended.BoqHeader.BoqStructureFk.HasValue)
					{
						continue;
					}

					var boqStructure = boqStructures.FirstOrDefault(e => e.Id == boqExtended.BoqHeader.BoqStructureFk.Value);

					if (boqStructure == null)
					{
						continue;
					}
					Dictionary<RVPC.IdentificationData, BoqItemEntity> targetBoqMap = rootItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }, e => e);
					foreach (var lineItem in merged)
					{
						var itemAssigns = validAssignments.ToList(); //.Where(e => e.EstLineItemFk == lineItem.Id && e.EstHeaderFk == lineItem.EstHeaderFk && !e.PrcItemAssignmentFk.HasValue).ToList();
						var tempToCreate = new List<BoqItemEntity>();
						var tempToUpdate = new List<BoqItemEntity>();
						var tempReplacementToUpdate = new List<BoqItemEntity>();
						var rootItem = rootItems.FirstOrDefault(e => e.Id == boqExtended.BoqRootItem.Id && e.BoqHeaderFk == boqExtended.BoqRootItem.BoqHeaderFk);
						UpdateOrCreateBoqItems(
							1,
							boqHeaderId,
							rootItem,
							lineItem,
							boqStructure,
							mergedCostGroups,
							isControllingUnitAsTitle,
							ref tempToUpdate,
							ref tempToCreate,
							ref itemAssigns,
							ref boqItem2CostGrpToSave,
							context.IsUpdateOperation,
							context.DoesUpdateBudgetOnly4AssignmentExist,
							itemAssignmentBoqMap,
							itemAssignmentChildrenMap,
							targetBoqMap,
							ref tempReplacementToUpdate,
							mergeMatch);

						#region create and delete prc item assignment
						var tempToCreateAssign = tempToCreate.ConcatNoRepeat((a, b) => a.Id == b.Id, tempToUpdate);
						if (tempToCreateAssign.Any())
						{
							var boqItemPosition = tempToCreateAssign.FirstOrDefault(e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position);

							if (lineItemId2ResourcesMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
							{
								var resoruces = lineItemId2ResourcesMap[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }];
								if (resoruces != null)
								{
									if (resoruces.Any())
									{
										foreach (var res in resoruces)
										{
											var found = GetValueFromDictionary(itemAssignmentEstimateMap, lineItem.Id, lineItem.EstHeaderFk)
												.FirstOrDefault(e => e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue); //  validAssignments.FirstOrDefault(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue);

											if (found == null)
											{
												var newItemAssignment = CreateItemAssignment(lineItem, packageId, boqHeaderId, boqItemPosition.Id, res.Id);
												itemAssignmentToSave.Add(newItemAssignment);
												var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(newItemAssignment))
													{
														newChildrenMap.Add(newItemAssignment, newChildren);
													}
													itemAssignmentToSave.AddRange(newChildren);
												}
											}
											else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
											{
												found.BoqHeaderFk = boqHeaderId;
												found.BoqItemFk = boqItemPosition.Id;
												itemAssignmentToSave.Add(found);
												var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(found))
													{
														newChildrenMap.Add(found, newChildren);
													}
													itemAssignmentToSave.AddRange(newChildren);
												}
											}
										}
									}
									else
									{
										var found = GetValueFromDictionary(itemAssignmentEstimateMap, lineItem.Id, lineItem.EstHeaderFk)
												.FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);

										if (found == null)
										{
											var newItemAssignment = CreateItemAssignment(lineItem, packageId, boqHeaderId, boqItemPosition.Id, null);
											itemAssignmentToSave.Add(newItemAssignment);
											var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
											if (newChildren != null && newChildren.Any())
											{
												if (!newChildrenMap.ContainsKey(newItemAssignment))
												{
													newChildrenMap.Add(newItemAssignment, newChildren);
												}
												itemAssignmentToSave.AddRange(newChildren);
											}
										}
										else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
										{
											found.BoqHeaderFk = boqHeaderId;
											found.BoqItemFk = boqItemPosition.Id;
											itemAssignmentToSave.Add(found);
											var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
											if (newChildren != null && newChildren.Any())
											{
												if (!newChildrenMap.ContainsKey(found))
												{
													newChildrenMap.Add(found, newChildren);
												}
												itemAssignmentToSave.AddRange(newChildren);
											}
										}
									}
								}

							}

							if (mergeMatch.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
							{
								var matches = mergeMatch[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }];
								foreach (var match in matches)
								{
									if (!lineItemId2ResourcesMap.ContainsKey(new { EstHeaderFk = match.EstHeaderFk, EstLineItemFk = match.Id }))
									{
										continue;
									}
									var reses4Merged = lineItemId2ResourcesMap[new { EstHeaderFk = match.EstHeaderFk, EstLineItemFk = match.Id }];
									if (reses4Merged != null)
									{
										if (reses4Merged.Any())
										{
											foreach (var res in reses4Merged)
											{
												var found = GetValueFromDictionary(itemAssignmentEstimateMap, match.Id, match.EstHeaderFk)
												.FirstOrDefault(e => e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == match.EstHeaderFk && e.EstLineItemFk == match.Id && e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue);

												if (found == null)
												{
													var newItemAssignment = CreateItemAssignment(match, packageId, boqHeaderId, boqItemPosition.Id, res.Id);
													itemAssignmentToSave.Add(newItemAssignment);
													var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(newItemAssignment))
														{
															newChildrenMap.Add(newItemAssignment, newChildren);
														}
														itemAssignmentToSave.AddRange(newChildren);
													}
												}
												else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
												{
													found.BoqHeaderFk = boqHeaderId;
													found.BoqItemFk = boqItemPosition.Id;
													itemAssignmentToSave.Add(found);
													var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(found))
														{
															newChildrenMap.Add(found, newChildren);
														}
														itemAssignmentToSave.AddRange(newChildren);
													}
												}
											}
										}
										else
										{
											var found = GetValueFromDictionary(itemAssignmentEstimateMap, match.Id, match.EstHeaderFk)
												.FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == match.EstHeaderFk && e.EstLineItemFk == match.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);

											if (found == null)
											{
												var newItemAssignment = CreateItemAssignment(match, packageId, boqHeaderId, boqItemPosition.Id, null);
												itemAssignmentToSave.Add(newItemAssignment);
												var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(newItemAssignment))
													{
														newChildrenMap.Add(newItemAssignment, newChildren);
													}
													itemAssignmentToSave.AddRange(newChildren);
												}
											}
											else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
											{
												found.BoqHeaderFk = boqHeaderId;
												found.BoqItemFk = boqItemPosition.Id;
												itemAssignmentToSave.Add(found);
												var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(found))
													{
														newChildrenMap.Add(found, newChildren);
													}
													itemAssignmentToSave.AddRange(newChildren);
												}
											}
										}
									}
								}
							}
						}
						#endregion create and delete prc item assignment
						toCreate.AddRange(tempToCreate);
						toUpdate.AddRange(tempToUpdate);
						toUpdate.AddRange(tempReplacementToUpdate);
						tempToCreate.Clear();
						tempToUpdate.Clear();
					}
					boqHeaderIds.Add(boqHeaderId);
				}

				var assignmentToCreate = itemAssignmentToSave.Where(e => e.Version == 0).ToList();
				var itemAssignIds = itemAssignmentLogic.GetNextIds(assignmentToCreate.Count).GetEnumerator();
				foreach (var item in assignmentToCreate)
				{
					itemAssignIds.MoveNext();
					item.Id = itemAssignIds.Current;
					if (newChildrenMap.ContainsKey(item))
					{
						newChildrenMap[item].ExtendEach(e => e.PrcItemAssignmentFk = item.Id);
					}
				}

				var toSave = toCreate.ConcatNoRepeat((a, b) => a.Id == b.Id, toUpdate).ToList();

				using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
				{
					SaveBoqItemTranslation(boqItemLogic, toCreate);
					// boq items and recalculate the boq tree
					SaveAndRecalculateBoqItems(package, boqHeaderIds, toSave, package.ExchangeRate);

					// boq item to cost group
					boqItem2CostGrpLogic.SaveEntities(boqItem2CostGrpToSave);

					// prc item assignment
					itemAssignmentLogic.Save(itemAssignmentToSave);
					//itemAssignmentLogic.BulkSave(itemAssignmentLogic.GetDbModel(), itemAssignmentToSave);
					// package totals
					CalculateTotals(package);

					// package update option
					new PrcPackageUpdateOptionLogic().CreateOrSaveUpdateOption(context);

					transaction.Complete();
				}
			}
			else if (!context.DoesUpdateBudgetOnly4AssignmentExist)
			{
				var boqHeaderLogic = new BoqHeaderLogic();

				// create new prc boq, boq header, boq structure, boq root item and boq items
				if (maxIndex.HasValue)
				{
					rootReferencNo = String.Format(rootReferencNo + "({0})", maxIndex + 1);
				}

				// Create Boq Header
				var creationParam = new BoqHeaderLogic.BoqHeaderCreationParam { BasCurrencyFk = package.CurrencyFk };
				var boqheader = boqHeaderLogic.CreateEntity(false, creationParam); // #!# !! mp20150429: at the moment, header is stored in db after creation, ...otherwise problems with boq properties (boq-structure/structure details are still stored immediately in db and require a stored boq-header) -> have to correct...

				// Create and assigns Boq Structure
				var boqStructure = boqHeaderLogic.CreateAndAssignNewGaebBoqStructureToPackageBoqHeader(boqheader);
				boqStructure.BoqRoundingConfigFk = 0;
				boqStructure.BoqRoundingConfig = null;

				// Create Boq root item
				var boqitem = boqItemLogic.CreateBoqRootItem(boqheader, false, rootReferencNo);
				var briefInfo = Resources.BoqRootItemBriefInfoFromLineItem;
				boqitem.BriefInfo = new Platform.Common.DescriptionTranslateType(briefInfo);

				// create prc boq
				var newPrcBoq = prcBoqLogic.Create(context.PrcHeaderId, boqheader.Id, packageId);

				// create Boq Items
				foreach (var lineItem in merged)
				{
					var itemAssigns = validAssignments.ToList(); //.Where(e => e.EstLineItemFk == lineItem.Id && !e.PrcItemAssignmentFk.HasValue).ToList();
					var tempToCreate = new List<BoqItemEntity>();
					var tempReplacementToUpdate = new List<BoqItemEntity>();
					UpdateOrCreateBoqItems(
							1,
							boqheader.Id,
							boqitem,
							lineItem,
							boqStructure,
							mergedCostGroups,
							isControllingUnitAsTitle,
							ref toUpdate,
							ref tempToCreate,
							ref itemAssigns,
							ref boqItem2CostGrpToSave,
							context.IsUpdateOperation,
							context.DoesUpdateBudgetOnly4AssignmentExist,
							itemAssignmentBoqMap,
							itemAssignmentChildrenMap,
							null,
							ref tempReplacementToUpdate,
							mergeMatch);

					#region create prc item assignment
					if (tempToCreate.Any())
					{
						var resources = lineItemId2ResourcesMap[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }];
						var boqItemPosition = tempToCreate.FirstOrDefault(e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Position);
						if (resources != null)
						{
							if (resources.Any())
							{
								foreach (var res in resources)
								{
									var found = GetValueFromDictionary(itemAssignmentEstimateMap, lineItem.Id, lineItem.EstHeaderFk)
												.FirstOrDefault(e => e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue);  // validAssignments.FirstOrDefault(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue);

									if (found == null)
									{
										var newItemAssignment = CreateItemAssignment(lineItem, packageId, boqheader.Id, boqItemPosition.Id, res.Id);
										itemAssignmentToSave.Add(newItemAssignment);
										var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
										if (newChildren != null && newChildren.Any())
										{
											if (!newChildrenMap.ContainsKey(newItemAssignment))
											{
												newChildrenMap.Add(newItemAssignment, newChildren);
											}
											itemAssignmentToSave.AddRange(newChildren);
										}
									}
									else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
									{
										found.BoqHeaderFk = boqheader.Id;
										found.BoqItemFk = boqItemPosition.Id;
										itemAssignmentToSave.Add(found);
										var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
										if (newChildren != null && newChildren.Any())
										{
											if (!newChildrenMap.ContainsKey(found))
											{
												newChildrenMap.Add(found, newChildren);
											}
											itemAssignmentToSave.AddRange(newChildren);
										}
									}
								}
							}
							else
							{
								var found = GetValueFromDictionary(itemAssignmentEstimateMap, lineItem.Id, lineItem.EstHeaderFk)
												.FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);

								if (found == null)
								{
									var newItemAssignment = CreateItemAssignment(lineItem, packageId, boqheader.Id, boqItemPosition.Id, null);
									itemAssignmentToSave.Add(newItemAssignment);
									var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
									if (newChildren != null && newChildren.Any())
									{
										if (!newChildrenMap.ContainsKey(newItemAssignment))
										{
											newChildrenMap.Add(newItemAssignment, newChildren);
										}
										itemAssignmentToSave.AddRange(newChildren);
									}
								}
								else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
								{
									found.BoqHeaderFk = boqheader.Id;
									found.BoqItemFk = boqItemPosition.Id;
									itemAssignmentToSave.Add(found);
									var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
									if (newChildren != null && newChildren.Any())
									{
										if (!newChildrenMap.ContainsKey(found))
										{
											newChildrenMap.Add(found, newChildren);
										}
										itemAssignmentToSave.AddRange(newChildren);
									}
								}
							}
						}

						if (mergeMatch.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
						{
							var matches = mergeMatch[new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }];
							foreach (var match in matches)
							{
								if (match.EstHeaderFk != lineItem.EstHeaderFk || match.Id != lineItem.Id)
								{
									if (lineItemId2ResourcesMap.ContainsKey(new { EstHeaderFk = lineItem.EstHeaderFk, EstLineItemFk = lineItem.Id }))
									{
										var reses4Merged = lineItemId2ResourcesMap[new { EstHeaderFk = match.EstHeaderFk, EstLineItemFk = match.Id }];
										if (reses4Merged != null)
										{
											if (reses4Merged.Any())
											{
												foreach (var res in reses4Merged)
												{
													var found = GetValueFromDictionary(itemAssignmentEstimateMap, match.Id, match.EstHeaderFk)
												.FirstOrDefault(e => e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == match.EstHeaderFk && e.EstLineItemFk == match.Id && e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue);

													if (found == null)
													{
														var newItemAssignment = CreateItemAssignment(match, packageId, boqheader.Id, boqItemPosition.Id, res.Id);
														itemAssignmentToSave.Add(newItemAssignment);
														var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
														if (newChildren != null && newChildren.Any())
														{
															if (!newChildrenMap.ContainsKey(newItemAssignment))
															{
																newChildrenMap.Add(newItemAssignment, newChildren);
															}
															itemAssignmentToSave.AddRange(newChildren);
														}
													}
													else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
													{
														found.BoqHeaderFk = boqheader.Id;
														found.BoqItemFk = boqItemPosition.Id;
														itemAssignmentToSave.Add(found);
														var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
														if (newChildren != null && newChildren.Any())
														{
															if (!newChildrenMap.ContainsKey(found))
															{
																newChildrenMap.Add(found, newChildren);
															}
															itemAssignmentToSave.AddRange(newChildren);
														}
													}
												}
											}
											else
											{
												var found = GetValueFromDictionary(itemAssignmentEstimateMap, match.Id, match.EstHeaderFk)
													.FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == match.EstHeaderFk && e.EstLineItemFk == match.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);

												if (found == null)
												{
													var newItemAssignment = CreateItemAssignment(match, packageId, boqheader.Id, boqItemPosition.Id, null);
													itemAssignmentToSave.Add(newItemAssignment);
													var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(newItemAssignment))
														{
															newChildrenMap.Add(newItemAssignment, newChildren);
														}
														itemAssignmentToSave.AddRange(newChildren);
													}
												}
												else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
												{
													found.BoqHeaderFk = boqheader.Id;
													found.BoqItemFk = boqItemPosition.Id;
													itemAssignmentToSave.Add(found);
													var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(found))
														{
															newChildrenMap.Add(found, newChildren);
														}
														itemAssignmentToSave.AddRange(newChildren);
													}
												}
											}
										}
									}
								}
								else
								{
									if (resources != null)
									{
										if (resources.Any())
										{
											foreach (var res in resources)
											{
												var found = GetValueFromDictionary(itemAssignmentEstimateMap, match.Id, match.EstHeaderFk)
													.FirstOrDefault(e => e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == match.EstHeaderFk && e.EstLineItemFk == match.Id && e.EstResourceFk == res.Id && !e.PrcItemAssignmentFk.HasValue);

												if (found == null)
												{
													var newItemAssignment = CreateItemAssignment(match, packageId, boqheader.Id, boqItemPosition.Id, res.Id);
													itemAssignmentToSave.Add(newItemAssignment);
													var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(newItemAssignment))
														{
															newChildrenMap.Add(newItemAssignment, newChildren);
														}
														itemAssignmentToSave.AddRange(newChildren);
													}
												}
												else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
												{
													found.BoqHeaderFk = boqheader.Id;
													found.BoqItemFk = boqItemPosition.Id;
													itemAssignmentToSave.Add(found);
													var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
													if (newChildren != null && newChildren.Any())
													{
														if (!newChildrenMap.ContainsKey(found))
														{
															newChildrenMap.Add(found, newChildren);
														}
														itemAssignmentToSave.AddRange(newChildren);
													}
												}
											}
										}
										else
										{
											var found = GetValueFromDictionary(itemAssignmentEstimateMap, match.Id, match.EstHeaderFk)
													.FirstOrDefault(e => !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue); // validAssignments.FirstOrDefault(e => e.EstHeaderFk == match.EstHeaderFk && e.EstLineItemFk == match.Id && !e.EstResourceFk.HasValue && !e.PrcItemAssignmentFk.HasValue);

											if (found == null)
											{
												var newItemAssignment = CreateItemAssignment(match, packageId, boqheader.Id, boqItemPosition.Id, null);
												itemAssignmentToSave.Add(newItemAssignment);
												var newChildren = CreateChildrenItemAssignments(newItemAssignment, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(newItemAssignment))
													{
														newChildrenMap.Add(newItemAssignment, newChildren);
													}
													itemAssignmentToSave.AddRange(newChildren);
												}
											}
											else if (!found.BoqHeaderFk.HasValue || !found.BoqItemFk.HasValue)
											{
												found.BoqHeaderFk = boqheader.Id;
												found.BoqItemFk = boqItemPosition.Id;
												itemAssignmentToSave.Add(found);
												var newChildren = CreateChildrenItemAssignments(found, itemAssignmentBoqMap, itemAssignmentChildrenMap);
												if (newChildren != null && newChildren.Any())
												{
													if (!newChildrenMap.ContainsKey(found))
													{
														newChildrenMap.Add(found, newChildren);
													}
													itemAssignmentToSave.AddRange(newChildren);
												}
											}
										}
									}
								}
							}
						}
					}
					#endregion create prc item assignment

					toCreate.AddRange(tempToCreate);
					tempToCreate.Clear();
				}

				var assignmentToCreate = itemAssignmentToSave.Where(e => e.Version == 0).ToList();
				var itemAssignIds = itemAssignmentLogic.GetNextIds(assignmentToCreate.Count).GetEnumerator();
				foreach (var item in assignmentToCreate)
				{
					itemAssignIds.MoveNext();
					item.Id = itemAssignIds.Current;
					if (newChildrenMap.ContainsKey(item))
					{
						newChildrenMap[item].ExtendEach(e => e.PrcItemAssignmentFk = item.Id);
					}
				}
				var toSave = toCreate.ConcatNoRepeat((a, b) => a.Id == b.Id, toUpdate).ToList();
				var boqHeaderIds = new List<int>() { boqheader.Id };

				using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
				{
					// boq structure
					boqStructure.BoqRoundingConfig = null;
					boqTypeLogic.SaveEntity(boqStructure);
					// boq structure details
					boqTypeLogic.SaveBoqStructureDetail(boqStructure.BoqStructureDetailEntities.ToList(), boqStructure.Id);
					// boq header
					new BoqHeaderLogic().Save(boqheader);

					// boq root item
					boqItemLogic.SaveEntity(boqitem);
					// prc boq
					prcBoqLogic.Save(newPrcBoq, boqheader, context.PrcHeaderId);   // write new entity into the database: BH: As mentioned above, currently we have to create the boq header and the boq root item and save it instantly. To avoid having saved these two instances without the prc boq we save it after creation, too -> this has to be corrected, too.
																														// boq items and recalculate the boq tree
					SaveBoqItemTranslation(boqItemLogic, toCreate);
					SaveAndRecalculateBoqItems(package, boqHeaderIds, toSave, package.ExchangeRate);

					// boq item to cost group
					boqItem2CostGrpLogic.SaveEntities(boqItem2CostGrpToSave);

					// prc item assignment
					itemAssignmentLogic.Save(itemAssignmentToSave);
					//itemAssignmentLogic.BulkSave(itemAssignmentLogic.GetDbModel(), itemAssignmentToSave);

					// package totals
					CalculateTotals(package);

					// package update option
					new PrcPackageUpdateOptionLogic().CreateOrSaveUpdateOption(context);

					transaction.Complete();
				}
			}
		}

		private void SaveBoqItemTranslation(BoqItemLogic boqItemLogic, IEnumerable<BoqItemEntity> boqItems)
		{
			if (boqItems == null || !boqItems.Any())
			{
				return;
			}
			IList<Func<BoqItemEntity, DescriptionTranslateType>> translationDescriptors = boqItemLogic.GetTranslationDescriptors();
			if (translationDescriptors != null && translationDescriptors.Any())
			{
				boqItems.SaveTranslate(base.UserLanguageId, translationDescriptors.ToArray());
			}
		}

		private IEnumerable<IBoqItemEntity> GetBoqItemsFromLineItems(IEnumerable<IEstLineItemEnhanceEntity> lineitems, Action<IEstLineItemEnhanceEntity, IBoqItemEntity> action = null)
		{
			List<IBoqItemEntity> boqItems = new List<IBoqItemEntity>();
			var boqItemLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IBoqItemLogic>();
			var boqItemIds = lineitems.CollectIds(e => e.BoqItemFk);
			var boqItemsTemp = boqItemLogic.GetBoqItemsByBoqItemIds(boqItemIds);

			foreach (var item in lineitems)
			{
				if (item.BoqHeaderFk.HasValue && item.BoqItemFk.HasValue)
				{
					var boqItem = boqItemsTemp.FirstOrDefault(e => e.BoqHeaderFk == item.BoqHeaderFk.Value && e.Id == item.BoqItemFk.Value);
					if (boqItem != null)
					{
						if (!boqItems.Any(e => e.BoqHeaderFk == boqItem.BoqHeaderFk && e.Id == boqItem.Id))
						{
							boqItems.Add(boqItem);
						}
						if (action != null)
						{
							action(item, boqItem);
						}
					}
				}
			}

			return boqItems;
		}

		private void ResetQuantityForAssignLineItems(IEnumerable<IEstLineItemEnhanceEntity> lineItems, CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);

			if (lineItems == null || !lineItems.Any())
			{
				return;
			}

			var isConsideredQtyRel = GetBooleanValue(context.UpdateOption.IsConsideredQtyRel);

			foreach (var lineItem in lineItems)
			{
				if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemAQ ||
					context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemWQ)
				{
					if (lineItem.BasUomTargetFk != lineItem.BoqItemUomId || IsIgnoreLineItemQuantity(isConsideredQtyRel, lineItem))
					{
						lineItem.QuantityTarget = 0;
					}
				}
				else if (context.UpdateOption.BoqQtySource == (int)UpdateOptionForBoq.QuantitySource.LineItemQuantityTotal && (lineItem.BasUomFk != lineItem.BoqItemUomId || IsIgnoreLineItemQuantity(isConsideredQtyRel, lineItem)))
				{
					lineItem.QuantityTarget = 0;
				}
			}
		}

		private bool IsIgnoreLineItemQuantity(bool isConsideredQtyRel, IEstLineItemEntity lineItem)
		{
			return isConsideredQtyRel && (lineItem.EstQtyRelBoqFk == 1 || lineItem.EstQtyRelBoqFk == 3);
		}

		private IEnumerable<IScriptEstResource> GetResourcesNotLinkToPackage(
			int packageId,
			IEnumerable<PrcItemAssignmentEntity> validItemAssigns,
			IEnumerable<IScriptEstResource> resources)
		{
			if (validItemAssigns.IsNullOrEmpty() || resources.IsNullOrEmpty())
			{
				return resources;
			}
			return resources.Where(e =>
				!validItemAssigns.Any(f => f.PrcPackageFk == packageId &&
					f.EstHeaderFk == e.EstHeaderFk && f.EstLineItemFk == e.EstLineItemFk && f.EstResourceFk == e.Id));
		}

		private IEnumerable<IEstLineItemEntity> GetLineItemsNotLinkToPackage(
			int packageId,
			IEnumerable<PrcItemAssignmentEntity> validItemAssigns,
			IEnumerable<IEstLineItemEntity> lineItems)
		{
			if (validItemAssigns.IsNullOrEmpty() || lineItems.IsNullOrEmpty())
			{
				return lineItems;
			}

			return lineItems.Where(e =>
				!validItemAssigns.Any(f => f.PrcPackageFk == packageId &&
					f.EstHeaderFk == e.EstHeaderFk && f.EstLineItemFk == e.Id && !f.EstResourceFk.HasValue));
		}

		private IEnumerable<IScriptEstResource> FetchValidReasources(IEnumerable<PrcItemAssignmentEntity> invalidItemAssigns, IEnumerable<IScriptEstResource> resources)
		{
			var validResources = new List<IScriptEstResource>();
			foreach (var resource in resources)
			{
				if (!invalidItemAssigns.Any(e => e.EstHeaderFk == resource.EstHeaderFk && e.EstLineItemFk == resource.EstLineItemFk && e.EstResourceFk == resource.Id))
				{
					validResources.Add(resource);
				}
			}
			return validResources;
		}

		private IEnumerable<IEstLineItemEntity> FetchValidLineItems(IEnumerable<PrcItemAssignmentEntity> invalidItemAssigns, IEnumerable<IEstLineItemEntity> lineItemsWithoutResources)
		{
			var validLineItemsWithoutResources = new List<IEstLineItemEntity>();
			foreach (var lineItem in lineItemsWithoutResources)
			{
				if (!invalidItemAssigns.Any(e => e.EstHeaderFk == lineItem.EstHeaderFk && e.EstLineItemFk == lineItem.Id && !e.EstResourceFk.HasValue))
				{
					validLineItemsWithoutResources.Add(lineItem);
				}
			}
			return validLineItemsWithoutResources;
		}

		private PrcItemAssignmentEntity CreateItemAssignment(IEstLineItemEntity lineItem, int packageId, int boqheaderId, int boqItemPositionId, int? resourceId)
		{
			var newItemAssignment = new PrcItemAssignmentEntity();
			newItemAssignment.EstHeaderFk = lineItem.EstHeaderFk;
			newItemAssignment.EstLineItemFk = lineItem.Id;
			newItemAssignment.PrcPackageFk = packageId;
			newItemAssignment.BoqHeaderFk = boqheaderId;
			newItemAssignment.BoqItemFk = boqItemPositionId;
			newItemAssignment.EstResourceFk = resourceId;
			return newItemAssignment;
		}

		private bool GetBooleanValue(bool? value)
		{
			if (!value.HasValue)
			{
				return false;
			}
			return value.Value;
		}

		private void CheckContext(CreateOrUpdatePackageBoqContext context)
		{
			if (context == null)
			{
				throw new ArgumentNullException(nameof(context));
			}
		}

		private string QuantityToString(decimal value)
		{
			return value.ToString("#0.######");
		}

		private class LineItemMergedProperty
		{
			/// <summary>
			///
			/// </summary>
			public decimal CostTotal { get; set; }

			/// <summary>
			///
			/// </summary>
			public decimal BudgetTotal { get; set; }
		}

		private void CreateNodeBoqItems(int sourceBoQHeaderId, int targetBoQHeaderId, bool isFromProject, BoqItemLogic boqItemLogic, IEnumerable<IBoqItemBaseInfo> nodeBoqItemsToSave, IDictionary<RVPC.IdentificationData, BoqItemEntity> sourceBoQMap,
			IDictionary<RVPC.IdentificationData, BoqItemEntity> targetBoqMap, IDictionary<RVPC.IdentificationData, List<BoqItemEntity>> targetBoqChildrenMap,
			ref IDictionary<RVPC.IdentificationData, BoqItemEntity> targetUpdateBoqMap, ref IDictionary<RVPC.IdentificationData, List<BoqItemEntity>> targetUpdateBoqChildrenMap,
			//ref List<BoqItemEntity> targetUpdateBoQItems,
			ref Dictionary<int, int> sourceBoqItemId2TargetBoqItemId)
		{
			if (nodeBoqItemsToSave != null && nodeBoqItemsToSave.Any(e => e.BoqHeaderFk == sourceBoQHeaderId) && sourceBoQMap != null && sourceBoQMap.Any() && sourceBoqItemId2TargetBoqItemId != null)
			{
				var tempNodeBoqItems = nodeBoqItemsToSave.Where(e => e.BoqHeaderFk == sourceBoQHeaderId).ToList();
				var targetParentItemId2NodeBoqItems = new Dictionary<int, List<BoqItemEntity>>();

				foreach (var nodeItem in tempNodeBoqItems)
				{
					var sourceNodeItem = GetValueFromDictionary(sourceBoQMap, nodeItem.Id, nodeItem.BoqHeaderFk); // sourceBoQItems.FirstOrDefault(e => e.Id == nodeItem.Id);
					if (sourceNodeItem == null)
					{
						continue;
					}

					if (!sourceNodeItem.BoqItemFk.HasValue)
					{
						continue;
					}

					var parentItem = GetValueFromDictionary(sourceBoQMap, sourceNodeItem.BoqItemFk.Value, sourceNodeItem.BoqHeaderFk); // sourceBoQItems.FirstOrDefault(e => e.Id == sourceNodeItem.BoqItemFk);
					if (parentItem == null)
					{
						continue;
					}

					if (!sourceBoqItemId2TargetBoqItemId.ContainsKey(parentItem.Id))
					{
						continue;
					}

					BoqItemEntity sourcePredecessor = sourceNodeItem.BoqItemBasisFk.HasValue ?
						GetValueFromDictionary(sourceBoQMap, sourceNodeItem.BoqItemBasisFk.Value, sourceNodeItem.BoqHeaderFk) : null; // sourceBoQItems.FirstOrDefault(e => e.Id == sourceNodeItem.BoqItemBasisFk);
					BoqItemEntity targetParentItem = null;
					var tempDic = sourceBoqItemId2TargetBoqItemId;
					if (tempDic.ContainsKey(parentItem.Id))
					{
						targetParentItem = GetValueFromDictionary(targetBoqMap, tempDic[parentItem.Id], targetBoQHeaderId);

						if (targetParentItem == null && targetUpdateBoqMap != null && targetUpdateBoqMap.Any())
						{
							var updateId2 = new RVPC.IdentificationData() { Id = tempDic[parentItem.Id], PKey1 = targetBoQHeaderId };
							targetUpdateBoqMap.TryGetValue(updateId2, out targetParentItem);
							//targetParentItem = targetUpdateBoQItems.FirstOrDefault(e => e.Id == tempDic[parentItem.Id]);
						}
					}

					if (targetParentItem == null)
					{
						continue;
					}

					var targetNodeItems = GetValueFromDictionary(targetBoqChildrenMap, targetParentItem.Id, targetBoQHeaderId).Where(e => e.Version > 0 && (e.BoqLineTypeFk == (int)BoqLineType.Note || e.BoqLineTypeFk == (int)BoqLineType.DesignDescription)).ToList();

					if (!targetNodeItems.Any() && targetUpdateBoqChildrenMap != null && targetUpdateBoqChildrenMap.Any())
					{
						var parentId = new RVPC.IdentificationData() { Id = targetParentItem.Id, PKey1 = targetParentItem.BoqHeaderFk };
						List<BoqItemEntity> tempBoqs = null;
						targetUpdateBoqChildrenMap.TryGetValue(parentId, out tempBoqs);
						if (tempBoqs != null)
						{
							targetNodeItems = tempBoqs.Where(e => e.Version > 0 && (e.BoqLineTypeFk == (int)BoqLineType.Note ||
							e.BoqLineTypeFk == (int)BoqLineType.DesignDescription ||
							e.BoqLineTypeFk == (int)BoqLineType.TextElement ||
							e.BoqLineTypeFk == (int)BoqLineType.SubDescription)).ToList();
						}
					}

					if (targetNodeItems.Any())
					{
						continue;
					}

					var newNodeItem = CopyBoqItem(sourceNodeItem, targetBoQHeaderId, boqItemLogic, targetParentItem, isFromProject);
					newNodeItem.BoqItemBasisFk = null;
					BoqItemEntity predecessorBoqItem = null;

					if (sourceNodeItem.BoqItemBasisFk.HasValue && tempDic.ContainsKey(sourceNodeItem.BoqItemBasisFk.Value))
					{
						predecessorBoqItem = GetPredecessorBoqItem(tempDic[sourceNodeItem.BoqItemBasisFk.Value], targetParentItem, boqItemLogic, targetBoqMap, targetBoqChildrenMap, targetUpdateBoqMap, targetUpdateBoqChildrenMap);
					}
					else
					{
						predecessorBoqItem = GetPredecessorBoqItem(null, targetParentItem, boqItemLogic, targetBoqMap, targetBoqChildrenMap, targetUpdateBoqMap, targetUpdateBoqChildrenMap);
					}

					if (predecessorBoqItem != null)
					{
						newNodeItem.BoqItemBasisFk = predecessorBoqItem.Id;
					}
					else
					{
						if (targetParentItemId2NodeBoqItems.ContainsKey(targetParentItem.Id))
						{
							var nodeItems = targetParentItemId2NodeBoqItems[targetParentItem.Id];
							var lastNodeItem = nodeItems.LastOrDefault();
							if (lastNodeItem != null)
							{
								newNodeItem.BoqItemBasisFk = lastNodeItem.Id;
							}

							nodeItems.Add(newNodeItem);
						}
					}
					if (!newNodeItem.BoqItemBasisFk.HasValue)
					{
						newNodeItem.BoqItemBasisFk = newNodeItem.BoqItemFk;
					}

					if (!targetParentItemId2NodeBoqItems.ContainsKey(targetParentItem.Id))
					{
						targetParentItemId2NodeBoqItems.Add(targetParentItem.Id, new List<BoqItemEntity>() { newNodeItem });
					}
					else
					{
						targetParentItemId2NodeBoqItems[targetParentItem.Id].Add(newNodeItem);
					}

					if (!sourceBoqItemId2TargetBoqItemId.ContainsKey(sourceNodeItem.Id))
					{
						sourceBoqItemId2TargetBoqItemId.Add(sourceNodeItem.Id, newNodeItem.Id);
					}

					//if (targetUpdateBoQItems == null)
					//{
					//	targetUpdateBoQItems = new List<BoqItemEntity>();
					//}

					//targetUpdateBoQItems.Add(newNodeItem);

					if (targetUpdateBoqMap == null)
					{
						targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
					}

					if (targetUpdateBoqChildrenMap == null)
					{
						targetUpdateBoqChildrenMap = new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();
					}

					var updateId = new RVPC.IdentificationData() { Id = newNodeItem.Id, PKey1 = newNodeItem.BoqHeaderFk };
					BoqItemEntity update = null;
					if (!targetUpdateBoqMap.TryGetValue(updateId, out update))
					{
						targetUpdateBoqMap.Add(updateId, newNodeItem);
						if (newNodeItem.BoqItemFk.HasValue)
						{
							List<BoqItemEntity> updates = null;
							var parentId = new RVPC.IdentificationData() { Id = newNodeItem.BoqItemFk.Value, PKey1 = newNodeItem.BoqHeaderFk };
							if (!targetUpdateBoqChildrenMap.TryGetValue(parentId, out updates))
							{
								targetUpdateBoqChildrenMap.Add(parentId, new List<BoqItemEntity>() { newNodeItem });
							}
							else
							{
								updates.Add(newNodeItem);
							}
						}
					}
				}
			}
		}

		private BoqItemEntity GetPredecessorBoqItem(int? targetNodeItemBasisId, BoqItemEntity targetParentItem, BoqItemLogic boqItemLogic,
			IDictionary<RVPC.IdentificationData, BoqItemEntity> targetBoqMap, IDictionary<RVPC.IdentificationData, List<BoqItemEntity>> targetBoqChildrenMap,
			IDictionary<RVPC.IdentificationData, BoqItemEntity> targetUpdateBoqMap, IDictionary<RVPC.IdentificationData, List<BoqItemEntity>> targetUpdateBoqChildrenMdap) //, IEnumerable<BoqItemEntity> targetUpdateBoQItems)
		{
			BoqItemEntity predecessorBoqItem = null;
			//List<BoqItemEntity> tempTargetBoqItems = targetBoQItems != null && targetBoQItems.Any() ? targetBoQItems.ToList() : new List<BoqItemEntity>();
			//var tempTargetBoqItemMap = targetBoqMap != null ? targetBoqMap : new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			//var tempTargetBoqChildrenMap = targetBoqChildrenMap != null ? targetBoqChildrenMap : new Dictionary<RVPC.IdentificationData, List<BoqItemEntity>>();

			if (targetNodeItemBasisId.HasValue)
			{
				if (targetBoqMap != null)
				{
					//if (targetNodeItemBasisId.HasValue)
					//{
					predecessorBoqItem = GetValueFromDictionary(targetBoqMap, targetNodeItemBasisId.Value, targetParentItem.BoqHeaderFk);
					//}
				}

				if (predecessorBoqItem == null && targetUpdateBoqMap != null)
				{
					predecessorBoqItem = GetValueFromDictionary(targetUpdateBoqMap, targetNodeItemBasisId.Value, targetParentItem.BoqHeaderFk);
				}
			}



			//if (tempTargetBoqChildrenMap.Any())
			//{
			if (predecessorBoqItem == null)
			{
				List<BoqItemEntity> children = new List<BoqItemEntity>(); ;

				if (targetBoqChildrenMap != null)
				{
					children = GetValueFromDictionary(targetBoqChildrenMap, targetParentItem.Id, targetParentItem.BoqHeaderFk);
				}

				if (targetUpdateBoqChildrenMdap != null)
				{
					var childrenTemp = GetValueFromDictionary(targetUpdateBoqChildrenMdap, targetParentItem.Id, targetParentItem.BoqHeaderFk);
					if (childrenTemp != null && childrenTemp.Any())
					{
						children = children.ConcatNoRepeat((a, b) => a.Id == b.Id && a.BoqHeaderFk == b.BoqHeaderFk, childrenTemp).ToList();
					}
				}
				var temp = targetParentItem.Clone() as BoqItemEntity;
				temp.BoqItemChildren = children;
				var tempChildren = boqItemLogic.GetSortedChildren(temp, null, null, true);
				predecessorBoqItem = tempChildren.LastOrDefault();
			}
			//}

			return predecessorBoqItem;
		}

		private TEntity GetValueFromDictionary<TEntity>(IDictionary<RVPC.IdentificationData, TEntity> map, int id, int pKey1) where TEntity : class
		{
			if (map == null)
			{
				return null;
			}
			var idData = new RVPC.IdentificationData() { Id = id, PKey1 = pKey1 };
			TEntity entity = null;
			if (map.TryGetValue(idData, out entity))
			{
				return entity;
			}
			return null;
		}

		private List<TEntity> GetValueFromDictionary<TEntity>(IDictionary<RVPC.IdentificationData, List<TEntity>> map, int id, int pKey1) where TEntity : class
		{
			if (map == null)
			{
				return new List<TEntity>();
			}
			var idData = new RVPC.IdentificationData() { Id = id, PKey1 = pKey1 };
			List<TEntity> children = null;
			if (map.TryGetValue(idData, out children))
			{
				return children;
			}
			return new List<TEntity>();
		}

		private List<BoqItemEntity> GetValueFromDictionary(IDictionary<string, List<BoqItemEntity>> map, string key)
		{
			if (map == null || string.IsNullOrEmpty(key) || !map.ContainsKey(key))
			{
				return new List<BoqItemEntity>();
			}

			return map[key];
		}

		private List<TEntity> GetValueFromDictionary<TEntity>(IDictionary<int, List<TEntity>> map, int key)
		{
			if (map == null || !map.ContainsKey(key))
			{
				return new List<TEntity>();
			}

			return map[key];
		}

		private IEnumerable<PrcItemAssignmentEntity> CreateChildrenItemAssignments(PrcItemAssignmentEntity parent,
			IDictionary<RVPC.IdentificationData, List<PrcItemAssignmentEntity>> itemAssignmentBoqMap,
			IDictionary<int, List<PrcItemAssignmentEntity>> itemAssignmentChildrenMap)
		{
			if (parent == null || itemAssignmentBoqMap == null || itemAssignmentChildrenMap == null)
			{
				return new List<PrcItemAssignmentEntity>();
			}
			var newItems = new List<PrcItemAssignmentEntity>();
			var itemAssignParent = GetValueFromDictionary(itemAssignmentBoqMap, parent.BoqItemFk.Value, parent.BoqHeaderFk.Value).FirstOrDefault();
			if (itemAssignParent != null)
			{
				var children = GetValueFromDictionary(itemAssignmentChildrenMap, itemAssignParent.Id);
				if (children != null && children.Any())
				{
					foreach (var child in children)
					{
						var newItem = (PrcItemAssignmentEntity)child.Clone();
						newItem.Version = 0;
						newItem.EstHeaderFk = parent.EstHeaderFk;
						newItem.EstLineItemFk = parent.EstLineItemFk;
						newItem.EstResourceFk = null;
						newItem.PrcItemAssignmentFk = parent.Id;
						newItems.Add(newItem);
					}
				}
			}

			return newItems;
		}

		private void RecalculateCostTotalIfLineItemIsOptional(
			IEnumerable<IEstLineItemEntity> validLineItemsWithoutResources,
			IEnumerable<IScriptEstResource> validResources)
		{
			var lineItemLogic = Injector.Get<IEstimateMainLineItemLogic>();
			var resourceLogic = Injector.Get<IEstimateMainResourceLogic>();
			var estHeaderLogic = Injector.Get<IEstimateMainHeaderLogic>();
			var estProjectLogic = Injector.Get<IEstimateCompositeLogic>();
			var isEstCalculateTotalWqMap = new Dictionary<int, bool>();
			if (validLineItemsWithoutResources != null && validLineItemsWithoutResources.Any())
			{
				foreach (var validLineItem in validLineItemsWithoutResources)
				{
					if (!(validLineItem.IsOptional && !validLineItem.IsOptionalIT))
					{
						continue;
					}
					var isEstCalculateTotalWq = false;
					if (isEstCalculateTotalWqMap.ContainsKey(validLineItem.EstHeaderFk))
					{
						isEstCalculateTotalWq = isEstCalculateTotalWqMap[validLineItem.EstHeaderFk];
					}
					else
					{
						isEstCalculateTotalWq = estHeaderLogic.IsCalcTotalWithWq(validLineItem.EstHeaderFk);
						isEstCalculateTotalWqMap.Add(validLineItem.EstHeaderFk, isEstCalculateTotalWq);
					}
					validLineItem.CostTotal = lineItemLogic.GetLineItemActualCostTotal(validLineItem, isEstCalculateTotalWq);
				}
			}

			var lineItemsForValidResources = GetScriptLineItemsByResources(validResources);
			var resourcesMap = new Dictionary<RVPC.IdentificationData, List<IScriptEstResource>>();
			var estHeaderIds = new List<int>();
			foreach (var res in validResources)
			{
				estHeaderIds.Add(res.EstHeaderFk);
				var temp = new RVPC.IdentificationData()
				{
					Id = res.EstLineItemFk,
					PKey1 = res.EstHeaderFk
				};
				if (!resourcesMap.TryGetValue(temp, out var resources))
				{
					resourcesMap.Add(temp, new List<IScriptEstResource>() { res });
				}
				else
				{
					resources.Add(res);
				}
			}

			var estHeaderId2PrjIdMap = estProjectLogic.GetPrjIdsByEstHeaderIds(estHeaderIds.Distinct().ToArray());

			foreach (var lineItem in lineItemsForValidResources)
			{
				if (!(lineItem.IsOptional && !lineItem.IsOptionalIT))
				{
					continue;
				}

				var temp = new RVPC.IdentificationData()
				{
					Id = lineItem.Id,
					PKey1 = lineItem.EstHeaderFk
				};
				resourcesMap.TryGetValue(temp, out var resources);
				var projectId = 0;
				if (estHeaderId2PrjIdMap != null && estHeaderId2PrjIdMap.ContainsKey(lineItem.EstHeaderFk))
				{
					projectId = estHeaderId2PrjIdMap[lineItem.EstHeaderFk];
				}
				lineItem.IsOptional = false;
				var calculationOption = new EstimateCoreCalculationOption(lineItem.EstHeaderFk, projectId);
				resourceLogic.CalculateLineItemAndResourceInTree(lineItem, resources, calculationOption);
			}
		}
	}
}
