/*
 * Copyright(c) RIB Software GmbH
 */

import { IConHeaderEntity } from './entities';
import {
	IPrcSubreferenceEntity,
	IPrcDocumentEntity,
	IPrcGeneralsEntity,
	ProcurementCommonComplete
} from '@libs/procurement/common';
import { IConTotalEntity, IPrcBoqExtendedEntity, IPrcCertificateEntity } from '@libs/procurement/interfaces';
import { ICommonBillingSchemaEntity } from '@libs/basics/shared';
import { IHeaderPparamEntity } from '@libs/basics/pricecondition';
import { IPrcBoqExtendedComplete } from '@libs/procurement/common';
import { IConAccountAssignmentEntity } from './entities/con-account-assignment-entity.interface';
import { IChangeEntity } from './entities/change-entity.interface';
import { IConMasterRestrictionEntity } from '@libs/procurement/shared';

export class ContractComplete extends ProcurementCommonComplete<IConHeaderEntity> {
	public ConHeader?: IConHeaderEntity;
	public ConHeaders?: IConHeaderEntity[];
	public EntitiesCount?: number;
	public TotalToDelete?: Array<IConTotalEntity>;
	public TotalToSave?: Array<IConTotalEntity>;
	public PrcDocumentToSave?: Array<IPrcDocumentEntity>;
	public PrcDocumentToDelete?: Array<IPrcDocumentEntity>;
	public BillingSchemaToSave?: Array<ICommonBillingSchemaEntity>;
	public BillingSchemaToDelete?: Array<ICommonBillingSchemaEntity>;
	public PrcSubreferenceToSave?: Array<IPrcSubreferenceEntity>;
	public PrcSubreferenceToDelete?: Array<IPrcSubreferenceEntity>;
	public HeaderPparamToSave?: Array<IHeaderPparamEntity>;
	public HeaderPparamToDelete?: Array<IHeaderPparamEntity>;
	public PrcBoqExtendedToSave?: Array<IPrcBoqExtendedComplete>;
	public PrcBoqExtendedToDelete?: Array<IPrcBoqExtendedEntity>;
	public ConAccountAssignmentDtoToSave?: Array<IConAccountAssignmentEntity>;
	public ConAccountAssignmentDtoToDelete?: Array<IConAccountAssignmentEntity>;
	public ProjectChangeToSave?:Array<IChangeEntity>;
	public ProjectChangeToDelete?:Array<IChangeEntity>;
	public ConMasterRestrictionToSave?: Array<IConMasterRestrictionEntity>;
	public ConMasterRestrictionToDelete?: Array<IConMasterRestrictionEntity>;
	public PrcGeneralsToSave?:Array<IPrcGeneralsEntity>;
	public PrcGeneralsToDelete?:Array<IPrcGeneralsEntity>;
	public PrcCertificateToSave?: Array<IPrcCertificateEntity>;
	public PrcCertificateToDelete?: Array<IPrcCertificateEntity>;
}
