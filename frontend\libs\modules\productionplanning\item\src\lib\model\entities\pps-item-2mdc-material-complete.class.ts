
import { CompleteIdentification } from '@libs/platform/common';
import {
	IPpsItem2MdcMaterialEntity,
	IPpsItem2MdcMaterialProductDescriptionEntity,
	PpsItem2MdcMaterialProductDescriptionComplete,
} from '../models';


export class PpsItem2MdcMaterialComplete extends CompleteIdentification<PpsItem2MdcMaterialComplete> {

	public MainItemId!: number;
	public PpsItem2MdcMaterial!: IPpsItem2MdcMaterialEntity;

	public MdcProductDescriptionToSave?: PpsItem2MdcMaterialProductDescriptionComplete[];

	public MdcProductDescriptionToDelete?: IPpsItem2MdcMaterialProductDescriptionEntity[];
}
