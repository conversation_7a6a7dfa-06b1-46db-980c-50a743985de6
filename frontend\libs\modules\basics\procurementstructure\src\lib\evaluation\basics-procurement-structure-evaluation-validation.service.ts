/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import {
	BaseValidationService,
	IEntityRuntimeDataRegistry,
	IValidationFunctions,
	ValidationInfo,
} from '@libs/platform/data-access';
import { IPrcStructure2EvaluationEntity } from '../model/entities/prc-structure-2-evaluation-entity.interface';
import { BasicsProcurementStructureEvaluationDataService } from './basics-procurement-structure-evaluation-data.service';
import { AllKeys } from '@libs/platform/common';


/**
 * validation service for ProcurementStructure evaluation
 */

@Injectable({
	providedIn: 'root'
})
export class BasicsProcurementStructureEvaluationValidationService extends BaseValidationService<IPrcStructure2EvaluationEntity> {
	private dataService = inject(BasicsProcurementStructureEvaluationDataService);

	protected generateValidationFunctions(): IValidationFunctions<IPrcStructure2EvaluationEntity> {
		return {
			CompanyFk: this.validateIsLocalGroupUnique,
			BpdEvaluationSchemaFk: this.validateIsLocalGroupUnique
		};
	}

	protected override getEntityService = () => this.dataService;


	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IPrcStructure2EvaluationEntity> {
		return this.dataService;
	}

	protected override getFieldsForGroupUnique = (info: ValidationInfo<IPrcStructure2EvaluationEntity>): AllKeys<IPrcStructure2EvaluationEntity>[] => {
		if (info.field === 'CompanyFk' || info.field === 'BpdEvaluationSchemaFk') {
			return ['CompanyFk', 'BpdEvaluationSchemaFk'];
		}
		return [];
	};

	protected override createUniqueValueError(info: ValidationInfo<IPrcStructure2EvaluationEntity>) {
		if (info.field === 'CompanyFk' || info.field === 'BpdEvaluationSchemaFk') {
			return this.createErrorResult({
				key: 'basics.procurementstructure.towFiledUniqueValueErrorMessage',
				params: {field1: 'Company', field2: 'Evaluation Schema'}
			});
		} else {
			return super.createUniqueValueError(info);
		}
	}
}
