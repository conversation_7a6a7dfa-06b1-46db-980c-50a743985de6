[CmdletBinding(SupportsShouldProcess=$true)]
param(
	[Parameter(Mandatory=$true)][string]$Version,
	[Parameter(Mandatory=$true)][string]$JiraIssue,
	[switch]$NoPush,
	[string]$RepoRoot,
	[string]$NextVersion,
	[string]$CommitId,
	[string]$BuildNo
)

Write-Host -ForegroundColor Magenta 'This tool will help you prepare a new release branch.'
Write-Host ''

function FormatPatchVersionList {
	param(
		[Parameter(Mandatory=$True)][Int32]$Major,
		[Parameter(Mandatory=$True)][Int32]$Minor,
		[Int32]$Update
	)
	
	$version = "$Major.$Minor"
	$result = "$Major.$Minor"

	if ($Update) {
		for ($i = 1; $i -le $Update; ++$i) {
			if ($Major -gt 6) {
				$result += ",$version.$i"
			} else {
				$result += ",$version.$([char](64 + $i))"
			}
		}
		
		if ($Major -gt 6) {
			$result += ",$version.$Update.P1"
		} else {
			$result += ",$version.$([char](64 + $Update)).P1"
		}
	} else {
		if ($Major -gt 6) {
			$result += ",$version.P1"
		} else {
			$result += ",$version.P1"
		}
	}
	
	return $result
}

# directories

$scriptDir = $PSScriptRoot
if ($RepoRoot) {
	$appRootDir = $RepoRoot
} else {
	$appRootDir = Join-Path "$scriptDir" '../..'
}

Write-Host -ForegroundColor DarkGray -NoNewLine 'Root path of application repo: '
Write-Host "$appRootDir"
Write-Host ''

Push-Location -Path "$appRootDir"

# pre-process arguments

$versionExpr = [regex]::new('^(?<ver>(?<mainVer>[0-9]+)\.(?<subVer>[0-9]+)(?:\.[0-9]+)*(?:\.[A-Z])?)(?<patch>p)?$')
$verMatch = $versionExpr.Match($Version)
if (-not $verMatch.Success) {
	Write-Host -ForegroundColor Red "$Version is not a valid version number."
	exit 10
}

$ver = $verMatch.Groups['ver'].Value
$snakeCaseVer = $ver -replace '\.', '-'
$mainVer = $verMatch.Groups['mainVer'].Value
$subVer = $verMatch.Groups['subVer'].Value
if ($verMatch.Groups['patch'].Success) {
	# new branch is a patch branch
	$isPatch = $true
	
	if (-not $CommitId) {
		Write-Host -ForegroundColor Red 'A commit ID must be supplied when creating a patch branch.'
		exit 11
	}
	if (-not $BuildNo) {
		Write-Host -ForegroundColor Red 'A build number must be suppiled when creating a patch branch.'
		exit 12
	}
} else {
	# new branch is a release branch
	$isPatch = $false
	
	if ($BuildNo) {
		Write-Host -ForegroundColor Red 'Do not supply a build number unless you are creating a patch branch.'
		exit 13
	}
}
$commitPrefix = "[$ver branch creation] "

$jiraIssueExpr = [regex]::new('^(?:DEV-?|dev-?)?(?<id>[0-9]+)$')
$jiraIssueMatch = $jiraIssueExpr.Match($JiraIssue)
if (-not $jiraIssueMatch.Success) {
	Write-Host -ForegroundColor Red "$JiraIssue is not a valid Jira issue."
	exit 15
}

$jiraId = "DEV-$($jiraIssueMatch.Groups['id'].Value)"

Write-Host -ForegroundColor Cyan -NoNewLine 'Preparing '
if ($isPatch) {
	Write-Host -ForegroundColor White -NoNewLine 'patch'
} else {
	Write-Host -ForegroundColor White -NoNewLine 'release'
}
Write-Host -ForegroundColor Cyan -NoNewLine ' branch for version '
Write-Host -ForegroundColor White -NoNewLine $ver
Write-Host -ForegroundColor Cyan '.'

# prepare branches

$tags = [System.Collections.ArrayList]::new()

$git = 'git'

$verBranchName = "release/$Version"

if ($isPatch) {
	$originBranchName = "release/$ver"
} else {
	$originBranchName = 'master'
}

Write-Host ''
Write-Host -ForegroundColor DarkYellow 'Branches:'
Write-Host $originBranchName
Write-Host -ForegroundColor White " *-> $verBranchName"
if (-not $isPatch) {
	$updateMasterBranchName = "feature/$jiraId-update-branch-after-v$snakeCaseVer"
	Write-Host -ForegroundColor White " *-> $updateMasterBranchName"
}
Write-Host ''

if ($CommitId) {
	$branchOrigin = $CommitId
	$originCommit = $CommitId
} else {
	$branchOrigin = $originBranchName
	$originCommit = (&$git log -n 1 --pretty=format:"%H" $originBranchName)
}
if ($WhatIfPreference) {
	Write-Host -ForegroundColor Magenta "[SKIP] Create $verBranchName from $branchOrigin and switch to it"
} else {
	&$git 'switch' -c $verBranchName $branchOrigin
}

# update pipeline settings

$filesToCommit = @()

Write-Host -ForegroundColor DarkYellow 'Updating pipeline settings files ...'

$settingsPath = Join-Path "$appRootDir" '_pipelinesettings.json'
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$settings.versionName = $Version
	if ($isPatch) {
		$settings.versionTitle = "$ver Patches"
		$settings.patchNumber = 1
		$settings.versionNumber = "$mainVer.$subVer.$BuildNo"
	} else {
		$settings.versionTitle = "Release $ver.*"
	}
	$settings['code-branch'] = $verBranchName
	
	if ($isPatch) {
		$patchedVersion = FormatPatchVersionList -Major $mainVer -Minor $subVer -Update $settings.updateNumber
		$settings.jiraFilters.releaseNotes = "$patchedVersion"
		$settings.jiraFilters.whatsNew = "$patchedVersion"
	} else {
		$settings.jiraFilters.knownIssues = "$mainVer.*"
		$settings.jiraFilters.releaseNotes = "$mainVer.$subVer"
		$settings.jiraFilters.whatsNew = "$mainVer.$subVer"
	}
	
	$settings.deployments = @(
		@{
			name = "$ver";
			enabled = -not $isPatch;
		}
	)
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

$settingsPath = Join-Path "$appRootDir" '_pipelinesettings-frontend-ci.json'
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$settings.versionName = $Version
	if ($isPatch) {
		$settings.versionTitle = "$ver Patches"
	} else {
		$settings.versionTitle = "Release $ver.*"
	}
	$settings.'code-branch' = $verBranchName
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Yellow "File not found: $settingsPath"
	Write-Host -ForegroundColor Yellow 'It is possible the source branch at hand does not contain this file.'
}

if ($filesToCommit.Count -gt 0) {
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
	} else {
		&$git commit '-m' "$($commitPrefix)prepared pipeline settings files for $Version ($jiraId)" @filesToCommit
	}
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# update appsettings.json

$filesToCommit = @()

Write-Host -ForegroundColor DarkYellow 'Updating appsettings files ...'

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'backend', 'framework', 'Platform', 'AppServer.Web', 'appsettings.json')
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$settings.ConnectionStrings.default = $settings.ConnectionStrings.default -replace 'Initial Catalog=[a-zA-Z0-9_]+;', "Initial Catalog=piTWO40rel$($mainVer)_$($subVer)_0;"
	
	$settings.AppSettings.'model-converter:blobstoragecontainer' = 'itwo40-models'
	$settings.AppSettings.'itwo40:productversion' = $ver
	$settings.AppSettings.'itwo40:buildversion' = "$mainVer.$subVer.0.[build]"
	$settings.AppSettings.'reporting:reportBasePath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\reports"
	$settings.AppSettings.'reporting:outputPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\downloads\reports"
	$settings.AppSettings.'fileserver:basePath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40"
	$settings.AppSettings.'fileserver:downloadsPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\downloads"
	$settings.AppSettings.'fileserver:uploadsPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\uploads"
	$settings.AppSettings.'audittrailPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\audittrail"
	$settings.AppSettings.'wde:dataPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\2d-wdemodels"
	$settings.AppSettings.'ige:dataPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\2d-igemodels"
	$settings.AppSettings.'model-converter:fontpath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\fonts\model"
	$settings.AppSettings.'fileserver:cdnPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\cdn"
	$settings.AppSettings.'fileserver:documentationPath' = "\\RIBPRDWEBAGW001.itwo40.eu\itwo40fs\$mainVer.$subVer\itwo40\documentation"
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# update testsettings.json

Write-Host -ForegroundColor DarkYellow 'Updating test settings files ...'

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'backend', 'framework', 'Platform', 'UnitTests.Common', '_testsettings.json')
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$settings.dbConnectionString = $settings.dbConnectionString -replace 'Initial Catalog=[a-zA-Z0-9_]+;', "Initial Catalog=piTWO40rel$($mainVer)_$($subVer)_0;"
	
	$settings.webApiBaseUrl = "https://apps.itwo40.eu/rib40/$mainVer.$subVer/services/"
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# update AppServer.Web app.config file

Write-Host -ForegroundColor DarkYellow 'Updating AppServer.Web config file ...'

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'backend', 'framework', 'Platform', 'AppServer.Web', 'App.config')
if (Test-Path "$settingsPath") {
	[xml]$settingsDoc = Get-Content "$settingsPath"
	
	$xmlns = New-Object System.Xml.XmlNamespaceManager -ArgumentList $settingsDoc.NameTable
	
	$storageConnEl = $settingsDoc.DocumentElement.SelectSingleNode('/configuration/modelStorage/connectionString')
	if ($storageConnEl -ne $NULL) {
		$storageConnEl.SetAttribute('container', 'itwo40-models')
	} else {
		Write-Host -ForegroundColor Yellow 'No /configuration/modelStorage/connectionString element found. This may be the case in older branches.'
	}
	
	$renderClusterEl = $settingsDoc.DocumentElement.SelectSingleNode('/configuration/renderCluster')
	
	if ($renderClusterEl -ne $NULL) {
		$renderClusterEl.SelectSingleNode('logging').SetAttribute('path', "C:\RIB_DEV\rib40\modelrendering\$mainVer.$subVer\clusternode\logs")
		$renderClusterEl.SelectSingleNode('internalStorage').SetAttribute('path', "C:\RIB_DEV\rib40\modelrendering\$mainVer.$subVer\clusternode\storage.sqlite")
		$renderClusterEl.SelectSingleNode('url').SetAttribute('url', "https://ribprdrdragw001.itwo40.eu/itwo40/$mainVer.$subVer/renderservices/services")
	} else {
		Write-Host -ForegroundColor Yellow 'No /configuration/renderCluster element found. This may be the case in older branches.'
	}
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta $settingsDoc.OuterXml
	} else {
		$settingsDoc.Save("$settingsPath")
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

if ($filesToCommit.Count -gt 0) {
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
	} else {
		&$git commit '-m' "$($commitPrefix)prepared runtime settings files for $Version ($jiraId)" @filesToCommit
	}
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# update config override templates

$filesToCommit = @()

Write-Host -ForegroundColor DarkYellow 'Updating config override template files ...'

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'binpool', 'myappsettings.json.tmpl')
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$settings.AppSettings.'itwo40:productversion' = "$ver (RIB-WXXXX)"
	$settings.AppSettings.'itwo40:serverurl' = "https://rib-wXXXX.rib-software.com/rib40/$mainVer.$subVer"
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'binpool', 'my_testsettings.json.tmpl')
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$settings.webApiBaseUrl = "https://rib-wXXXX.rib-software.com/rib40/$mainVer.$subVer"
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

if ($filesToCommit.Count -gt 0) {
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
	} else {
		&$git commit '-m' "$($commitPrefix)prepared config template files for $Version ($jiraId)" @filesToCommit
	}
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# update frontend package.json

$filesToCommit = @()

Write-Host -ForegroundColor DarkYellow 'Updating frontend package.json version ...'

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'frontend', 'package.json')
if (Test-Path "$settingsPath") {
	$packageJson = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	$targetVersion = "$mainVer.$subVer.0"
	
	# Check if version is already correct
	if ($packageJson.version -eq $targetVersion) {
		Write-Host -ForegroundColor Green "Frontend package.json version is already correct ($targetVersion) - no update needed"
	} else {
		Write-Host -ForegroundColor Cyan "Updating frontend package.json version from '$($packageJson.version)' to '$targetVersion'"
		
		# Update version to match the release version
		$packageJson.version = $targetVersion
		
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
			Write-Host -ForegroundColor DarkMagenta ($packageJson | ConvertTo-Json -depth 20)
		} else {
			$packageJson | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
		}
		$filesToCommit += "$settingsPath"
	}
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

if ($filesToCommit.Count -gt 0) {
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
	} else {
		&$git commit '-m' "$($commitPrefix)updated frontend package.json version for $Version ($jiraId)" @filesToCommit
	}
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# update DevCenter version root menu

$filesToCommit = @()

Write-Host -ForegroundColor DarkYellow 'Updating DevCenter version root menu ...'

$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'tools', 'devcenter', 'versionroot', 'hub-page-data.json')
if (Test-Path "$settingsPath") {
	$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
	
	foreach ($tileDef in $settings.tiles) {
		switch ($tileDef.id) {
			'instance' { $tileDef.url = 'https://apps.itwo40.eu/itwo40/%%versionName%%/client/#/' }
			'viewertest-web' { $tileDef.url = 'https://apps.itwo40.eu/itwo40/%%versionName%%/model-server/viewertest/' }
			'viewertest-render' { $tileDef.url = 'https://ribprdrdragw001.itwo40.eu/itwo40/%%versionName%%/renderservices/model-server/viewertest/' }
		}
	}
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
		Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
	} else {
		$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
	}
	$filesToCommit += "$settingsPath"
} else {
	Write-Host -ForegroundColor Red "File not found: $settingsPath"
}

if ($filesToCommit.Count -gt 0) {
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
	} else {
		&$git commit '-m' "$($commitPrefix)updated DevCenter version menu root for $Version ($jiraId)" @filesToCommit
	}
}

Write-Host -ForegroundColor DarkGreen 'Done.'

# post-processing on origin branch

if (-not $isPatch) {
	$branchingTagName = "V/$mainVer/$subVer/branched"

	$tags.Add("$branchingTagName") | Out-Null

	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Create tag $branchingTagName on $branchOrigin"
	} else {
		&$git 'tag' -a "$branchingTagName" "$branchOrigin" -m "$($commitPrefix)created $verBranchName branch ($jiraId)" -f
	}

	Write-Host -ForegroundColor DarkYellow 'The following changes update the master branch.'
	
	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Create $updateMasterBranchName from master and switch to it"
	} else {
		&$git 'switch' -c $updateMasterBranchName 'master'
	}
	
	$filesToCommit = @()
	
	# compute next version
	
	if ($NextVersion) {
		$nextVer = $NextVersion
	} else {
		$nextMainVer = [int]$mainVer
		$nextSubVer = [int]$subVer
		
		$nextSubVer = $nextSubVer + 1
		if ($nextSubVer -ge 4) {
			$nextSubVer = 1
			$nextMainVer = $nextMainVer + 1
		}
		
		$nextVer = "$nextMainVer.$nextSubVer"
	}
	
	# pipeline settings files

	$filesToCommit = @()
	
	Write-Host -ForegroundColor DarkYellow 'Updating pipeline settings files ...'

	$settingsPath = Join-Path "$appRootDir" '_pipelinesettings.json'
	if (Test-Path "$settingsPath") {
		$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
		
		$oldTlsVersion = $settings.dbversion
		$settings.dbversion = "$nextVer"
		
		$settings.jiraFilters.knownIssues = "$nextMainVer.*"
		$settings.jiraFilters.releaseNotes = "$nextVer"
		$settings.jiraFilters.whatsNew = "$nextVer"
		
		$previewVer = $settings.deployments | Where-Object {$_.name -eq "$oldTlsVersion"}
		if ($previewVer.Count -gt 0) {
			$previewVer | ForEach-Object {$_.name = "$nextVer"}
		} else {
			Write-Host -ForegroundColor Yellow "WARNING: No previous preview version found."
		}
		
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
			Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
		} else {
			$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
		}
		$filesToCommit += "$settingsPath"
	} else {
		Write-Host -ForegroundColor Red "File not found: $settingsPath"
	}
	
	$settingsPath = Join-Path "$appRootDir" '_pipelinesettings-frontend-ci.json'
	if (Test-Path "$settingsPath") {
		$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
		
		$settings.dbversion = "$nextVer"
		
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
			Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
		} else {
			$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
		}
		$filesToCommit += "$settingsPath"
	} else {
		Write-Host -ForegroundColor Red "File not found: $settingsPath"
	}
	
	if ($filesToCommit.Count -gt 0) {
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
		} else {
			&$git commit '-m' "$($commitPrefix)updated config after branching off $Version ($jiraId)" @filesToCommit
		}
	}

	Write-Host -ForegroundColor DarkGreen 'Done.'
	
	# update DevCenter version root menu

	$filesToCommit = @()
	
	Write-Host -ForegroundColor DarkYellow 'Creating Sub-folders and its content for Database ...'

	$settingsPath = Join-Path "$appRootDir" "\database\setup\CurrentSetup\Database\sqlserver\update_database"
	if (Test-Path "$settingsPath") {
		$nextVerFolderName = "{0}00{1}000" -f $nextMainVer, $nextSubVer
		$nextVerBaseFolderPath = Join-Path "$settingsPath" $nextVerFolderName

		$mainVerFolderName = "{0}00{1}000" -f $MainVer, $SubVer
		$mainVerBaseFolderPath = Join-Path "$settingsPath" $mainVerFolderName

		# Create the main folder
		if (-not (Test-Path $nextVerBaseFolderPath)) {
			if ($WhatIfPreference) {
				Write-Host -ForegroundColor Magenta "[SKIP] Create New Database Folder $($nextVerBaseFolderPath)"
				Write-Host -ForegroundColor DarkMagenta (New-Item -Path $nextVerBaseFolderPath -ItemType Directory)
			} else {
				New-Item -Path $nextVerBaseFolderPath -ItemType Directory | Out-Null
			}
		}

		# Create subfolders
		$subFolders = @('patches', 'schema', 'vanilla')
		foreach ($subFolder in $subFolders) {
			$subFolderPath = Join-Path $nextVerBaseFolderPath $subFolder
			if (-not (Test-Path $subFolderPath)) {
				if ($WhatIfPreference) {
					Write-Host -ForegroundColor Magenta "[SKIP] Create Sub Folder '$subFolder' in Database Folder $($nextVerBaseFolderPath)"
					Write-Host -ForegroundColor DarkMagenta (New-Item -Path $subFolderPath -ItemType Directory)
				} else {
					New-Item -Path $subFolderPath -ItemType Directory | Out-Null
				}
			}
		}

		# Create files
		$filerNamePrefix = "{0}{1}000" -f $nextMainVer, $nextSubVer
		$filerNamePrefix = $filerNamePrefix - 1
        foreach ($subFolder in @('patches', 'vanilla')) {
            $subFolderPath = Join-Path $nextVerBaseFolderPath $subFolder
			$fileName = "$filerNamePrefix" + "_PlaceHolderFile_CanBeDeletedLater.sql"
            $filePath = Join-Path $subFolderPath $fileName
            if (-not (Test-Path $filePath)) {
				if ($WhatIfPreference) {
					Write-Host -ForegroundColor Magenta "[SKIP] Create PlaceHolderFile '$fileName' in Database Sub Folder '$($subFolder)'"
					Write-Host -ForegroundColor DarkMagenta (New-Item -Path $filePath -ItemType File)
				} else {
					New-Item -Path $filePath -ItemType File | Out-Null
				}
            }
        }

		# copy files from Main Version to Next Version for schema folder
		$mainVersionSchemaFolderPath = Join-Path "$mainVerBaseFolderPath" "schema"
		$nextVersionSchemaFolderPath = Join-Path "$nextVerBaseFolderPath" "schema"

		# Copy files from the main version schema folder to the next version schema folder
		Get-ChildItem -Path $mainVersionSchemaFolderPath -File | ForEach-Object {
			if ($WhatIfPreference) {
				Write-Host -ForegroundColor Magenta "[SKIP] Copy schema file from '$_' to '$nextVerFolderName\schema'"
				Write-Host -ForegroundColor DarkMagenta (Copy-Item -Path $_.FullName -Destination $nextVersionSchemaFolderPath -Force)
			} else {
				Copy-Item -Path $_.FullName -Destination $nextVersionSchemaFolderPath -Force
			}
		}

		# replace content in file $nextVersionSchemaFolderPath\99_DBVersion.sql
		$filePath99_DBVersion = Join-Path $nextVersionSchemaFolderPath "99_DBVersion.sql"

		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] replace content in file '$nextVersionSchemaFolderPath\99_DBVersion.sql'"
		} else {
			(Get-Content -Path $filePath99_DBVersion -Raw) `
			-replace [regex]::Escape($mainVerFolderName), $nextVerFolderName `
			-replace [regex]::Escape($Version), $nextVer `
			| Set-Content -Path $filePath99_DBVersion -NoNewline
		}		

		$filesToCommit += Get-ChildItem -Path $nextVerBaseFolderPath -Recurse -File | Select-Object -ExpandProperty FullName
	} else {
		Write-Host -ForegroundColor Red "Folder not found: $settingsPath"
	}

	if ($filesToCommit.Count -gt 0) {
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
		} else {
			&$git add @filesToCommit
			&$git commit '-m' "$($commitPrefix)adding DB files after branching off $Version ($jiraId)" @filesToCommit
		}
	}

	Write-Host -ForegroundColor DarkGreen 'Done.'
	
	# update frontend package.json for next version

	$filesToCommit = @()

	Write-Host -ForegroundColor DarkYellow 'Updating frontend package.json version for next version ...'

	$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'frontend', 'package.json')
	if (Test-Path "$settingsPath") {
		$packageJson = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
		
		$targetVersion = "$nextVer.0"
		
		# Check if version is already correct
		if ($packageJson.version -eq $targetVersion) {
			Write-Host -ForegroundColor Green "Frontend package.json version is already correct for next version ($targetVersion) - no update needed"
		} else {
			Write-Host -ForegroundColor Cyan "Updating frontend package.json version from '$($packageJson.version)' to '$targetVersion' for next version"
			
			# Update version to match the next version
			$packageJson.version = $targetVersion
			
			if ($WhatIfPreference) {
				Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
				Write-Host -ForegroundColor DarkMagenta ($packageJson | ConvertTo-Json -depth 20)
			} else {
				$packageJson | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
			}
			$filesToCommit += "$settingsPath"
		}
	} else {
		Write-Host -ForegroundColor Red "File not found: $settingsPath"
	}

	if ($filesToCommit.Count -gt 0) {
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
		} else {
			&$git commit '-m' "$($commitPrefix)updated frontend package.json version for next version $nextVer ($jiraId)" @filesToCommit
		}
	}

	Write-Host -ForegroundColor DarkGreen 'Done.'
	
	# update DevCenter version root menu

	$filesToCommit = @()
	
	Write-Host -ForegroundColor DarkYellow 'Updating DevCenter version root menu ...'

	$settingsPath = [System.IO.Path]::Combine("$appRootDir", 'tools', 'devcenter', 'versionroot', 'hub-page-data.json')
	if (Test-Path "$settingsPath") {
		$settings = Get-Content -Raw "$settingsPath" | ConvertFrom-Json -AsHashTable
		
		foreach ($tileDef in $settings.tiles) {
			switch ($tileDef.id) {
				'gitlog' { $tileDef.url = "https://devcenter.itwo40.eu/ccnet-data/versions/$nextVer/gitlog/"; }
			}
		}
		
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Save $($settingsPath):"
			Write-Host -ForegroundColor DarkMagenta ($settings | ConvertTo-Json -depth 20)
		} else {
			$settings | ConvertTo-Json -depth 20 | Out-File "$settingsPath" -Encoding UTF8 -Force
		}
		$filesToCommit += "$settingsPath"
	} else {
		Write-Host -ForegroundColor Red "File not found: $settingsPath"
	}

	if ($filesToCommit.Count -gt 0) {
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Commit $($filesToCommit)"
		} else {
			&$git commit '-m' "$($commitPrefix)updated DevCenter version menu root for $Version ($jiraId)" @filesToCommit
		}
	}

	Write-Host -ForegroundColor DarkGreen 'Done.'
}

# push to remote

if ($NoPush) {
	Write-Host -ForegroundColor Magenta 'Pushing to the following branches skipped:'
	Write-Host -ForegroundColor Magenta "  - $verBranchName"
	if (-not $isPatch) {
		Write-Host -ForegroundColor Magenta "  - $updateMasterBranchName"
	}
	
	Write-Host -ForegroundColor Magenta 'Pushing of the following tags skipped:'
	foreach ($tag in $tags) {
		Write-Host -ForegroundColor Magenta "  - $tag"
	}
} else {
	Write-Host -ForegroundColor DarkYellow "Pushing branch $verBranchName ..."

	if ($WhatIfPreference) {
		Write-Host -ForegroundColor Magenta "[SKIP] Push $verBranchName"
	} else {
		&$git push 'origin' $verBranchName
	}
	
	Write-Host -ForegroundColor DarkGreen 'Done.'
	
	if (-not $isPatch) {
		Write-Host -ForegroundColor DarkYellow "Pushing branch $updateMasterBranchName ..."
		
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Push $updateMasterBranchName"
		} else {
			&$git push 'origin' $updateMasterBranchName
		}
		
		Write-Host -ForegroundColor DarkGreen 'Done.'
	}
	
	foreach ($tag in $tags) {
		Write-Host -ForegroundColor DarkYellow "Pushing tag $tag ..."
		
		if ($WhatIfPreference) {
			Write-Host -ForegroundColor Magenta "[SKIP] Push tag $tag"
		} else {
			&$git 'push' 'origin' 'tag' "$tag"
		}
		
		Write-Host -ForegroundColor DarkGreen 'Done.'
	}
}

# clean-up

Write-Host -ForegroundColor DarkYellow 'Clean-up in progress ...'

Pop-Location

Write-Host -ForegroundColor DarkGreen 'Done.'

Write-Host -ForegroundColor Green 'Operations have concluded.'
Write-Host ''
Write-Host -ForegroundColor Black -BackgroundColor Green -NoNewLine '  You may now raise the following pull requests:  '
Write-Host -BackgroundColor Black ''

$prsAdded = $False

function Print-Message {
	param(
		[Parameter(Mandatory=$True)]$Lines,
		[Parameter(Mandatory=$True)][Int32]$Width
	)
	
	foreach ($line in $Lines) {
		if (-not ($line -eq $NULL)) {
			Write-Host -ForegroundColor Black -BackgroundColor Gray -NoNewLine "$line".PadRight($Width)
			Write-Host -BackgroundColor Black ''
		}
	}
}

function Write-PR {
	param(
		[Parameter(Mandatory=$True)][string]$FromBranch,
		[Parameter(Mandatory=$True)][string]$ToBranch,
		[Parameter(Mandatory=$True)][string]$Title,
		[Parameter(Mandatory=$True)]$Description
	)
	
	Write-Host ''
	Write-Host -ForegroundColor White 'PR:'
	Write-Host -ForegroundColor DarkGray -NoNewLine '  From:  '
	Write-Host -ForegroundColor Gray "$FromBranch"
	Write-Host -ForegroundColor DarkGray -NoNewLine '  To:    '
	Write-Host -ForegroundColor Gray "$ToBranch"
	Write-Host -ForegroundColor DarkGray -NoNewLine '  Title: '
	Write-Host -ForegroundColor Gray "$Title"
	Write-Host -ForegroundColor DarkGray '  Description:   '
	Print-Message -Lines $Description -Width 80
}

if (-not $isPatch) {
	Write-PR -FromBranch $updateMasterBranchName -ToBranch 'master' -Title "Updated after $ver release branch creation" -Description @(
		"- update settings after $ver release branch creation",
		"- prepared settings files for $nextVer"
	)
	$prsAdded = $True
}

if (-not $prsAdded) {
	Write-Host '(none)'
}

Write-Host -BackgroundColor Black ''

Write-Host -ForegroundColor Black -BackgroundColor Green -NoNewLine '  Please send out the following message to the development team:  '
Write-Host -BackgroundColor Black ''
Write-Host -BackgroundColor Black ''

$env:TZ = 'UTC0'
Print-Message -Width 100 -Lines @(
	'Hi all,',
	'',
	$(if ($isPatch) { "The patch branch for patches of RIB 4.0 release $ver is now available." } else { "The release branch for RIB 4.0 release $mainVer.$subVer is now available." }),
	'',
	'The branch has been created from commit',
	(&$git show $originCommit --pretty=format:"%H (%cd)" --date=format-local:'%A, %Y-%m-%d %I:%M:%S %p UTC' -s),
	$(if ($isPatch) {"with the name $verBranchName"} else {"(tagged as $branchingTagName) with the name $verBranchName"}),
	'',
	$(if ($isPatch) { "All commits that should be part of patches for release $ver" } else {'All commits after the aforementioned revision that should be'}),
	$(if ($isPatch) { 'must be cherry-picked/merged!' } else {"part of the $mainVer.$subVer release must be cherry-picked/merged!"}),
	'',
	'Note that changes to the new TypeScript/Angular front-end',
	"should not be merged to $verBranchName.",
	'',
	$(if ($isPatch) { $NULL } else { "The release $mainVer.$subVer instance can be found at" }),
	$(if ($isPatch) { $NULL } else { "https://apps.itwo40.eu/itwo40/$mainVer.$subVer/client/#/app/main" }),
	$(if ($isPatch) { $NULL } else { '' }),
	'Information on freeze and release dates can be found in the',
	'Release Branch Graph on DevCenter:',
	'https://devcenter.itwo40.eu/releasegraph/iTWO40Releases.html',
	'',
	'Best Regards'
)

Write-Host -BackgroundColor Black ''


Write-Host ''