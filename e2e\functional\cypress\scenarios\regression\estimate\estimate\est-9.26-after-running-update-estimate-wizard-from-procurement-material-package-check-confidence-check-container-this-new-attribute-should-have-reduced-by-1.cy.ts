import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import Buttons from "cypress/locators/buttons";
import { _common, _estimatePage, _validate, _mainView, _package, _projectPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

// VARIABLES----------------------------------------------------------------
const ESTIMATE_CODE = '1' + Cypress._.random(0, 999);
const ESTIMATE_DESCRIPTION = 'EST-DESC-' + Cypress._.random(0, 999);
const LINE_ITEM_DESCRIPTION = 'LI-DESC-' + Cypress._.random(0, 999);


let ESTIMATE_PARAMETERS: DataCells,
  LINE_ITEM_PARAMETERS: DataCells,
  CONTAINER_COLUMNS_LINE_ITEM,
  RESOURCE_PARAMETERS: DataCells,
  RESOURCE_PARAMETERS_1: DataCells,
  UPDATE_ESTIMATE_PARAMETER: DataCells


let CONTAINERS_ESTIMATE,
  CONTAINER_COLUMNS_ESTIMATE,
  CONTAINERS_LINE_ITEM,
  CONTAINERS_RESOURCE,
  CONTAINER_COLUMNS_RESOURCE,
  CONTAINER_COLUMNS_CONFIDENCE_CHECK,
  CONTAINERS_PACKAGE,
  CONFIDENCE_CONFIDENCE_CHECK,
  CONTAINERS_PACKAGE_ITEMS,
  CONTAINER_COLUMNS_PACKAGE_ITEMS,
  MODAL_UPDATE_ESTIMATE_WIZARD,
  CONTAINER_COLUMNS_ESTIMATE_LINE_ITEM


describe('EST- 9.26 | After running update estimate wizard from procurement material package check confidence check container this new attribute should have reduced by 1', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

  before(function () {
    cy.fixture("estimate/est-9.26-after-running-update-estimate-wizard-from-procurement-material-package-check-confidence-check-container-this-new-attribute-should-have-reduced-by-1.json")
      .then((data) => {
        this.data = data;
        CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
        CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE
        ESTIMATE_PARAMETERS = {
          [app.GridCells.CODE]: ESTIMATE_CODE,
          [app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
          [app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
          [app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE,
        }
        CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM
        CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM
        LINE_ITEM_PARAMETERS = {
          [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
          [app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY,
          [app.GridCells.BAS_UOM_FK]: CONTAINERS_LINE_ITEM.UOM,
        }
        CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE
        CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE
        RESOURCE_PARAMETERS = {
          [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
          [app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE[0],
        }
        RESOURCE_PARAMETERS_1 = {
          [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
          [app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE[1],
        }
        CONTAINER_COLUMNS_CONFIDENCE_CHECK = this.data.CONTAINER_COLUMNS.CONFIDENCE_CHECK
        CONTAINERS_PACKAGE = this.data.CONTAINERS.PACKAGE
        CONFIDENCE_CONFIDENCE_CHECK = this.data.CONTAINERS.CONFIDENCE_CHECK
        CONTAINERS_PACKAGE_ITEMS = this.data.CONTAINERS.PACKAGE_ITEMS
        CONTAINER_COLUMNS_PACKAGE_ITEMS = this.data.CONTAINER_COLUMNS.PACKAGE_ITEMS
        CONTAINER_COLUMNS_ESTIMATE_LINE_ITEM = this.data.CONTAINER_COLUMNS.ESTIMATE_LINE_ITEM
        MODAL_UPDATE_ESTIMATE_WIZARD = this.data.MODAL.UPDATE_ESTIMATE_WIZARD
        UPDATE_ESTIMATE_PARAMETER = {
          [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_ESTIMATE_WIZARD
        }

      }).then(() => {
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.setDefaultView(app.TabBar.PROJECT)
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        })
        _common.waitForLoaderToDisappear();
        _commonAPI.getAccessToken().then((result) => {
          cy.log(`Token Retrieved: ${result.token}`);
        });
      })
  });
  after(() => {
    cy.LOGOUT();
  });

  it('TC - API: Create project', function () {
    _commonAPI.createProject().then(() => {
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
    });
  });
  it('TC - Create new estimate record', function () {
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATE)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE);
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
    _common.create_newRecord(cnt.uuid.ESTIMATE);
    _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
  });
  it("TC - Create new line item record", function () {
    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATELINEITEM)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
    });
    _common.waitForLoaderToDisappear()

    _common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
    _common.create_newRecord(cnt.uuid.ESTIMATE_LINEITEMS);
    _estimatePage.enterRecord_toCreateLineItem(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_PARAMETERS);
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.minimizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
  });
  it("TC - Create new material resource", function () {
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
      _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE)
    });
    _common.maximizeContainer(cnt.uuid.RESOURCES)
    _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
    _common.create_newRecord(cnt.uuid.RESOURCES);
    _common.waitForLoaderToDisappear()
    _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
    _common.minimizeContainer(cnt.uuid.RESOURCES)
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
  });
  it("TC - Create new 2nd material resource", function () {
    _common.create_newRecord(cnt.uuid.RESOURCES);
    _common.waitForLoaderToDisappear()
    _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS_1);
    _common.minimizeContainer(cnt.uuid.RESOURCES)
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
  });
  it('TC - verify confidence check count after package creation', function () {
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.CONFIDENCE_CHECK, app.FooterTab.CONFIDENCE_CHECK, 1);
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_UPDATE_MATERIAL_PACKAGE)
      _package.create_materialPackagefromWizard(CONTAINERS_PACKAGE.CRITERIA_SELECTION)
      _common.waitForLoaderToDisappear()
      _common.clickOn_modalFooterButton(Buttons.ButtonText.OK)
      _common.select_tabFromFooter(cnt.uuid.CONFIDENCE_CHECK, app.FooterTab.CONFIDENCE_CHECK, 1);
      _common.maximizeContainer(cnt.uuid.CONFIDENCE_CHECK)
      _common.waitForLoaderToDisappear()
      _common.clickOn_toolbarButton(cnt.uuid.CONFIDENCE_CHECK, Buttons.ToolBar.ICO_REFRESH)
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.CONFIDENCE_CHECK)
      _common.clickOn_cellHasValue(cnt.uuid.CONFIDENCE_CHECK, app.GridCells.DESCRIPTION_INFO, CONFIDENCE_CONFIDENCE_CHECK.DESCRIPTION)
      cy.wait(1000) //required wait to load page
      _common.assert_cellData_insideActiveRow(cnt.uuid.CONFIDENCE_CHECK, app.GridCells.COUNT, "2")
      _common.minimizeContainer(cnt.uuid.CONFIDENCE_CHECK)
    });
  })
  it('TC - Update Price in package Items', function () {
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PACKAGE_ITEM_ASSIGNMENT, app.FooterTab.PACKAGE_ITEM_ASSIGNMENT, 2)
      _common.clickOn_cellHasValue(cnt.uuid.PACKAGE_ITEM_ASSIGNMENT, app.GridCells.EST_RESOURCE_FK, CONTAINERS_RESOURCE.CODE[0])
      _common.waitForLoaderToDisappear()
      _common.clickOn_activeRowCell(cnt.uuid.PACKAGE_ITEM_ASSIGNMENT, app.GridCells.EST_LINE_ITEM_FK)
      _common.waitForLoaderToDisappear()
      _common.goToButton_inActiveRow(cnt.uuid.PACKAGE_ITEM_ASSIGNMENT, app.GridCells.EST_LINE_ITEM_FK, btn.IconButtons.ICO_PACKAGE, btn.ButtonText.GOTO_PACKAGE_CAMELCASE)
      _common.waitForLoaderToDisappear()
      cy.wait(2000)
    });
    _common.openTab(app.TabBar.PACKAGE).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PACKAGEITEMS, app.FooterTab.ITEMS, 2)
      _common.setup_gridLayout(cnt.uuid.PACKAGEITEMS, CONTAINER_COLUMNS_PACKAGE_ITEMS)
      _common.set_columnAtTop([CONTAINER_COLUMNS_PACKAGE_ITEMS.price], cnt.uuid.PACKAGEITEMS)
      _common.waitForLoaderToDisappear()
      _common.clear_subContainerFilter(cnt.uuid.PACKAGEITEMS)
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.select_rowInSubContainer(cnt.uuid.PACKAGEITEMS)
      _common.waitForLoaderToDisappear()
      _common.edit_containerCell(cnt.uuid.PACKAGEITEMS, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PACKAGE_ITEMS.UPDATED_QUANTITY)
      _common.waitForLoaderToDisappear()
      cy.SAVE()
      _common.waitForLoaderToDisappear()
      cy.SAVE()
      _common.waitForLoaderToDisappear()
    });
  })

  it('TC - Verify after running “Update Estimate” wizard from Procurement (material package), check confidence check container. This new attribute should have reduced by 1', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env(`API_PROJECT_NAME_1`))
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.PACKAGE).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINE_ITEM, app.FooterTab.ESTIMATELINEITEM, 2)
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINE_ITEM, CONTAINER_COLUMNS_ESTIMATE_LINE_ITEM)
      _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINE_ITEM)
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE)
      _common.waitForLoaderToDisappear()
      _estimatePage.updateEstimate_fromWizard_byClass(UPDATE_ESTIMATE_PARAMETER)
      _common.waitForLoaderToDisappear()
      _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
      _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
      _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINE_ITEM, app.FooterTab.ESTIMATELINEITEM, 2)
      _common.maximizeContainer(cnt.uuid.ESTIMATE_LINE_ITEM)
      _common.waitForLoaderToDisappear()
      _common.goToButton_inActiveRow(cnt.uuid.ESTIMATE_LINE_ITEM, app.GridCells.PROJECT_NAME_SMALL, Buttons.IconButtons.ICO_GO_TO, btn.ButtonText.GO_TO_ESTIMATE_LINE_ITEM)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.CONFIDENCE_CHECK, app.FooterTab.CONFIDENCE_CHECK, 1);
      _common.clickOn_cellHasValue(cnt.uuid.CONFIDENCE_CHECK, app.GridCells.DESCRIPTION_INFO, CONFIDENCE_CONFIDENCE_CHECK.DESCRIPTION)
      _common.assert_cellData_insideActiveRow(cnt.uuid.CONFIDENCE_CHECK, app.GridCells.COUNT, "1")
    });
  })

})