/*
 * Copyright(c) RIB Software GmbH
 */

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {
	ModulePreloadInfoBase,
	PlatformModuleManagerService
} from '@libs/platform/common';
import { ControllingPreloadInfo } from './model/controlling-preload-info.class';

/**
 * This class initializes the `controlling` preload module.
 */
@NgModule({
	imports: [CommonModule],
})
export class ControllingPreloadModule extends ModulePreloadInfoBase {
	public constructor(moduleManager: PlatformModuleManagerService) {
		moduleManager.registerPreloadModule(ControllingPreloadInfo.instance);
		super();
	}

	public override get internalModuleName(): string {
		return 'controlling';
	}
}
