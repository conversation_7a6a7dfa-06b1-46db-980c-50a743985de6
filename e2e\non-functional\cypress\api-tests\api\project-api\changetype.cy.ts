describe('GET- Change Type Details',()=>{

    enum methods {
        GET = "GET",
        POST = "POST",
        PUT = "PUT",
        DELETE = "DELETE",
      }

      var token, tokenType,changetypeId;
      before(function () {
        cy.fixture("login-headers.json").then((data)=>{
          this.data= data;
          const input= this.data;
        cy.request({
          method: 'POST',
          url: Cypress.env("Base_URL")+'identityservercore/core/connect/token',
          headers:input.Login,
          body: input.Body
        }).then((responce) => {
          expect(responce.status).to.be.eq(200);
          token = responce.body.access_token;
          tokenType = responce.body.token_type;
          cy.log(Cypress.env("TokenType", tokenType) + " " + Cypress.env("LoginToken", token));
        }); 
      }) 
     });
    
      it("1- GET-Returns items of change type as specified by OData arguments ver 2.0",function (){

        cy.request({
            method: 'GET',
            url: Cypress.env("Base_URL")+"services/basics/customizepublicapi/changetype/2.0",
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json, text/plain, */*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Connection': 'keep-alive',
              'Client-Context':Cypress.env("Client_Context"),
              'Authorization': (Cypress.env("TokenType") + " " + (Cypress.env("LoginToken", token)))
            },
            body: {
            
            }
        }).then((response) => {
            expect(response.status).to.be.eq(200);
            
            changetypeId= response.body[0].Id;
            cy.log(changetypeId);


        });
    })
    it("2- GET-Retrieves an item of change type based on its ID ver 2.0",function (){

        cy.request({
            method: 'GET',
            url: Cypress.env("Base_URL")+"services/basics/customizepublicapi/changetype/2.0/"+changetypeId,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json, text/plain, */*',
              'Accept-Encoding': 'gzip, deflate, br',
              'Connection': 'keep-alive',
              'Client-Context':Cypress.env("Client_Context"),
              'Authorization': (Cypress.env("TokenType") + " " + (Cypress.env("LoginToken", token)))
            },
            body: {
            
            }
        }).then((response) => {
            expect(response.status).to.be.eq(200);
            cy.log(response.body);
        });
    })
    it("3- GET-Retrieves an item of change type based on lastchanged ver 2.0",function (){

      cy.request({
          method: 'GET',
          url: Cypress.env("Base_URL")+"services/basics/customizepublicapi/changetype/2.0/lastchanged",
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Client-Context':Cypress.env("Client_Context"),
            'Authorization': (Cypress.env("TokenType") + " " + (Cypress.env("LoginToken", token)))
          },
          body: {
          
          }
      }).then((response) => {
          expect(response.status).to.be.eq(200);
          cy.log(response.body);
      });
  })
  it("4- GET-Retrieves an item of change type based on count ver 2.0.",function (){

    cy.request({
        method: 'GET',
        url: Cypress.env("Base_URL")+"services/basics/customizepublicapi/changetype/2.0/$count",
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Client-Context':Cypress.env("Client_Context"),
          'Authorization': (Cypress.env("TokenType") + " " + (Cypress.env("LoginToken", token)))
        },
        body: {
        
        }
    }).then((response) => {
        expect(response.status).to.be.eq(200);
        cy.log(response.body);
    }); 
  })
})