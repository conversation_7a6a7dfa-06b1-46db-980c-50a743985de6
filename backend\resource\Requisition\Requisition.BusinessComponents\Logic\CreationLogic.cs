using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using RIB.Visual.Basics.Unit.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Resource.Requisition.Common;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Basics.Company.BusinessComponents;

namespace RIB.Visual.Resource.Requisition.BusinessComponents
{
	/// <summary>
	/// CreationLogic
	/// </summary>
	public class CreationLogic
	{
		/// <summary>
		/// CreateRequistionsByResources
		/// </summary>
		/// <param name="reqByResConfig"></param>
		public IEnumerable<RequisitionEntity> CreateRequistionsByResources(RequisitionByResourceEntity reqByResConfig)
		{
			int RequistionsToGenerate = reqByResConfig.Resource.Count();
			var resources = reqByResConfig.Resource.ToList();
			List<RequisitionEntity> requistions = new List<RequisitionEntity>();
			IEnumerable<RequisitionEntity> requisitionsSaveResult = null;
			if (RequistionsToGenerate > 0)
			{
				int const_Quantity = 1;
				int const_UoM = GetDefaultBAS_UoM();
				DateTime const_LastDateOfMonth = CalculateLastDayOfMonth(Convert.ToDateTime(reqByResConfig.Startdate));
				int const_CompanyId = GetCurrentCopmanyId();
				int? const_ClerkId = GetCurrentClerkId();
				int const_ReqTypeFk = GetRequisitionTypeId();
				int? const_ReqGroupFk = GetRequisitionGroupId();
				int? const_ReqPriorityFk = GetRequisitionPriorityId();

				requistions = new ResourceRequisitionMainLogic().CreateMultipleRequisitions(RequistionsToGenerate);

				for (int i = 0; i < requistions.Count(); i++)
				{
					requistions[i].Description = resources[i].Description;
					requistions[i].ProjectFk = reqByResConfig.ProjectFk;
					requistions[i].ResourceFk = resources[i].Id;
					requistions[i].RequisitionStatusFk = reqByResConfig.StatusFk;
					requistions[i].TypeFk = resources[i].TypeFk;
					requistions[i].Quantity = const_Quantity;
					requistions[i].RequestedFrom = Convert.ToDateTime(reqByResConfig.Startdate);
					requistions[i].RequestedTo = const_LastDateOfMonth;
					requistions[i].JobFk = reqByResConfig.JobFk;
					requistions[i].ResourceContextFk = resources[i].ResourceContextFk;
					requistions[i].UserDefinedText01 = resources[i].Userdefined1;
					requistions[i].UserDefinedText02 = resources[i].Userdefined2;
					requistions[i].UserDefinedText03 = resources[i].Userdefined3;
					requistions[i].UserDefinedText04 = reqByResConfig.UserDefineText4 != null ? reqByResConfig.UserDefineText4 : resources[i].Userdefined4;
					requistions[i].UserDefinedText05 = reqByResConfig.UserDefineText4 != null ? reqByResConfig.UserDefineText5 : resources[i].Userdefined5;
					requistions[i].CompanyFk = const_CompanyId;
					requistions[i].ClerkOwnerFk = const_ClerkId;
					requistions[i].RequisitionGroupFk = const_ReqGroupFk;
					requistions[i].RequisitionPriorityFk = const_ReqPriorityFk;
					requistions[i].RequisitionTypeFk = const_ReqTypeFk;
					requistions[i].UomFk = const_UoM;
				}
			}
			if (requistions.Any())
			{
				requisitionsSaveResult = new ResourceRequisitionMainLogic().Save(requistions);
			}
			return requisitionsSaveResult;
		}

		private DateTime CalculateLastDayOfMonth(DateTime date)
		{
			int resultOfDays = DateTime.DaysInMonth(date.Year, date.Month);
			return new DateTime(date.Year, date.Month, resultOfDays, date.Hour, date.Minute, date.Millisecond);
		}

		private int GetCurrentCopmanyId()
		{
			var currentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			return currentContext.ClientId;
		}

		private int? GetCurrentClerkId()
		{
			int? result = null;
			var clerkprovider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IClerkInfoProvider>();
			var resultClerk = clerkprovider.GetLoginClerkInfo();
			if (resultClerk != null)
			{
				result = resultClerk.Id;
			}
			return result;
		}
		private int GetDefaultBAS_UoM()
		{
			int result = 0;
			var resultUoM = new BasicsUnitLogic().GetUoM(e => e.Id > 0 && e.IsBase == true).FirstOrDefault();
			if (resultUoM != null)
			{
				result = resultUoM.Id;
			}
			return result;
		}

		private int GetRequisitionTypeId()
		{//It is fix one, as this are resource requisition by design. See ALM 114996.
			return 1;
		}

		private int? GetRequisitionGroupId()
		{
			int? result = null;
			var resultGroup = new BasicsCustomizeResRequisitionGroupLogic().GetDefault();
			if (resultGroup != null)
			{
				result = resultGroup.Id;
			}
			return result;
		}

		private int? GetRequisitionPriorityId()
		{
			int? result = null;
			var resultPriority = new BasicsCustomizeResRequisitionPriorityLogic().GetDefault();
			if (resultPriority != null)
			{
				result = resultPriority.Id;
			}
			return result;
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public int CreateByEstimate(RequisitionByEstimateRequest request)
		{
			var calendarLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ICalendarUtilitiesLogic>();

			var primary = CreatePrimaryByEstimate(request, calendarLogic);
			var secondary = CreateSecondaryRequisitions(primary, calendarLogic);
			var primarySkill = CreateSkillRequisitions(primary);
			var secondarySkill = CreateSkillRequisitions(secondary);
			return primary.Count() + secondary.Count() + primarySkill.Count() + secondarySkill.Count();
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <param name="calendarLogic"></param>
		private IEnumerable<RequisitionEntity> CreatePrimaryByEstimate(RequisitionByEstimateRequest request, ICalendarUtilitiesLogic calendarLogic)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var tempToGenerate = dbcontext.Entities<RequisitionGenReqFromEstVEntity>().Where(e => e.PrjProjectFk == request.ProjectFk && e.EstHeaderFk == request.EstimateHeaderFk && e.PlantFk.HasValue).ToArray();
				if(tempToGenerate.IsNullOrEmpty())
				{
					throw new BusinessLayerException("There are no estimate plant resources linked to plants via pricelist in the selected header. No requisitions are generated");
				}
				if (tempToGenerate.Any(g => !g.MdcControllingunitPlannedStart.HasValue))
				{
					throw new BusinessLayerException("At least one of the Line Item's Controlling Unit has no Planned Start Date set");
				}
				var toGenerateGrouped = tempToGenerate.GroupBy(e => e.EtmPlantCode).ToDictionary(e => e.Key, e => e.First());
				var reducedToGenerate = new List<RequisitionGenReqFromEstVEntity>();
				foreach(var kvp in toGenerateGrouped)
				{
					reducedToGenerate.Add(kvp.Value);
				}
				tempToGenerate = reducedToGenerate.ToArray();
				if (tempToGenerate.IsNullOrEmpty())
				{
					return Array.Empty<RequisitionEntity>();
				}
				var requisitionLogic = new ResourceRequisitionMainLogic();
				var lineItemFks = tempToGenerate.Select(t => t.EstLineItemFk).ToList();
				// check if already data from the line items are created
				var existingLineItemFks = requisitionLogic.GetByFilter(e => lineItemFks.Contains(e.EstLineItemFk.Value)).ToList();
				if (existingLineItemFks.Any())
				{
					throw new Exception("requisitions are already created");
				}
				var toGenerate = requisitionLogic.CreateRequisitions(tempToGenerate.Count()).EqualSizeJoinByIndex(tempToGenerate, (req, gen) => new { Requisition = req, Generate = gen }).ToArray();
				var requisitionEntities = toGenerate.Select(req => req.Requisition);
				var rubricCatFk = requisitionEntities.Where(r => r.RubricCategoryFk.HasValue).Select(r => r.RubricCategoryFk.Value).FirstOrDefault();
				
				// Get code sequences 
				var sequenceNumbers = GetSequenceNumbersForCode(rubricCatFk, request.ProjectFk, toGenerate.Count());

				for (int i = 0; i < toGenerate.Count(); i++)
				{
					var req = toGenerate.ElementAt(i);
					var requisition = req.Requisition;
					var generate = req.Generate;

			      //	Fill primary requisition properties
					requisition.RequisitionTypeFk = ResourceRequisitionConstants.RequisitionTypePlant;
					RelationDefaultValueSetter.Handle(requisition, new List<Tuple<string, Action<RequisitionEntity, int>, int?>>() {
						new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.resrequisitionstatus", (e, i) => e.RequisitionStatusFk = i, null)
					});
					requisition.CompanyFk = generate.BasCompanyFk;
					requisition.ProjectFk = generate.PrjProjectFk;
					requisition.JobFk = generate.LgmJobFk.Value;
					requisition.Description = generate.EstLineItemDescription;
					requisition.UomFk = generate.EtmPlantBasUomFk.Value;
					requisition.ProjectFk = generate.PrjProjectFk;
					requisition.Quantity = 1;
					requisition.DispatcherGroupFk = generate.LgmDispatcherGroupFk;
					requisition.TypeFk = generate.ResTypeFk;
					requisition.RequisitionFk = null;
					requisition.PreferredResourceSiteFk = null;
					requisition.RequestedFrom = generate.MdcControllingunitPlannedStart.Value.AddHours(8);
					requisition.ProjectFk = generate.PrjProjectFk;
					requisition.Quantity = 1;
					requisition.RequestedTo = calendarLogic.EndDate(generate.CalCalendarFk.Value, requisition.RequestedFrom, GetDurationInDays(generate));
					requisition.EstHeaderFk = generate.EstHeaderFk;
					requisition.EstLineItemFk = generate.EstLineItemFk;
					requisition.EstResourceFk = generate.EstResourceFk;
					requisition.IsBottleNeck = false;
					requisition.QuantityFromEstimate = generate.ResQuantity;
					requisition.TypeFromEstimateFk = generate.ResTypeFk;
					requisition.WorkOperationTypeFk = generate.WorkOperationTypeFk;
					requisition.WorkOperationTypeFromEstimateFk = generate.WorkOperationTypeFk;
					requisition.MdcControllingUnitFk = generate.ControllingUnitFk;


					requisition.Code = sequenceNumbers[i];
				}

				requisitionLogic.Save(toGenerate.Select(g => g.Requisition));
				return toGenerate.Select(g => g.Requisition);
			}
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="primaryRequisitions"></param>
		/// <param name="calendarLogic"></param>
		/// <returns></returns>
		private IEnumerable<RequisitionEntity> CreateSecondaryRequisitions(IEnumerable<RequisitionEntity> primaryRequisitions, ICalendarUtilitiesLogic calendarLogic)
		{
			var joblogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var req2reqLogic = new Requisition2RequisitionLogic();
			var requisitionLogic = new ResourceRequisitionMainLogic();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var primaryRequisitionIds = primaryRequisitions.Select(r => r.Id).ToList();
				var tempSecondaryDemands = dbcontext.Entities<RequisitionGenReqTypVEntity>().Where(e => primaryRequisitionIds.Contains(e.ResRequisitionFk)).ToArray();
				var filteredTempSecondaryDemands = new List<RequisitionGenReqTypVEntity>();
				foreach(var temp in tempSecondaryDemands)
				{
					if (temp.ResTyperequestedFk.HasValue)
					{
						filteredTempSecondaryDemands.Add(temp);
					}
				}
				var temp2SecondaryDemands = filteredTempSecondaryDemands.Join(primaryRequisitions, d => d.ResRequisitionFk, r => r.Id, (d, r) => new { Demand = d, PrimaryRequisition = r });
				var secondaryDemands = temp2SecondaryDemands.EqualSizeJoinByIndex(
					requisitionLogic.CreateRequisitions(temp2SecondaryDemands.Count()),
					req2reqLogic.MultiCreate(temp2SecondaryDemands.Count()),
					(d2, r, r2r) => new {
						Demand = d2.Demand,
						PrimaryRequisition = d2.PrimaryRequisition,
						newRequistion = r,
						NewReq2Req = r2r
					}).ToArray();

				if (secondaryDemands.IsNullOrEmpty())
				{
					return Array.Empty<RequisitionEntity>();
				}
				var secondaryRequisitions = secondaryDemands.Select(tuple => tuple.newRequistion);
				var projectFk = primaryRequisitions.Select(pr => pr.ProjectFk).FirstOrDefault();
				var rubricCatFk = primaryRequisitions.Where(pr => pr.RubricCategoryFk.HasValue).Select(pr => pr.RubricCategoryFk.Value).FirstOrDefault();
				//var jobs = joblogic.GetJobByIds(secondaryDemands.Select(d => d.PrimaryRequisition.JobFk));
				// Get code sequences 
				var sequenceNumbers = GetSequenceNumbersForCode(rubricCatFk, projectFk, secondaryDemands.Count());

				for (int i = 0; i < secondaryDemands.Count(); i++)
				{
					var dem = secondaryDemands.ElementAt(i);
					var primaryRequisition = dem.PrimaryRequisition;
					var requisition = dem.newRequistion;
					var demand = dem.Demand;
					var req2req = dem.NewReq2Req;

					//var job = jobs.First(j => primaryRequisition.JobFk == j.Id);
					//Fill secondary requisition properties;
					requisition.RequisitionTypeFk = DetermineRequisitionTypeFromSecondaryDemand(demand);
					RelationDefaultValueSetter.Handle(requisition, new List<Tuple<string, Action<RequisitionEntity, int>, int?>>() {
						new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.resrequisitionstatus", (e, i) => e.RequisitionStatusFk = i, null)
					});
					requisition.CompanyFk = primaryRequisition.CompanyFk;
					requisition.ProjectFk = primaryRequisition.ProjectFk;
					requisition.JobFk = primaryRequisition.JobFk;
					requisition.Description = primaryRequisition.Description;
					requisition.UomFk = demand.BasUomdayFk.HasValue ? demand.BasUomdayFk.Value : 0;
					requisition.Quantity = 1;
					requisition.DispatcherGroupFk = demand.LgmDispatcherGroupFk;
					requisition.TypeFk = demand.ResTyperequestedFk;
					requisition.RequisitionFk = null;
					requisition.PreferredResourceSiteFk = null;
					requisition.RequestedFrom = primaryRequisition.RequestedFrom;
					requisition.Quantity = 1;
					requisition.RequestedTo = demand.Isrequestedentireperiod ? primaryRequisition.RequestedTo : calendarLogic.EndDate(demand.CalCalendarFk, requisition.RequestedFrom, demand.Duration.Value + 1);
					requisition.EstHeaderFk = primaryRequisition.EstHeaderFk;
					requisition.EstLineItemFk = primaryRequisition.EstLineItemFk;
					requisition.EstResourceFk = primaryRequisition.EstResourceFk;
					requisition.IsBottleNeck = false;
					requisition.QuantityFromEstimate = primaryRequisition.QuantityFromEstimate;
					requisition.TypeFromEstimateFk = primaryRequisition.TypeFromEstimateFk;
					requisition.WorkOperationTypeFk = primaryRequisition.WorkOperationTypeFk;
					requisition.WorkOperationTypeFromEstimateFk = primaryRequisition.WorkOperationTypeFk;
					requisition.MdcControllingUnitFk = primaryRequisition.MdcControllingUnitFk;
					requisition.Code = sequenceNumbers[i];
					req2req.RequisitionFk = primaryRequisition.Id;
					req2req.RequisitionLinkedFk = requisition.Id;
				}

				requisitionLogic.Save(secondaryDemands.Select(g => g.newRequistion));
				req2reqLogic.Save(secondaryDemands.Select(g => g.NewReq2Req));
				return secondaryDemands.Select(g => g.newRequistion);
			}
		}

		private Int32? DetermineRequisitionTypeFromSecondaryDemand(RequisitionGenReqTypVEntity demand)
		{
			if(demand.IsPlantRequested.HasValue && demand.IsPlantRequested.Value) {
				return ResourceRequisitionConstants.RequisitionTypePlant;
			}

			if((demand.SkillOfRequestedTypeFk.HasValue || (demand.IsDriverRequested.HasValue && demand.IsDriverRequested.Value )) &&
				(!demand.IsCraneRequested.HasValue && !demand.IsCraneRequested.Value) && (!demand.IsTruckRequested.HasValue && !demand.IsTruckRequested.Value))
			{
				return ResourceRequisitionConstants.RequisitionTypeEmployee;
			}

			return ResourceRequisitionConstants.RequisitionTypeResource;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="primaryRequisitions"></param>
		/// <returns></returns>
		public IEnumerable<RequisitionEntity> CreateSkillRequisitions(IEnumerable<RequisitionEntity> primaryRequisitions)
		{
			var joblogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ILogisticJobLogic>();
			var req2reqLogic = new Requisition2RequisitionLogic();
			var requisitionLogic = new ResourceRequisitionMainLogic();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var primaryRequisitionIds = primaryRequisitions.Select(r => r.Id).ToList();
				var tempSecondaryDemands = dbcontext.Entities<RequisitionGenReqTypVEntity>().Where(e => primaryRequisitionIds.Contains(e.ResRequisitionFk)).ToArray();
				var temp2SecondaryDemands = tempSecondaryDemands.Join(primaryRequisitions, d => d.ResRequisitionFk, r => r.Id, (d, r) => new Tuple<RequisitionGenReqTypVEntity, RequisitionEntity>(d, r)).ToArray();
				//var temp1 = requisitionLogic.CreateRequisitions(temp2SecondaryDemands.Count());
				//var temp2 = req2reqLogic.MultiCreate(temp2SecondaryDemands.Count());
				var secondaryDemands = temp2SecondaryDemands.EqualSizeJoinByIndex(
					requisitionLogic.CreateRequisitions(temp2SecondaryDemands.Count()),
					req2reqLogic.MultiCreate(temp2SecondaryDemands.Count()),
					(d2, r, r2r) => new {
						Demand = d2.Item1,
						PrimaryRequisition = d2.Item2,
						newRequistion = r,
						NewReq2Req = r2r
					}).ToArray();

				if (secondaryDemands.IsNullOrEmpty()){
					return Array.Empty<RequisitionEntity>();
				}

				//var jobs = joblogic.GetJobByIds(secondaryDemands.Select(d => d.PrimaryRequisition.JobFk));
				var skillReqs = secondaryDemands.Select(tuple => tuple.newRequistion);
				var projectFk = primaryRequisitions.Select(pr => pr.ProjectFk).FirstOrDefault();
				var rubricCatFk = primaryRequisitions.Where(pr => pr.RubricCategoryFk.HasValue).Select(pr => pr.RubricCategoryFk.Value).FirstOrDefault();
				var sequenceNumbers = GetSequenceNumbersForCode(rubricCatFk, projectFk, secondaryDemands.Count());

				for (int i = 0; i < secondaryDemands.Count(); i++)
				{
					var dem = secondaryDemands.ElementAt(i);
					var primaryRequisition = dem.PrimaryRequisition;
					var requisition = dem.newRequistion;
					var demand = dem.Demand;
					var req2req = dem.NewReq2Req;

					//Fill skill requisition properties
					//var job = jobs.First(j => primaryRequisition.JobFk == j.Id);
					requisition.RequisitionTypeFk = ResourceRequisitionConstants.RequisitionTypeEmployee;
					RelationDefaultValueSetter.Handle(requisition, new List<Tuple<string, Action<RequisitionEntity, int>, int?>>() {
						new Tuple<string, Action<RequisitionEntity, int>, int?>("basics.customize.resrequisitionstatus", (e, i) => e.RequisitionStatusFk = i, null)
					});
					requisition.CompanyFk = primaryRequisition.CompanyFk;
					requisition.ProjectFk = primaryRequisition.ProjectFk;
					requisition.JobFk = primaryRequisition.JobFk;
					requisition.Description = primaryRequisition.Description;
					requisition.UomFk = demand.BasUomdayFk.HasValue ? demand.BasUomdayFk.Value : 0;
					requisition.Quantity = 1;
					requisition.DispatcherGroupFk = demand.LgmDispatcherGroupFk;
					requisition.TypeFk = demand.ResSkillResTypeFk;
					requisition.RequisitionFk = null;
					requisition.PreferredResourceSiteFk = null;
					requisition.RequestedFrom = primaryRequisition.RequestedFrom;
					requisition.Quantity = 1;
					requisition.RequestedTo = primaryRequisition.RequestedTo;
					requisition.EstHeaderFk = primaryRequisition.EstHeaderFk;
					requisition.EstLineItemFk = primaryRequisition.EstLineItemFk;
					requisition.EstResourceFk = primaryRequisition.EstResourceFk;
					requisition.IsBottleNeck = false;
					requisition.QuantityFromEstimate = primaryRequisition.QuantityFromEstimate;
					requisition.TypeFromEstimateFk = primaryRequisition.TypeFromEstimateFk;
					requisition.WorkOperationTypeFk = primaryRequisition.WorkOperationTypeFk;
					requisition.WorkOperationTypeFromEstimateFk = primaryRequisition.WorkOperationTypeFk;
					requisition.MdcControllingUnitFk = primaryRequisition.MdcControllingUnitFk;
					requisition.Code = sequenceNumbers[i];
					req2req.RequisitionFk = primaryRequisition.Id;
					req2req.RequisitionLinkedFk = requisition.Id;
				}
				requisitionLogic.Save(secondaryDemands.Select(g => g.newRequistion));
				req2reqLogic.Save(secondaryDemands.Select(g => g.NewReq2Req));

				new RequisitionRequiredSkillLogic().CreateRequestedSkills(secondaryDemands.GroupBy(sd => sd.Demand).ToDictionary(e => e.Key, e => e.First().newRequistion));
				return secondaryDemands.Select(g => g.newRequistion);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="generate"></param>
		/// <returns></returns>
		private decimal GetDurationInDays(RequisitionGenReqFromEstVEntity generate)
		{
			switch (generate.ResWotBasUomFk)
			{
				case var value when value == generate.BasUomhourFk:
					return generate.ResQuantity / generate.Workhoursperday.Value + 1;
				case var value when value == generate.BasUomdayFk:
					return generate.ResQuantity + 1;
				case var value when value == generate.BasUommonthFk:
					return (generate.ResQuantity * generate.Workhourspermonth.Value) / generate.Workhoursperday.Value + 1;
				default:
					return generate.ResQuantity + 1;
			}
		}

		/// <summary>
		/// Get code sequence 
		/// </summary>
		/// <param name="rubricCategoryFk"></param>
		/// <param name="projectFk"></param>
		/// <param name="count"></param>
		/// <returns></returns>
		private List<string> GetSequenceNumbersForCode(int rubricCategoryFk, int projectFk, int count)
		{
			var project = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>().GetProjectById(projectFk);
			var projectNo = project.ProjectNo;
			var sequenceConfig = new CompanySequenceConfig("resource.requisition.requisition")
			{
				ProjectFk = projectFk,
				ProjectNo = projectNo
			};

			var basicsCompanyNumberLogic = new BasicsCompanyNumberLogic();
			NextCompanyNumberResultData nextCompanyNumberResultData = basicsCompanyNumberLogic.GetNextCompanyNumberResultData(rubricCategoryFk, count, sequenceConfig);

			return nextCompanyNumberResultData != null && nextCompanyNumberResultData.IsSuccess && nextCompanyNumberResultData.Service_FormattedCompanyNos != null ?
			  nextCompanyNumberResultData.Service_FormattedCompanyNos.ToList()
		     : new List<string>();
		}
	}
}
