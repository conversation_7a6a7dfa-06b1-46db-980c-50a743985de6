import { tile, app, cnt, btn, commonLocators, sidebar } from "cypress/locators";
import { _common, _projectPage, _procurementPage, _estimatePage, _validate, _controllingUnit, _materialPage, _assembliesPage, _boqPage, _mainView, _package, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import { EST_HEADER } from "cypress/pages/variables";
import CommonLocators from "cypress/locators/common-locators";


const BOQ_DESC = "BOQ-DESC-" + Cypress._.random(0, 999);
const BOQ_DESC1 = "BOQ1-DESC-" + Cypress._.random(0, 999);
const BOQ_STRUCTURE_DESC = "BOQSTR-DESC-" + Cypress._.random(0, 999);
const BOQ_STRUCTURE_DESC1 = "BOQSTR1-DESC-" + Cypress._.random(0, 999);

const ESTIMATE_CODE = '1' + Cypress._.random(0, 999);
const ESTIMATE_DESCRIPTION = 'EST-DESC-' + Cypress._.random(0, 999);

let
    BOQ_PARAMETERS: DataCells, BOQ_PARAMETERS1: DataCells,
    BOQ_STRUCTURE_PARAMETERS: DataCells,
    BOQ_STRUCTURE_PARAMETERS1: DataCells,
    CREATE_BOQ_PACKAGE_PARAMETERS: DataCells,
    CREATE_BOQ_PACKAGE_PARAMETERS1: DataCells;

let CONTAINER_COLUMNS_BOQS,
    CONTAINERS_BOQSTRUCTURE,
    CONTAINER_COLUMNS_BOQ_STRUCTURE,
    ESTIMATE_PARAMETERS: DataCells,
    UPDATE_ESTIMATE_PARAMETER: DataCells,
    MODAL_UPDATE_ESTIMATE_WIZARD,

    CONTAINERS_ESTIMATE,
    CONTAINER_COLUMNS_ESTIMATE,
    RESOURCE_PARAMETERS: DataCells, RESOURCE_PARAMETERS1: DataCells,
    CONTAINERS_RESOURCE,
    CONTAINER_COLUMNS_RESOURCE,
    GENERATE_LINE_ITEMS_PARAMETERS: DataCells, GENERATE_LINE_ITEMS_PARAMETERS1: DataCells,
    CONTAINER_COLUMNS_LINE_ITEM,
    CONTAINERS_PACKAGE,
    CONTAINER_COLUMNS_ESTIMATELINEITEM,
    CONTAINERS_CONFIDENCECHECK


describe("EST- 9.25 | After running “Update Estimate” wizard from Procurement (service package), check confidence check container. This new attribute should have reduced by 1", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture("estimate/est-9.25-after-running-update-estimate-wizard-from-procurement-service-package-check-confidence-check-container-this-new-attribute-should-have-reduced-by-one.json")
            .then((data) => {
                this.data = data;
                CONTAINER_COLUMNS_BOQS = this.data.CONTAINER_COLUMNS.BOQS
                CONTAINERS_BOQSTRUCTURE = this.data.CONTAINERS.BOQSTRUCTURE
                CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
                CONTAINERS_CONFIDENCECHECK = this.data.CONTAINERS.CONFIDENCECHECK
                CONTAINER_COLUMNS_ESTIMATELINEITEM = this.data.CONTAINER_COLUMNS.ESTIMATELINEITEM
                MODAL_UPDATE_ESTIMATE_WIZARD = this.data.MODAL.UPDATE_ESTIMATE_WIZARD

                UPDATE_ESTIMATE_PARAMETER = {
                    [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_ESTIMATE_WIZARD
                }
                BOQ_PARAMETERS = {
                    [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC
                }
                BOQ_PARAMETERS1 = {
                    [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC1
                }
                BOQ_STRUCTURE_PARAMETERS = {
                    [commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
                    [app.GridCells.BRIEF_INFO_SMALL]: BOQ_STRUCTURE_DESC,
                    [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQSTRUCTURE.QUANTITY,
                    [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQSTRUCTURE.UNIT_RATE,
                    [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQSTRUCTURE.UOM
                }
                BOQ_STRUCTURE_PARAMETERS1 = {
                    [commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
                    [app.GridCells.BRIEF_INFO_SMALL]: BOQ_STRUCTURE_DESC1,
                    [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQSTRUCTURE.QUANTITY,
                    [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQSTRUCTURE.UNIT_RATE,
                    [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQSTRUCTURE.UOM
                }
                CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE
                CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE
                RESOURCE_PARAMETERS = {
                    [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY[0],
                    [app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE[0],
                    [app.GridCells.QUANTITY_SMALL]: CONTAINERS_RESOURCE.QUANTITY
                };
                RESOURCE_PARAMETERS1 = {
                    [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY[0],
                    [app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE[1],
                    [app.GridCells.QUANTITY_SMALL]: CONTAINERS_RESOURCE.QUANTITY
                };
                CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM
                GENERATE_LINE_ITEMS_PARAMETERS = {
                    [commonLocators.CommonLabels.HEADER_TEXT]: [commonLocators.CommonLabels.BASIC_SETTING],
                    [commonLocators.CommonLabels.SOURCE_LEADING_STRUCTURE]: BOQ_DESC
                }
                GENERATE_LINE_ITEMS_PARAMETERS1 = {
                    [commonLocators.CommonLabels.HEADER_TEXT]: [commonLocators.CommonLabels.BASIC_SETTING],
                    [commonLocators.CommonLabels.SOURCE_LEADING_STRUCTURE]: BOQ_DESC1
                }
                CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
                CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE
                ESTIMATE_PARAMETERS = {
                    [app.GridCells.CODE]: ESTIMATE_CODE,
                    [app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
                    [app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
                    [app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE,
                }
                CONTAINERS_PACKAGE = this.data.CONTAINERS.PACKAGE
                CREATE_BOQ_PACKAGE_PARAMETERS = {
                    [CommonLocators.CommonLabels.SELECT_ESTIMATE_SCOPE]: CONTAINERS_PACKAGE.ESTIMATE_SCOPE,
                    [CommonLocators.CommonKeys.RADIO_INDEX]: CONTAINERS_PACKAGE.INDEX,
                    [CommonLocators.CommonLabels.SELECT_GROUPING_STRUCTURE_TO_CREATE_PACKAGE]: CONTAINERS_PACKAGE.GROUPING_STRUCTURE,
                    [CommonLocators.CommonLabels.COLUMN_FILTER_SELECTION]: CommonLocators.CommonKeys.ALL,
                    [CommonLocators.CommonLabels.ASSIGNMENT_VALUE_GRID]: app.GridCells.BRIEF,
                    [CommonLocators.CommonLabels.ASSIGNMENT_VALUE]: BOQ_DESC,
                    [CommonLocators.CommonLabels.ASSIGNMENTS_PROCUREMENT_STRUCTURE]: CONTAINERS_PACKAGE.PROCUREMENT_STRUCTURE,
                    [CommonLocators.CommonLabels.QUANTITY_TRANSFER_FROM]: CONTAINERS_PACKAGE.QUANTITY_TRANSFER,
                    [CommonLocators.CommonLabels.CONSOLIDATE_TO_ONE_PACKAGE_FOR_ALL_SELECTED_CRITERIA]: commonLocators.CommonKeys.UNCHECK,
                    [CommonLocators.CommonLabels.CREATE_PACKAGE_FOR_LINE_ITEM_WITH_NO_RESOURCE]: commonLocators.CommonKeys.CHECK
                }
                CREATE_BOQ_PACKAGE_PARAMETERS1 = {
                    [CommonLocators.CommonLabels.SELECT_ESTIMATE_SCOPE]: CONTAINERS_PACKAGE.ESTIMATE_SCOPE,
                    [CommonLocators.CommonKeys.RADIO_INDEX]: CONTAINERS_PACKAGE.INDEX,
                    [CommonLocators.CommonLabels.SELECT_GROUPING_STRUCTURE_TO_CREATE_PACKAGE]: CONTAINERS_PACKAGE.GROUPING_STRUCTURE,
                    [CommonLocators.CommonLabels.COLUMN_FILTER_SELECTION]: CommonLocators.CommonKeys.ALL,
                    [CommonLocators.CommonLabels.ASSIGNMENT_VALUE_GRID]: app.GridCells.BRIEF,
                    [CommonLocators.CommonLabels.ASSIGNMENT_VALUE]: BOQ_DESC1,
                    [CommonLocators.CommonLabels.ASSIGNMENTS_PROCUREMENT_STRUCTURE]: CONTAINERS_PACKAGE.PROCUREMENT_STRUCTURE,
                    [CommonLocators.CommonLabels.QUANTITY_TRANSFER_FROM]: CONTAINERS_PACKAGE.QUANTITY_TRANSFER,
                    [CommonLocators.CommonLabels.CONSOLIDATE_TO_ONE_PACKAGE_FOR_ALL_SELECTED_CRITERIA]: commonLocators.CommonKeys.UNCHECK,
                    [CommonLocators.CommonLabels.CREATE_PACKAGE_FOR_LINE_ITEM_WITH_NO_RESOURCE]: commonLocators.CommonKeys.CHECK
                }
            }).then(() => {
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.setDefaultView(app.TabBar.PROJECT)
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        })
        _common.waitForLoaderToDisappear();
        _commonAPI.getAccessToken().then((result) => {
          cy.log(`Token Retrieved: ${result.token}`);
        });
      })
  });
  after(() => {
    cy.LOGOUT();
  });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });
    it("TC - Create first BoQ header and BoQ structure", function () {
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.openTab(app.TabBar.BOQS).then(() => {
            cy.wait(2000)
            _common.select_tabFromFooter(cnt.uuid.BOQS, app.FooterTab.BOQs, 2);
            _common.setup_gridLayout(cnt.uuid.BOQS, CONTAINER_COLUMNS_BOQS)
        });
        _common.clear_subContainerFilter(cnt.uuid.BOQS);
        _common.maximizeContainer(cnt.uuid.BOQS)
        _common.create_newRecord(cnt.uuid.BOQS);
        _boqPage.enterRecord_toCreateBoQ(cnt.uuid.BOQS, BOQ_PARAMETERS);
        cy.SAVE();
        _common.minimizeContainer(cnt.uuid.BOQS)
        _common.clickOn_toolbarButton(cnt.uuid.BOQS, btn.IconButtons.ICO_GO_TO);
        _common.openTab(app.TabBar.BOQSTRUCTURE).then(() => {
            _common.setup_gridLayout(cnt.uuid.BOQ_STRUCTURES, CONTAINER_COLUMNS_BOQ_STRUCTURE)
        });
        _boqPage.enterRecord_toCreateBoQStructure(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_PARAMETERS);
        cy.SAVE()
    });
    it("TC - Create estimate header", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, commonLocators.CommonLabels.PROJECT);
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATE)
            cy.wait(2000)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.create_newRecord(cnt.uuid.ESTIMATE);
        _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
        cy.SAVE()
    });
    it("TC - Generate first boq line item", function () {
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATELINEITEM)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATE_LINE_ITEMS);
        _estimatePage.generate_lineItems_fromWizard(GENERATE_LINE_ITEMS_PARAMETERS);
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        cy.SAVE();
    });
    it("TC - Create resources for line item", function () {
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
            _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE)
        });
        _common.maximizeContainer(cnt.uuid.RESOURCES)
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
        _common.create_newRecord(cnt.uuid.RESOURCES);
        _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
        cy.SAVE();
        _common.minimizeContainer(cnt.uuid.RESOURCES)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
    })
    it("TC - Create first BoQ package from the estimate", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_UPDATE_BOQ_PACKAGE)
        _package.enterRecord_toCreateBoQPackage_usingWizard_new(CREATE_BOQ_PACKAGE_PARAMETERS)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.FINISH)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButtonWithTitle(btn.ButtonText.GO_TO_PACKAGE)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create second BoQ header and BoQ structure", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, commonLocators.CommonLabels.PROJECT);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.openTab(app.TabBar.BOQS).then(() => {
            cy.wait(2000)
            _common.select_tabFromFooter(cnt.uuid.BOQS, app.FooterTab.BOQs, 2);
            _common.setup_gridLayout(cnt.uuid.BOQS, CONTAINER_COLUMNS_BOQS)
        });
        _common.clear_subContainerFilter(cnt.uuid.BOQS);
        _common.maximizeContainer(cnt.uuid.BOQS)
        _common.create_newRecord(cnt.uuid.BOQS);
        _boqPage.enterRecord_toCreateBoQ(cnt.uuid.BOQS, BOQ_PARAMETERS1);
        cy.SAVE();
        _common.minimizeContainer(cnt.uuid.BOQS)
        _common.clickOn_toolbarButton(cnt.uuid.BOQS, btn.IconButtons.ICO_GO_TO);
        _common.openTab(app.TabBar.BOQSTRUCTURE).then(() => {
            _common.setDefaultView(app.TabBar.BOQSTRUCTURE)
            cy.REFRESH_CONTAINER()
            _common.setup_gridLayout(cnt.uuid.BOQ_STRUCTURES, CONTAINER_COLUMNS_BOQ_STRUCTURE)
        });
        _boqPage.enterRecord_toCreateBoQStructure(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_PARAMETERS1);
        cy.SAVE()
    });
    it("TC - Update estimate header", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, commonLocators.CommonLabels.PROJECT);
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATE)
            cy.wait(2000)/*This wait is mandatory here, as tab shift takes time.*/
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.select_rowInContainer(cnt.uuid.ESTIMATE)
        cy.SAVE()
    });
    it("TC - Generate second boq line item", function () {
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATELINEITEM)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATE_LINE_ITEMS);
        _estimatePage.generate_lineItems_fromWizard(GENERATE_LINE_ITEMS_PARAMETERS1);
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        cy.SAVE();
    });
    it("TC - Create resources for line item", function () {
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
            _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE)
        });
        _common.maximizeContainer(cnt.uuid.RESOURCES)
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
        _common.create_newRecord(cnt.uuid.RESOURCES);
        _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS1);
        cy.SAVE();
        _common.minimizeContainer(cnt.uuid.RESOURCES)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
    })
    it("TC - Create second BoQ package from the estimate", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_UPDATE_BOQ_PACKAGE)
        _package.enterRecord_toCreateBoQPackage_usingWizard_new(CREATE_BOQ_PACKAGE_PARAMETERS1)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.FINISH)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButtonWithTitle(btn.ButtonText.GO_TO_PACKAGE)
        _common.waitForLoaderToDisappear()
    })
    it('TC- Verify confidence check container', function () {
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PACKAGEESTIMATELINEITEM, app.FooterTab.ESTIMATELINEITEM)
            _common.maximizeContainer(cnt.uuid.PACKAGEESTIMATELINEITEM)
        });
        _common.clear_subContainerFilter(cnt.uuid.PACKAGEESTIMATELINEITEM)
        _common.select_rowInContainer(cnt.uuid.PACKAGEESTIMATELINEITEM)
        cy.wait(500).then(() => {
            _common.select_rowInContainer(cnt.uuid.PACKAGEESTIMATELINEITEM)
            _common.clickOn_activeRowCell(cnt.uuid.PACKAGEESTIMATELINEITEM, app.GridCells.CODE)
            cy.wait(1000)//Need wait to right click on the popup option
            _common.goToButton_inActiveRow(cnt.uuid.PACKAGEESTIMATELINEITEM, app.GridCells.CODE, btn.IconButtons.ICO_GO_TO)
        })
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONFIDENCE_CHECK, app.FooterTab.CONFIDENCE_CHECK, 4);
            _common.maximizeContainer(cnt.uuid.CONFIDENCE_CHECK)
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.CONFIDENCE_CHECK, CONTAINERS_CONFIDENCECHECK.SERVICE_PACKAGE[0])
            _common.assert_forNumericValues(cnt.uuid.CONFIDENCE_CHECK, app.GridCells.COUNT, "2")
        });
    });
    it('TC- Update BoQ price and quantity in Package module', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, commonLocators.CommonKeys.PACKAGE);
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PACKAGE, app.FooterTab.PACKAGE)
        });
        _common.clear_subContainerFilter(cnt.uuid.PACKAGE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.select_rowInContainer(cnt.uuid.PACKAGE)
        _common.openTab(app.TabBar.BOQBASED).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.setup_gridLayout(cnt.uuid.BOQ_STRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE)
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_BOQ_STRUCTURE.price], cnt.uuid.BOQ_STRUCTURE)
        });
        _common.select_rowInContainer(cnt.uuid.BOQ_STRUCTURE)
        _common.edit_containerCell(cnt.uuid.BOQ_STRUCTURE, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQSTRUCTURE.NEW_QUANTITY)
        _common.edit_containerCell(cnt.uuid.BOQ_STRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQSTRUCTURE.NEW_UNIT_RATE)
        cy.SAVE();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
        _common.waitForLoaderToDisappear()
        _estimatePage.updateEstimate_fromWizard_byClass(UPDATE_ESTIMATE_PARAMETER);
        cy.wait(1000)/*This wait is mandatory here, as modal takes time to popup.*/
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });
    it('TC- Verify confidence check container', function () {
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PACKAGE, app.FooterTab.PACKAGE)
        });
        _common.clear_subContainerFilter(cnt.uuid.PACKAGE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.select_rowInContainer(cnt.uuid.PACKAGE)
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PACKAGEESTIMATELINEITEM, app.FooterTab.ESTIMATELINEITEM)
            _common.maximizeContainer(cnt.uuid.PACKAGEESTIMATELINEITEM)
        });
        _common.clear_subContainerFilter(cnt.uuid.PACKAGEESTIMATELINEITEM)
        _common.select_rowInContainer(cnt.uuid.PACKAGEESTIMATELINEITEM)
        cy.wait(500).then(() => {
            _common.select_rowInContainer(cnt.uuid.PACKAGEESTIMATELINEITEM)
            _common.clickOn_activeRowCell(cnt.uuid.PACKAGEESTIMATELINEITEM, app.GridCells.CODE)
             cy.wait(1000)//Need wait to right click on the popup option
            _common.goToButton_inActiveRow(cnt.uuid.PACKAGEESTIMATELINEITEM, app.GridCells.CODE, btn.IconButtons.ICO_GO_TO)
        })
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONFIDENCE_CHECK, app.FooterTab.CONFIDENCE_CHECK, 4);
            _common.maximizeContainer(cnt.uuid.CONFIDENCE_CHECK)
            _common.select_rowHasValue(cnt.uuid.CONFIDENCE_CHECK, CONTAINERS_CONFIDENCECHECK.SERVICE_PACKAGE[1])
            _common.assert_forNumericValues(cnt.uuid.CONFIDENCE_CHECK, app.GridCells.COUNT, "1")
        });
    });
})