﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Resource.Requisition.BusinessComponents;

namespace RIB.Visual.Resource.Requisition.ServiceFacade.WebApi
{


    /// <summary>
    /// There are no comments for RequisitionGenReqTypVEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("RES_GEN_REQ_TYP_V")]
    public partial class RequisitionGenReqTypVDto : RIB.Visual.Platform.Core.ITypedDto<RequisitionGenReqTypVEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class RequisitionGenReqTypVDto.
        /// </summary>
        public RequisitionGenReqTypVDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class RequisitionGenReqTypVDto.
        /// </summary>
        /// <param name="entity">the instance of class RequisitionGenReqTypVEntity</param>
        public RequisitionGenReqTypVDto(RequisitionGenReqTypVEntity entity)
        {
            Id = entity.Id;
            ResRequisitionFk = entity.ResRequisitionFk;
            ResTypeFk = entity.ResTypeFk;
            Duration = entity.Duration;
            BasUomdayFk = entity.BasUomdayFk;
            ResTyperequestedFk = entity.ResTyperequestedFk;
            LgmDispatcherGroupFk = entity.LgmDispatcherGroupFk;
            IsPlantRequested = entity.IsPlantRequested;
            IsDriverRequested = entity.IsDriverRequested;
            IsCraneRequested = entity.IsCraneRequested;
            IsTruckRequested = entity.IsTruckRequested;
            SkillOfRequestedTypeFk = entity.SkillOfRequestedTypeFk;
            Isrequestedentireperiod = entity.Isrequestedentireperiod;
            Necessaryoperators = entity.Necessaryoperators;
            CalCalendarFk = entity.CalCalendarFk;
            ResSkillResTypeFk = entity.ResSkillResTypeFk;
            ResSkillFk = entity.ResSkillFk;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for ResRequisitionFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_REQUISITION_FK", TypeName = "int", Order = 1)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ResRequisitionFk { get; set; }
    
        /// <summary>
        /// There are no comments for ResTypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_TYPE_FK", TypeName = "int", Order = 2)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ResTypeFk { get; set; }
    
        /// <summary>
        /// There are no comments for Duration in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DURATION", TypeName = "int", Order = 3)]
        public int? Duration { get; set; }
    
        /// <summary>
        /// There are no comments for BasUomdayFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOMDAY_FK", TypeName = "int", Order = 4)]
        public int? BasUomdayFk { get; set; }
    
        /// <summary>
        /// There are no comments for ResTyperequestedFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_TYPEREQUESTED_FK", TypeName = "int", Order = 5)]
        public int? ResTyperequestedFk { get; set; }
    
        /// <summary>
        /// There are no comments for LgmDispatcherGroupFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LGM_DISPATCHER_GROUP_FK", TypeName = "int", Order = 6)]
        public int? LgmDispatcherGroupFk { get; set; }
    
        /// <summary>
        /// There are no comments for IsPlantRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISPLANTREQUESTED", TypeName = "bit", Order = 7)]
        public bool? IsPlantRequested { get; set; }
    
        /// <summary>
        /// There are no comments for IsDriverRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDRIVERREQUESTED", TypeName = "bit", Order = 8)]
        public bool? IsDriverRequested { get; set; }
    
        /// <summary>
        /// There are no comments for IsCraneRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISCRANEREQUESTED", TypeName = "bit", Order = 9)]
        public bool? IsCraneRequested { get; set; }
    
        /// <summary>
        /// There are no comments for IsTruckRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISTRUCKREQUESTED", TypeName = "bit", Order = 10)]
        public bool? IsTruckRequested { get; set; }
    
        /// <summary>
        /// There are no comments for SkillOfRequestedTypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_SKILL_OFREQUESTED_FK", TypeName = "int", Order = 11)]
        public int? SkillOfRequestedTypeFk { get; set; }
    
        /// <summary>
        /// There are no comments for Isrequestedentireperiod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREQUESTEDENTIREPERIOD", TypeName = "bit", Order = 12)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isrequestedentireperiod { get; set; }
    
        /// <summary>
        /// There are no comments for Necessaryoperators in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("NECESSARYOPERATORS", TypeName = "int", Order = 13)]
        public int Necessaryoperators { get; set; }
    
        /// <summary>
        /// There are no comments for CalCalendarFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CAL_CALENDAR_FK", TypeName = "int", Order = 14)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CalCalendarFk { get; set; }
    
        /// <summary>
        /// There are no comments for ResSkillResTypeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_SKILL_RES_TYPE_FK", TypeName = "int", Order = 15)]
        public int? ResSkillResTypeFk { get; set; }
    
        /// <summary>
        /// There are no comments for ResSkillFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RES_SKILL_FK", TypeName = "int", Order = 16)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ResSkillFk { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(RequisitionGenReqTypVEntity); }
        }

        /// <summary>
        /// Copy the current RequisitionGenReqTypVDto instance to a new RequisitionGenReqTypVEntity instance.
        /// </summary>
        /// <returns>a new instance of class RequisitionGenReqTypVEntity</returns>
        public RequisitionGenReqTypVEntity Copy()
        {
          var entity = new RequisitionGenReqTypVEntity();

          entity.Id = this.Id;
          entity.ResRequisitionFk = this.ResRequisitionFk;
          entity.ResTypeFk = this.ResTypeFk;
          entity.Duration = this.Duration;
          entity.BasUomdayFk = this.BasUomdayFk;
          entity.ResTyperequestedFk = this.ResTyperequestedFk;
          entity.LgmDispatcherGroupFk = this.LgmDispatcherGroupFk;
          entity.IsPlantRequested = this.IsPlantRequested;
          entity.IsDriverRequested = this.IsDriverRequested;
          entity.IsCraneRequested = this.IsCraneRequested;
          entity.IsTruckRequested = this.IsTruckRequested;
          entity.SkillOfRequestedTypeFk = this.SkillOfRequestedTypeFk;
          entity.Isrequestedentireperiod = this.Isrequestedentireperiod;
          entity.Necessaryoperators = this.Necessaryoperators;
          entity.CalCalendarFk = this.CalCalendarFk;
          entity.ResSkillResTypeFk = this.ResSkillResTypeFk;
          entity.ResSkillFk = this.ResSkillFk;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(RequisitionGenReqTypVEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(RequisitionGenReqTypVEntity entity);
    }

}
