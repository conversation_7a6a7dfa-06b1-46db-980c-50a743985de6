/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { QtoShareDetailLayoutService } from '@libs/qto/shared';
import { QtoShareBoqType } from '@libs/qto/shared';

/**
 * Sales Billing QTO Detail Layout Service
 * Provides layout configuration for billing QTO detail entities
 */
@Injectable({
	providedIn: 'root',
})
export class SalesBillingQtoDetailLayoutService extends QtoShareDetailLayoutService {
	public constructor() {
		super(QtoShareBoqType.BillingBoq);
	}

	// Add any billing-specific layout customizations here if needed
}
