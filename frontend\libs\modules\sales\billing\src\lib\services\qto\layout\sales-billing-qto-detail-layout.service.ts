/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { ILayoutConfiguration, FieldType } from '@libs/ui/common';
import { ISalesBillingQtoDetailEntity } from '../../../model/entities/sales-billing-qto-detail-entity.interface';
import { BasicsSharedLookupOverloadProvider, BasicsSharedCustomizeLookupOverloadProvider } from '@libs/basics/shared';
import { ProjectSharedLookupOverloadProvider } from '@libs/project/shared';

/**
 * Sales Billing QTO Detail Layout Service
 * Provides layout configuration for billing QTO detail entities
 */
@Injectable({
	providedIn: 'root',
})
export class SalesBillingQtoDetailLayoutService {
	/**
	 * Generate layout config for billing QTO detail
	 */
	public async generateLayout(): Promise<ILayoutConfiguration<ISalesBillingQtoDetailEntity>> {
		return {
			groups: [
				{
					gid: 'basicData',
					title: {
						text: 'Basic Data',
						key: 'cloud.common.entityProperties',
					},
					attributes: [
						'PageNumber',
						'LineReference',
						'LineIndex',
						'QtoDetailStatusFk',
						'BoqItemFk',
						'BasUomFk',
						'PrjLocationFk',
						'QtoLineTypeFk',
					],
				},
				{
					gid: 'quantityData',
					title: {
						text: 'Quantity Data',
						key: 'qto.main.quantityData',
					},
					attributes: [
						'IsBQ',
						'IsIQ',
						'Value1',
						'Value2',
						'Value3',
						'Value4',
						'Value5',
						'QtoFormula',
					],
				},
				{
					gid: 'additionalInfo',
					title: {
						text: 'Additional Information',
						key: 'qto.main.additionalInfo',
					},
					attributes: [
						'RemarkText',
						'LineText',
						'PerformedDate',
						'IsReadonly',
					],
				},
			],
			labels: {
				...prefixAllTranslationKeys('qto.main.', {
					PageNumber: { key: 'pageNumber', text: 'Page Number' },
					LineReference: { key: 'lineReference', text: 'Line Reference' },
					LineIndex: { key: 'lineIndex', text: 'Line Index' },
					IsBQ: { key: 'isBQ', text: 'Is BQ' },
					IsIQ: { key: 'isIQ', text: 'Is IQ' },
					Value1: { key: 'value1', text: 'Value 1' },
					Value2: { key: 'value2', text: 'Value 2' },
					Value3: { key: 'value3', text: 'Value 3' },
					Value4: { key: 'value4', text: 'Value 4' },
					Value5: { key: 'value5', text: 'Value 5' },
					RemarkText: { key: 'remarkText', text: 'Remark' },
					LineText: { key: 'lineText', text: 'Line Text' },
					PerformedDate: { key: 'performedDate', text: 'Performed Date' },
				}),
				...prefixAllTranslationKeys('cloud.common.', {
					QtoDetailStatusFk: { key: 'entityStatus', text: 'Status' },
					BoqItemFk: { key: 'entityBoqItem', text: 'BOQ Item' },
					BasUomFk: { key: 'entityUoM', text: 'Unit of Measure' },
					PrjLocationFk: { key: 'entityLocation', text: 'Location' },
					QtoLineTypeFk: { key: 'entityLineType', text: 'Line Type' },
					IsReadonly: { key: 'entityReadonly', text: 'Read Only' },
				}),
			},
			overloads: {
				PageNumber: { readonly: true },
				LineReference: { readonly: true },
				LineIndex: { readonly: true },
				BasUomFk: BasicsSharedLookupOverloadProvider.provideUoMLookupOverload(true),
				PrjLocationFk: ProjectSharedLookupOverloadProvider.provideProjectLocationLookupOverload(true),
				QtoDetailStatusFk: BasicsSharedCustomizeLookupOverloadProvider.provideQtoDetailStatusReadonlyLookupOverload(),
				// TODO: Add BOQ Item lookup overload when available
				// BoqItemFk: BillingSharedLookupOverloadProvider.provideBoqItemLookupOverload(true),
				// TODO: Add QTO Line Type lookup overload when available
				// QtoLineTypeFk: BasicsSharedLookupOverloadProvider.provideQtoLineTypeLookupOverload(true),
				RemarkText: {
					type: FieldType.Description,
					maxLength: 500,
				},
				LineText: {
					type: FieldType.Description,
					maxLength: 500,
				},
				PerformedDate: {
					type: FieldType.Date,
				},
				IsReadonly: { readonly: true },
				IsBQ: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
				IsIQ: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
				Value1: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
				Value2: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
				Value3: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
				Value4: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
				Value5: {
					type: FieldType.Decimal,
					formatterOptions: {
						decimalPlaces: 6,
					},
				},
			},
		};
	}
}
