import { sidebar, commonLocators, app, cnt, btn, tile } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _businessPartnerPage, _validate, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const companyCode = Cypress.env('childCompanyName').match(/\d+/)?.[0]



let CONTAINERS_BUSINESS_PARTNER, CONTAINERS_REGISTERED_FOR_COMPANY;
let CONTAINER_COLUMNS_BUSINESS_PARTNER;
let CONTAINERS_ERROR_MESSAGE
let BUSINESS_PARTNER_PARAMETER: DataCells

describe("PCM- 4.356 | BPD BP Registered for Company container Check adding record", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture('pcm/bpd-4.356-bpd-bp-registered-for-company-container-check-adding-record.json')
            .then((data) => {
                this.data = data;
                CONTAINERS_BUSINESS_PARTNER = this.data.CONTAINERS.BUSINESS_PARTNER
                CONTAINER_COLUMNS_BUSINESS_PARTNER = this.data.CONTAINER_COLUMNS.BUSINESS_PARTNER
                CONTAINERS_REGISTERED_FOR_COMPANY = this.data.CONTAINERS.REGISTERED_FOR_COMPANY
                CONTAINERS_ERROR_MESSAGE = this.data.CONTAINERS.ERROR_MESSAGE
            }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear()
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            })
    });
    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it("TC - API: Create a business partner in business partner module", function () {

        BUSINESS_PARTNER_PARAMETER = {
            [commonLocators.CommonLabels.STREET]: CONTAINERS_BUSINESS_PARTNER.STREET_NAME,
            [commonLocators.CommonLabels.CITY]: CONTAINERS_BUSINESS_PARTNER.CITY_NAME,
            [commonLocators.CommonLabels.ZIP_CODE]: CONTAINERS_BUSINESS_PARTNER.ZIP_CODE,
            [commonLocators.CommonLabels.COUNTY]: CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME,
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.BUSINESS_PARTNER);
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.setDefaultView(app.TabBar.BUSINESS_PARTNERS)
            _common.select_tabFromFooter(cnt.uuid.BUSINESS_PARTNERS, app.FooterTab.BUSINESS_PARTNER, 0);
            _common.setup_gridLayout(cnt.uuid.BUSINESS_PARTNERS, CONTAINER_COLUMNS_BUSINESS_PARTNER)
        });
        _common.maximizeContainer(cnt.uuid.BUSINESS_PARTNERS)
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _commonAPI.createBusinessPartner(BUSINESS_PARTNER_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.BUSINESS_PARTNERS)
    })
    it("TC - Check whether Company field is default selected to the logined company", function () {
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.FooterTab.REGISTERED_FOR_COMPANY, 1);
        });

        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("API_BP_NAME_1"))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.BUSINESS_PARTNERS, Cypress.env("API_BP_NAME_1"))
        _common.clear_subContainerFilter(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.select_rowHasValue(cnt.uuid.BP_REGISTERED_FOR_COMPANY, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[0])
        _common.delete_recordFromContainer(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.clear_subContainerFilter(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.assert_cellData_insideActiveRow(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_FK, companyCode)
        cy.SAVE()
        cy.REFRESH_CONTAINER()
        _common.select_rowHasValue(cnt.uuid.BP_REGISTERED_FOR_COMPANY, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[0])
        _common.assert_cellData_insideActiveRow(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_FK, companyCode)

    })

    it("TC - Verify assertions for Unique attribute ", function () {
        _common.create_newRecord(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
          _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.wait(2000)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.BP_REGISTERED_FOR_COMPANY, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[0], 0)
        _common.assert_cellData_insideActiveRow(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_FK, companyCode)

        _common.select_rowHasValue_onIndexBased(cnt.uuid.BP_REGISTERED_FOR_COMPANY, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[0], 2)
        _common.assert_cellData_insideActiveRow(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_FK, companyCode)
        _validate.validate_textIn_invalidCell(CONTAINERS_ERROR_MESSAGE.MESSAGE)
        cy.SAVE()
                _common.clickOn_validationModalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.wait(2000)

        _common.clear_subContainerFilter(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Add record in registered for company container and Check whether Company field should be unique error messgae", function () {
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.FooterTab.REGISTERED_FOR_COMPANY, 1);
        });
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, app.FooterTab.REGISTERED_FOR_COMPANY_DETAIL, 2);
        });
        _common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.FooterTab.REGISTERED_FOR_COMPANY, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("API_BP_NAME_1"))
        _common.search_inSubContainer(cnt.uuid.BUSINESS_PARTNERS, Cypress.env("API_BP_NAME_1"))
        _common.select_rowHasValue(cnt.uuid.BUSINESS_PARTNERS, Cypress.env("API_BP_NAME_1"))

        _common.create_newRecord(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, app.FooterTab.REGISTERED_FOR_COMPANY_DETAIL, 2);
        _common.assert_errorMessage_underContainerForm(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, CONTAINERS_ERROR_MESSAGE.MESSAGE)
        _common.delete_recordFromContainer(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.FooterTab.REGISTERED_FOR_COMPANY, 1);
        _common.create_newRecord(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.edit_dropdownCellWithInput(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[1])
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.create_newRecord(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.edit_dropdownCellWithInput(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[1])
        _common.edit_dropdownCellWithInput(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.COMPANY_RESPONSIBLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[1])
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, app.FooterTab.REGISTERED_FOR_COMPANY_DETAIL, 2);
        _common.assert_errorMessage_underContainerForm(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, CONTAINERS_ERROR_MESSAGE.MESSAGE)
        _common.delete_recordFromContainer(cnt.uuid.BP_REGISTERED_FOR_COMPANY)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
    it("TC - Check Is Active field is default ticked", function () {
        _common.select_rowHasValue(cnt.uuid.BP_REGISTERED_FOR_COMPANY, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[0])
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
        _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, app.FooterTab.REGISTERED_FOR_COMPANY_DETAIL, 2);
        _validate.validate_checkboxFrom_containerForm(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, CommonLocators.CommonLabels.IS_ACTIVE, commonLocators.CommonKeys.CHECK)


        _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.FooterTab.REGISTERED_FOR_COMPANY, 1);
        _common.select_rowHasValue(cnt.uuid.BP_REGISTERED_FOR_COMPANY, CONTAINERS_REGISTERED_FOR_COMPANY.COMPANY[1])
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.BP_REGISTERED_FOR_COMPANY, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
        _common.select_tabFromFooter(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, app.FooterTab.REGISTERED_FOR_COMPANY_DETAIL, 2);
        _validate.validate_checkboxFrom_containerForm(cnt.uuid.BP_REGISTERED_FOR_COMPANY_DETAILS, CommonLocators.CommonLabels.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
    })
})
