import { createLookup, FieldType, LookupSimpleEntity, UiCommonLookupTypeDataService } from '@libs/ui/common';
import { Injectable } from '@angular/core';
import { IBusinessPartnerSearchMainEntity, IContactLookupEntity } from '@libs/businesspartner/interfaces';
import { BusinesspartnerSharedContactRoleLookupService } from './businesspartner-contact-role-lookup.service';
import { BusinessPartnerLookupService } from './businesspartner-lookup.service';
import { BusinesspartnerSharedContactTimelinessLookService } from './businesspartner-contact-timeliness-lookup.service';
import { BusinesspartnerSharedContactOriginLookupService } from './businesspartner-contact-origin-lookup.service';
import { BusinesspartnerSharedContactAbcLookupService } from './contact-abc-lookup.service';
import { BasicsCompanyLookupService, BasicsSharedClerkLookupService, BasicsSharedCountryLookupService, BasicsSharedTitleLookupService } from '@libs/basics/shared';
import { IBasicsClerkEntity, IBasicsCountryEntity, IBasicsCustomizeTitleEntity, ICompanyEntity } from '@libs/basics/interfaces';

@Injectable({
	providedIn: 'root',
})
export class BusinesspartnerSharedContactLookupService<TEntity extends object> extends UiCommonLookupTypeDataService<IContactLookupEntity, TEntity> {
	/**
	 * constructor
	 */
	public constructor() {
		super('contact', {
			displayMember: 'FullName',
			uuid: 'e008134d1ba941f1ac9af03db4548fd5',
			valueMember: 'Id',
			gridConfig: {
				columns: [
					{
						id: 'FirstName',
						model: 'FirstName',
						type: FieldType.Description,
						label: { text: 'FirstName', key: 'businesspartner.main.firstName' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'FamilyName',
						model: 'FamilyName',
						type: FieldType.Description,
						label: { text: 'FamilyName', key: 'businesspartner.main.familyName' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Title',
						model: 'Title',
						type: FieldType.Description,
						label: { text: 'Street', key: 'businesspartner.main.title' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Description',
						model: 'Description',
						type: FieldType.Description,
						label: { text: 'Description', key: 'cloud.common.entityCity' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'AddressLine',
						model: 'AddressLine',
						type: FieldType.Description,
						label: { text: 'Address', key: 'cloud.common.entityAddress' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Telephone1',
						model: 'Telephone1',
						type: FieldType.Description,
						label: { text: 'Telephone', key: 'businesspartner.main.telephoneNumber' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Telephone2',
						model: 'Telephone2',
						type: FieldType.Description,
						label: { text: 'Other Tel.', key: 'businesspartner.main.telephoneNumber2' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Telefax',
						model: 'Telefax',
						type: FieldType.Description,
						label: { text: 'Telefax', key: 'businesspartner.main.telephoneFax' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Mobile',
						model: 'Mobile',
						type: FieldType.Description,
						label: { text: 'Mobile', key: 'businesspartner.main.mobileNumber' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Internet',
						model: 'Internet',
						type: FieldType.Description,
						label: { text: 'Internet', key: 'businesspartner.main.internet' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Email',
						model: 'Email',
						type: FieldType.Email,
						label: { text: 'Email', key: 'businesspartner.main.email' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'ContactRoleFk',
						model: 'ContactRoleFk',
						type: FieldType.Lookup,
						label: { text: 'Role', key: 'businesspartner.main.role' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, LookupSimpleEntity>({
							dataServiceToken: BusinesspartnerSharedContactRoleLookupService,
							displayMember: 'Description',
						}),
					},
					{
						id: 'Remark',
						model: 'Remark',
						type: FieldType.Description,
						label: { text: 'Remark', key: 'cloud.common.entityRemark' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'BusinessPartnerFk',
						model: 'BusinessPartnerFk',
						type: FieldType.Lookup,
						label: { text: 'Business Partner', key: 'cloud.common.entityBusinessPartner' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, IBusinessPartnerSearchMainEntity>({
							dataServiceToken: BusinessPartnerLookupService,
							displayMember: 'Code',
						}),
					},
					{
						id: 'BpdContactTimelinessFk',
						model: 'BpdContactTimelinessFk',
						type: FieldType.Lookup,
						label: { text: 'Contact Timeliness', key: 'businesspartner.main.timeliness' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, LookupSimpleEntity>({
							dataServiceToken: BusinesspartnerSharedContactTimelinessLookService,
							displayMember: 'Description',
						}),
					},
					{
						id: 'BpdContactOriginFk',
						model: 'BpdContactOriginFk',
						type: FieldType.Lookup,
						label: { text: 'Origin', key: 'businesspartner.main.origin' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, LookupSimpleEntity>({
							dataServiceToken: BusinesspartnerSharedContactOriginLookupService,
							displayMember: 'Description',
						}),
					},
					{
						id: 'BpdContactAbcFk',
						model: 'BpdContactAbcFk',
						type: FieldType.Lookup,
						label: { text: 'ABC Classification', key: 'businesspartner.main.customerAbc' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, LookupSimpleEntity>({
							dataServiceToken: BusinesspartnerSharedContactAbcLookupService,
							displayMember: 'Description',
						}),
					},
					{
						id: 'BasTitleFk',
						model: 'BasTitleFk',
						type: FieldType.Lookup,
						label: { text: 'Opening', key: 'businesspartner.contact.titleName' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, IBasicsCustomizeTitleEntity>({
							dataServiceToken: BasicsSharedTitleLookupService,
							displayMember: 'Description',
						}),
					},
					{
						id: 'Initials',
						model: 'Initials',
						type: FieldType.Description,
						label: { text: 'Initials', key: 'businesspartner.main.initials' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Pronunciation',
						model: 'Pronunciation',
						type: FieldType.Description,
						label: { text: 'Pronunciation', key: 'businesspartner.main.pronunciation' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'CompanyFk',
						model: 'CompanyFk',
						type: FieldType.Lookup,
						label: { text: 'Company', key: 'cloud.common.entityCompany' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, ICompanyEntity>({
							dataServiceToken: BasicsCompanyLookupService,
							displayMember: 'Code',
						}),
					},
					{
						id: 'CompanyName',
						model: 'CompanyFk',
						type: FieldType.Lookup,
						label: { text: 'Company Name', key: 'cloud.common.entityCompanyName' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, ICompanyEntity>({
							dataServiceToken: BasicsCompanyLookupService,
							displayMember: 'CompanyName',
						}),
					},
					{
						id: 'BasClerkResponsibleFk',
						model: 'BasClerkResponsibleFk',
						type: FieldType.Lookup,
						label: { text: 'Responsible', key: 'cloud.common.entityResponsible' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, IBasicsClerkEntity>({
							dataServiceToken: BasicsSharedClerkLookupService,
							displayMember: 'Code',
						}),
					},
					{
						id: 'BasClerkResponsibleDescription',
						model: 'BasClerkResponsibleFk',
						type: FieldType.Lookup,
						label: { text: 'Responsible Name', key: 'cloud.common.entityResponsibleName' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, IBasicsClerkEntity>({
							dataServiceToken: BasicsSharedClerkLookupService,
							displayMember: 'Description',
						}),
					},
					{
						id: 'Birthdate',
						model: 'Birthdate',
						type: FieldType.Date,
						label: { text: 'Birthdate', key: 'businesspartner.main.birthDate' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'NickName',
						model: 'NickName',
						type: FieldType.Description,
						label: { text: 'NickName', key: 'businesspartner.main.nickname' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'PartnerName',
						model: 'PartnerName',
						type: FieldType.Description,
						label: { text: 'PartnerName', key: 'businesspartner.main.partnerName' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Children',
						model: 'Children',
						type: FieldType.Description,
						label: { text: 'Children', key: 'businesspartner.main.children' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'IsDefault',
						model: 'IsDefault',
						type: FieldType.Boolean,
						label: { text: 'IsDefault', key: 'cloud.common.entityIsDefault' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'Provider',
						model: 'Provider',
						type: FieldType.Description,
						label: { text: 'Provider', key: 'businesspartner.main.provider' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'ProviderId',
						model: 'ProviderId',
						type: FieldType.Integer,
						label: { text: 'ProviderId', key: 'businesspartner.main.providerId' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'ProviderFamilyName',
						model: 'ProviderFamilyName',
						type: FieldType.Description,
						label: { text: 'ProviderFamilyName', key: 'businesspartner.main.providerFamilyName' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'ProviderEmail',
						model: 'ProviderEmail',
						type: FieldType.Email,
						label: { text: 'ProviderEmail', key: 'businesspartner.main.providerEmail' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'ProviderAddress',
						model: 'ProviderAddress',
						type: FieldType.Description,
						label: { text: 'ProviderAddress', key: 'businesspartner.main.providerAddress' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'ProviderComment',
						model: 'ProviderComment',
						type: FieldType.Description,
						label: { text: 'Comment', key: 'cloud.common.entityCommentText' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'PortalUserGroupName',
						model: 'PortalUserGroupName',
						type: FieldType.Description,
						label: { text: 'PortalUserGroupName', key: 'businesspartner.main.portalAccessGroup' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'LogonName',
						model: 'LogonName',
						type: FieldType.Description,
						label: { text: 'LogonName', key: 'cloud.common.User_LogonName' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'IdentityProviderName',
						model: 'IdentityProviderName',
						type: FieldType.Description,
						label: { text: 'IdentityProviderName', key: 'businesspartner.main.identityProviderName' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'LastLogin',
						model: 'LastLogin',
						type: FieldType.Description,
						label: { text: 'LastLogin', key: 'businesspartner.main.lastLogin' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'State',
						model: 'State',
						type: FieldType.Integer,
						label: { text: 'Statement', key: 'businesspartner.main.state' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'SetInactiveDate',
						model: 'SetInactiveDate',
						type: FieldType.Date,
						label: { text: 'SetInactiveDate', key: 'businesspartner.main.setInactiveDate' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'IsLive',
						model: 'IsLive',
						type: FieldType.Boolean,
						label: { text: 'Is Live', key: 'cloud.common.entityIsLive' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'EmailPrivat',
						model: 'EmailPrivat',
						type: FieldType.Email,
						label: { text: 'EmailPrivat', key: 'cloud.common.emailPrivate' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'CountryFk',
						model: 'CountryFk',
						type: FieldType.Lookup,
						label: { text: 'Responsible Name', key: 'cloud.common.entityCountry' },
						width: 120,
						sortable: true,
						visible: true,
						lookupOptions: createLookup<IContactLookupEntity, IBasicsCountryEntity>({
							dataServiceToken: BasicsSharedCountryLookupService,
							displayMember: 'DescriptionInfo.Translated',
						}),
					},
					{
						id: 'PortalUserGroupName',
						model: 'PortalUserGroupName',
						type: FieldType.Description,
						label: { text: 'PortalUserGroupName', key: 'businesspartner.main.portalAccessGroup' },
						width: 100,
						sortable: true,
						visible: true,
					},
					{
						id: 'AddressDescriptor',
						model: 'AddressDescriptor',
						type: FieldType.Description,
						label: { text: 'Private address', key: 'businesspartner.main.contactAddress' },
						width: 120,
						sortable: true,
						visible: true,
					},
					{
						id: 'PrivateTelephone',
						model: 'PrivateTelephone',
						type: FieldType.Description,
						label: { text: 'Private Telephone', key: 'businesspartner.main.contactTelephoneNumber' },
						width: 120,
						sortable: true,
						visible: true,
					},
				],
			},
			dialogOptions: {
				headerText: {
					text: 'Assign Contact',
					key: 'cloud.common.dialogTitleContact',
				},
			},
			showDialog: true,
			//todo return new BasicsLookupdataLookupDirectiveDefinition('lookup-edit', defaults,{
		});
	}
}
