/**
 * Copyright (c) RIB Software GmbH
 */

(function () {
	'use strict';
	angular.module('basics.customize').factory('basicsCustomizeUserLabelAssignmentConfigurationService', ['platformDialogService', '$http', 'globals', '$translate', '$timeout', 'platformGridAPI',
		function (platformDialogService, $http, globals, $translate, $timeout, platformGridAPI) {
			let service = {};

			function getUserLabelAssignmentMainDialogOptions(newEntity) {
				return {
					headerText$tr$: 'basics.customize.userLabelAssignment.dialogTitle.Create',
					bodyTemplateUrl: globals.appBaseUrl + 'basics.customize/templates/basics-customize-user-label-assignment.html',
					height: '200px',
					width: '800px',
					resizeable: true,
					showCancelButton: true,
					showOkButton: true,
					value: {
						selectedItem: newEntity,
						defaultForm: null,
					},
					buttons: []
				};
			}

			let requiredFieldValidator = function (entity, value) {
				if (value === '' || value === null) {
					return {apply: true, valid: false, error: 'This field is required!!'};
				}

				return {apply: true, valid: true, error: ''};
			};

			service.refreshGrid = function () {
				$timeout(function () {
					const gridId = '3a51bf834b8649069172d23ec1ba35e2';
					platformGridAPI.grids.refresh(gridId);
				});
			};

			service.showUserLabelAssignmentConfigurationDialog = function (selectedItem) {
				const dialogConfig = getUserLabelAssignmentMainDialogOptions(selectedItem);
				platformDialogService.showDialog(dialogConfig);
			};

			service.saveNewLabel = async function (dataToSave) {
				const expectedLabelKey = dataToSave.Entity && dataToSave.Field
					? dataToSave.Entity + '.' + dataToSave.Field
					: dataToSave.Entity || '';

				if (dataToSave.LabelKey !== expectedLabelKey) {
					throw new Error($translate.instant('basics.customize.userLabelAssignment.messages.keyMismatchError', {expected: expectedLabelKey, provided: dataToSave.LabelKey}));
				}

				const payload = {
					LabelKey: dataToSave.Entity + '.' + dataToSave.Field,
					Label: dataToSave.Label
				};
				return $http.post(globals.webApiBaseUrl + 'basics/customize/userlabel/savenew', payload);
			};

			service.getDefaultForm = function () {
				return {
					fid: 'userLabel.form',
					version: '1.0.0',
					showGrouping: false,
					groups: [
						{
							gid: 'main',
							header$tr$: 'basics.customize.userLabelAssignment.groupHeader.main',
							isOpen: true
						}
					],
					rows: [
						{
							gid: 'main',
							rid: 'labelKey',
							label$tr$: 'basics.customize.userLabelAssignment.rowLabel.labelKey',
							type: 'text',
							model: 'LabelKey',
							placeholder: $translate.instant('basics.customize.userLabelAssignment.rowLabel.labelKeyPlaceholder'),
							validator: requiredFieldValidator
						},
						{
							gid: 'main',
							rid: 'label',
							label$tr$: 'basics.customize.userLabelAssignment.rowLabel.label',
							type: 'text',
							model: 'Label',
							placeholder: $translate.instant('basics.customize.userLabelAssignment.rowLabel.labelPlaceholder'),
							validator: requiredFieldValidator
						}
					]
				};
			};

			return service;
		}
	]);
})();