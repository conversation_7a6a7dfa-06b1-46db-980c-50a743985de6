using RIB.Visual.Basics.Common.BusinessComponents.Entities;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
using RIB.Visual.Platform.Core;
using RIB.Visual.Procurement.Common.BusinessComponents;
using System;
using System.Linq;
using System.Net.Http;
using System.Web.Http;
using System.Collections.Generic;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Final;

namespace RIB.Visual.Procurement.Common.ServiceFacade.WebApi
{
	/// <summary>
	/// Base class of WebApi service facades
	/// </summary>
	[RoutePrefix("procurement/common/prcdocument")]
	public class ProcurementRequisitionPrcDocumentController : EntityUpdateController<PrcDocumentLogic, PrcDocumentDto, PrcDocumentEntity, IdentificationData>
	{
		/// <summary>
		/// Post api/prcdocument
		/// </summary>
		/// <param name="containerSpec"></param>
		/// <returns></returns>
		[Route("listbyparent")]
		[HttpPost]
		public Dictionary<string,object> GetList(IdentificationData containerSpec)
		{
			if (containerSpec.PKey1 == null)
			{
				return null;
			}

			var prcHeaderId = containerSpec.PKey1.Value;
			var results = new PrcDocumentLogic().GetListWithPermission(x => x.PrcHeaderFk == prcHeaderId, true);
			var main = results.Select(item => new PrcDocumentDto(item)).ToList();
			var defaultDocumentType = new DocumentTypeLogic().GetDefault();
			var documentTypeIds = new List<int?>();
			if (defaultDocumentType != null)
			{
				documentTypeIds.Add(defaultDocumentType.Id);
			}
			var data = main.CollectLookups<PrcDocumentDto>(collector => collector.Add("DocumentType", documentTypeIds, x => x.DocumentTypeFk));
			return data;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="PrcHeaderFks"></param>
		/// <returns></returns>
		[Route("listbyids")]
		[HttpPost]
		public IEnumerable<PrcDocumentDto> GetListByIds(IEnumerable<int> PrcHeaderFks)
		{
			var results = new PrcDocumentLogic().GetListWithPermission(PrcHeaderFks);
			return results.Select(item => new PrcDocumentDto(item)).ToList();
		}

		/// <summary>
		/// Returns the JSON schema description of the PrcDocumentEntity class
		/// </summary>
		/// <returns></returns>
		[Route("schema")]
		[HttpGet]
		public HttpResponseMessage GetSchema()
		{
			return GetJsonSchema(typeof(PrcDocumentEntity));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[Route("createforuploadfile")]
		[HttpPost]
		public CreateDocsForUploadResultDto<PrcDocumentDto> CreateForUploadFile(CreateDocsForUploadRequest request)
		{
			var documentLogic = request.PermissionGuid != null ? new PrcDocumentLogic(request.PermissionGuid) : Logic;
			var uploadResult = documentLogic.HandleCreateDocsForUpload(request);

			CreateDocsForUploadResultDto<PrcDocumentDto> result = new CreateDocsForUploadResultDto<PrcDocumentDto>
			{
				InvalidFileList = uploadResult.InvalidFileList,
				Documents = uploadResult.Documents?.Select(doc => new PrcDocumentDto(doc)).ToList()
			};

			return result;
		}

		/// <summary>
		/// Check whether file with the same file name already exists.
		/// </summary>
		/// Note: added for angular. The old Route("checkduplicatebyfilename") can be removed when Angular is released.
		/// <returns></returns>
		[Route("checkduplicateforuploadfile")]
		[HttpPost]
		public List<UploadedFileInfo> CheckDuplicateForUpload(CheckDuplicateForUploadRequest paramDto)
		{
			var updateFiles = Logic.CheckDuplicateForUpload(paramDto.UploadedFileDataList, paramDto.IdentData, paramDto.ExtractZipOrNot, paramDto.SectionType).ToList();
			return updateFiles;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[Route("checkduplicatebyfilename")]
		[HttpPost]
		public List<UploadedFileInfo> CheckDuplicateByFileName(DocumentCreateParameter paramDto)
		{ 
		    var updateFiles =Logic.CheckDuplicateByFileName(paramDto.UploadedFileDataList, paramDto.MainItemId, paramDto.ExtractZipOrNot).ToList();
			return updateFiles;
		}
	}
}
