{"boq": {"main": {"createAndImportMultipleBoQs": "Create and import multiple BoQs", "IsFixedPrice": "Fixed Price", "allSplitAssignedMessage": "The Split Quantity cannot be deleted for it is still referenced by other items !", "powerbiView": "PowerBI Viewer", "powerBIElements": "Power BI Workspaces,Dashboards and Reports", "assignBoqItemResult": "Parameters in detail columns can not be found in Customization Estimate Parameter, the detail formula is deleted.", "invalidParam": "Can't find parameter in Customizing Estimate parameter or the parameter value type is not double type‎", "invalidParam2": "Can't find parameter in Customizing Estimate and Project parameter or the parameter value type is not double type‎", "cycleReferenceErrorMessage": "Cyclic Dependence:{{object}}", "selfReferenceErrorMessage": "The valueDetail has reference self parameter code", "CustomParameter": "Customizing EstParameter", "source": "Source WIC BoQ", "PriceCondition": "BoQ Price Condition", "ExtraIncrement": "Extra Increment", "PreEscalation": "Pre Escalation", "ExtraIncrementOc": "Extra Increment OC", "PreEscalationOc": "Pre Escalation OC", "PreEscalationTotal": "Pre Escalation Total", "ExtraPrevious": "Extra Previous", "ExtraTotal": "Extra Total", "OutSpecification": "Out Specification", "userDefinedTexts": "Copy User Defined Texts", "CharacteristicNContent": "Copy Project Characteristic/Content", "SpecificationTexts": "Copy Specification Texts", "CopyRuleParameter": "Copy Rule/Parameter", "CopyDocument": "Document", "CopyPricecondition": "Pricecondition", "target": "Range of Target Bill of Quantities", "comparisonOn": "Comparison On", "generate": "Generate Options", "compareItemNumberUpToBoqLevel": "compareItemNumberUpToBoqLevel", "ignoreIndex": "Ignore index", "boqLineTypeFk": "Boq LineType", "compareNumberUpTo": "Compare number up to", "replace": "Only target items without WIC number", "GeneratePredefineLineItems": "Generate Predefine Line Items", "sameUnitOfMeasure": "Same unit of measure", "identicalOutlineSpecification": "Identical Outline Specification", "toRN": "To RN", "fromRN": "From RN", "canNotGenerateWicNumberInWicBoQ": "Can Not Generate WIC Number In Wic BoQ", "canNotUpdateDataWicBoQ": "Can not Update data from WIC in WIC BoQ!", "generateWicNumber": "Generate WIC Number", "updateDatafromWIC": "Update Data from WIC", "ProjectBoq": "Project Boq Parameter", "KeepBidderData": "Keep Bidder Data(Only Gaeb-xml-import)", "OnlyAqQuantities": "Only AQ Quantities", "DeleteMissing": "Delete Missing", "WicGroup": "WIC Group", "ruleAndParam": "Rule/Parameter", "Param": "Param", "ParamAssignment": "Param", "Parameter": "Parameter", "RuleFormula": "Rule Formula", "RuleFormulaDesc": "Rule Formula Description", "Rule": "Rule", "RuleAssignment": "Rule", "Assignments": "Assignments", "navDocumentProperties": "Document Properties", "boqProperties": "BoQ Properties", "Reference": "Reference No.", "Reference2": "Reference No. 2", "ExternalCode": "External Code", "BaseBoqReference": "Base BoQ Reference No.", "DesignDescriptionNo": "Design/Sub-Description No.", "WicNumber": "WIC No.", "BasUomFk": "UoM", "Brief": "Outline Specification", "BriefInfo": "Outline Specification", "BasItemTypeFk": "Item Type Stand/Opt", "BasItemType2Fk": "Item Type Base/Alt", "BoqRootItemReference": "BoQ Root Item Reference", "AAN": "AAN", "AGN": "AGN", "Factor": "Factor", "FactorDetail": "Factor-Detail", "Quantity": "Quantity", "QuantityA": "AQ-Quantity", "QuantityAdj": "AQ-Quantity", "QuantityAdjDetail": "AQ-Quantity Detail", "QuantityDetail": "Quantity Detail", "QuantitySplit": "Quantity Split", "QuantityTarget": "Target Quantity", "ExSalesRejectedQuantity": "Rejected Quantity", "ExWipIsFinalQuantity": "Is Final WIP Quantity", "ExWipQuantity": "WIP Quantity", "ExWipExpectedRevenue": "Expected Revenue", "HoursUnit": "Hrs/Unit", "HoursPerUnit": "Hrs/Unit", "Hours": "Hours", "Cost": "Cost", "Correction": "Correction", "Price": "Unit Rate", "BasePrice": "Base Price", "SurchargeFactor": "Surcharge Factor", "CostOc": "Cost OC", "CorrectionOc": "Correction OC", "PriceOc": "Unit Rate OC", "DiscountPercent": "Discount %", "DiscountedUnitprice": "Discounted UR", "DiscountedPrice": "Discounted Price", "Finalprice": "Final Price", "LumpsumPrice": "Lumpsum Price", "DiscountedUnitpriceOc": "Discounted UR OC", "DiscountedPriceOc": "Discounted Price OC", "FinalpriceOc": "Final Price OC", "ItemTotal": "Item Total", "ItemTotalOc": "Item Total OC", "ItemInfo": "Item Info", "LumpsumPriceOc": "Lumpsum Price OC", "IsUrb": "UR Breakdown", "Urb1": "UR Breakdown 1", "Urb2": "UR Breakdown 2", "Urb3": "UR Breakdown 3", "Urb4": "UR Breakdown 4", "Urb5": "UR Breakdown 5", "Urb6": "UR Breakdown 6", "Urb1Oc": "UR Breakdown 1 OC", "Urb2Oc": "UR Breakdown 2 OC", "Urb3Oc": "UR Breakdown 3 OC", "Urb4Oc": "UR Breakdown 4 OC", "Urb5Oc": "UR Breakdown 5 OC", "Urb6Oc": "UR Breakdown 6 OC", "UnitRateFrom": "UR from", "UnitRateTo": "UR to", "Discount": "Discount abs. IT", "UnitRateFromOc": "UR from OC", "UnitRateToOc": "UR to OC", "DiscountOc": "Discount abs. IT OC", "DiscountPercentIt": "Discount % IT", "DiscountText": "Discount Comment", "Finaldiscount": "Final Discount", "FinaldiscountOc": "Final Discount OC", "ShowItForInfo": "Show IT For Info", "IsLumpsum": "Lump <PERSON>", "BoqItemReferenceFk": "Reference to", "BoqItemReferenceDescription": "Reference to Description", "BoqItemReferenceDesignDescription": "Reference to Design Description", "wic2AssemblyQuantity": "Takeover Quantity", "prjLocationDescription": "prjLocationDescription", "IsDisabled": "Disabled", "IsNotApplicable": "N/A", "IsLeadDescription": "Lead Description", "IsKeyitem": "Key Item", "IsDaywork": "DW/T+M Item", "IsSurcharged": "Surcharged", "IsFreeQuantity": "Free Quantity", "IsFixed": "Fixed", "IsUrFromSd": "UR from Sub-Description", "IsNoMarkup": "No Markup", "IsCostItem": "Cost Item", "Userdefined1": "Userdefined 1", "Userdefiend1": "Userdefined 1", "Userdefined2": "Userdefined 2", "Userdefined3": "Userdefined 3", "Userdefined4": "Userdefined 4", "Userdefined5": "Userdefined 5", "ExternalUom": "External UoM", "BpdAgreementFk": "Agreement", "SelectMarkup": "Select", "CharacteristicContent": "Characteristic / Content", "PrjCharacter": "Project Characteristic", "WorkContent": "Work Content", "ItemType85Fk": "Alternative Bid", "Stlno": "Service catalog no.", "BasItemStatusFk": "Item Status", "StatusComment": "Status Comment", "QuantityMax": "Maximum Quantity", "BoqStatusFk": "BoQ Status", "NotSubmitted": "Not Submitted", "Included": "Included", "CopyInfo": "Copy Info", "CopySourcePreview": "Preview", "CalculateQuantitySplitting": "Calculate Quantity Splitting", "BilledQuantity": "Billed Quantity", "InstalledQuantity": "Installed Quantity", "IsGCBoq": "GC BoQ", "BudgetPerUnit": "Budget/Unit", "BudgetTotal": "Budget Total", "BudgetFixedUnit": "Fixed Budget/Unit", "BudgetFixedTotal": "Fixed Budget Total", "BudgetDifference": "Budget Difference", "PrjChangeFk": "Project Change", "PrjChangeId": "Project Change", "PrjChangeStatusFk": "Project Change Status", "PrjChangeStatusId": "Project Change Status", "PrjChangeStatusFactorByReason": "Factor By Reason", "PrjChangeStatusFactorByAmount": "Factor By Amount", "RecordingLevel": "Recording Level", "UseSubQuantityPrice": "UR From Sub Quantity", "BoqRevenueTypeFk": "Revenue Type", "BoqRevenuePercentage": "Revenue Percentage", "TextConfiguration": "Text Configuration", "AssignmentError": "No WIC Assignments of Current Result Set", "BasClobsFk": "Specification Plain Text", "TextComplements": "Text Complements", "errorTextComplementEditFailed": "Did not found adequate complement in the specification! Please remove row.", "BasBlobsSpecificationFk": "Specification", "BasicData": "Copy Basic Data", "ItemType": "Item Type/ Base / Alternative", "FactorItem": "Factor", "QuantityPrice": "Quantity/Price", "UrBreakdown": "UR Breakdown", "UrFromTo": "UR from/to", "DiscountLumpSum": "Discount/Lumpsum Div/BoQ", "ReferenceTo": "Reference to", "AdditionsBoq": "Additions BoQ", "AdditionsEstimate": "Additions Estimate", "PrcItemEvaluationFk": "Procurement Item Evaluation", "PrcStructureFk": "Procurement Structure", "ExSalesTaxGroupFk": "Sales Tax Group", "CommentContractor": "Comment Contractor", "CommentClient": "Comment Client", "UserDefined": "User Defined", "System": "System", "Structure": "BoQ Structure", "EditStructure": "Edit Structure", "MasterDataAssignments": "Master Data Assignments", "BudgetOnBoq": "Budget", "PrjChange": "Project Change", "VobDirectCostPerUnit": "Direct Cost / Unit", "VobDirectCostPerUnitOc": "Direct Cost OC / Unit", "VobIsIndirectCostBalancing": "Indirect Cost Balancing", "VobIsSpecialIndirectCostBalancing": "Indirect Cost Balancing Special", "Performance": "Performance", "Margin": "<PERSON><PERSON>", "boqHeaderSel": "BoQ Selection", "fromEstimate": "From Estimate", "toEstimate": "To Estimate", "isActive": "Is Active", "estimateHeader": "Estimate Header", "boqLookup": "Source BoQ", "sourceBoqDetailsTitle": "Source BoQ Assignments", "sourceBoqDetailResources": "Resources", "boqSpecification": "Specification", "boqConfiguration": "BoQ Configuration", "boqStructure": "BoQ Structure", "addIndexToBoqStructure": "Add Index to BoQ Structure?", "addIndexToBoqStructureTitle": "Add Index to BoQ Structure", "generateSplitQuantitiesWithBillToAssignmentTitle": "Add Split Quantities with Bill-To Assignment", "boqContainsQtos": "This boq contains qto's", "estimateLineItems": "Estimate Database Line Items", "estimateResources": "Estimate Database Resources", "boqDetails": "BoQ Details", "structureDetailTypeNumeric": "numeric", "structureDetailTypeAlphaNumeric": "alphanumeric", "procurementBoqList": "Procurement BoQs", "boqList": "BoQs", "boqTranslation": "BoQ", "boqSurchargeOn": "Surcharge On", "boqMainHtmlTextComplement": "Text Complements", "totalQuantity": "Total Quantity", "TotalQuantityAccepted": "Total Quantity (Approved)", "totalIQAccepted": "Total IQ (Approved)", "totalPrice": "Total Price", "totalHours": "Total Hours", "previousPrice": "Previous PES Price", "previousQuantity": "Previous Quantity", "contractQuantity": "Contract Quantity", "remainingQuantity": "Remaining Quantity", "TotalQuantity": "Total", "TotalPrice": "Total Price", "TotalPriceOc": "Total Price OC", "TotalHours": "Total Hours", "Total": "Total", "QuantitySplitTotal": "Total", "PrevQuantity": "Previous Quantity", "PrevRejectedQuantity": "Previous Rejected Quantity", "TotalRejectedQuantity": "Total Rejected Quantity", "OrdQuantity": "Contract Quantity", "RemQuantity": "Remaining Quantity", "ItemTotalEditable": "Item Total Editable", "ItemTotalEditableOc": "Item Total Editable (OC)", "PercentageQuantity": "Percentage Quantity", "CumulativePercentage": "Cumulative Percentage", "PrjBillToId": "Code", "ProjectBillToFk": "Project Bill-To", "IQPreviousQuantity": "IQ Previous Quantity", "BQPreviousQuantity": "BQ Previous Quantity", "IQQuantityTotal": "IQ Quantity Total", "BQQuantityTotal": "BQ Quantity Total", "newItem": "New Item", "newDivision": "New Division", "newSubDivision": "New Sub-Division", "newBoq": "New BoQ", "openBoq": "Open BoQ", "boqNumber": "BoQ Number", "projectList": "Project", "wicGroup": "WIC Group", "copyFrom": "Copy From", "wicBoq": "WIC BoQ", "projectBoq": "Project BoQ", "packageBoq": "Package BoQ", "bidBoq": "<PERSON><PERSON>", "contractBoq": "Procurement Contract BoQ", "gaebFile81": "81 Bill of Quantities", "gaebFile82": "82 Cost Planning Transfer", "gaebFile83": "83 Invitation to Tender", "gaebFile84": "84 Tender Export", "gaebFile85": "85 Alternativ Tender", "gaebFile86": "86 Contract Award", "gaebXML": "GAEB XML", "gaeb2000": "GAEB 2000", "gaeb90": "GAEB 90", "gaebExport": "GAEB-Export", "gaebImport": "GAEB-Import", "GaebFormat": "GAEB Version", "Gaebtype": "Exchange Format", "Selection": "Selection", "Format": "Format", "Specification": "Specification", "SelectionType": "Selection Type", "Settings": "Settings", "Prices": "Prices", "HourRates": "HourRates", "completeBoQDocument": "Complete BoQ Document", "boqArea": "BoQ Area", "From": "From", "To": "To", "partialImport": "Partial Import", "addNewElements": "Add New Elements", "overwriteExistingElements": "Overwrite Existing Elements", "DeleteMissingItems": "Delete Missing Items", "LikeItemsGroups": "Like Items & Groups", "GaebFileContainsErrors": "Gaeb file {{p1}} contains {{p2}} error(s).", "gaebPartialImport": "Merge Options", "gaeb90ExportInfo": "Reference No. is not compliant to GAEB 90", "gaebX84ImportError": "Cannot import a GAEB 84 file into an empty BOQ", "fromToNotNullError": "From or To value should not be null", "rootAndDivisionAllowedInFrom": "Please select root or division in 'From' field", "rootAndDivisionAllowedInTo": "Please select root or division in 'To' field", "DoubletFindMethod0": "Id", "DoubletFindMethod1": "Reference No.", "boqRenumber": "<PERSON><PERSON><PERSON>", "boqRenumberText": "The BoQ structure will be renumbered by the actual BoQ document settings.", "freeBoqRenumber": "Renumber Free BoQ", "freeBoqWarningMessage": "The selected BoQ is not Free BoQ.", "freeBoqOnlyCanBeSwitchedToGaeb": "A Free BoQ only can be switched to a GAEB BoQ.", "renumberOptionTitle": "Please select an option:", "renumberBoqScope": "<PERSON><PERSON>", "renumberDependance": "Numbering Dependance", "renumberAllBoqs": "Renumber all BoQs in current project", "renumberEachBoq": "Independant numbering in each BoQ", "renumberCurrentPrj": "Cross numbering for BoQs within current project", "renumberSelectedBoq": "Renumber only the current BoQ", "OnlyForRibExcelImport": "Only considered for RIB Excel import", "createNewBoq": "Create new BoQ", "information": "Information", "warning": "Warning", "referenceInUse": "The entered reference number is already in use. Enter another one!", "referenceInUseInBaseBoq": "The entered reference number is already in use in the base boq. Enter another one!", "invalidGaebAGN": "The value must be a number!", "invalidGaebAAN": "The value must be a number!", "invalidReference": "The entered reference number is invalid according to the currently active boq structure!", "invalidReferenceFinalDot": "The reference number is invalid due to missing final dot!", "invalidReferenceLengthExceeded": "The length of the reference number exceeds the given limit!", "invalidReferenceParentPartMismatch": "Currently it is not allowed to change the parent part of the reference. Keep this part of the reference in sync with the parent reference!", "setSubdescriptionError": "No fitting position or lead description found for sub description", "gaebImportBoqMissing": "BoQ missing or not selected! Please create a BoQ or select an existing one!", "projectChangeMissing": "Current BOQ item has no project change", "projectChangeStatusMissing": "Current BOQ item has no project change status", "projectChangeText": "Project Change Assignments already exist on lower levels. Do you want to keep the assignments?", "selectTargetBoq": "Please first select a target BoQ!", "selectSourceBoq": "Please first select a project or WIC source BoQ!", "selectOenContactParent": "Please first select an element in one of the containers", "catalogHeader": "BoQ Catalog Assign Type", "boqStandard": "BoQ Standard", "boqSpecificStruc": "Edit BoQ Configuration", "boqType": "BoQ Configuration Type", "boqMask": "Structure Mask", "structDetails": "Structure Details", "enforceStruct": "Enforce Structure", "KeepRefNo": "Keep Reference Number", "KeepQuantity": "Keep Quantity", "KeepUnitRate": "Keep Unit Rate", "KeepBudget": "Keep Budget", "autoInsert": "Auto-insert Hierarchies at insert/copy", "CopyEstimateOrAssembly": "Copy Estimate or Assembly", "CopyAndPasteEstimateOrAssembly": "Estimate or Assembly data not copied. Use source boq container to copy Estimate or Assembly", "refNoLeadingZeros": "Reference No. with leading zeros", "discountAllowedAtLevel": "Discount allowed for BoQ-hierarchy-level", "urCalcByURB": "UR is calculated from URB portions for entire BoQ", "showTotalHours": "Show Total of Hours", "SkippedHierarchiesAllowed": "Allow skipped hierarchies", "BoqLineTypeFk": "BoQ Line Type", "BoqDivisionTypeFk": "Division Type", "Code": "Code", "Description": "Description", "DataType": "Data Type", "DiscountAllowed": "Discount Allowed", "LengthReference": "Length Reference", "StartValue": "Start Value", "Stepincrement": "Step Increment", "catalogSection": "BOQ Catalog Assignments", "boqCatalogAssignType": "BoQ Catalog Assign Type", "editBoqCatalogConfigType": "Edit Type", "boqCatalogAssignDetails": "BoQ Catalog Assign Details", "catalogsourcefk": "Source Catalog", "BoqStructureInvalid": "The current boq structure details are invalid", "BoqStructureNoDetails": "There are no boq structure details.", "BoqStructureDetailLineInvalid": "The following fields of the stucture detail line at index {%i} are invalid", "BoqStructurePositionLineMissing": "The mandatory detail line for a position is missing.", "BoqStructureDetailValuesDiscrepant": "Start Value, Step Increment and Length of Reference are discrepant.", "DocumentPropertiesNoBoq": "Select a BoQ! Without a selected BoQ the corresponding properties can not be displayed!", "itemCreateErrorTypeMissing": "There is no information for this item type added to the boq structure definition so we cannot create it!", "itemCreateErrorLevelMismatch": "This item type cannot be added at this level", "itemCreateErrorReferenceCreationFailed": "According to the structure definitions the reference number cannot be generated here so the item cannot be created either!", "itemCreateErrorDivisionIsSubOfPosition": "Cannot add division as sub item of position!", "loadWicItemsOnly": "Load WIC Items Only", "ComplType": "Type", "ComplCaption": "Introduction", "ComplBody": "Text", "ComplTail": "Subsequent Text", "Sorting": "No.", "ComplTypeBidder": "<PERSON><PERSON><PERSON>", "ComplTypeClient": "Owner", "SplitDiscountTitle": "Split Discount", "SplitDiscountText": "The discount will be splitted. Do you want to continue?", "SplitQuantity": "Split Quantities", "PrcStructureDescription": "Procurement Structure Description", "catalogAssignments": "Catalog Assignments", "CtlgName": "GAEB Catalog Name", "CtlgType": "GAEB Catalog Type", "MappedCatalogId": "BoQ Cost Group", "CatalogAssignmentMode": "Assignment Mode", "costgroupcatcode": "Structure", "costgroupgroupcode": "Cost Group", "newcostgroupcatcode": "New Catalog Code", "newcostgroupcatDescr": "Catalog Description", "procurementstructure": "Procurement Structure", "locations": "Locations", "controllingstructure": "Controlling Structure", "newcatalog": "(New Catalog)", "Wic2AssemblyListTitle": "Assembly Assignment", "WorkContentInfo": "Work Content", "WicEstAssembly2WicFlagFk": "Takeover Mode", "EstLineItemFk": "Assembly Code", "EstAssemblyCatFk": "Assembly Category", "assemblyDescription": "Assembly Description", "uniqCode": "Please enter a unique Code. This code is already exist.", "costGroup1Description": "Cost Group 1 Description", "costGroup2Description": "Cost Group 2 Description", "costGroup3Description": "Cost Group 3 Description", "costGroup4Description": "Cost Group 4 Description", "costGroup5Description": "Cost Group 5 Description", "confirmationToDeleteWic2AssemblyTitle": "Assembly assignment records will be removed.", "confirmationToDeleteWic2AssemblyBody": "Only Position and Surcharges items can have assigned assemblies. Do you want to continue?", "createDeepCopy": "Create a deep copy", "CommentText": "Comment", "textConfig": "Text Configuration", "textType": "Type", "textSplit": "Split", "textCombination": "Combination", "textNumbering": "Numbering", "textTypeName": "Type Name:", "ProjectCharacteristic": "Project Characteristic", "WithLinefeed": "With Linefeed", "WithComma": "With Comma", "CommaRound": "With Comma & Round Bracket", "CombinationDetail": "Description: Detail + Postfix", "DescriptionPostfix": "Description + Postfix", "DetailPostfix": "Detail + Postfix", "Digit": "Digit", "Lowercase": "Lowercase", "Uppercase": "Uppercase", "None": "None", "moveUp": "Move Up", "moveDown": "Move Down", "one": "Update To Current BoqItem", "all": "Update To All BoqItem", "ConfigCaption": "Description", "ConfigBody": "Detail", "ConfigTail": "Postfix", "Isoutput": "Is Output", "Remark": "Remark", "ConfirmDialog": "the existing data (including manual input) in the column (Project Characteristic or Work Content) of all BoQ items will be updated by the configuration texts of every BoQ item.", "BoqItemFlagFk": "BoQ Item Flag", "ItemFlag": "BoQ Item Flag", "CrbGroup": "CRB Group", "HintText": "Hint Text", "PrdProduct": "PRD Product", "PrdProductFk": "PRD Product", "ReleaseYear": "Release Year", "Stand": "Stand", "EntryStart": "Entry Start", "TitleIsReadonly": "Brief <PERSON>", "PublicationCode": "Publication Code", "Grp": "Group", "ProdNormCode": "Production Norm Code", "RepeatableUntil": "Repeatable Until", "Number": "Number", "DescriptionDe": "Description", "DescriptionMutableDe": "Replacement", "HintDe": "Hint", "Group": "Group", "ContractorText": "Pos Contractor Text", "ContractorTextDe": "Contractor Text", "EcoDevisMark": "EcoBQ", "AssociationCalculation": "Association Calculation", "QuantityType": "Quantity Type", "PriceType": "Price Type", "SerialNumber": "Serial Number", "BoqItemPreliminaryFk": "Preliminary", "GroupNumber": "Group Number", "LineNumber": "Line Number", "Formula": "Formula", "DispatchRecordHeaderCode": "Header Code", "DispatchRecordHeaderDescr": "Header Description", "DispatchRecordNumber": "Record Number", "DispatchRecordQuantity": "Quantity", "DispatchRecordDateEffective": "DateEffective", "DispatchRecordProductCode": "Product Code", "DispatchRecordProductDescr": "Product Description", "EngDrwCompTypeFk": "Component Type", "MdcMaterialCostCodeProductFk": "Component Result", "ProductComponentQuantity": "Component Quantity", "ProductComponentUomFk": "Component Uom", "MdcMaterialCostCodeProductDesc": "Component Result Description", "importSucceeded": "The import was completed successfully.", "exportSucceeded": "The export was completed successfully.", "importFailed": "The import failed.", "exportFailed": "The export failed.", "addTextComplementBidder": "Add text complement (bidder)", "addTextComplementClient": "Add text complement (owner)", "textComplementHint": "The text can be completed in container", "selectGroupsPopup": "Select Base-/Alternate BoQ Groups", "scanResultPopup": "BoQ scan result", "validateGaebExport": "<PERSON><PERSON>", "DivisionTypeAssignment": "Division Type Assignment", "scanWasSuccessful": "No errors found ...", "deleteBoqItemsHeader": "Deletion of BoQ items", "deleteBoqItemsWithDependantItemsInfo1": "The execution was interrupted because BoQ items are referenced by other items.", "deleteBoqItemsWithDependantItemsInfo2": "Please go on by cancelling or accepting one of the following options.", "deleteBoqItemsWithDependantEstimateItems": "Referencing estimate items", "deleteBoqItemsWithDependantEstimateItemsOption1": "Delete the estimate items too", "deleteBoqItemsWithDependantEstimateItemsOption2": "Delete references to the BoQ items", "deleteBoqItemsWithDependantQtoDetails": "Delete the referencing QTO details too", "deleteBoqItemsQtoLineReference": "QTO Line Reference", "boqItemWithDependentEstimateHeader": "Dependent Estimate items", "boqItemWithDependentEstimateInfo1": "This BoQ item is linked to estimate items. ", "boqItemWithDependentEstimateInfo2": "Remove the links to the following estimate items:", "EstimateCode": "Estimate Code", "EstimateDescription": "Estimate Description", "records": "Records", "totalRecords": "Total Records", "changedRecords": "Changed Records", "unchangedRecords": "Unchanged Records", "details": "Details", "reportLog": "Report Log", "changedRecordsLog": "Changed Records Log", "unChangedRecordsLog": "UnChanged Records Log", "comparisonType": "Comparison Property", "miscellaneousSplitQuantityProperties": "Miscellaneous values in split quantities", "miscellaneousPrjChangesInBidBoqs": "Miscellaneous project changes in bid BoQs", "renumberSelection": "Selection", "renumberModeAll": "Renumber entire boq", "renumberModeSelected": "Renumber only selected divisions", "renumberProperties": "<PERSON><PERSON><PERSON>", "renumberPropertiesSave": "Save and Renumber", "renumberInvalidSelection": "Invalid selection", "renumberNoValidSelection": "This selection is currently not supported. Please select divisions only!", "renumberAborted": "Renumbering aborted", "renumberFailed": "Renumbering could not be performed with these settings. Adjust settings please!", "createSpecificBoqProperties": "To save the changes we have to create specific structure settings. Do you want to proceed?", "splitUrbPopupTitle": "Create new BoQ items from Unit Rate breakdown", "splitUrbWizardOptScopeLabel": "Select BoQ items", "splitUrbWizardOptScopeOptAllItems": "All items", "splitUrbWizardOptScopeOptOnlySelectedItems": "Only selected items", "splitUrbWizardOptModeLabel": "Select mode", "splitUrbWizardOptModeCreate": "Split Unit Rate Portions to BoQ Items", "splitUrbWizardOptModeRemove": "Remove Index BoQ Items from Unit Rate Portions", "creationOfBoqPositionImpossible": "The creation of a BQ position is not possible on this level!", "Pricegross": "Corrected UR (Gross)", "PricegrossOc": "Corrected UR (Gross OC)", "Finalgross": "Final Price (Gross)", "FinalgrossOc": "Final Price (Gross OC)", "scanBoqColReference": "Reference", "scanBoqColMsg": "Message", "QuantityExceedsContractedQuantity": "Quantity exceeds contracted quantity", "IQuantityExceedsContractedQuantity": "IQ Total Quantity exceeds contracted quantity", "wicDisabledFunc": "This functionality is not available for a WIC.", "wicExclisiveFunc": "This functionality is only available for a WIC.", "copyOptionsDisabled": "The copy options of system BoQ configurations can not be edited.", "roundingConfigDisabled": "The rounding configuration of system BoQ configurations can not be edited.", "oenBoqTypeIsReadonly": "The fix BoQ configurations can not be edited for the Austrian OENORM.", "crbBoqTypeIsReadonly": "The unique BoQ configuration can not be edited for the CRB standard.", "npkImportWicMissing": "WIC Group missing or not selected! Please create a WIC Group or select an existing one!", "crbExclusiveFunc": "This functionality only is available for the CRB standard.", "oenExclusiveFunc": "This functionality only is available for the ÖNORM standard.", "crbNpkExclusiveFunc": "This functionality only is available for a NPK of the CRB standard.", "crbDisabledFunc": "This functionality is not available for the CRB standard.", "oenDisabledFunc": "This functionality is not available for the ÖNROM standard.", "oenWicDisabledFunc": "This functionality is not available for a LB of the ÖNROM standard.", "crbFullLicenseExclusiveFunc": "Diese Funktionalität ist ausschließlich im CRB-Volllizenz-Modus verfügbar.", "crbIsReadonlyMode": "Im Lesemodus stehen die CRB-Standards nur zur Darstellung bereits erfasster oder importierter Daten zur Verfügung. Der Anwender kann im Lesemodus keine neuen Daten erfassen oder vorhandene Daten bearbeiten.", "crbRevisioninfo": "Revision information", "crbRevisioninfoMark": "Revision information mark", "crbRevisionDetails": "Revision details", "crbRevisionError": "Error", "crbRevisionReplacement": "Replacement", "crbRevisioninfoMarksUpdated": "The Revision information mark in the chapter was updated.", "crbRevisioninfoMarksUnavailable": "Revision information are not available!", "crbRevisioninfoFiles": "Revision information files", "crbRevisioninfoFilesUnavailable": "Revision information files are not available!", "crbRevisioninfoLinks": "Revision information web links", "crbRevisioninfoLinksUnavailable": "Revision information files are not available!", "crbEcodevis": "EcoBQ", "crbEcodevisMarksUpdated": "The EcoBQ marks were updated.", "crbEcodevisMarksUnavailable": "EcoBQ marks are not available!", "crbEcodevisInfo": "EcoBQ information", "crbEcodevisInfoShortDescription": "Short Description", "crbEcodevisInfoNote": "Note", "crbEcodevisInfoEcoText": "Eco Text (can be taken over in the variable text)", "crbEcodevisInfoUnavailable": "An EcoBQ information is not available!", "crbEcodevisVariabletext": "EcoBQ variable texts takover", "crbEcodevisVariabletextChanged": "EcoBQ variable texts were taken over for the following variables", "crbEcodevisVariabletextUnavailable1": "EcoBQ variable texts are not available!", "crbEcodevisVariabletextUnavailable2": "EcoBQ variable texts were not taken over, because the variables container is not available!", "crbEcodevisVariabletextUnavailable3": "EcoBQ variable texts were not taken over, because the current position of the source BOQ ist not available in the target BOQ!", "crbEcodevisVariabletextUnavailable4": "EcoBQ variable texts were not taken over, because the current variables of the source BOQ are not available in the target BOQ!", "crbEcodevisRatingUnavailable": "An EcoBQ rating is not available!", "crbEcodevisRating": "EcoBQ rating", "crbEcodevisComparisonUnavailable": "An EcoBQ comparison is not available!", "crbEcodevisComparison": "EcoBQ comparison", "crbEcodevisMark1": "Eco", "crbEcodevisMark2": "EcoBQ mark", "crbEcodevisFiles": "EcoBQ files", "crbEcodevisFilesUnavailable": "EcoBQ files are not available!", "crbEcodevisLinks": "EcoBQ web links", "crbEcodevisLinksUnavailable": "EcoBQ files are not available!", "crbEcodevisProE": "1st-priority requirements", "crbEcodevisProe": "2nd-priority requirements", "crbEcodevisMaterial": "Material", "crbEcodevisGraue_Energie": "Embodied energy", "crbEcodevisLoesemittelemission": "Solvent emissions", "crbEcodevisUmwelt_und_gesundheitsrelevante_Bestandteile": "Parts relevant to health and the environment", "crbEcodevisEmissionen_aus_Schwermetallen": "Heavy metal emissions", "crbEcodevisFormaldehydemissionen": "Formaldehyde emissions", "crbEcodevisBiozide": "Biocides", "crbEcodevisEmissionsstandard": "Emission standard", "crbEcodevisEntsorgung": "Disposal", "crbPrdMark": "PRD mark", "crbPrdMarksUpdated": "The PRD marks were updated.", "crbPrdProductsUnavailable": "PRD products are not available!", "crbPrdSelectProduct": "Select PRD product", "crbPrdProductDetail": "Product", "crbPrdSupplierDetail": "Supplier", "crbPrdName": "Name", "crbPrdSupplier": "Supplier", "crbNewKapitel": "New Work Section", "crbNewAbschnitt": "New Division", "crbNewUnterabschnitt": "New Subdivision", "crbNewHauptposition": "New Main Text", "crbNewUnterpositionsgruppe": "New Subtext group", "crbNewUnterpositionsuntergruppe": "New Subtext subgroup", "crbNewUnterposition": "New Subtext", "crbNewSubQuantity": "Neue Teilmenge", "crbNewPreliminary": "New Preliminary", "crbNewPositiontext": "New Positiontext", "crbNewVariable": "New Variable", "crbEditVariable": "Edit Text", "crbNewPriceconditionScope": "<PERSON> Scope", "crbDelPriceconditionScope": "Delete Scope", "crbPriceconditionScope": "<PERSON><PERSON>", "crbImportantInformation": "Important Information", "crbImportantInformationUnavailable": "Important information are not available!", "crbImportantInformationDownloaded": "A document with important information is downloaded!", "crbSpecialBoqItemsToBeDeleted": "Die Unterabschnitte 010, 020 und 030 müssen ebenfalls gelöscht werden!", "crbBoqItemRemoval": "Removal of no longer used higher-level items according to the NPK system", "crbBoqItemRemovalCanceled": "No higher-level positions that are no longer used were found.", "crbBoqItemRemovalDone": "The BOQ was reduced by the following items.", "crbBoqItem000sAttached": "Die Abschnitte 000 wurden nachgeladen.", "crbAbschnitt000": "Abschnitte 000 nachladen", "crbDocumentType": "Document Type", "crbOutputFormat": "Format", "crbBoqOutput": "BoQ output", "crbBoqOutputFullText": "Full Text", "crbBoqOutputShortText": "Short Text", "crbBoqOutputShortHeading": "Short Heading", "siaVersion": "Version", "siaOptions": "Options", "siaRanges": "Ranges", "siaImport": "Import SIA", "siaExport": "Export SIA", "crbReportParameters": "Additional parameters", "npkImport": "Import NPK", "npkGroupBy": "Group by", "npkChapter": "Chapter", "npkVersion": "Version", "npkYear": "Year", "npkStand": "Stand", "itemInfoOwnerTextComplements": "TO", "itemInfoBidderTextComplements": "TB", "itemInfoKeyItem": "KI", "wizardChangeBoqStatus": "Change BoQ Status", "DeliveryDate": "Delivery Date", "DocOwner": "Owner", "documentTitle": "BoQ Documents", "crbVariableTitle": "BoQ Variables (CRB)", "crbPriceconditionTitle": "BoQ Price Conditions (CRB)", "crbBoqItemScopeTitle": "Structure assignments (CRB)", "crbSubQuantityDetailTitle": "Detail Quantities (CRB)", "crbSubQuantityDetailObsolete": "This Container will be replaced by Container Quantity Takeoff soon. In the meantime it is read only.", "linkedDispatchNoteTitle": "Linked Dispatch Notes", "Level": "Level", "CrbPriceconditionFk": "Reference Level", "CrbPriceconditionTypeFk": "Condition Type", "IsConsidered": "Is Considered", "CalculationType": "Calculation Type", "ReferenceAmount": "Reference Amount", "ConditionPercentage": "Condition Percentage", "ConditionAmount": "Condition Amount", "CalculationAmount": "Calculation Amount", "TaxCodeFk": "Tax Code", "PaymentTermFk": "Payment Term", "PaymentTerm": "Payment Term", "prjCostgrpCatNew": "New catalog", "prjCostgrpCatExist": "Existing catalog", "crbCostgrpCatAssign": "Configure CRB assignment to costgroup catalogs", "crbCostgrpCatStructure": "CRB structure", "crbCostgrpCatAssign001": "Die Standardgliederung für KAG erlaubt ausschließlich BKP, eBKP-H oder eBKP-T.", "crbCostgrpCatAssign003": "Die Standardgliederung für EGL erlaubt ausschließlich eBKP-H oder eBKP-T.", "crbCostgrpCatAssignMissing": "Die Konfiguration von Gliederungen ist für Teilmengen erforderlich.", "crbCostgrpCatAssignAmbiguous": "Die folgenden Teilmengen haben keinen eindeutigen Universalschlüssel.", "crbWicGroupSelection": "Bei der Auswahl einer TLK-Gruppe werden alle in der SIA-Datei verwendeten neuen NPK's zusätzlich dorthin importiert", "CostgroupKagFk": "KAG", "CostgroupOglFk": "OGL", "CostgroupEglFk": "EGL", "CostgroupEtFk": "ET", "CostgroupVgrFk": "VGR", "CostgroupNglFk": "NGL", "CostgroupRglFk": "RGL", "assignDocument": "Assign document", "assignTo": "to the current item of container", "IsCrbPrimaryVariant": "Is CRB Primary Variant", "errorCrbPriceconditionLevel": "Must refer to a lower level!", "errorCrbPriceconditionVatSequence": "VAT lines must always be in direct sequence!", "errorCrbPriceconditionVatReference": "VAT lines must always refer to the last level before VAT!", "errorCrbPriceconditionVatUnique": "The VAT rate only may exist once!", "errorCrbPriceconditionScopeUnique": "The scope only may exist once!", "errorCrbPriceconditionTooManyTradeDiscounts": "There is not more than one price condition of type trading discount allowed!", "errorCrbPriceconditionDeleted": "No lines may be deleted that remaining refer to!", "errorCrbBoqitemScopeUnique": "The structure assignment only may exist once!", "errorCrbBoqitemScopePreliminary": "Structure assignments only are allowed for preliminaries!", "errorCrbMissingPaymentTerm": "There must be a payment term in the context!", "errorCrbQuantityDetailNumberUnique": "The number only may exist once!", "errorCrbQuantityDetailNumberWrong": "This number is not allowed!", "refreshReferenceQuantity": "Refresh Reference Quantity", "crbSiaExportError2021Comment": "Possible source of error: each subset of a subitem must have a unique cost group combination.", "crbSiaExportError4721Comment": "Please check the variable \"00\" for the following elements:", "crbSiaExportError6305Comment": "Please delete the unnecessary variables for the following elements:", "goToEstimate": "Go to Estimate", "noEstimateLineItemData": "Line item information does not exist to navigate", "selectEstimateHeader": "Please select estimate header", "gridSettings1": "Upper Grid", "gridSettings2": "Lower Grid", "copyOptions": "Copy Options", "pasteFromClipboard": "Paste From Clipboard To Unit Rate", "pasteFromClipboardToBoqItemDisabled": "The feature is permanently disabled due to possible validation issues.", "roundingConfig": "Rounding Configuration", "emptyDivisionErasure": "Erasure Of Empty Divisions", "emptyDivisionErasureResult": "The number of erased empty divisions is ", "errorBoqStructureChangeMultipleVersionBoqs": "Cannot execute this action for there are multiple related version boqs detected!", "errorBoqStructureChangeMultipleVersionBoqsTitle": "Multiple version BoQs", "boqInReadonlyStatus": "This Boq cannot be modified because of its status!", "billToListTitle": "<PERSON> (BoQ)", "createUpdateBillToWizardTitle": "Create/Update Bill-To's", "IncludeSplitQuantities": "Include Split Quantities", "billToWizardPercentCheckHeader": "Quantity Portion Exceeded", "billToWizardPercentCheckText": "The sum of Quantity Portions of all selected Bill-To's exceeds 100 percent!", "billToEntity": "Bill <PERSON>", "billToWizardCreateAction": "Create Or Update", "billToWizardDeleteAction": "Delete", "billToAssignmentsDetectedHeader": "Bill-To Assignments Detected", "billToAssignmentsDetectedText": "There are already some Bill-To assignments given. How do you want to procceed ?", "billToWizardKeepAssignment": "Keep the existing Bill-To assignments", "billToWizardOverwriteAssignment": "Overwrite the existing Bill-To assignments", "QuantityPortion": "Quantity Portion (in %)", "addNewItems": "Add new Items", "overwriteExistingItems": "Overwrite existing items", "splitUrbSameFirstLetter": "First letter in UR Breakdown names has to be unique!", "confirmDeleteUnitRate": "Confirm deletion of Unit Rate", "askDeleteUnitRate": "Unit Rate will be deleted. Do you want to continue?", "confirmChangeOfRecordingLevel": "Change of Recording Level", "askChangeOfRecordingLevel": "There are already quantities recorded! Do you want to change the recording level?", "confirmChangeOfPercentageValue": "Change of percentage values", "askChangeOfPercentageValue": "There are already percentage values recorded inconsistently! Do you want to change them to this consistent value?", "confirmChangeOfQuantityIfQtoDetailsExist": "Check for Quantity Details", "askChangeOfQuantityIfQtoDetailsExist": "There are already quantity details recorded! Do you want to change the quantity anyway?", "boqResetServiceCatalogNo": "Reset service catalog number", "resetServiceCatalogNoSelection": "Reset service catalog number", "resetServiceCataLogNoModeAll": "Reset service catalog number in whole BoQ", "resetServiceCataLogNoModeSelected": "Reset service catalog number only for selected items and their children", "resetServiceCatalogNoInvalidSelection": "Invalid selection", "resetServiceCatalogNoNoValidSelection": "Check the selection of items in the BoQ!", "resetServiceCatalogNoAborted": "Reset of service catalog numbers aborted", "resetServiceCatalogNoFailed": "Resetting of service catalog numbers could not be performed!", "wizardChangeProjectChangeStatus": "Change Project Change Status", "confirmDeleteTitle": "Delete BoQ item", "confirmDeleteBoqItem": "Do you really want to delete?", "decoupledVersionBoqItemsHeaderText": "Decoupled Version BoQ Items", "decoupledVersionBoqItemsBodyText": "Only items that are also available in Base-BoQ have been renumbered.", "decoupledVersionBoqItemsDetailText": "Therefore the following items have not been renumbered:", "gaebEnforceStructure": " GAEB-Standard will be left and the change is irreversible", "boqItemFk": "BoQ Item Ref. No.", "updateEstimateWizard": {"title": "Update Estimate", "selectBoqScope": "Select Bo<PERSON>", "highlightedBoqItems": "Selected BoQ Items", "entireBoqs": "En<PERSON><PERSON>", "updateEstimateSummaryTitle": "Update Estimate Completed", "updateEstimateErr": "{{count}} Project BoQ(s) not assigned to LineItems OR Estimate is not Active!", "countBoqforUpdatedEstimate": "{{count}} Project BoQ(s) assigned to LineItems has been updated successfully"}, "copyMaterialPriceCondtionToBoq": "Copy Material Price Condtions to Boq", "forSameCodePriceCondition": "For the same code Price Condition", "coverOldOne": "Cover the old one", "ignoreNewOne": "Ignore the new one", "formatBoQSpecification": {"title": "Format BoQ Specifications", "font": "Font", "size": "Font Size", "alignment": "Alignment"}, "reclevelpos": "<PERSON><PERSON>", "reclevellineitem": "LI", "splitNo": "Split No", "dialogDelete": {"bodyMessage": "This item has been used in another container and cannot be deleted."}, "copyUnitRateToBudgetUnitWizard": {"title": "Copy Unit Rate to Budget/Unit", "continueQuestion": "Unit Rate will be copyed. Do you want to continue?"}, "oen": {"dto": {"OenAddressDto": {"Street": "Street", "City": "City", "ZipCode": "Zip Code", "CountryFk": "Country"}, "OenBoqItemDto": {"OenLineTypeFk": "ÖNORM Positionsart", "IsUnsharedPosition": "Ungeteilte Position", "OenStatusFk": "Status", "OriginMark": "Herkunftskennzeichen", "OenZzFk": "Zuordnungskennzeichen (ZZ)", "OenZzVariantFk": "Variantennummer (ZZ)", "PreliminaryMark": "Vorbemerkungskennzeichen", "IsEssentialPosition": "Wesentliche Position", "GuaranteedOfferSumGroup": "Garantierte Angebotssummengruppe", "PartOfferMark": "Teilangebotskennzeichen", "OenServicePartFk": "Leistungsteil", "PartSumMark": "Teilsummenkennzeichen", "IsNotOffered": "<PERSON>cht angeboten", "ItemTotalUrb1": "EP-Anteil 1 GB", "ItemTotalUrb1Oc": "EP-Anteil 1 GB OC", "ItemTotalUrb2": "EP-Anteil 2 GB", "ItemTotalUrb2Oc": "EP-Anteil 2 GB OC", "DiscountPercentItUrb1": "Nachlass % EP-Anteil 1", "DiscountPercentItUrb2": "Nachlass % EP-Anteil 2", "DiscountUrb1": "Nachlass abs. EP-Anteil 1", "DiscountUrb1Oc": "Nachlass abs. OC EP-Anteil 1", "DiscountUrb2": "Nachlass abs. EP-Anteil 2", "DiscountUrb2Oc": "Nachlass abs. OC EP-Anteil 2", "FinalpriceUrb1": "GB nach Nachlass EP-Anteil 1", "FinalpriceUrb1Oc": "GB nach Nachlass OC EP-Anteil 1", "FinalpriceUrb2": "GB nach Nachlass EP-Anteil 2", "FinalpriceUrb2Oc": "GB nach Nachlass OC EP-Anteil 2", "OenPricingMethodFk": "Preiserstellungsverfahren", "LbChangeVersionNumber": "LB-Version", "OenLbChangeTypeFk": "Änderungsumfang", "BlobsLbChangeFk": "Änderungsbeschreibung", "BlobsCommentFk": "Kommentar", "LbReferencePrev": "Vorherige Position", "LbNotInPartialEdition": "Nicht in Teilausgabe"}, "OenCommunicationDto": {"Phone": "Phone", "Fax": "Fax", "Email": "Email", "Url": "Url", "AdditionalInfo": "Additional Info"}, "OenCompanyDto": {"Name": "Name"}, "OenLbMetadataDto": {"Type": "<PERSON><PERSON>", "VersionNumber": "Versionsnummer", "VersionDate": "Versiondatum", "DownloadUrl": "Download-URL", "DescriptionPartialEdition": "Bezeichnung der Teilausgabe", "OenReleaseStatusFk": "Release Status"}, "OenLvHeaderDto": {"OenLvTypeFk": "Lv Type", "ProcessingStatusDate": "Processing Status Date", "PriceBaseDate": "Price Base Date", "OfferDeadline": "Offer Deadline", "BidderNr": "Bidder Nr", "AlternativOfferNr": "Alternativ Offer Nr", "ChangeOfferNr": "Change Offer Nr", "OrderCode": "Order Code", "AdditionalOfferNr": "Additional Offer Nr", "ContractAdjustmentNr": "Contract Adjustment Nr", "IsWithPriceShares": "Is With Price Shares", "NamePriceShare1": "Name Price Share1", "NamePriceShare2": "Name Price Share2", "IsSumDiscount": "Is Sum Discount", "IsAllowedBoqDiscount": "Is Allowed BoQ Discount", "IsAllowedHgDiscount": "Is Allowed HG Discount", "IsAllowedOgDiscount": "Is Allowed OG Discount", "IsAllowedLgDiscount": "Is Allowed LG Discount", "IsAllowedUlgDiscount": "Is Allowed ULG Discount"}, "OenZzDto": {"Nr": "Nr", "Description": "Description"}, "OenZzVariantDto": {"Nr": "Nr", "Description": "Description"}, "OenAkzDto": {"Nr": "Nr", "Description": "Description"}, "OenPersonDto": {"FirstName": "First Name", "FamilyName": "Family Name"}, "OenServicePartDto": {"Nr": "Nr", "Description": "Description"}, "OenParamListDto": {"Description": "Bezeichnung", "Code": "<PERSON><PERSON><PERSON>", "VersionDate": "Versionsdatum", "VersionNumber": "Versionsnummer", "DownloadUrl": "Download Url", "CodeFunctionCat": "Kennung Funktionenkatalog", "CodeProductCat": "Kennung Bauproduktekatalog"}, "OenParamSetDto": {"Description": "Bezeichnung"}, "OenParamDto": {"Description": "Bezeichnung", "Code": "<PERSON><PERSON><PERSON>", "Type": "<PERSON><PERSON>", "NumberUnit": "Zahl Einheit", "NumberDecimalPlaces": "Zahl Nachkommastellen", "NumberMinValue": "Zahl Minimalwert", "NumberMaxValue": "Zahl Maximalwert", "NumberBuildSum": "Zahl Summe bilden", "NumberQuantityDependent": "<PERSON>ahl Mengenabhä<PERSON>"}, "OenParamHeadlineDto": {"Description": "Bezeichnung"}, "OenParamValueListDto": {"IsCustomValueAllowed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "OenParamValueDto": {"ValueText": "Text", "Description": "Bezeichnung", "ValueNumber": "<PERSON><PERSON>"}}, "uicontainer": {"boqStructure": {"newHauptgruppe": "Neue Hauptgruppe", "newObergruppe": "Neue Obergruppe", "newLeistungsgruppe": "Neue Leistungsgruppe", "newUnterleistungsgruppe": "Neue Unterleistungsgruppe", "newGrundtext": "<PERSON><PERSON><PERSON> Grundtext", "newFolgeposition": "Neue Folgeposition", "newUngeteiltePosition": "Neue Ungeteilte Position"}, "blobSpecification": {"al": "Ausschreiberlücke hinzufügen", "bl": "Echte Bieterlücke hinzufügen", "blo": "Unechte Bieterlücke hinzufügen"}, "textComplement": {"al": "Ausschreiberlücke", "bl": "<PERSON><PERSON><PERSON> Bieterlücke", "blo": "Unechte Bieterlücke"}, "contact": {"title": "Kontakt (ÖNORM)", "groupCompany": "Firma", "groupPerson": "Person", "groupContactPerson": "Kontaktperson", "createCompany": "Neuer Kontakt als Firma", "createPerson": "Neuer Kontakt als Person", "lvHeaderContext": "Kontext", "lvHeaderContextClient": "Auftraggeber", "lvHeaderContextProcUnit": "Vergebende Stelle", "lvHeaderContextCreator": "LV-Ersteller", "lvHeaderContextBidder": "Bieter/Auftragnehmer"}, "lvHeader": {"title": "LV Header (ÖNORM)", "groupDiscount": "Discount"}, "akz": {"title": "Auswertungskennzeichen AKZ (ÖNORM)"}, "zz": {"title": "Zuordnungskennzeichen ZZ (ÖNORM)"}, "zzVariant": {"title": "Variantennummer ZZ (ÖNORM)"}, "servicePart": {"title": " Leistungsteil (ÖNORM)"}, "parameter": {"title": "Parameter (ÖNORM)", "groupOenParamList": "Parameterliste", "groupOenParamSet": "Parameterset", "groupOenParamHeadlines": "Überschrift", "groupOenParam": "Parameter", "groupOenParamValueList": "<PERSON><PERSON>", "groupOenParamValue": "Wert"}}, "BasBlobsSpecificationFk": "Langtext", "BasBlobsSpecificationFk2": "Vorbemerkung", "BlobsCommentFk": "Kommentar", "BlobsCommentFk2": "Notiz", "BlobsLbChangeFk": "Änderungsbeschreibung", "LbMetadataContainerTitle": "LB Kenndaten (ÖNORM)", "LbMetadata": "LB Kenndaten (ÖNORM)", "graphicContainerTitle": "Grafik (ÖNORM)", "onlbImport": "Import Leistungsbeschreibungen", "onlvImport": "Import LV", "onlvExport": "Export LV", "onlbExport": "Export LB", "version": {"2021": "A2063:2021", "2015": "A2063:2015"}, "lbMetadataType": {"1": "LB Kenndaten", "2": "Ergänzungs-LB Kenndaten", "3": "LB Kenndaten Vorversion", "4": "Ergänzungs-LB Kenndaten Vorversion"}, "lvTypes": {"Draft": "<PERSON><PERSON><PERSON><PERSON>", "CostEstimate": "Kostenschätzung", "Tender": "Ausschreibung", "Bid": "<PERSON><PERSON><PERSON>", "AlternativeBid": "Alternativangebot", "ModifiedBid": "Abänderungsangebot", "Contract": "Vertrag", "Invoice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AdditionalBid": "Zusatzangebot", "AdjustedContract": "Vertragsanpassung"}}, "oenExtension": {"detailContainerTitle": "BoQ Details (OENORM)", "PhotoContainerTitle": "Reference Images"}, "BoqItemSubPriceDto": {"Description": "Description", "Approach": "Approach", "Price": "Price", "Totalprice": "Total", "Remark": "Remark"}, "priceSideComputation": {"title": "Price Side Computation"}, "QuantityInspector": {"Title": "Quantity Inspector", "Synchronize": "Synchronize Quantities", "SynchronizeSource": "Source", "SynchronizeTarget": "Destination", "Layout": "Layout", "NoSelection": "There is no selection!", "SourceEqualTarget": "Source and Destination are equal!", "TargetReadonly": "The Destination is read only!", "SynchronizationSucceeded": "The synchronization of the quantities in the selected items was completed successfully"}, "Backup": {"Create": "Create Bo<PERSON>up", "Restore": "Restore BoQ Backup", "Filter": "Filter <PERSON>ups", "Description": "Backup Description", "Comment": "Backup Comment", "Number": "Backup No", "Hint": "Caution! A backup of a BoQ only can be restored if the source BoQ does not have any dependent data in the modules QTO, estimate, procurement or sales.", "DeletionFailed": "The source BoQ of a backup cannot be deleted.", "CreationDisabled": "It is not possible to create a backup of a backup.", "CreationSucceeded": "The backup was created successfully.", "CreationFailed": "A backup cannot be created.", "RestoreDisabled": "This is not a BoQ backup. It is not possible to restore it.", "RestoreFailed": "The backup cannot be restored.", "RestoreSucceeded": "The restore was completed successfully. Of the former source BoQ a backup is created.", "RestoreQuestion": "Of the former source BoQ a backup will be created. Do you want to proceed?", "Boqs": "There are dependent BoQs", "Estimates": "There are dependent Estimates", "Qtos": "There are dependent QTOs"}, "copyTotalPrice": "Copy Total Price", "wizard": {"baseOn": "Base On", "updateBoq": "Update BoQ", "selected": "Selected"}, "oenpicture": {"format": "Format"}, "comparison": {"title": "BoQ Comparison", "settings": "Settings", "boqStructure": "BoQ Structure", "project": "Project", "package": "Package", "compareBoQ": "Compare BoQ", "boq": "BoQ", "baseBoQ": "Base BoQ", "compBoQ": "Comp<PERSON>", "compare": "Compare", "showDiffRowsOnly": "Only show the difference rows"}}}}