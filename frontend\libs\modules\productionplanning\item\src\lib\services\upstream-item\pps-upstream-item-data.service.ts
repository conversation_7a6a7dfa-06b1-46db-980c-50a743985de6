import { PlatformConfigurationService } from '@libs/platform/common';
import { DataServiceFlatNode, IDataServiceChildRoleOptions, ServiceRole } from '@libs/platform/data-access';
import { IPpsUpstreamItemEntity, PPSItemComplete, PpsUpstreamItemComplete } from '../../model/models';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { get, isNil, set } from 'lodash';
import { IPPSItemEntity } from '@libs/productionplanning/shared';
import { PpsItemDataService } from '../pps-item-data.service';

@Injectable({
	providedIn: 'root'
})
export class PpsUpstreamItemDataService extends DataServiceFlatNode<IPpsUpstreamItemEntity, PpsUpstreamItemComplete, IPPSItemEntity, PPSItemComplete> {

	private http = inject(HttpClient);
	private configService = inject(PlatformConfigurationService);

	private readonly ppsHeaderFkColumnName: string;
	private readonly ppsItemFkColumnName: string;
	private readonly mainItemColumnName: string;

	public onlyShowCurrentUpstreams: boolean = false;
	public listGuid: string = '';

	public constructor(private parentService: PpsItemDataService) {
		super({
			apiUrl: 'productionplanning/item/upstreamitem',
			roleInfo: <IDataServiceChildRoleOptions<IPpsUpstreamItemEntity, IPPSItemEntity, PPSItemComplete>>{
				role: ServiceRole.Node,
				itemName: 'PpsUpstreamItem',
				parent: parentService,
			},
			createInfo: {
				endPoint: 'create'
			},
			readInfo: {
				endPoint: 'list',
			},
			entityActions: {
				deleteSupported: false,
				createSupported: false,
			}
		});

		this.ppsHeaderFkColumnName = 'PPSHeaderFk';
		this.ppsItemFkColumnName = 'Id';
		this.mainItemColumnName = 'Id';

		this.processor.addProcessor([
			{
				process: (entity: IPpsUpstreamItemEntity) => {
					const parentSelection = this.getSelectedParent();
					const prjId = get(parentSelection, 'PrjProjectFk') ?? get(parentSelection, 'ProjectFk') ?? -1;
					entity.ProjectFk = prjId as number;
				},
				revertProcess() { },
			},
		]);
	}

	public copy() {
		const selection = this.getSelectedEntity();
		const parentSelection = this.parentService.getSelectedEntity();
		if (!selection || !parentSelection) {
			return;
		}

		this.http.post(this.configService.webApiBaseUrl + 'productionplanning/item/upstreamitem/create', {})
			.subscribe(response => {
				const newItem = response as IPpsUpstreamItemEntity;
				newItem.PpsHeaderFk = get(parentSelection, this.ppsHeaderFkColumnName) ?? selection.PpsHeaderFk;
				newItem.PpsItemFk = get(parentSelection, this.ppsItemFkColumnName) ?? null;
				newItem.PpsUpstreamGoodsTypeFk = selection.PpsUpstreamGoodsTypeFk;
				newItem.PpsUpstreamTypeFk = selection.PpsUpstreamTypeFk;
				newItem.UpstreamGoods = selection.UpstreamGoods;
				newItem.UomFk = selection.UomFk;

				// todo process
				this.append(newItem);
				this.select(newItem);
				this.setModified(newItem);
			});
	}

	public showListByFilter() {
		throw new Error('todo call the setFilterExtension in slick.rib.dataview.js');
		// if(_.isNil(service.listGuid)){  // if need to filter, pass the guid
		// 	return;
		// }
		// var parentSelected = parentService.getSelected();
		// if(_.isNil(parentSelected)){
		// 	return;
		// }
		// var ppsItemId = parentSelected[ppsItemColumn];  // in pps Header container, not need to filter
		// if(!_.isNil(ppsItemId)){
		// 	if(service.onlyShowCurrentUpstreams === true){
		// 		platformGridAPI.filters.extendFilterFunction(service.listGuid, function (item) {
		// 			return item.PpsItemFk === ppsItemId;
		// 		});
		// 	} else {
		// 		platformGridAPI.filters.extendFilterFunction(service.listGuid, function (item) {
		// 			return true;
		// 		});
		// 	}
		// }
	}

	public getPpsItem() {
		const parentSelected = this.parentService.getSelectedEntity();
		if (isNil(parentSelected)) {
			return undefined;
		}
		return get(parentSelected, this.ppsItemFkColumnName);
	}

	public override getModificationsFromUpdate(complete: PpsUpstreamItemComplete): IPpsUpstreamItemEntity[] {
		if (complete.PpsUpstreamItem === null) {
			return [];
		}
		return [complete.PpsUpstreamItem];
	}

	protected override provideLoadPayload() {
		const parentSelection = this.getSelectedParent();
		if (!parentSelection) {
			return { mainItemId: -1, ppsHeaderFk: -1 };
		}
		return {
			mainItemId: get(parentSelection, this.mainItemColumnName),
			ppsHeaderFk: get(parentSelection, this.ppsHeaderFkColumnName),
		};
	}

	protected override onLoadSucceeded(loaded: object): IPpsUpstreamItemEntity[] {
		if (loaded) {
			return get(loaded, 'Main', []);
		}
		return [];
	}

	public override isParentFn(parent: IPPSItemEntity, entity: IPpsUpstreamItemEntity): boolean {
		return entity.PpsItemFk === parent.Id;
	}

	public override createUpdateEntity(modified: IPpsUpstreamItemEntity | null): PpsUpstreamItemComplete {
		const complete = new PpsUpstreamItemComplete();
		if (modified != null) {
			complete.MainItemId = modified.Id;
			complete.PpsUpstreamItem = modified;
		}
		return complete;
	}

	public override registerByMethod(): boolean {
		return true;
	}

	public override getSavedEntitiesFromUpdate(complete: PPSItemComplete): IPpsUpstreamItemEntity[] {
		const ppsUpstreamItemToSave = get(complete, 'PpsUpstreamItemToSave') as PpsUpstreamItemComplete[];
		if (Array.isArray(ppsUpstreamItemToSave)) {
			return ppsUpstreamItemToSave.map(i => i.PpsUpstreamItem);
		}
		return [];
	}

	public override registerNodeModificationsToParentUpdate(parentUpdate: PPSItemComplete, modified: PpsUpstreamItemComplete[], deleted: IPpsUpstreamItemEntity[]): void {
		if (modified && modified.some(() => true)) {
			set(parentUpdate, 'PpsUpstreamItemToSave', modified);
		}
		if (deleted && deleted.some(() => true)) {
			set(parentUpdate, 'PpsUpstreamItemToDelete', deleted);
		}
	}
}