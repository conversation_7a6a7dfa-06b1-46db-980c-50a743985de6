using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Common;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace RIB.Visual.Procurement.Package.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public interface IEstLineItemEnhanceEntity : IEstLineItemEntity
	{

		/// <summary>
		/// QuantityAdj
		/// </summary>
		decimal? QuantityAdj { get; set; }
		/// <summary>
		/// BoqItemUomId
		/// </summary>
		int BoqItemUomId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		int PackageTaxCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		decimal BoqItemFactor { get; set; }

		/// <summary>
		/// BoQItemId
		/// </summary>
		public int BoQItemId { get; set; }

		/// <summary>
		/// BoQHeaderId
		/// </summary>
		public int BoQHeaderId { get; set; }
	}


	/// <summary>
	/// BoQ Item Linked Estimate LineItem
	/// </summary>
	public class BoQItemLinkedEstimateLineItem : IEstLineItemEnhanceEntity
	{

		/// <summary>
		/// 
		/// </summary>
		[NotMapped]
		public int? GccEstimateFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }

		/// <summary>
		/// BoQItemId
		/// </summary>
		public int BoQItemId { get; set; }

		/// <summary>
		/// BoQHeaderId
		/// </summary>
		public int BoQHeaderId { get; set; }

		/// <summary>
		/// EstLineItemCode
		/// </summary>
		public string EstLineItemCode { get; set; }

		/// <summary>
		/// EstLineItemDescription
		/// </summary>
		public string EstLineItemDescription { get; set; }

		/// <summary>
		/// WicGroupCode
		/// </summary>
		public string WicGroupCode { get; set; }

		/// <summary>
		/// WicGroupDescription
		/// </summary>
		public string WicGroupDescription { get; set; }

		/// <summary>
		/// BoqItemUomId
		/// </summary>
		public int BoqItemUomId { get; set; }

		/// <summary>
		/// QuantityAdj
		/// </summary>
		public decimal? QuantityAdj { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int PackageTaxCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal BoqItemFactor { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ActivityFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int BasUomFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int BasUomTargetFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? BoqHeaderFk
		{
			get
			{
				return BoQHeaderId;
			}
			set
			{
				BoQHeaderId = value.HasValue ? value.Value : -1;
			}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? BoqItemFk
		{
			get
			{
				return BoQItemId;
			}

			set
			{
				BoQItemId = value.HasValue ? value.Value : -1;
			}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? BoqWicCatFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Budget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal? BudgetMargin { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string Code { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? CosInstanceFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostFactor1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostFactor2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostTotal { get; set; }

		/// <summary>
		/// grand cost unit
		/// </summary>
		public decimal GrandCostUnit { get; set; }

		/// <summary>
		/// grand cost unit target
		/// </summary>
		public decimal GrandCostUnitTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal GrandTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal CostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstAssemblyFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstCostRiskFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int EstHeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstLineItemFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyRelActFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyRelBoqFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyRelGtuFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyTelAotFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal HoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsLumpsum { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsOptional { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LgmJobFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup1Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup2Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup3Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup4Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup5Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcAssetMasterFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcControllingUnitFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcCostCodeFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcMaterialFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrcPackage2HeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrcPackageFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrcStructureFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjChangeFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup1Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup2Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup3Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup4Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup5Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjLocationFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal ProductivityFactor { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PsdActivityFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Quantity { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor3 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor4 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public ICollection<IScriptEstResource> Resources { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode01Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode02Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode03Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode04Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode05Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode06Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode07Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode08Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode09Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode10Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined3 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined4 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined5 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? WicBoqHeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? WicBoqItemFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Revenue { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Margin { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int ProjectFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal TotalOfCostResources
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public BoQItemLinkedEstimateLineItem()
		{

		}

		/// <summary/>
		public int? ScheduleFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int Version { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal WqQuantityTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Margin1 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Margin2 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal RevenueUnit { get; set; }

		/// <summary/>
		public DateTime InsertedAt { get; set; }
		/// <summary/>
		public int InsertedBy { get; set; }
		/// <summary/>
		public int? UpdatedBy { get; set; }
		/// <summary/>
		public DateTime? UpdatedAt { get; set; }
		/// <summary/>
		public System.DateTime? FromDate { get; set; }
		/// <summary/>
		public System.DateTime? ToDate { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal BudgetUnit
		{
			get;
			set;
		}
		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedBudget
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedBudgetUnit
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public ICollection<int> PackageIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="source"></param>
		public BoQItemLinkedEstimateLineItem(IEstLineItemEntity entity, BoqSource source)
		{
			this.Id = entity.Id;
			this.Code = entity.Code;
			this.DescriptionInfo = entity.DescriptionInfo;
			this.EstHeaderFk = entity.EstHeaderFk;
			this.EstLineItemFk = entity.Id;
			switch (source)
			{
				case BoqSource.Project:
					{
						this.BoQHeaderId = entity.BoqHeaderFk.HasValue ? entity.BoqHeaderFk.Value : -1;
						this.BoQItemId = entity.BoqItemFk.HasValue ? entity.BoqItemFk.Value : -1;
					}
					break;
				case BoqSource.Wic:
					{
						this.BoQHeaderId = entity.WicBoqHeaderFk.HasValue ? entity.WicBoqHeaderFk.Value : -1;
						this.BoQItemId = entity.WicBoqItemFk.HasValue ? entity.WicBoqItemFk.Value : -1;
					}
					break;
				default:
					{
						this.BoQHeaderId = -1;
						this.BoQItemId = -1;
					}
					break;
			}
			this.QuantityTarget = entity.QuantityTarget;
			this.CostTotal = entity.CostTotal;
			this.EstQtyRelBoqFk = entity.EstQtyRelBoqFk;
			this.BoqWicCatFk = entity.BoqWicCatFk;
			this.BasUomTargetFk = entity.BasUomTargetFk;
			this.QuantityTotal = entity.QuantityTotal;
			this.DescriptionInfo = entity.DescriptionInfo;
			this.CostUnitTarget = entity.CostUnitTarget;
			this.IsLumpsum = entity.IsLumpsum;
			this.MdcMaterialFk = entity.MdcMaterialFk;
			this.BasUomFk = entity.BasUomFk;
			this.MdcControllingUnitFk = entity.MdcControllingUnitFk;
			this.MdcCostCodeFk = entity.MdcCostCodeFk;
			this.MdcAssetMasterFk = entity.MdcAssetMasterFk;
			this.PrcStructureFk = entity.PrcStructureFk;
			this.UserDefined1 = entity.UserDefined1;
			this.UserDefined2 = entity.UserDefined2;
			this.UserDefined3 = entity.UserDefined3;
			this.UserDefined4 = entity.UserDefined4;
			this.UserDefined5 = entity.UserDefined5;
			this.PrjCostGroup1Fk = entity.PrjCostGroup1Fk;
			this.PrjCostGroup2Fk = entity.PrjCostGroup2Fk;
			this.PrjCostGroup3Fk = entity.PrjCostGroup3Fk;
			this.PrjCostGroup4Fk = entity.PrjCostGroup4Fk;
			this.PrjCostGroup5Fk = entity.PrjCostGroup5Fk;
			this.LicCostGroup1Fk = entity.LicCostGroup1Fk;
			this.LicCostGroup2Fk = entity.LicCostGroup2Fk;
			this.LicCostGroup3Fk = entity.LicCostGroup3Fk;
			this.LicCostGroup4Fk = entity.LicCostGroup4Fk;
			this.LicCostGroup5Fk = entity.LicCostGroup5Fk;
			this.PrjLocationFk = entity.PrjLocationFk;
			this.WqQuantityTarget = entity.WqQuantityTarget;
			//this.PrcPackageFk = entity.PrcPackageFk;
			//this.PrcPackage2HeaderFk = entity.PrcPackage2HeaderFk;
			this.Budget = entity.Budget;
			this.BudgetUnit = entity.BudgetUnit;
			this.IsFixedBudget = entity.IsFixedBudget;
			this.IsFixedBudgetUnit = entity.IsFixedBudgetUnit;
			this.IsOptional = entity.IsOptional;
			this.IsOptionalIT = entity.IsOptionalIT;
			this.Quantity = entity.Quantity;
			this.QuantityFactor1 = entity.QuantityFactor1;
			this.QuantityFactor2 = entity.QuantityFactor2;
			this.QuantityFactor3 = entity.QuantityFactor3;
			this.QuantityFactor4 = entity.QuantityFactor4;
			this.ProductivityFactor = entity.ProductivityFactor;
			this.IsIncluded = entity.IsIncluded;
			this.DirCostUnit = entity.DirCostUnit;
			this.IndCostUnit = entity.IndCostUnit;
			this.CostFactor1 = entity.CostFactor1;
			this.CostFactor2 = entity.CostFactor2;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public BoQItemLinkedEstimateLineItem Copy()
		{
			var newEntity = new BoQItemLinkedEstimateLineItem();

			newEntity.Id = this.Id;
			newEntity.BoQItemId = this.BoQItemId;
			newEntity.BoQHeaderId = this.BoQHeaderId;
			newEntity.EstLineItemCode = this.EstLineItemCode;
			newEntity.EstLineItemDescription = this.EstLineItemDescription;
			newEntity.WicGroupCode = this.WicGroupCode;
			newEntity.WicGroupDescription = this.WicGroupDescription;
			newEntity.BoqItemUomId = this.BoqItemUomId;
			newEntity.QuantityAdj = this.QuantityAdj;
			newEntity.PackageTaxCodeFk = this.PackageTaxCodeFk;
			newEntity.ActivityFk = this.ActivityFk;
			newEntity.BasUomFk = this.BasUomFk;
			newEntity.BasUomTargetFk = this.BasUomTargetFk;
			newEntity.BoqWicCatFk = this.BoqWicCatFk;
			newEntity.Budget = this.Budget;
			newEntity.Code = this.Code;
			newEntity.CosInstanceFk = this.CosInstanceFk;
			newEntity.CostFactor1 = this.CostFactor1;
			newEntity.CostFactor2 = this.CostFactor2;
			newEntity.CostTotal = this.CostTotal;
			newEntity.GrandTotal = this.GrandTotal;
			newEntity.CostUnitTarget = this.CostUnitTarget;
			newEntity.DescriptionInfo = this.DescriptionInfo;
			newEntity.EstAssemblyFk = this.EstAssemblyFk;
			newEntity.EstCostRiskFk = this.EstCostRiskFk;
			newEntity.EstHeaderFk = this.EstHeaderFk;
			newEntity.EstLineItemFk = this.EstLineItemFk;
			newEntity.EstQtyRelActFk = this.EstQtyRelActFk;
			newEntity.EstQtyRelBoqFk = this.EstQtyRelBoqFk;
			newEntity.EstQtyRelGtuFk = this.EstQtyRelGtuFk;
			newEntity.EstQtyTelAotFk = this.EstQtyTelAotFk;
			newEntity.HoursTotal = this.HoursTotal;
			newEntity.IsLumpsum = this.IsLumpsum;
			newEntity.IsOptional = this.IsOptional;
			newEntity.LgmJobFk = this.LgmJobFk;
			newEntity.LicCostGroup1Fk = this.LicCostGroup1Fk;
			newEntity.LicCostGroup2Fk = this.LicCostGroup2Fk;
			newEntity.LicCostGroup3Fk = this.LicCostGroup3Fk;
			newEntity.LicCostGroup4Fk = this.LicCostGroup4Fk;
			newEntity.LicCostGroup5Fk = this.LicCostGroup5Fk;
			newEntity.MdcAssetMasterFk = this.MdcAssetMasterFk;
			newEntity.MdcControllingUnitFk = this.MdcControllingUnitFk;
			newEntity.WicBoqHeaderFk = this.MdcCostCodeFk;
			newEntity.MdcMaterialFk = this.MdcMaterialFk;
			newEntity.PrcPackage2HeaderFk = this.PrcPackage2HeaderFk;
			newEntity.PrcPackageFk = this.PrcPackageFk;
			newEntity.PrcStructureFk = this.PrcStructureFk;
			newEntity.PrjChangeFk = this.PrjChangeFk;
			newEntity.PrjCostGroup1Fk = this.PrjCostGroup1Fk;
			newEntity.PrjCostGroup2Fk = this.PrjCostGroup2Fk;
			newEntity.PrjCostGroup3Fk = this.PrjCostGroup3Fk;
			newEntity.PrjCostGroup4Fk = this.PrjCostGroup4Fk;
			newEntity.PrjCostGroup5Fk = this.PrjCostGroup5Fk;
			newEntity.PrjLocationFk = this.PrjLocationFk;
			newEntity.ProductivityFactor = this.ProductivityFactor;
			newEntity.PsdActivityFk = this.PsdActivityFk;
			newEntity.Quantity = this.Quantity;
			newEntity.QuantityFactor1 = this.QuantityFactor1;
			newEntity.QuantityFactor2 = this.QuantityFactor2;
			newEntity.QuantityFactor3 = this.QuantityFactor3;
			newEntity.QuantityFactor4 = this.QuantityFactor4;
			newEntity.QuantityTarget = this.QuantityTarget;
			newEntity.QuantityTotal = this.QuantityTotal;
			newEntity.SortCode01Fk = this.SortCode01Fk;
			newEntity.SortCode02Fk = this.SortCode02Fk;
			newEntity.SortCode03Fk = this.SortCode03Fk;
			newEntity.SortCode04Fk = this.SortCode04Fk;
			newEntity.SortCode05Fk = this.SortCode05Fk;
			newEntity.SortCode06Fk = this.SortCode06Fk;
			newEntity.SortCode07Fk = this.SortCode07Fk;
			newEntity.SortCode08Fk = this.SortCode08Fk;
			newEntity.SortCode09Fk = this.SortCode09Fk;
			newEntity.SortCode10Fk = this.SortCode10Fk;
			newEntity.UserDefined1 = this.UserDefined1;
			newEntity.UserDefined2 = this.UserDefined2;
			newEntity.UserDefined3 = this.UserDefined3;
			newEntity.UserDefined4 = this.UserDefined4;
			newEntity.UserDefined5 = this.UserDefined5;
			newEntity.WicBoqHeaderFk = this.WicBoqHeaderFk;
			newEntity.WicBoqItemFk = this.WicBoqItemFk;
			newEntity.Revenue = this.Revenue;
			newEntity.Margin = this.Margin;
			newEntity.ProjectFk = this.ProjectFk;
			newEntity.TotalOfCostResources = this.TotalOfCostResources;
			newEntity.ScheduleFk = this.ScheduleFk;
			newEntity.Version = this.Version;
			newEntity.WqQuantityTarget = this.WqQuantityTarget;
			newEntity.Margin1 = this.Margin1;
			newEntity.Margin2 = this.Margin2;
			newEntity.RevenueUnit = this.RevenueUnit;
			newEntity.InsertedAt = this.InsertedAt;
			newEntity.InsertedBy = this.InsertedBy;
			newEntity.UpdatedBy = this.UpdatedBy;
			newEntity.UpdatedAt = this.UpdatedAt;
			newEntity.FromDate = this.FromDate;
			newEntity.ToDate = this.ToDate;
			newEntity.BudgetUnit = this.BudgetUnit;
			newEntity.IsFixedBudget = this.IsFixedBudget;
			newEntity.IsFixedBudgetUnit = this.IsFixedBudgetUnit;
			newEntity.BoqItemFactor = this.BoqItemFactor;
			newEntity.IsOptionalIT = this.IsOptional;
			newEntity.Quantity = this.Quantity;
			newEntity.IsIncluded = this.IsIncluded;
			newEntity.DirCostUnit = this.DirCostUnit;
			newEntity.IndCostUnit = this.IndCostUnit;

			return newEntity;
		}

		/// <summary>
		/// 
		/// </summary>
		public string CostFactorDetail1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string CostFactorDetail2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal HoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal HoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string QuantityDetail { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string QuantityTargetDetail { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityUnitTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsDisabled { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EstAssemblyCatFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqSplitQuantityFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsGc { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsIncluded { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedPrice { get; set; }

		/// <summary>
		/// 
		/// </summary>
        public int? EstHeaderAssemblyFk { get ; set; }

		/// <summary>
		/// /
		/// </summary>
		public int? OrdHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsTemp { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsMNA { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Int32? BasCostGroupFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public ICollection<ICostGroupEntity> CostGroupList { get; set; }

		/// <summary>
		/// There are no comments for ManualMarkupUnitItem in the schema.
		/// </summary>
		public decimal ManualMarkupUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for ManualMarkupUnit in the schema.
		/// </summary>
		public decimal ManualMarkupUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for ManualMarkup in the schema.
		/// </summary>
		public decimal ManualMarkup
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AdvancedAllUnitItem in the schema.
		/// </summary>
		public decimal AdvancedAllUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AdvancedAllUnit in the schema.
		/// </summary>
		public decimal AdvancedAllUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AdvancedAll in the schema.
		/// </summary>
		public decimal AdvancedAll
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GcUnitItem in the schema.
		/// </summary>
		public decimal GcUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GcUnit in the schema.
		/// </summary>
		public decimal GcUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Gc in the schema.
		/// </summary>
		public decimal Gc
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GaUnitItem in the schema.
		/// </summary>
		public decimal GaUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GaUnit in the schema.
		/// </summary>
		public decimal GaUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Ga in the schema.
		/// </summary>
		public decimal Ga
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AmUnitItem in the schema.
		/// </summary>
		public decimal AmUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AmUnit in the schema.
		/// </summary>
		public decimal AmUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Am in the schema.
		/// </summary>
		public decimal Am
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for RpUnitItem in the schema.
		/// </summary>
		public decimal RpUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for RpUnit in the schema.
		/// </summary>
		public decimal RpUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Rp in the schema.
		/// </summary>
		public decimal Rp
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AllowanceUnitItem in the schema.
		/// </summary>
		public decimal AllowanceUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AllowanceUnit in the schema.
		/// </summary>
		public decimal AllowanceUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Allowance in the schema.
		/// </summary>
		public decimal Allowance
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Fm in the schema.
		/// </summary>
		public decimal Fm
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal URDUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for URD in the schema.
		/// </summary>
		public decimal URD
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for IsOptionalIT in the schema.
		/// </summary>
		public bool IsOptionalIT
		{
			get; set;
		}
		/// <summary>
		/// 
		/// </summary>
		public String PackageAssignments
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public string ExternalCode { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Int32? PlantAssemblyTypeFk { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public class ScriptEstimateLineItem : IScriptEstLineItem
	{
		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }


		/// <summary>
		/// EstLineItemCode
		/// </summary>
		public string EstLineItemCode { get; set; }

		/// <summary>
		/// EstLineItemDescription
		/// </summary>
		public string EstLineItemDescription { get; set; }

		/// <summary>
		/// WicGroupCode
		/// </summary>
		public string WicGroupCode { get; set; }

		/// <summary>
		/// WicGroupDescription
		/// </summary>
		public string WicGroupDescription { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ActivityFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int BasUomFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int BasUomTargetFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? BoqHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqItemFk { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public int? BoqWicCatFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Budget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal? BudgetMargin { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string Code { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? CosInstanceFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostFactor1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostFactor2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal GrandTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal CostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstAssemblyFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstCostRiskFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int EstHeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstLineItemFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyRelActFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyRelBoqFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyRelGtuFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? EstQtyTelAotFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal HoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsLumpsum { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsOptional { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LgmJobFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup1Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup2Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup3Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup4Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? LicCostGroup5Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcAssetMasterFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcControllingUnitFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcCostCodeFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? MdcMaterialFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrcPackage2HeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrcPackageFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrcStructureFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjChangeFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup1Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup2Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup3Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup4Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjCostGroup5Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PrjLocationFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal ProductivityFactor { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PsdActivityFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Quantity { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor3 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityFactor4 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public ICollection<IScriptEstResource> Resources { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode01Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode02Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode03Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode04Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode05Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode06Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode07Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode08Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode09Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? SortCode10Fk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined3 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined4 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string UserDefined5 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? WicBoqHeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? WicBoqItemFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Revenue { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal Margin { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int ProjectFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal TotalOfCostResources
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public ScriptEstimateLineItem()
		{

		}

		/// <summary/>
		public int? ScheduleFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int Version { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal WqQuantityTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Margin1 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Margin2 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal RevenueUnit { get; set; }

		/// <summary/>
		public DateTime InsertedAt { get; set; }
		/// <summary/>
		public int InsertedBy { get; set; }
		/// <summary/>
		public int? UpdatedBy { get; set; }
		/// <summary/>
		public DateTime? UpdatedAt { get; set; }
		/// <summary/>
		public System.DateTime? FromDate { get; set; }
		/// <summary/>
		public System.DateTime? ToDate { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal BudgetUnit
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		[NotMapped]
		public int? GccEstimateFk { get; set; }


		#region from script line item
		/// <summary>
		/// 
		/// </summary>
		public decimal? EscalationCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal? EscalationCostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? RiskCostUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal? RiskCostTotal { get; set; }

		//
		// Summary:
		//     There are no comments for LineItemType in the schema.
		/// <summary>
		/// 
		/// </summary>
		public int LineItemType { get; set; }

		//
		// Summary:
		//     There are no comments for QuantityFactorDetail1 in the schema.
		/// <summary>
		/// 
		/// </summary>
		public string QuantityFactorDetail1 { get; set; }

		//
		// Summary:
		//     There are no comments for QuantityFactorDetail2 in the schema.
		/// <summary>
		/// 
		/// </summary>
		public string QuantityFactorDetail2 { get; set; }

		//
		// Summary:
		//     There are no comments for ProductivityFactorDetail in the schema.
		/// <summary>
		/// 
		/// </summary>
		public string ProductivityFactorDetail { get; set; }

		//
		// Summary:
		//     There are no comments for MdcWorkCategoryFk in the schema.
		/// <summary>
		/// 
		/// </summary>
		public int? MdcWorkCategoryFk { get; set; }

		//
		// Summary:
		//     There are no comments for IsChecked in the schema.
		/// <summary>
		/// 
		/// </summary>
		public bool IsChecked { get; set; }

		//
		// Summary:
		//     There are no comments for CommentText in the schema.
		/// <summary>
		/// 
		/// </summary>
		public string CommentText { get; set; }

		//
		// Summary:
		//     There are no comments for CosInsHeaderFk in the schema.
		/// <summary>
		/// 
		/// </summary>
		public int? CosInsHeaderFk { get; set; }

		//
		// Summary:
		//     There are no comments for Hint in the schema.
		/// <summary>
		/// 
		/// </summary>
		public string Hint { get; set; }

		//
		// Summary:
		//     There are no comments for IsNoMarkup in the schema.
		/// <summary>
		/// 
		/// </summary>
		public bool IsNoMarkup { get; set; }

		//
		// Summary:
		//     There are no comments for IsFixedBudget in the schema.
		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedBudget { get; set; }

		//
		// Summary:
		//     There are no comments for IsFixedBudgetUnit in the schema.
		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedBudgetUnit { get; set; }

		//
		// Summary:
		//     There are no comments for CosMatchText in the schema.
		/// <summary>
		/// 
		/// </summary>
		public string CosMatchText { get; set; }

		//
		// Summary:
		//     There are no comments for BudgetDifference in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal? BudgetDifference { get; set; }

		//
		// Summary:
		//     There are no comments for FormFk in the schema.
		/// <summary>
		/// 
		/// </summary>
		public int? FormFk { get; set; }

		//
		// Summary:
		//     There are no comments for AdvancedAllowanceCostUnit in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal AdvancedAllowanceCostUnit { get; set; }

		//
		// Summary:
		//     There are no comments for AdvancedAllowance in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal AdvancedAllowance { get; set; }

		//
		// Summary:
		//     There are no comments for CostExchangeRate1 in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal? CostExchangeRate1 { get; set; }

		//
		// Summary:
		//     There are no comments for CostExchangeRate2 in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal? CostExchangeRate2 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string WqQuantityTargetDetail { get; set; }

		//
		// Summary:
		//     There are no comments for EstLineItemStatusFk in the schema.
		/// <summary>
		/// 
		/// </summary>
		public int? EstLineItemStatusFk { get; set; }

		//
		// Summary:
		//     Save model object info related to line item
		/// <summary>
		/// 
		/// </summary>
		public ICollection<IScriptEstLineItem2MdlObject> ModelObjects { get; set; }

		//
		// Summary:
		//     COS script, setCharacteristic
		/// <summary>
		/// 
		/// </summary>
		public ICollection<ICharacteristicDataEntity> Characteristic1List { get; set; }

		//
		// Summary:
		//     COS script, setCharacteristic2
		/// <summary>
		/// 
		/// </summary>
		public ICollection<ICharacteristicDataEntity> Characteristic2List { get; set; }

		//
		// Summary:
		//     CompareFlag (just for line item comparison)
		/// <summary>
		/// 
		/// </summary>
		public short CompareFlag { get; set; }

		//
		// Summary:
		//     LineItem2Objects (just for line item comparison) used to judge two different
		//     line item are the same (should have the same line item objects)
		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<IEstLineItem2MdlObjectEntity> LineItem2Objects { get; set; }

		//
		// Summary:
		//     There are no comments for GrandCostUnit in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal GrandCostUnit { get; set; }

		//
		// Summary:
		//     There are no comments for GrandCostUnitTarget in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal GrandCostUnitTarget { get; set; }

		//
		// Summary:
		//     There are no comments for DayWorkRateTotal in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal DayWorkRateTotal { get; set; }

		//
		// Summary:
		//     There are no comments for DayWorkRateUnit in the schema.
		/// <summary>
		/// 
		/// </summary>
		public decimal DayWorkRateUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IUserDefinedcolValEntity UserDefinedcolValEntity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsStandardItem { get; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsOptionItemWithIT { get; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsOptionItemWithoutIT { get; }

		/// <summary>
		/// 
		/// </summary>
		public decimal GcInternal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal GaInternal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal AmInternal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal RpInternal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EstRuleSourceFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int AssemblyType { get; set; }

		#endregion from script line item

		/// <summary>
		/// 
		/// </summary>
		public ICollection<int> PackageIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		public ScriptEstimateLineItem(IEstLineItemEntity entity)
		{
			this.EstHeaderFk = entity.EstHeaderFk;
			this.Id = entity.Id;
			this.EstHeaderAssemblyFk = entity.EstHeaderAssemblyFk;
			this.EstLineItemFk = entity.EstLineItemFk;
			this.EstAssemblyCatFk = entity.EstAssemblyCatFk;
			this.EstAssemblyFk = entity.EstAssemblyFk;
			this.Code = entity.Code;
			this.DescriptionInfo = (DescriptionTranslateType)entity.DescriptionInfo.Clone();
			this.QuantityTarget = entity.QuantityTarget;
			this.QuantityTargetDetail = entity.QuantityTargetDetail;
			this.BasUomTargetFk = entity.BasUomTargetFk;
			this.QuantityDetail = entity.QuantityDetail;
			this.Quantity = entity.Quantity;
			this.BasUomFk = entity.BasUomFk;
			this.QuantityFactor1 = entity.QuantityFactor1;
			this.QuantityFactor2 = entity.QuantityFactor2;
			this.QuantityFactor3 = entity.QuantityFactor3;
			this.QuantityFactor4 = entity.QuantityFactor4;
			this.ProductivityFactor = entity.ProductivityFactor;
			this.QuantityUnitTarget = entity.QuantityUnitTarget;
			this.QuantityTotal = entity.QuantityTotal;
			this.CostUnit = entity.CostUnit;
			this.EntCostUnit = entity.EntCostUnit;
			this.DruCostUnit = entity.DruCostUnit;
			this.DirCostUnit = entity.DirCostUnit;
			this.IndCostUnit = entity.IndCostUnit;
			this.CostFactorDetail1 = entity.CostFactorDetail1;
			this.CostFactor1 = entity.CostFactor1;
			this.CostFactorDetail2 = entity.CostFactorDetail2;
			this.CostFactor2 = entity.CostFactor2;
			this.CostUnitTarget = entity.CostUnitTarget;
			this.EntCostUnitTarget = entity.EntCostUnitTarget;
			this.DruCostUnitTarget = entity.DruCostUnitTarget;
			this.DirCostUnitTarget = entity.DirCostUnitTarget;
			this.IndCostUnitTarget = entity.IndCostUnitTarget;
			this.CostTotal = entity.CostTotal;
			this.EntCostTotal = entity.EntCostTotal;
			this.DruCostTotal = entity.DruCostTotal;
			this.DirCostTotal = entity.DirCostTotal;
			this.IndCostTotal = entity.IndCostTotal;
			this.EstQtyRelBoqFk = entity.EstQtyRelBoqFk;
			this.EntHoursUnit = entity.EntHoursUnit;
			this.DruHoursUnit = entity.DruHoursUnit;
			this.DirHoursUnit = entity.DirHoursUnit;
			this.IndHoursUnit = entity.IndHoursUnit;
			this.EstQtyRelActFk = entity.EstQtyRelActFk;
			this.EntHoursUnitTarget = entity.EntHoursUnitTarget;
			this.DruHoursUnitTarget = entity.DruHoursUnitTarget;
			this.DirHoursUnitTarget = entity.DirHoursUnitTarget;
			this.IndHoursUnitTarget = entity.IndHoursUnitTarget;
			this.EstQtyRelGtuFk = entity.EstQtyRelGtuFk;
			this.EntHoursTotal = entity.EntHoursTotal;
			this.DruHoursTotal = entity.DruHoursTotal;
			this.DirHoursTotal = entity.DirHoursTotal;
			this.IndHoursTotal = entity.IndHoursTotal;
			this.EstQtyTelAotFk = entity.EstQtyTelAotFk;
			this.HoursUnit = entity.HoursUnit;
			this.HoursUnitTarget = entity.HoursUnitTarget;
			this.HoursTotal = entity.HoursTotal;
			this.MdcControllingUnitFk = entity.MdcControllingUnitFk;
			this.EstCostRiskFk = entity.EstCostRiskFk;
			this.BoqHeaderFk = entity.BoqHeaderFk;
			this.BoqItemFk = entity.BoqItemFk;
			this.PsdActivityFk = entity.PsdActivityFk;
			this.MdcAssetMasterFk = entity.MdcAssetMasterFk;
			this.PrjLocationFk = entity.PrjLocationFk;
			//this.PrcPackageFk = entity.PrcPackageFk;
			//this.PrcPackage2HeaderFk = entity.PrcPackage2HeaderFk;
			this.UserDefined1 = entity.UserDefined1;
			this.UserDefined2 = entity.UserDefined2;
			this.UserDefined3 = entity.UserDefined3;
			this.UserDefined4 = entity.UserDefined4;
			this.UserDefined5 = entity.UserDefined5;
			this.IsLumpsum = entity.IsLumpsum;
			this.IsDisabled = entity.IsDisabled;
			this.UpdatedAt = entity.UpdatedAt;
			this.InsertedAt = entity.InsertedAt;
			this.IsTemp = entity.IsTemp;
			this.IsGc = entity.IsGc;
			this.PrcStructureFk = entity.PrcStructureFk;
			this.CosInstanceFk = entity.CosInstanceFk;
			this.PrjChangeFk = entity.PrjChangeFk;
			this.FromDate = entity.FromDate;
			this.ToDate = entity.ToDate;
			this.MdcCostCodeFk = entity.MdcCostCodeFk;
			this.MdcMaterialFk = entity.MdcMaterialFk;
			this.InsertedBy = entity.InsertedBy;
			this.UpdatedBy = entity.UpdatedBy;
			this.SortCode01Fk = entity.SortCode01Fk;
			this.SortCode02Fk = entity.SortCode02Fk;
			this.SortCode03Fk = entity.SortCode03Fk;
			this.SortCode04Fk = entity.SortCode04Fk;
			this.SortCode05Fk = entity.SortCode05Fk;
			this.SortCode06Fk = entity.SortCode06Fk;
			this.SortCode07Fk = entity.SortCode07Fk;
			this.SortCode08Fk = entity.SortCode08Fk;
			this.SortCode09Fk = entity.SortCode09Fk;
			this.SortCode10Fk = entity.SortCode10Fk;
			this.Version = entity.Version;
			this.OrdHeaderFk = entity.OrdHeaderFk;
			this.Budget = entity.Budget;
			this.BudgetUnit = entity.BudgetUnit;
			this.IsFixedBudget = entity.IsFixedBudget;
			this.IsFixedBudgetUnit = entity.IsFixedBudgetUnit;
			this.IsOptional = entity.IsOptional;
			this.LgmJobFk = entity.LgmJobFk;
			this.WicBoqHeaderFk = entity.WicBoqHeaderFk;
			this.WicBoqItemFk = entity.WicBoqItemFk;
			this.BoqWicCatFk = entity.BoqWicCatFk;
			this.Revenue = entity.Revenue;
			this.WqQuantityTarget = entity.WqQuantityTarget;
			this.RevenueUnit = entity.RevenueUnit;
			this.Margin1 = entity.Margin1;
			this.Margin2 = entity.Margin2;
			this.MarkupCostUnit = entity.MarkupCostUnit;
			this.MarkupCostUnitTarget = entity.MarkupCostUnitTarget;
			this.MarkupCostTotal = entity.MarkupCostTotal;
			this.GrandTotal = entity.GrandTotal;
			this.BoqSplitQuantityFk = entity.BoqSplitQuantityFk;
			this.IsIncluded = entity.IsIncluded;
			this.IsFixedPrice = entity.IsFixedPrice;
			this.IsMNA = entity.IsMNA;
			this.ManualMarkupUnitItem = entity.ManualMarkupUnitItem;
			this.ManualMarkupUnit = entity.ManualMarkupUnit;
			this.ManualMarkup = entity.ManualMarkup;
			this.AdvancedAllUnitItem = entity.AdvancedAllUnitItem;
			this.AdvancedAllUnit = entity.AdvancedAllUnit;
			this.AdvancedAll = entity.AdvancedAll;
			this.GcUnitItem = entity.GcUnitItem;
			this.GcUnit = entity.GcUnit;
			this.Gc = entity.Gc;
			this.GaUnitItem = entity.GaUnitItem;
			this.GaUnit = entity.GaUnit;
			this.Ga = entity.Ga;
			this.AmUnitItem = entity.AmUnitItem;
			this.AmUnit = entity.AmUnit;
			this.Am = entity.Am;
			this.RpUnitItem = entity.RpUnitItem;
			this.RpUnit = entity.RpUnit;
			this.Rp = entity.Rp;
			this.AllowanceUnitItem = entity.AllowanceUnitItem;
			this.AllowanceUnit = entity.AllowanceUnit;
			this.Allowance = entity.Allowance;
			this.Fm = entity.Fm;
			this.URDUnitItem = entity.URDUnitItem;
			this.URD = entity.URD;
			this.IsOptionalIT = entity.IsOptionalIT;
		}

		/// <summary>
		/// 
		/// </summary>
		public string CostFactorDetail1 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string CostFactorDetail2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal CostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DirHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal DruHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal EntHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal HoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal HoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndHoursTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndHoursUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal IndHoursUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostTotal { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostUnit { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostUnitTarget { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string QuantityDetail { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public string QuantityTargetDetail { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityUnitTarget { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsDisabled { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EstAssemblyCatFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? BoqSplitQuantityFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsGc { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsIncluded { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsFixedPrice { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EstHeaderAssemblyFk { get; set; }

		/// <summary>
		/// /
		/// </summary>
		public int? OrdHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsTemp { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsDaywork { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsMNA { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Int32? BasCostGroupFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public ICollection<ICostGroupEntity> CostGroupList { get; set; }

		/// <summary>
		/// There are no comments for ManualMarkupUnitItem in the schema.
		/// </summary>
		public decimal ManualMarkupUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for ManualMarkupUnit in the schema.
		/// </summary>
		public decimal ManualMarkupUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for ManualMarkup in the schema.
		/// </summary>
		public decimal ManualMarkup
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AdvancedAllUnitItem in the schema.
		/// </summary>
		public decimal AdvancedAllUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AdvancedAllUnit in the schema.
		/// </summary>
		public decimal AdvancedAllUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AdvancedAll in the schema.
		/// </summary>
		public decimal AdvancedAll
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GcUnitItem in the schema.
		/// </summary>
		public decimal GcUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GcUnit in the schema.
		/// </summary>
		public decimal GcUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Gc in the schema.
		/// </summary>
		public decimal Gc
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GaUnitItem in the schema.
		/// </summary>
		public decimal GaUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for GaUnit in the schema.
		/// </summary>
		public decimal GaUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Ga in the schema.
		/// </summary>
		public decimal Ga
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AmUnitItem in the schema.
		/// </summary>
		public decimal AmUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AmUnit in the schema.
		/// </summary>
		public decimal AmUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Am in the schema.
		/// </summary>
		public decimal Am
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for RpUnitItem in the schema.
		/// </summary>
		public decimal RpUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for RpUnit in the schema.
		/// </summary>
		public decimal RpUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Rp in the schema.
		/// </summary>
		public decimal Rp
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AllowanceUnitItem in the schema.
		/// </summary>
		public decimal AllowanceUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for AllowanceUnit in the schema.
		/// </summary>
		public decimal AllowanceUnit
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Allowance in the schema.
		/// </summary>
		public decimal Allowance
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for Fm in the schema.
		/// </summary>
		public decimal Fm
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal URDUnitItem
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for URD in the schema.
		/// </summary>
		public decimal URD
		{
			get; set;
		}


		/// <summary>
		/// There are no comments for IsOptionalIT in the schema.
		/// </summary>
		public bool IsOptionalIT
		{
			get; set;
		}
		/// <summary>
		/// 
		/// </summary>
		public String PackageAssignments
		{
			get; set;
		}
		/// <summary>
		/// There are no comments for Co2SourceTotal in the schema.
		/// </summary>
		public decimal? Co2SourceTotal
		{
			get;
			set;
		}

		/// <summary>
		/// There are no comments for Co2ProjectTotal in the schema.
		/// </summary>
		public decimal? Co2ProjectTotal
		{
			get;
			set;
		}

		/// <summary>
		/// There are no comments for Co2TotalVariance in the schema.
		/// </summary>
		public decimal? Co2TotalVariance
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public string ExternalCode { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Int32? PlantAssemblyTypeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public ICollection<IEstimateRuleCommonParamEntity> EstLineItemParameterList { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsFromBid {  get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public enum BoqSource
	{
		/// <summary>
		/// 
		/// </summary>
		Project,
		/// <summary>
		/// 
		/// </summary>
		Wic,
		/// <summary>
		/// 
		/// </summary>
		LineItem
	}
}
