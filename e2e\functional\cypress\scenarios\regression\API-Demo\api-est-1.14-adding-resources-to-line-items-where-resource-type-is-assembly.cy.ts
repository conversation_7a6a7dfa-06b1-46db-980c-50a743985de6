import apiConstantData from 'cypress/constantData/apiConstantData';
import { tile, app, cnt, sidebar, commonLocators, btn, apiParameters } from 'cypress/locators';
import { _common, _estimatePage, _validate, _mainView, _commonAPI } from 'cypress/pages';
import { DataCells } from 'cypress/pages/interfaces';

// VARIABLES----------------------------------------------------------------
const ESTIMATE_CODE = '1' + Cypress._.random(0, 999);
const ESTIMATE_DESCRIPTION = 'EST-DESC-' + Cypress._.random(0, 999);
const LINE_ITEM_DESCRIPTION = 'LI-DESC-' + Cypress._.random(0, 999);

let ESTIMATE_PARAMETERS: DataCells;
let CONTAINERS_ESTIMATE;
let CONTAINER_COLUMNS_ESTIMATE;
let LINE_ITEM_PARAMETERS: DataCells;
let CONTAINERS_LINE_ITEM;
let CONTAINER_COLUMNS_LINE_ITEM;
let RESOURCE_PARAMETERS: DataCells;
let CONTAINERS_RESOURCE;
let CONTAINER_COLUMNS_RESOURCE;


describe('EST- 1.14 | Adding Resources to Line Items where Resource Type is Assembly', () => {
	beforeEach(() => {
		cy.clearCookies();
		cy.clearLocalStorage();
		cy.WaitUntilLoaderComplete_Trial();
		cy.waitUntilDOMLoaded();
	});
	afterEach(() => {
		cy.clearCookies();
		cy.clearLocalStorage();
		cy.WaitUntilLoaderComplete_Trial();
		cy.waitUntilDOMLoaded();
	});
	before(function () {
		cy.fixture('estimate/est-1.14-adding-resource-to-line-item-where-resource-type-is-assembly.json')
			.then((data) => {
				this.data = data;
				CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
				CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE;
				ESTIMATE_PARAMETERS = {
					[app.GridCells.CODE]: ESTIMATE_CODE,
					[app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
					[app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
					[app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE,
				};
				CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM;
				CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM;
				LINE_ITEM_PARAMETERS = {
					[app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
					[app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY,
					[app.GridCells.BAS_UOM_FK]: '1097',
				};
				CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE;
				CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE;
				RESOURCE_PARAMETERS = {
					[app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
					[app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE,
					[app.GridCells.QUANTITY_SMALL]: CONTAINERS_RESOURCE.QUANTITY,
				};
			})
			.then(() => {
				cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
				_common.openDesktopTile(tile.DesktopTiles.PROJECT);
				_common.waitForLoaderToDisappear();
				_common.openTab(app.TabBar.PROJECT).then(() => {
					_common.setDefaultView(app.TabBar.PROJECT);
					_common.waitForLoaderToDisappear();
					_common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
				});
				_commonAPI.getAccessToken().then((result) => {
					cy.log(`Token Retrieved: ${result.token}`);
				});
			});
	});

	after(() => {
		cy.LOGOUT();
	});

	it('TC - API: Create project', function () {
		_commonAPI.createProject().then(() => {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
		});
	});
	it('TC - API: Create estimate header', function () {
		_commonAPI.createEstimateHeader(Cypress.env('API_PROJECT_ID_1'));

	});

	it('TC - API: Create new line item record', function () {
		cy.log(`Estimate Description ===> ${Cypress.env('API_EST_DESCRIPTION_1')}`)

		_common.openTab(app.TabBar.ESTIMATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
		});
		_common.waitForLoaderToDisappear();
		_common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
		_common.search_inSubContainer(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
		_common.select_rowHasValue(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
		_common.waitForLoaderToDisappear();
		_common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
		_common.waitForLoaderToDisappear();
		_commonAPI.createEstimateLineItems(Cypress.env('API_EST_ID_1'), LINE_ITEM_PARAMETERS);
		_common.waitForLoaderToDisappear();
	});

	it('TC - Create new record in resource', function () {
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
			_common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS);
			_common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_DESCRIPTION)
			_common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
		});
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
			_common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
			_common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE);
		});
		_common.maximizeContainer(cnt.uuid.RESOURCES);
		_common.clear_subContainerFilter(cnt.uuid.RESOURCES);
		_common.create_newRecord(cnt.uuid.RESOURCES);
		_common.waitForLoaderToDisappear();
		_estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
		_common.minimizeContainer(cnt.uuid.RESOURCES);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.RESOURCES)
		_common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
		_common.waitForLoaderToDisappear();
		_common.select_rowInContainer(cnt.uuid.RESOURCES)
		_common.waitForLoaderToDisappear();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.RESOURCES, app.GridCells.COST_TOTAL, 'COST_TOTAL');
	});

	it('TC - Validate Cost Total in line item', function () {
		_common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
		});
		_common.waitForLoaderToDisappear();
		_common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS);
		_common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
		_common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_DESCRIPTION);
		_common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.COST_TOTAL, Cypress.env('COST_TOTAL'));
	});
});
