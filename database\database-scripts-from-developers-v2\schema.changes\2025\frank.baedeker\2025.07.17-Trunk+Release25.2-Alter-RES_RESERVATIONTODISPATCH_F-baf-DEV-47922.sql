-----------------------------------------
-- Ignore Errors: Off
-- Jira Number:   DEV-42709
-- Script Type:   Required Schema Change
-- Reason:        TVF for effective evaluation of due reservations
--                Vanilla Data for wizard 
-- Install On:    All Trunk, Release25.2
-----------------------------------------

-- TVF for finding the reservation done before
EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC 'RES_RESERVEDTODISPATCHBYRESERVE_F'
GO

CREATE FUNCTION [dbo].[RES_RESERVEDTODISPATCHBYRESERVE_F] (@fromDate date, @toDate date, @company int)
RETURNS TABLE
RETURN

SELECT
-- Info from reservation 
 candidate.ID AS Id,
 candidate.RESERVED_FROM AS ReservedFrom,
 candidate.RESERVED_TO AS ReservedTo,
 candidate.RES_RESERVATIONTYPE_FK AS ReservationTypeFk,
 candidate.RES_RESERVATIONSTATUS_FK AS ReservationStatusFk,
 candidate.QUANTITY AS Quantity,
 candidate.BAS_UOM_FK AS UomFk,
 candidate.COMMENT_TEXT AS [Comment],
 candidate.PRJ_PROJECT_FK AS ReceivingProjectFk,
 candidate.LGM_JOB_FK AS ReceivingJobFk,

-- Info from the requisition it is for
 requisition.ID AS RequisitionFk,
 reqstate.ID AS RequisitionStatusFk,
 requisition.REQUESTED_FROM AS RequestedFrom,
 requisition.REQUESTED_TO AS RequestedTo,
 requisition.RES_TYPE_FK AS RequiredResourceTypeFk,
 requisition.RES_RESOURCE_FK AS RequiredResourceFk,
 requisition.ETM_WORKOPERATIONTYPEEST_FK AS WorkOperationTypeFk,
 requisition.QUANTITY AS RequiredQuantity,
 requisition.BAS_UOM_FK AS UomRequiredFk,
 requisition.PRJ_DROPPOINT_FK AS ReceivingDropPointFk,

-- Info on reserved resource 
 reservedres.ID AS ReservedResourceFk,
 reservedtype.ID AS ReservedResourceTypeFk,
 reservedtype.ISTRANSPORTPERMISSION AS IsTransportPermission,
 reservedplant.ID AS ReservedPlantFk,
 reservedplant.TRANSPORTWEIGHT AS TransportWeight,

-- Info on last project / reservation
 lastreservation.ID AS LastReservationFk,
 lastrequisition.PRJ_DROPPOINT_FK AS PerformingDropPointFk,
 lastprojectreserved.ID AS PerformingProjectFk,
 lastprojectreserved.ISADMINISTRATION AS IsAdministration,
 lastreservation.LGM_JOB_FK AS PerformingJobFk,
 dispactivityreserved.ID AS DispatchActivityFk,
 disprubcatreserved.BAS_RUBRIC_CATEGORY_FK AS DispatchRubricCategoryFk,
 
-- History of reservation 
 candidate.INSERTED AS InsertedAt,
 candidate.WHOISR AS InsertedBy,
 candidate.UPDATED AS UpdatedAt,
 candidate.WHOUPD AS UpdatedBy,
 candidate.VERSION AS Version

FROM
 RES_RESERVATION candidate
 INNER JOIN BAS_COMPANY comp ON comp.ID=@company AND comp.ID=candidate.BAS_COMPANY_FK
 INNER JOIN RES_REQUISITION requisition ON requisition.ID = candidate.RES_REQUISITION_FK
 INNER JOIN RES_REQUISITIONSTATUS reqstate ON reqstate.ID=requisition.RES_REQUISITIONSTATUS_FK AND reqstate.ISCANCELED=0
 INNER JOIN RES_RESOURCE reservedres ON reservedres.ID = candidate.RES_RESOURCE_FK
 INNER JOIN RES_TYPE reservedtype ON reservedtype.ID = reservedres.RES_TYPE_FK
 INNER JOIN RES_RESOURCE2ETM_PLANT plresreserved on plresreserved.RES_RESOURCE_FK=candidate.RES_RESOURCE_FK --and plresreserved.BAS_ETM_CONTEXT_FK=division.BAS_ETM_CONTEXT_FK
 INNER JOIN ETM_PLANT reservedplant on reservedplant.ID=plresreserved.ETM_PLANT_FK-- and plant.BAS_ETM_CONTEXT_FK=comp.BAS_ETM_CONTEXT_FK
 OUTER APPLY (
  SELECT TOP(1) ID FROM RES_RESERVATION older
   WHERE older.RES_RESOURCE_FK=candidate.RES_RESOURCE_FK AND older.RESERVED_TO<candidate.RESERVED_TO
   ORDER BY RESERVED_TO DESC
 ) thelastreserved
 INNER JOIN RES_RESERVATION lastreservation ON lastreservation.ID=thelastreserved.ID
 INNER JOIN RES_REQUISITION lastrequisition ON lastrequisition.ID=lastreservation.RES_REQUISITION_FK
 INNER JOIN PRJ_PROJECT lastprojectreserved ON lastprojectreserved.ID=lastreservation.PRJ_PROJECT_FK
 INNER JOIN LGM_DISPATCHACTIVITY dispactivityreserved ON (dispactivityreserved.ID=1 AND lastprojectreserved.ISADMINISTRATION=1) OR
  (dispactivityreserved.ID=2 AND lastprojectreserved.ISADMINISTRATION=0)
 INNER JOIN LGM_DISPATCHACTRUBCAT disprubcatreserved ON disprubcatreserved.LGM_DISPATCHACTIVITY_FK=dispactivityreserved.ID AND disprubcatreserved.LGM_DISPATCH_HEADER_TYPE_FK=3

WHERE
 candidate.BAS_COMPANY_FK = @company
 AND NOT EXISTS (SELECT ID FROM LGM_DISPATCH_RECORD WHERE RES_RESERVATION_FK=candidate.ID)
 AND candidate.RESERVED_FROM >= @fromDate
 AND candidate.RESERVED_FROM < @toDate
GO


-- TVF for finding plants on droppoints
EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC 'RES_RESERVEDTODISPATCHBYDRPPNT_F'
GO

CREATE FUNCTION [dbo].[RES_RESERVEDTODISPATCHBYDRPPNT_F] (@fromDate date, @toDate date, @company int)
RETURNS TABLE
RETURN

SELECT
-- Info from reservation 
 candidate.ID AS Id,
 candidate.RESERVED_FROM AS ReservedFrom,
 candidate.RESERVED_TO AS ReservedTo,
 candidate.RES_RESERVATIONTYPE_FK AS ReservationTypeFk,
 candidate.RES_RESERVATIONSTATUS_FK AS ReservationStatusFk,
 candidate.QUANTITY AS Quantity,
 candidate.BAS_UOM_FK AS UomFk,
 candidate.COMMENT_TEXT AS [Comment],
 candidate.PRJ_PROJECT_FK AS ReceivingProjectFk,
 candidate.LGM_JOB_FK AS ReceivingJobFk,

-- Info from the requisition it is for
 requisition.ID AS RequisitionFk,
 reqstate.ID AS RequisitionStatusFk,
 requisition.REQUESTED_FROM AS RequestedFrom,
 requisition.REQUESTED_TO AS RequestedTo,
 requisition.RES_TYPE_FK AS RequiredResourceTypeFk,
 requisition.RES_RESOURCE_FK AS RequiredResourceFk,
 requisition.ETM_WORKOPERATIONTYPEEST_FK AS WorkOperationTypeFk,
 requisition.QUANTITY AS RequiredQuantity,
 requisition.BAS_UOM_FK AS UomRequiredFk,
 requisition.PRJ_DROPPOINT_FK AS ReceivingDropPointFk,

-- Info on reserved resource 
 reservedres.ID AS ReservedResourceFk,
 reservedtype.ID AS ReservedResourceTypeFk,
 reservedtype.ISTRANSPORTPERMISSION AS IsTransportPermission,
 reservedplant.ID AS ReservedPlantFk,
 reservedplant.TRANSPORTWEIGHT AS TransportWeight,

-- Info on last project / transport
 NULL AS LastReservationFk,
 lastdroppoint.ID AS PerformingDropPointFk,
 lastprojectdroppedto.ID AS PerformingProjectFk,
 lastprojectdroppedto.ISADMINISTRATION AS IsAdministration,
 lastdefaultjobdroppedto.ID AS PerformingJobFk,
 dispactivitydroppedto.ID AS DispatchActivityFk,
 disprubcatdroppedto.BAS_RUBRIC_CATEGORY_FK AS DispatchRubricCategoryFk,
 
-- History of reservation 
 candidate.INSERTED AS InsertedAt,
 candidate.WHOISR AS InsertedBy,
 candidate.UPDATED AS UpdatedAt,
 candidate.WHOUPD AS UpdatedBy,
 candidate.VERSION AS Version

FROM
 RES_RESERVATION candidate
 INNER JOIN BAS_COMPANY comp ON comp.ID=@company AND comp.ID=candidate.BAS_COMPANY_FK
 INNER JOIN RES_REQUISITION requisition ON requisition.ID = candidate.RES_REQUISITION_FK
 INNER JOIN RES_REQUISITIONSTATUS reqstate ON reqstate.ID=requisition.RES_REQUISITIONSTATUS_FK AND reqstate.ISCANCELED=0
 INNER JOIN RES_RESOURCE reservedres ON reservedres.ID = candidate.RES_RESOURCE_FK
 INNER JOIN RES_TYPE reservedtype ON reservedtype.ID = reservedres.RES_TYPE_FK
 INNER JOIN RES_RESOURCE2ETM_PLANT plresreserved on plresreserved.RES_RESOURCE_FK=candidate.RES_RESOURCE_FK --and plresreserved.BAS_ETM_CONTEXT_FK=division.BAS_ETM_CONTEXT_FK
 INNER JOIN ETM_PLANT reservedplant on reservedplant.ID=plresreserved.ETM_PLANT_FK-- and plant.BAS_ETM_CONTEXT_FK=comp.BAS_ETM_CONTEXT_FK
 OUTER APPLY (
  SELECT TOP 1 dpa.PRJ_DROPPOINT_FK AS ID
  FROM
  PRJ_DROP_POINT_ARTICLES dpa
  WHERE dpa.ETM_PLANT_FK IS NOT NULL AND dpa.ETM_PLANT_FK=reservedplant.ID AND dpa.QUANTITY >= 1
  ORDER BY dpa.INSERTED DESC
 ) lastpointdroppedto
 INNER JOIN PRJ_DROPPOINT lastdroppoint ON lastdroppoint.ID=lastpointdroppedto.ID
 INNER JOIN PRJ_PROJECT lastprojectdroppedto ON lastprojectdroppedto.ID=lastdroppoint.PRJ_PROJECT_FK
 INNER JOIN LGM_JOB lastdefaultjobdroppedto ON lastdefaultjobdroppedto.PRJ_PROJECT_FK=lastdroppoint.PRJ_PROJECT_FK AND lastdefaultjobdroppedto.ISPROJECTDEFAULT=1
 INNER JOIN LGM_DISPATCHACTIVITY dispactivitydroppedto ON (dispactivitydroppedto.ID=1 AND lastprojectdroppedto.ISADMINISTRATION=1) OR
  (dispactivitydroppedto.ID=2 AND lastprojectdroppedto.ISADMINISTRATION=0)
 INNER JOIN LGM_DISPATCHACTRUBCAT disprubcatdroppedto ON disprubcatdroppedto.LGM_DISPATCHACTIVITY_FK=dispactivitydroppedto.ID AND disprubcatdroppedto.LGM_DISPATCH_HEADER_TYPE_FK=3

WHERE
 candidate.BAS_COMPANY_FK = @company
 AND NOT EXISTS (SELECT ID FROM LGM_DISPATCH_RECORD WHERE RES_RESERVATION_FK=candidate.ID)
 AND candidate.RESERVED_FROM >= @fromDate
 AND candidate.RESERVED_FROM < @toDate
GO



-- TVF for finding reservation performing information
EXEC FRM_DROPSPVIEWFNIFEXISTS_PROC 'RES_RESERVATIONTODISPATCH_F'
GO

CREATE FUNCTION [dbo].[RES_RESERVATIONTODISPATCH_F] (@fromDate date, @toDate date, @company int)
RETURNS @Result TABLE
(
 Id INT NOT NULL,
 ReservedFrom DATETIME NOT NULL,
 ReservedTo DATETIME NOT NULL,
 ReservationTypeFk INT NULL,
 ReservationStatusFk INT NOT NULL,
 Quantity NUMERIC NOT NULL,
 UomFk INT NOT NULL,
 [Comment] nvarchar(255) NULL,
 ReceivingProjectFk INT NOT NULL,
 ReceivingJobFk INT NOT NULL,
 RequisitionFk INT NOT NULL,
 RequisitionStatusFk INT NOT NULL,
 RequestedFrom DATETIME NOT NULL,
 RequestedTo DATETIME NOT NULL,
 RequiredResourceTypeFk INT NULL,
 RequiredResourceFk INT NULL,
 WorkOperationTypeFk INT NULL,
 RequiredQuantity NUMERIC NOT NULL,
 UomRequiredFk INT NOT NULL,
 ReceivingDropPointFk INT NULL,
 ReservedResourceFk INT NOT NULL,
 ReservedResourceTypeFk INT NOT NULL,
 IsTransportPermission bit NOT NULL,
 ReservedPlantFk INT NOT NULL,
 TransportWeight NUMERIC NULL,
 LastReservationFk INT NULL,
 PerformingDropPointFk INT NULL,
 PerformingProjectFk INT NOT NULL,
 IsAdministration bit NOT NULL,
 PerformingJobFk INT NOT NULL,
 DispatchActivityFk INT NOT NULL,
 DispatchRubricCategoryFk INT NOT NULL,
 InsertedAt DATETIME NOT NULL,
 InsertedBy INT NOT NULL,
 UpdatedAt DATETIME NULL,
 UpdatedBy INT NULL,
 Version INT NOT NULL
)
AS
BEGIN
 INSERT INTO @Result
 SELECT * FROM dbo.RES_RESERVEDTODISPATCHBYRESERVE_F(@fromDate, @toDate, @company);

 INSERT INTO @Result
  SELECT * FROM dbo.RES_RESERVEDTODISPATCHBYDRPPNT_F(@fromDate, @toDate, @company) as droppointreserved
  WHERE NOT EXISTS (SELECT ID FROM @Result res WHERE res.ID = droppointreserved.ID);

 RETURN;
END
GO

