(function () {
	'use strict';
	var moduleName = 'basics.customize';

	angular.module(moduleName).controller('basicsCustomizeUserLabelAssignmentController',
		['$scope', 'platformTranslateService', 'basicsCustomizeUserLabelAssignmentConfigurationService', 'platformDialogService', '$translate', '_', 'basicsLookupdataConfigGenerator', 'basicsLookupdataSimpleLookupService', 'basicsCustomizeInstanceDataService', 'basicsCustomizeInstanceValidationProviderService', 'platformDataValidationService', 'platformRuntimeDataService',
			function ($scope, platformTranslateService, basicsCustomizeUserLabelAssignmentConfigurationService, platformDialogService, $translate, _, basicsLookupdataConfigGenerator, basicsLookupdataSimpleLookupService, basicsCustomizeInstanceDataService, basicsCustomizeInstanceValidationProviderService, platformDataValidationService, platformRuntimeDataService) {

				$scope.messages = {
					createOngoing: $translate.instant('basics.customize.userLabelAssignment.messages.createOngoing'),
				};

				function updateUserFkWithNewlyCreatedUserLabel(userLabelId) {
					const itemInDataService = basicsCustomizeInstanceDataService.getItemById($scope.selectedItem.Id);
					if (itemInDataService) {
						itemInDataService.UserLabelFk = userLabelId;
					}
					const validationService = basicsCustomizeInstanceValidationProviderService.getInstanceValidationService();
					const validationResult = platformDataValidationService.validateMandatory(itemInDataService, userLabelId, 'UserLabelFk', validationService, basicsCustomizeInstanceDataService);
					platformDataValidationService.finishValidation(validationResult, itemInDataService, userLabelId, 'UserLabelFk', validationService, basicsCustomizeInstanceDataService);
					platformRuntimeDataService.applyValidationResult(validationResult, itemInDataService, 'UserLabelFk');
				}

				function updateLabelKeyWithEntityField() {
					const itemInDataService = basicsCustomizeInstanceDataService.getItemById($scope.selectedItem.Id);
					if (itemInDataService.Entity && itemInDataService.Field) {
						$scope.selectedItem.LabelKey = itemInDataService.Entity + '.' + itemInDataService.Field;
					}
				}

				const saveBtn = {
					id: 'create',
					caption: $translate.instant('basics.customize.userLabelAssignment.button.btnCreate'),
					fn: function ($event, info) {
						$scope.isLoading = true;
						$scope.loadingMessage = $scope.messages.createOngoing;
						basicsCustomizeUserLabelAssignmentConfigurationService.saveNewLabel($scope.selectedItem).then(function (result) {
							$scope.isLoading = false;
							if (result) {
								const lookUpOptions = basicsLookupdataConfigGenerator.provideGenericLookupConfig('basics.customize.userlabel', 'Description', null);
								basicsLookupdataSimpleLookupService.refreshCachedData(lookUpOptions.detail.options).then(function () {
									updateUserFkWithNewlyCreatedUserLabel(result.data.id);
									basicsCustomizeUserLabelAssignmentConfigurationService.refreshGrid();
									info.$close();
								}, function (error) {
									platformDialogService.showMsgBox(error.message, 'basics.customize.userLabelAssignment.messages.lookupRefreshError', 'error');
								});
							}
						}, function (error) {
							platformDialogService.showMsgBox(error.message, 'basics.customize.userLabelAssignment.messages.createError', 'error');
							$scope.isLoading = false;
						});
					}
				};

				const cancelBtn = {
					id: 'cancel',
					caption: $translate.instant('basics.customize.userLabelAssignment.button.btnCancel'),
					fn: function (event, info) {
						info.$close(false);
					}
				};

				$scope.formOptions = {
					configure: basicsCustomizeUserLabelAssignmentConfigurationService.getDefaultForm()
				};

				$scope.selectedItem = $scope.dialog.modalOptions.value.selectedItem;
				$scope.dialog.buttons = [saveBtn, cancelBtn];

				platformTranslateService.translateFormConfig($scope.formOptions.configure);

				updateLabelKeyWithEntityField();

			}
		]);
})();