/*
 * Copyright(c) RIB Software GmbH
 */
import { IPrcInventoryEntityGenerated } from './prc-inventory-entity-generated.interface';
import { IPrcInventoryHeaderEntity } from './prc-inventory-header-entity.interface';
export interface IPrcInventoryEntity extends IPrcInventoryEntityGenerated {
  /**
   * Indicates if the record is selected from a wizard context
   */
  isFromWizard?: boolean;
  /**
   * Reference to parent inventory header entity (required for DataServiceFlatLeaf)
   */
  PrcInventoryHeaderEntity?: IPrcInventoryHeaderEntity | null;
  /*
 * Description2
 */
  Description2?: string | null;
  
}