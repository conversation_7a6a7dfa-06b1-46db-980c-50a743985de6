/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { SalesWipWipsDataService } from '../sales-wip-wips-data.service';
import { BasicsSharedDataValidationService } from '@libs/basics/shared';
import { PlatformHttpService, PropertyType } from '@libs/platform/common';
import { SalesWipBillingSchemaDataService } from '../sales-wip-billing-schema-data.service';
import { IWipHeaderEntity } from '@libs/sales/interfaces';
import { HttpParams } from '@angular/common/http';

@Injectable({
    providedIn: 'root'
})

/**
 * sales Wip validation service
 */
export class SalesWipWipValidationService extends BaseValidationService<IWipHeaderEntity> {

    private dataService = inject(SalesWipWipsDataService);
    private validationUtils = inject(BasicsSharedDataValidationService);
    private http = inject(PlatformHttpService);
    private wipBillingSchema = inject(SalesWipBillingSchemaDataService);

    protected generateValidationFunctions(): IValidationFunctions<IWipHeaderEntity> {
        return {
            Code: [this.validateCode, this.asyncValidateCode],
            ClerkFk: this.validateClerkFk,
            RubricCategoryFk: this.asyncValidateRubricCategoryFk,
            CompanyResponsibleFk: this.validateCompanyResponsibleFk,
            OrdHeaderFk: this.validateOrdHeaderFk,
            ProjectFk: this.validateProjectFk,
            PerformedFrom: this.validatePerformedFrom,
            PerformedTo: this.validatePerformedTo,
            DocumentDate: this.validateDocumentDate,
            BillingSchemaFk: this.validateBillingSchemaFk,
            CurrencyFk: this.asyncValidateCurrencyFk,
            DateEffective: this.validateDateEffective,
            ExchangeRate: this.asyncValidateExchangeRate,
            TaxCodeFk: this.validateTaxCodeFk,
        };
    }
    protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IWipHeaderEntity> {
        return this.dataService;
    }

    private validateCode(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        const entity = info.entity;
        if (entity.ClerkFk) {
            const res = this.validateClerkFk(new ValidationInfo(entity, entity.ClerkFk, 'ClerkFk'));
            this.validationUtils.applyValidationResult(this.dataService, {
                entity: entity,
                field: 'ClerkFk',
                result: res,
            });
        }
        if (entity.OrdHeaderFk) {
            const res = this.validateOrdHeaderFk(new ValidationInfo(entity, entity.OrdHeaderFk, 'OrdHeaderFk'));
            this.validationUtils.applyValidationResult(this.dataService, {
                entity: entity,
                field: 'OrdHeaderFk',
                result: res,
            });
        }
        return this.validationUtils.isMandatory(info);
    }

    protected async asyncValidateCode(info: ValidationInfo<IWipHeaderEntity>): Promise<ValidationResult> {
        const entity = info.entity;
        const companyId = entity.CompanyFk || -1;
        const code = info.value as string;
        try {
            const params = new HttpParams({
                fromObject: {
                    companyId: companyId,
                    code: code,
                    entityid: entity.Id
                }
            });
            const isUnique = await this.http.get<boolean>('sales/wip/isuniquecode', { params });
            if (!isUnique) {
                return this.validationUtils.createErrorObject('sales.common.errorCodeMustBeUniqueInCompany');
            }
            return this.validationUtils.createSuccessObject();
        } catch (error) {
            return this.validationUtils.createErrorObject('sales.common.errorCodeMustBeUniqueInCompany');
        }
    }

    protected validateClerkFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        return this.validationUtils.isMandatory(info);
    }

    protected asyncValidateRubricCategoryFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        try {
            const url = 'sales/wip/status/IsStatusAvailable';
            const isAvailable = this.http.get<boolean>(url);
            if (!isAvailable) {
                return this.validationUtils.createErrorObject('sales.common.errorStatusNotAvailable');
            }
            return this.validationUtils.createSuccessObject();
        } catch (error) {
            return this.validationUtils.createErrorObject('sales.common.errorStatusNotAvailable');
        }
    }

    protected validateCompanyResponsibleFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        return this.validationUtils.isMandatory(info);
    }

    protected validateOrdHeaderFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        return this.validationUtils.isMandatory(info);
    }

    protected validateProjectFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        return this.validationUtils.isMandatory(info);
    }

    protected validatePerformedFrom(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        return this.validateDate(info.entity, info.value as string, info.entity?.PerformedFrom as string);
    }

    protected validatePerformedTo(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        return this.validateDate(info.entity, info.entity?.PerformedFrom as string, info.value as string);
    }

    protected validateDocumentDate(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        info.entity.DateEffective = info.value as string || info.entity.DateEffective;
        return this.validationUtils.createSuccessObject();
    }
    protected validateBillingSchemaFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        info.value ? this.onValidateBillingSchemaFk(info.entity, info.value, true) : true;
        return this.validationUtils.createSuccessObject();
    }

    protected asyncValidateCurrencyFk(info: ValidationInfo<IWipHeaderEntity>) {
        // Todo: return this.currencyExchangeRateService.setAsyncExchangeRateByCurrency(entity, currencyId, model, url );
        return new ValidationResult();
    }

    protected validateDateEffective(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        // Todo: return salesCommonDateEffectiveValidateService.asyncModifyDateEffectiveAndUpdateBoq(entity, value, model,'salesWipBoqStructureService', salesWipService, service,'sales.wip');
        // Required procurementCommonDateEffectiveValidateService.asyncModifyDateEffectiveAndUpdateBoq not implemented yet
        return this.validationUtils.createSuccessObject();
    }

    protected asyncValidateExchangeRate(info: ValidationInfo<IWipHeaderEntity>) {
        // TODO: return salesWipService.setAsyncExchangeRate(entity, value, model, updateExchangeRateUrl);
        return new ValidationResult();
    }

    protected validateTaxCodeFk(info: ValidationInfo<IWipHeaderEntity>): ValidationResult {
        if (info.value === 0) {
            info.entity.TaxCodeFk = 0;
        }
        return this.validationUtils.isMandatory(info);
    }

    private onValidateBillingSchemaFk(entity: IWipHeaderEntity, value: PropertyType | undefined, forceReload: boolean): boolean {
        if (entity.BillingSchemaFk !== value || forceReload) {
            entity.BillingSchemaFk = value as number;
				// TODO: getWipData does not exist, please fix (DEV-47779)
            //this.wipBillingSchema.getWipData(entity);
            // this.dataService.reloadBillingSchemas();
        }
        return true;
    }

    private validateDate(entity: IWipHeaderEntity, fromDate: string | null, toDate: string | null): ValidationResult {
        let result = this.validationUtils.createSuccessObject();

        if (fromDate !== null && toDate !== null) {
            if (fromDate > toDate) {
                result = this.validationUtils.createErrorObject({
                    key: 'sales.wip.dateError',
                });
            }
        }
        if (!result.valid) {
            this.validationUtils.applyValidationResult(this.dataService, {
                entity: entity,
                field: 'PerformedFrom',
                result: result,
            });
            this.validationUtils.applyValidationResult(this.dataService, {
                entity: entity,
                field: 'PerformedTo',
                result: result,
            });
        } else {
            this.validationUtils.applyValidationResult(this.dataService, {
                entity: entity,
                field: 'PerformedFrom',
                result: result,
            });
            this.validationUtils.applyValidationResult(this.dataService, {
                entity: entity,
                field: 'PerformedTo',
                result: result,
            });
        }
        return result;
    }
}