import { Injectable } from '@angular/core';
import { EntityDomainType, IConcreteEntitySchemaProperty, IEntitySchema } from '@libs/platform/data-access';
import { IInitializationContext, prefixAllTranslationKeys, Translatable } from '@libs/platform/common';
import { IPesBoqEntity } from '../model/entities/pes-boq-entity.interface';
import { IPrcBoqEntityContext, ProcurementCommonBoqConfigService, ProcurementCommonBoqDataServiceBase } from '@libs/procurement/common';
import { IPesHeaderEntity } from '../model/entities';
import { PesCompleteNew } from '../model/complete-class/pes-complete-new.class';
import { PesBoqComplete } from '../model/complete-class/pes-boq-complete.class';
import { FieldOverloadSpec } from '@libs/ui/common';
import { ProcurementSharedLookupOverloadProvider } from '@libs/procurement/shared';
import { IPesContractLookupFilter } from '../model/entities/pes-contract-lookup-filter.interface';
import { BASICS_CURRENCY_LOOKUP_PROVIDER_TOKEN } from '@libs/basics/interfaces';
import { BasicsSharedLookupOverloadProvider } from '@libs/basics/shared';

@Injectable({ providedIn: 'root' })
export class ProcurementPesBoqConfigService extends ProcurementCommonBoqConfigService<IPesHeaderEntity, PesCompleteNew, IPesBoqEntity, PesBoqComplete> {
	protected override getPrcBoqProperties(): { [key: string]: IConcreteEntitySchemaProperty } {
		return {
			ControllingUnitFk: { domain: EntityDomainType.Integer, mandatory: false },
			PackageFk: { domain: EntityDomainType.Integer, mandatory: false },
			ConHeaderFk: { domain: EntityDomainType.Integer, mandatory: false },
			PrcBoqFk: { domain: EntityDomainType.Integer, mandatory: true },
			CurrencyFk: { domain: EntityDomainType.Integer, mandatory: false },
			PrcStructureFk: { domain: EntityDomainType.Integer, mandatory: true },
			MdcTaxCodeFk: { domain: EntityDomainType.Integer, mandatory: true },
			// PrcItemStatusFk: { domain: EntityDomainType.Integer, mandatory: false }, TODO - this property does seem to appear on UI on angularjs
			PerformedFrom: { domain: EntityDomainType.DateUtc, mandatory: true },
			PerformedTo: { domain: EntityDomainType.DateUtc, mandatory: true },
		};
	}

	protected override getPrcBoqLabels(): { [p: string]: Translatable } {
		return {
			...prefixAllTranslationKeys('cloud.common.', {
				ControllingUnitFk: 'entityControllingUnitCode',
				PackageFk: 'entityPackageCode',
				CurrencyFk: 'entityCurrency',
				PrcBoqFk: 'entityCode',
				PrcStructureFk: 'entityStructureCode',
				MdcTaxCodeFk: 'entityTaxCode',
			}),
			...prefixAllTranslationKeys('procurement.pes.', {
				ConHeaderFk: 'entityContractCode',
				PerformedFrom: 'entityPerformedFrom',
				PerformedTo: 'entityPerformedTo',
				PrcItemStatusFk: 'entityPcItemStatus',
			}),
		};
	}

	protected override async getPrcBoqOverloads(
		dataService: ProcurementCommonBoqDataServiceBase<IPesHeaderEntity, PesCompleteNew, IPesBoqEntity, PesBoqComplete>,
		ctx: IInitializationContext,
	): Promise<{ [key: string]: FieldOverloadSpec<IPesBoqEntity> }> {
		return {
			ControllingUnitFk: await this.provideControllingUnitOverload(dataService, ctx),
			PackageFk: ProcurementSharedLookupOverloadProvider.providePackageReadonlyLookupOverload(false),
			ConHeaderFk: ProcurementSharedLookupOverloadProvider.provideContractLookupOverload(true, 'procurement.pes.entityConHeaderDescription', false, {
				key: 'prc-con-header-for-pes-filter',
				execute(entityContext) {
					const entity = entityContext.entity;
					const pesContext = dataService.getHeaderContext();

					if (!entity || !pesContext) {
						return {};
					}

					const filterParams: IPesContractLookupFilter = {
						StatusIsInvoiced: false,
						StatusIsCanceled: false,
						StatusIsVirtual: false,
						StatusIsOrdered: true,
						ControllingUnit: entity.ControllingUnitFk ?? pesContext.controllingUnitFk ?? undefined,
						PrcConfigurationId: pesContext.prcConfigFk,
						IsFramework: false,
						BusinessPartnerFk: pesContext.businessPartnerFk,
						PrcPackageFk: entity.PackageFk ?? pesContext.packageFk ?? undefined,
						PrcStructureFk: entity.PrcStructureFk ?? pesContext.structureFk ?? undefined,
						ProjectFk: pesContext.projectFk,
					};

					return filterParams;
				},
			}),
			CurrencyFk: (await this.lazyInjector.inject(BASICS_CURRENCY_LOOKUP_PROVIDER_TOKEN)).provideCurrencyLookupOverload({}),
			PrcStructureFk: BasicsSharedLookupOverloadProvider.provideProcurementStructureLookupOverload(false),
			MdcTaxCodeFk: BasicsSharedLookupOverloadProvider.provideTaxCodeListLookupOverload(false),
		};
	}

	public override getBoqScheme(): IEntitySchema<IPesBoqEntity> {
		return this.getSchema('IPesBoqEntity');
	}

	protected override getPrcBoqEntityContext(entity: IPesBoqEntity): IPrcBoqEntityContext {
		return {
			controllingUnitFk: entity.ControllingUnitFk,
		};
	}
}
