/*
 * Copyright(c) RIB Software GmbH
 */

import { ProcurementCommonCertificateEntityInfo } from '@libs/procurement/common';
import { ProcurementContractCertificateDataService } from '../../services/procurement-contract-certificate-data.service';
import { ProcurementContractCertificateValidationService } from '../../services/procurement-contract-certificate-validation.service';

export const PROCUREMENT_CONTRACT_CERTIFICATE_ENTITY_INFO = ProcurementCommonCertificateEntityInfo.create({
	permissionUuid: '5055ba9ce9c14f78b445a97d74bc8b90',
	formUuid: '2ef9cdb7254c4fb597dd79b86cefa948',
	dataServiceToken: ProcurementContractCertificateDataService,
	validationServiceToken: ProcurementContractCertificateValidationService,
});
