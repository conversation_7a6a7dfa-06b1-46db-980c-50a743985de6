/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { PpsUpstreamItemValidationService } from '../../services/upstream-item/pps-upstream-item-validation.service';
import { PpsUpstreamItemLayoutService } from '../../services/upstream-item/pps-upstream-item-layout.service';
import { PpsUpstreamItemDataService } from '../../services/upstream-item/pps-upstream-item-data.service';
import { PpsItemUpstreamItemBehavior } from '../../behaviors/pps-item-upstream-item-behavior.service';
import { IPpsUpstreamItemEntity } from '../../model/models';

export const PPS_UPSTREAM_ITEM_ENTITY_INFO: EntityInfo = EntityInfo.create<IPpsUpstreamItemEntity>({
	grid: {
		title: {key: 'productionplanning.item.upstreamItem.listTitle', text: '*Upstream Requirements'},
		containerUuid: '23edab57edgb492d84r2gv47e734fh8u',
		behavior: ctx => ctx.injector.get(PpsItemUpstreamItemBehavior),
	},
	dataService: ctx => ctx.injector.get(PpsUpstreamItemDataService),
	validationService: (ctx) => ctx.injector.get(PpsUpstreamItemValidationService),
	dtoSchemeId: {moduleSubModule: 'ProductionPlanning.Item', typeName: 'PpsUpstreamItemDto'},
	layoutConfiguration: ctx => ctx.injector.get(PpsUpstreamItemLayoutService).generateLayout(),
	permissionUuid: '23edab57edgb492d84r2gv47e734fh8u',
	prepareEntityContainer: async (ctx) => {
		await Promise.all([
			ctx.translateService.load(['productionplanning.common', 'productionplanning.item']),
			// other promises...
		]);
	},
});

