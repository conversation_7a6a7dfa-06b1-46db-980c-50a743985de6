﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Resource.Requisition.BusinessComponents;

namespace RIB.Visual.Resource.Requisition.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		private static readonly object Locking = new object();
		private static DbCompiledModel _model;

		/// <summary>Creates a compiled entity model </summary>
		public static DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (Locking)
					{
						if (_model != null) return _model;
            var modelBuilder = new DbModelBuilder();

            AddMappings(modelBuilder);
            AddAdditionalMappings(modelBuilder);

            modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

            _model = modelBuilder.Build(RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		public static void AddMappings(DbModelBuilder modelBuilder)
		{

            #region DdTempIdsEntity

            modelBuilder.Entity<DdTempIdsEntity>()
                .HasKey(p => new { p.Id, p.RequestId })
                .ToTable("BAS_DDTEMPIDS");
            // Properties:
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key1)
                    .HasColumnName(@"KEY1")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key2)
                    .HasColumnName(@"KEY2")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key3)
                    .HasColumnName(@"KEY3")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<DdTempIdsEntity>(modelBuilder);

            #endregion

            #region RequisitionEntity

            modelBuilder.Entity<RequisitionEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUISITION");
            // Properties:
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ResourceContextFk)
                    .HasColumnName(@"BAS_RESOURCE_CONTEXT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.CompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ProjectFk)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ResourceFk)
                    .HasColumnName(@"RES_RESOURCE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequisitionStatusFk)
                    .HasColumnName(@"RES_REQUISITIONSTATUS_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.TypeFk)
                    .HasColumnName(@"RES_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.UomFk)
                    .HasColumnName(@"BAS_UOM_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequestedFrom)
                    .HasColumnName(@"REQUESTED_FROM")
                    .IsRequired()
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequestedTo)
                    .HasColumnName(@"REQUESTED_TO")
                    .IsRequired()
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ReservedFrom)
                    .HasColumnName(@"RESERVED_FROM")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ReservedTo)
                    .HasColumnName(@"RESERVED_TO")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.JobFk)
                    .HasColumnName(@"LGM_JOB_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ActivityFk)
                    .HasColumnName(@"PSD_ACTIVITY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.TrsRequisitionFk)
                    .HasColumnName(@"TRS_REQUISITION_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.PpsEventFk)
                    .HasColumnName(@"PPS_EVENT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.IsLinkedFixToReservation)
                    .HasColumnName(@"ISLINKEDFIXTORESERVATION")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ReservationId)
                    .HasColumnName(@"RESERVATION_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.UserDefinedText01)
                    .HasColumnName(@"USERDEFINEDTEXT01")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.UserDefinedText02)
                    .HasColumnName(@"USERDEFINEDTEXT02")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.UserDefinedText03)
                    .HasColumnName(@"USERDEFINEDTEXT03")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.UserDefinedText04)
                    .HasColumnName(@"USERDEFINEDTEXT04")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.UserDefinedText05)
                    .HasColumnName(@"USERDEFINEDTEXT05")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.MaterialFk)
                    .HasColumnName(@"MDC_MATERIAL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.Remark)
                    .HasColumnName(@"REMARK")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.SearchPattern)
                    .HasColumnName(@"SEARCH_PATTERN")
                    .HasMaxLength(450)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ClerkOwnerFk)
                    .HasColumnName(@"BAS_CLERKOWNER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ClerkResponsibleFk)
                    .HasColumnName(@"BAS_CLERKRESPONSIBLE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.SiteFk)
                    .HasColumnName(@"BAS_SITE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.StockFk)
                    .HasColumnName(@"PRJ_STOCK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequisitionGroupFk)
                    .HasColumnName(@"RES_REQUISITIONGROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequisitionPriorityFk)
                    .HasColumnName(@"RES_REQUISITIONPRIORITY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequisitionTypeFk)
                    .HasColumnName(@"RES_REQUISITION_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RequisitionFk)
                    .HasColumnName(@"RES_REQUISITION_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.JobPreferredFk)
                    .HasColumnName(@"LGM_JOBPREFERRED_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ProjectChangeFk)
                    .HasColumnName(@"PRJ_CHANGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.RubricCategoryFk)
                    .HasColumnName(@"BAS_RUBRIC_CATEGORY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.EstHeaderFk)
                    .HasColumnName(@"EST_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.EstLineItemFk)
                    .HasColumnName(@"EST_LINE_ITEM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.EstResourceFk)
                    .HasColumnName(@"EST_RESOURCE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ExecPlannerItemFk)
                    .HasColumnName(@"RES_EXECPLANNERITEM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.ProjectTimeSlotFk)
                    .HasColumnName(@"RES_PROJECTTIMESLOT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.MdcControllingUnitFk)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.WorkOperationTypeFk)
                    .HasColumnName(@"ETM_WORKOPERATIONTYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.DropPointFk)
                    .HasColumnName(@"PRJ_DROPPOINT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.SkillFk)
                    .HasColumnName(@"RES_SKILL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.TypeAlternativeFk)
                    .HasColumnName(@"RES_TYPEALTERNATIVE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.TypeFromEstimateFk)
                    .HasColumnName(@"RES_TYPEFROMESTIMATE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.IsBottleNeck)
                    .HasColumnName(@"ISBOTTLENECK")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.QuantityFromEstimate)
                    .HasColumnName(@"QUANTITYFROMESTIMATE")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.WorkOperationTypeFromEstimateFk)
                    .HasColumnName(@"ETM_WORKOPERATIONTYPEEST_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.IsDeleted)
                    .HasColumnName(@"ISDELETED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionEntity>()
                .Property(p => p.IsArchived)
                    .HasColumnName(@"ISARCHIVED")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RequisitionEntity>(modelBuilder);
            // Associations:
            modelBuilder.Entity<RequisitionEntity>()
                .HasMany(p => p.ResRequisition2skillEntities)
                    .WithRequired()
                .HasForeignKey(p => new { p.RequisitionFk })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<RequisitionEntity>()
                .HasMany(p => p.Requisition2RequisitionEntities_ResRequisitionFk)
                    .WithRequired(c => c.RequisitionEntity_ResRequisitionFk)
                .HasForeignKey(p => new { p.RequisitionFk })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<RequisitionEntity>()
                .HasMany(p => p.Requisition2RequisitionEntities_ResRequisitionlinkedFk)
                    .WithRequired(c => c.RequisitionEntity_ResRequisitionlinkedFk)
                .HasForeignKey(p => new { p.RequisitionLinkedFk })
                    .WillCascadeOnDelete(false);

            #endregion

            #region RequisitionRequiredSkillEntity

            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUISITION2SKILL");
            // Properties:
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.RequisitionFk)
                    .HasColumnName(@"RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.SkillFk)
                    .HasColumnName(@"RES_SKILL_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedText01)
                    .HasColumnName(@"USERDEFTEXT01")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedText02)
                    .HasColumnName(@"USERDEFTEXT02")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedText03)
                    .HasColumnName(@"USERDEFTEXT03")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedText04)
                    .HasColumnName(@"USERDEFTEXT04")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedText05)
                    .HasColumnName(@"USERDEFTEXT05")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedNumber01)
                    .HasColumnName(@"USERDEFNUMBER01")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedNumber02)
                    .HasColumnName(@"USERDEFNUMBER02")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedNumber03)
                    .HasColumnName(@"USERDEFNUMBER03")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedNumber04)
                    .HasColumnName(@"USERDEFNUMBER04")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedNumber05)
                    .HasColumnName(@"USERDEFNUMBER05")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedDate01)
                    .HasColumnName(@"USERDEFDATE01")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedDate02)
                    .HasColumnName(@"USERDEFDATE02")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedDate03)
                    .HasColumnName(@"USERDEFDATE03")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedDate04)
                    .HasColumnName(@"USERDEFDATE04")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionRequiredSkillEntity>()
                .Property(p => p.UserDefinedDate05)
                    .HasColumnName(@"USERDEFDATE05")
                    .HasColumnType("datetime");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RequisitionRequiredSkillEntity>(modelBuilder);

            #endregion

            #region RequisitionDocumentEntity

            modelBuilder.Entity<RequisitionDocumentEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUISITIONDOCUMENT");
            // Properties:
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.RequisitionFk)
                    .HasColumnName(@"RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.DocumentTypeFk)
                    .HasColumnName(@"BAS_DOCUMENT_TYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.Date)
                    .HasColumnName(@"DATE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.Barcode)
                    .HasColumnName(@"BARCODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.FileArchiveDocFk)
                    .HasColumnName(@"BAS_FILEARCHIVEDOC_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionDocumentEntity>()
                .Property(p => p.Url)
                    .HasColumnName(@"URL")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RequisitionDocumentEntity>(modelBuilder);

            #endregion

            #region RequisitionitemEntity

            modelBuilder.Entity<RequisitionitemEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUISITIONITEM");
            // Properties:
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.RequisitionFk)
                    .HasColumnName(@"RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.MaterialFk)
                    .HasColumnName(@"MDC_MATERIAL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.ReservationId)
                    .HasColumnName(@"RESERVATION_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.StockFk)
                    .HasColumnName(@"PRJ_STOCK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.UomFk)
                    .HasColumnName(@"BAS_UOM_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.UserDefinedText01)
                    .HasColumnName(@"USERDEFINEDTEXT01")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.UserDefinedText02)
                    .HasColumnName(@"USERDEFINEDTEXT02")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.UserDefinedText03)
                    .HasColumnName(@"USERDEFINEDTEXT03")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.UserDefinedText04)
                    .HasColumnName(@"USERDEFINEDTEXT04")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionitemEntity>()
                .Property(p => p.UserDefinedText05)
                    .HasColumnName(@"USERDEFINEDTEXT05")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RequisitionitemEntity>(modelBuilder);

            #endregion

            #region StockTotalVEntity

            modelBuilder.Entity<StockTotalVEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("PRC_STOCKTOTAL_V");
            // Properties:
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Stock2matId)
                    .HasColumnName(@"STOCK2MAT_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.StockFk)
                    .HasColumnName(@"PRJ_STOCK_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.CatalogCode)
                    .HasColumnName(@"CATALOG_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.CatalogDescription)
                    .HasColumnName(@"CATALOG_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.MaterialCode)
                    .HasColumnName(@"MATERIAL_CODE")
                    .HasMaxLength(20)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.CatalogId)
                    .HasColumnName(@"CATALOG_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.StructureFk)
                    .HasColumnName(@"PRC_STRUCTURE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.MaterialGroupId)
                    .HasColumnName(@"MATERIAL_GROUP_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.MaterialFk)
                    .HasColumnName(@"MDC_MATERIAL_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Description1)
                    .HasColumnName(@"DESCRIPTION1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.BasBlobsFk)
                    .HasColumnName(@"BAS_BLOBS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Description2)
                    .HasColumnName(@"DESCRIPTION2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Modelname)
                    .HasColumnName(@"MODELNAME")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.BrandId)
                    .HasColumnName(@"BRAND_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.BrandDescription)
                    .HasColumnName(@"BRAND_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Total)
                    .HasColumnName(@"TOTAL")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ProvisionTotal)
                    .HasColumnName(@"PROVISION_TOTAL")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ProvisionPercent)
                    .HasColumnName(@"PROVISION_PERCENT")
                    .HasPrecision(10, 2)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ProvisionPeruom)
                    .HasColumnName(@"PROVISION_PERUOM")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Islotmanagement)
                    .HasColumnName(@"ISLOTMANAGEMENT")
                    .HasColumnType("bit");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.MinQuantity)
                    .HasColumnName(@"MIN_QUANTITY")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.MaxQuantity)
                    .HasColumnName(@"MAX_QUANTITY")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.Uom)
                    .HasColumnName(@"UOM")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.QuantityReceipt)
                    .HasColumnName(@"QUANTITY_RECEIPT")
                    .HasPrecision(38, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.QuantityConsumed)
                    .HasColumnName(@"QUANTITY_CONSUMED")
                    .HasPrecision(38, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.TotalReceipt)
                    .HasColumnName(@"TOTAL_RECEIPT")
                    .HasPrecision(38, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.TotalConsumed)
                    .HasColumnName(@"TOTAL_CONSUMED")
                    .HasPrecision(38, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.QuantityReserved)
                    .HasColumnName(@"QUANTITY_RESERVED")
                    .HasPrecision(38, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.QuantityAvailable)
                    .HasColumnName(@"QUANTITY_AVAILABLE")
                    .HasPrecision(38, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ProvisionReceipt)
                    .HasColumnName(@"PROVISION_RECEIPT")
                    .HasPrecision(38, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ProvisionConsumed)
                    .HasColumnName(@"PROVISION_CONSUMED")
                    .HasPrecision(38, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ExpenseTotal)
                    .HasColumnName(@"EXPENSE_TOTAL")
                    .HasPrecision(38, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<StockTotalVEntity>()
                .Property(p => p.ProductFk)
                    .HasColumnName(@"PPS_PRODUCT_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<StockTotalVEntity>(modelBuilder);

            #endregion

            #region RequisitionInformationEntity

            modelBuilder.Entity<RequisitionInformationEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUISITIONINFO_V");
            // Properties:
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.IsReadOnly)
                    .HasColumnName(@"ISREADONLY")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectCompanyFk)
                    .HasColumnName(@"PRJ_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectNo)
                    .HasColumnName(@"PROJECTNO")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectName)
                    .HasColumnName(@"PROJECT_NAME")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectName2)
                    .HasColumnName(@"PROJECT_NAME2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectCurrencyFk)
                    .HasColumnName(@"PRJ_CURRENCY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectBusinessPartnerFk)
                    .HasColumnName(@"PRJ_BUSINESSPARTNER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectIsLive)
                    .HasColumnName(@"PRJ_ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectUserDefined01)
                    .HasColumnName(@"PRJ_USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectUserDefined02)
                    .HasColumnName(@"PRJ_USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectAddressFk)
                    .HasColumnName(@"PRJ_ADDRESS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.StockProjectFk)
                    .HasColumnName(@"PRJ_STOCKPROJECT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ScheduleFk)
                    .HasColumnName(@"PSD_SCHEDULE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobCode)
                    .HasColumnName(@"JOB_CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobDescription)
                    .HasColumnName(@"JOB_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobGroupFk)
                    .HasColumnName(@"LGM_JOBGROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobSiteFk)
                    .HasColumnName(@"JOB_SITE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobControllingUnitFk)
                    .HasColumnName(@"JOB_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobUserDefined01)
                    .HasColumnName(@"JOB_USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobUserDefined02)
                    .HasColumnName(@"JOB_USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobValidFrom)
                    .HasColumnName(@"JOB_VALIDFROM")
                    .HasColumnType("date");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobValidTo)
                    .HasColumnName(@"JOB_VALIDTO")
                    .HasColumnType("date");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobBusinessPartnerFk)
                    .HasColumnName(@"JOB_BUSINESSPARTNER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobDeliveryAddressRemark)
                    .HasColumnName(@"JOB_DELIVERYADDR_REMARK")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobDeliveryAddressBlobFk)
                    .HasColumnName(@"JOB_BLOBSDELIVERYADDR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobAddressFk)
                    .HasColumnName(@"JOB_ADDRESS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobPlantFk)
                    .HasColumnName(@"JOB_PLANT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobIsProjectDefault)
                    .HasColumnName(@"JOB_ISPROJECTDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobIsLive)
                    .HasColumnName(@"JOB_ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobTypeFk)
                    .HasColumnName(@"JOB_TYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobIsMaintenance)
                    .HasColumnName(@"JOB_ISMAINTENANCE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobCalendarFk)
                    .HasColumnName(@"JOB_CALENDAR_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobCompanyFk)
                    .HasColumnName(@"JOB_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobRubricCategoryFk)
                    .HasColumnName(@"JOB_RUBRICCATEGORY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobContextFk)
                    .HasColumnName(@"JOB_CONTEXT_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobCostCodePriceListFk)
                    .HasColumnName(@"JOB_COSTCODEPRICELIST_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.JobCostCodePriceVersionFk)
                    .HasColumnName(@"JOB_COSTCODEPRICEVER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ResourceTypeIsBulk)
                    .HasColumnName(@"RES_TYPEISBULK")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ResourceTypeIsSmallTools)
                    .HasColumnName(@"RES_TYPEISSMALLTOOLS")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.DispatcherGroupFk)
                    .HasColumnName(@"LGM_DISPATCHER_GROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ResourceSiteFk)
                    .HasColumnName(@"RES_SITE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ReservedFrom)
                    .HasColumnName(@"RESERVEDFROM")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.OnSiteFrom)
                    .HasColumnName(@"ONSITEFROM")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ReservedTo)
                    .HasColumnName(@"RESERVEDTO")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.OnSiteTo)
                    .HasColumnName(@"ONSITETO")
                    .HasColumnType("datetime");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectChangeFk)
                    .HasColumnName(@"PRJ_CHANGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ProjectChangeStatusFk)
                    .HasColumnName(@"PRJ_CHANGESTATUS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.IsTimeEnhancement)
                    .HasColumnName(@"ISTIMEENHANCEMENT")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.Requisition2RequisitionFk)
                    .HasColumnName(@"RES_REQUISITION2REQUISITION_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.IsRequestBizPartner)
                    .HasColumnName(@"ISREQUESTBIZPARTNER")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.IsRequestProjectDoc)
                    .HasColumnName(@"ISREQUESTPRJDOC")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.PlannedStart)
                    .HasColumnName(@"PLANNEDSTART")
                    .HasColumnType("date");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.PlannedEnd)
                    .HasColumnName(@"PLANNEDEND")
                    .HasColumnType("date");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.EstimateQuantity)
                    .HasColumnName(@"ESTIMATEQUANTITY")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.EstWorkOperationTypeFk)
                    .HasColumnName(@"EST_WORKOPERATIONTYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.LgmDispatchHeaderMapFk)
                    .HasColumnName(@"LGM_DISPATCHHEADERMAP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ResRequisitionFormDataFk)
                    .HasColumnName(@"RES_REQUISITION_FORMDATA_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionInformationEntity>()
                .Property(p => p.ResRequisitionMapperFk)
                    .HasColumnName(@"RES_REQUISITION_MAPPER_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<RequisitionInformationEntity>(modelBuilder);

            #endregion

            #region Requisition2RequisitionEntity

            modelBuilder.Entity<Requisition2RequisitionEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUISITION2REQUISITION");
            // Properties:
            modelBuilder.Entity<Requisition2RequisitionEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<Requisition2RequisitionEntity>()
                .Property(p => p.RequisitionFk)
                    .HasColumnName(@"RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<Requisition2RequisitionEntity>()
                .Property(p => p.RequisitionLinkedFk)
                    .HasColumnName(@"RES_REQUISITIONLINKED_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<Requisition2RequisitionEntity>()
                .Property(p => p.IsReference)
                    .HasColumnName(@"ISREFERENCE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<Requisition2RequisitionEntity>()
                .Property(p => p.IsTimeEnhancement)
                    .HasColumnName(@"ISTIMEENHANCEMENT")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<Requisition2RequisitionEntity>(modelBuilder);

            #endregion

            #region RequisitionGenReqFromEstVEntity

            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .HasKey(p => new { p.BasCompanyFk, p.EstHeaderFk, p.EstLineItemFk, p.EstResourceFk, p.EstResourceType, p.PrjEstimateFk, p.PrjProjectFk, p.ResBasUomFk })
                .ToTable("RES_GEN_REQ_FROM_EST_V");
            // Properties:
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.PrjProjectFk)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.PrjProjectName)
                    .HasColumnName(@"PRJ_PROJECT_NAME")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.BasCompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.PrjEstimateFk)
                    .HasColumnName(@"PRJ_ESTIMATE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstHeaderFk)
                    .HasColumnName(@"EST_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstHeaderDescription)
                    .HasColumnName(@"EST_HEADER_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstLineItemFk)
                    .HasColumnName(@"EST_LINE_ITEM_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstLineItemDescription)
                    .HasColumnName(@"EST_LINE_ITEM_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstAssemblyLineItemFk)
                    .HasColumnName(@"EST_ASSEMBLY_LINE_ITEM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstAssemblyLineItemDescription)
                    .HasColumnName(@"EST_ASSEMBLY_LINE_ITEM_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstResourceFk)
                    .HasColumnName(@"EST_RESOURCE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstResourceDescription)
                    .HasColumnName(@"EST_RESOURCE_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstResourceType)
                    .HasColumnName(@"EST_RESOURCE_TYPE")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EstAssemblyFk)
                    .HasColumnName(@"EST_ASSEMBLY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.ResQuantity)
                    .HasColumnName(@"RES_QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.ResBasUomFk)
                    .HasColumnName(@"RES_BAS_UOM_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.LgmJobFk)
                    .HasColumnName(@"LGM_JOB_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.LgmJobDescription)
                    .HasColumnName(@"LGM_JOB_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.CalCalendarFk)
                    .HasColumnName(@"CAL_CALENDAR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.BasUomhourFk)
                    .HasColumnName(@"BAS_UOMHOUR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.BasUomdayFk)
                    .HasColumnName(@"BAS_UOMDAY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.BasUommonthFk)
                    .HasColumnName(@"BAS_UOMMONTH_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.Workhoursperday)
                    .HasColumnName(@"WORKHOURSPERDAY")
                    .HasPrecision(15, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.Workhourspermonth)
                    .HasColumnName(@"WORKHOURSPERMONTH")
                    .HasPrecision(15, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EtmPlantDescription)
                    .HasColumnName(@"ETM_PLANT_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EtmPlantgroupDescription)
                    .HasColumnName(@"ETM_PLANTGROUP_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.PlantFk)
                    .HasColumnName(@"ETM_PLANT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EtmPlantCode)
                    .HasColumnName(@"ETM_PLANT_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EtmPlantBasUomFk)
                    .HasColumnName(@"ETM_PLANT_BAS_UOM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.EtmPlantgroupCode)
                    .HasColumnName(@"ETM_PLANTGROUP_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.MdcControllingunitCode)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.MdcControllingunitDescription)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.MdcControllingunitPlannedStart)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_PLANNED_START")
                    .HasColumnType("date");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.ResTypeFk)
                    .HasColumnName(@"RES_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.ResTypeDescription)
                    .HasColumnName(@"RES_TYPE_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.LgmDispatcherGroupFk)
                    .HasColumnName(@"LGM_DISPATCHER_GROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.ResWotBasUomFk)
                    .HasColumnName(@"RES_WOT_BAS_UOM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.WorkOperationTypeFk)
                    .HasColumnName(@"ETM_WORKOPERATIONTYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqFromEstVEntity>()
                .Property(p => p.ControllingUnitFk)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");

            #endregion

            #region RequisitionGenReqSkillVEntity

            modelBuilder.Entity<RequisitionGenReqSkillVEntity>()
                .HasKey(p => new { p.Id, p.ResSkillFk, p.ResTypeFk })
                .ToTable("RES_GEN_REQ_SKILL_V");
            // Properties:
            modelBuilder.Entity<RequisitionGenReqSkillVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqSkillVEntity>()
                .Property(p => p.ResTypeFk)
                    .HasColumnName(@"RES_TYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqSkillVEntity>()
                .Property(p => p.ResSkillFk)
                    .HasColumnName(@"RES_SKILL_FK")
                    .IsRequired()
                    .HasColumnType("int");

            #endregion

            #region RequisitionGenReqTypVEntity

            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .HasKey(p => new { p.Id, p.ResSkillFk, p.ResTypeFk })
                .ToTable("RES_GEN_REQ_TYP_V");
            // Properties:
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.ResRequisitionFk)
                    .HasColumnName(@"RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.ResTypeFk)
                    .HasColumnName(@"RES_TYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.Duration)
                    .HasColumnName(@"DURATION")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.BasUomdayFk)
                    .HasColumnName(@"BAS_UOMDAY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.ResTyperequestedFk)
                    .HasColumnName(@"RES_TYPEREQUESTED_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.LgmDispatcherGroupFk)
                    .HasColumnName(@"LGM_DISPATCHER_GROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.IsPlantRequested)
                    .HasColumnName(@"ISPLANTREQUESTED")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.IsDriverRequested)
                    .HasColumnName(@"ISDRIVERREQUESTED")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.IsCraneRequested)
                    .HasColumnName(@"ISCRANEREQUESTED")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.IsTruckRequested)
                    .HasColumnName(@"ISTRUCKREQUESTED")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.SkillOfRequestedTypeFk)
                    .HasColumnName(@"RES_SKILL_OFREQUESTED_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.Isrequestedentireperiod)
                    .HasColumnName(@"ISREQUESTEDENTIREPERIOD")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.Necessaryoperators)
                    .HasColumnName(@"NECESSARYOPERATORS")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.CalCalendarFk)
                    .HasColumnName(@"CAL_CALENDAR_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.ResSkillResTypeFk)
                    .HasColumnName(@"RES_SKILL_RES_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionGenReqTypVEntity>()
                .Property(p => p.ResSkillFk)
                    .HasColumnName(@"RES_SKILL_FK")
                    .IsRequired()
                    .HasColumnType("int");

            #endregion

            #region Requisition2RequisitionInfoVEntity

            modelBuilder.Entity<Requisition2RequisitionInfoVEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("RES_REQUI_2_REQUI_INFO_V");
            // Properties:
            modelBuilder.Entity<Requisition2RequisitionInfoVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<Requisition2RequisitionInfoVEntity>()
                .Property(p => p.ParentResRequisitionFk)
                    .HasColumnName(@"PARENT_RES_REQUISITION_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<Requisition2RequisitionInfoVEntity>()
                .Property(p => p.PrimaryResRequisitionFk)
                    .HasColumnName(@"PRIMARY_RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<Requisition2RequisitionInfoVEntity>()
                .Property(p => p.ChildResRequisitionFk)
                    .HasColumnName(@"CHILD_RES_REQUISITION_FK")
                    .HasColumnType("int");

            #endregion

            #region RequisitionChangeReqdateInfoVEntity

            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .HasKey(p => new { p.PrimaryResRequisitionFk })
                .ToTable("RES_REQ_CHANGE_REQDATE_INFO_V");
            // Properties:
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ParentResRequisitionFk)
                    .HasColumnName(@"PARENT_RES_REQUISITION_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.PrimaryResRequisitionFk)
                    .HasColumnName(@"PRIMARY_RES_REQUISITION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ResTypeFk)
                    .HasColumnName(@"RES_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.Duration)
                    .HasColumnName(@"DURATION")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.PrimaryBasUomdayFk)
                    .HasColumnName(@"PRIMARY_BAS_UOMDAY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ResTyperequestedFk)
                    .HasColumnName(@"RES_TYPEREQUESTED_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.LgmDispatcherGroupFk)
                    .HasColumnName(@"LGM_DISPATCHER_GROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.Isrequestedentireperiod)
                    .HasColumnName(@"ISREQUESTEDENTIREPERIOD")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.Necessaryoperators)
                    .HasColumnName(@"NECESSARYOPERATORS")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ParentDuration)
                    .HasColumnName(@"PARENT_DURATION")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ParentBasUomdayFk)
                    .HasColumnName(@"PARENT_BAS_UOMDAY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ParentResTyperequestedFk)
                    .HasColumnName(@"PARENT_RES_TYPEREQUESTED_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ParentIsrequestedentireperiod)
                    .HasColumnName(@"PARENT_ISREQUESTEDENTIREPERIOD")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ParentNecessaryoperators)
                    .HasColumnName(@"PARENT_NECESSARYOPERATORS")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.LgmJobFk)
                    .HasColumnName(@"LGM_JOB_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.LgmJobDescription)
                    .HasColumnName(@"LGM_JOB_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.CalCalendarFk)
                    .HasColumnName(@"CAL_CALENDAR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.BasUomhourFk)
                    .HasColumnName(@"BAS_UOMHOUR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.BasUomdayFk)
                    .HasColumnName(@"BAS_UOMDAY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.BasUommonthFk)
                    .HasColumnName(@"BAS_UOMMONTH_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.Workhoursperday)
                    .HasColumnName(@"WORKHOURSPERDAY")
                    .HasPrecision(15, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.Workhourspermonth)
                    .HasColumnName(@"WORKHOURSPERMONTH")
                    .HasPrecision(15, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ResQuantity)
                    .HasColumnName(@"RES_QUANTITY")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ResWotBasUomFk)
                    .HasColumnName(@"RES_WOT_BAS_UOM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.ResSkillFk)
                    .HasColumnName(@"RES_SKILL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.IsSkillDemand)
                    .HasColumnName(@"IS_SKILL_DEMAND")
                    .HasColumnType("bit");
            modelBuilder.Entity<RequisitionChangeReqdateInfoVEntity>()
                .Property(p => p.IsTertiaryDemand)
                    .HasColumnName(@"IS_TERTIARY_DEMAND")
                    .HasColumnType("bit");

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for DdTempIdsEntity in the schema.
        /// </summary>
        public DbSet<DdTempIdsEntity> DdTempIdsEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionEntity in the schema.
        /// </summary>
        public DbSet<RequisitionEntity> RequisitionEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionRequiredSkillEntity in the schema.
        /// </summary>
        public DbSet<RequisitionRequiredSkillEntity> RequisitionRequiredSkillEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionDocumentEntity in the schema.
        /// </summary>
        public DbSet<RequisitionDocumentEntity> RequisitionDocumentEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionitemEntity in the schema.
        /// </summary>
        public DbSet<RequisitionitemEntity> RequisitionitemEntities { get; set; }
    
        /// <summary>
        /// There are no comments for StockTotalVEntity in the schema.
        /// </summary>
        public DbSet<StockTotalVEntity> StockTotalVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionInformationEntity in the schema.
        /// </summary>
        public DbSet<RequisitionInformationEntity> RequisitionInformationEntities { get; set; }
    
        /// <summary>
        /// There are no comments for Requisition2RequisitionEntity in the schema.
        /// </summary>
        public DbSet<Requisition2RequisitionEntity> Requisition2RequisitionEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionGenReqFromEstVEntity in the schema.
        /// </summary>
        public DbSet<RequisitionGenReqFromEstVEntity> RequisitionGenReqFromEstVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionGenReqSkillVEntity in the schema.
        /// </summary>
        public DbSet<RequisitionGenReqSkillVEntity> RequisitionGenReqSkillVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionGenReqTypVEntity in the schema.
        /// </summary>
        public DbSet<RequisitionGenReqTypVEntity> RequisitionGenReqTypVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for Requisition2RequisitionInfoVEntity in the schema.
        /// </summary>
        public DbSet<Requisition2RequisitionInfoVEntity> Requisition2RequisitionInfoVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for RequisitionChangeReqdateInfoVEntity in the schema.
        /// </summary>
        public DbSet<RequisitionChangeReqdateInfoVEntity> RequisitionChangeReqdateInfoVEntities { get; set; }
    }
}
